{"__meta": {"id": "Xd3cbf2f90e6669b171bd1a49dcaa60c5", "datetime": "2025-06-30 18:10:44", "utime": **********.550765, "method": "GET", "uri": "/search-products?search=&cat_id=25&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:10:44] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.532746, "xdebug_link": null, "collector": "log"}, {"message": "[18:10:44] LOG.info: ✅ انتهاء searchProducts - الوقت: 12.71 مللي ثانية - المنتجات: 2", "message_html": null, "is_string": false, "label": "info", "time": **********.54523, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.080579, "end": **********.550782, "duration": 0.4702029228210449, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.080579, "relative_start": 0, "end": **********.460271, "relative_end": **********.460271, "duration": 0.37969183921813965, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.460281, "relative_start": 0.3797018527984619, "end": **********.550783, "relative_end": 9.5367431640625e-07, "duration": 0.09050202369689941, "duration_str": "90.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47762944, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.008969999999999999, "accumulated_duration_str": "8.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.502, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.618}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.511886, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.618, "width_percent": 4.348}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.526405, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 22.965, "width_percent": 5.24}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.528255, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 28.205, "width_percent": 3.122}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`category_id` = '25' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.533587, "duration": 0.00531, "duration_str": "5.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 31.327, "width_percent": 59.197}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.540533, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 90.524, "width_percent": 5.351}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.542933, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 95.875, "width_percent": 4.125}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2095127879 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095127879\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532072, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-59866685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-59866685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-106542867 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106542867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-648178179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648178179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1952394348 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InU1UE91ODV1K2t6cXRlZUdPd1Foa2c9PSIsInZhbHVlIjoiMnppSkpyZ3I4SlZrKzE4ODZEeVNZNHlGQitoY2oycUV2a2dMN2pkU3UrdkN2b25tV1ZEcENCSHVGMFZGaENxbnhIVUtEejlKTkZ3MWZueWVyeTZnZzRlWnQ5bk9lSkZHMmFGY2F0UFJmYVhMTTRJeCszRE9Xb0tSVVdlZmRGZUZ3YzZFR3ZhVzAyWXdlZkFpYkZsbUR2d3F5cGJPb0t2a1QzRmJzNHJiWisyTVpDaXVTSlZla3FHSjhmR0V3QTdqTGlHczNZbytXVlFFR3c4dmh6YTF2L085Z2d0VHpXYTRpcXhFVitMbS93QXVJRHl2WmlrK0YvZVBzdzBNMXRrK09qaUx6YkNCWmVMMnJ3Y3NUbjdsZ1llQ0ZpYVdVb0RVd1dCaDRCbjRobUVZZTJpVjZ4UWlzeE9FTWVLU0Z1WnRYbCttSjZKZ1lsQThXMUlUSE5KUHlBNDdzdlZDOFNEVUR6MjZsb2ErN0VYTGpGbk5KVVN1Q3hnR3NKRi9YQ0U1aEZQRlBvV2JOWlB4T2g1NEltUVVGeVVlS0lzMmt1MUN1dTloTmJnNlpxcFZOMGtwZUpPZ1dESFNRZlc3OWlHdWFxb21XUWtSR1V6RXVxU1lqdmRCVzcrb2ZwelZWcURJSE04ZXRXMHFobE5MMzVTRitwN3pSdE1iZ1lTZ3FHdGwiLCJtYWMiOiI1N2IwZTVmMjNmZTQ4NDcwOTEyMTM5ZTM2MTE0NzJkMzViMzM2MzRhOTE2Mjg4NzI2YTE1YjVlYTgwYWMzMjEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVDYnBTeVpwbVZpUXZ4YlJYU1I4TXc9PSIsInZhbHVlIjoibUdHeFdMYjVFaVFsUnN6b2NYRDFqTGJDdEo1Mi9DS1crZkU0UU11bmJ4QStpYmpMYlNtK1U4aGgvbjlsRkE5ODk4TGtRMUk4dnNLNlRCN1YwWlgvSFZLb0FwZklYUEdFV2pjYXArV2c4Ym1WWkJyM1V6MGliTktiL2FWWVVxYjBYekw5YmFqb2lzWUJmK3NOcGVRUW81VVk5QzY5YWRTb3lJTDhEaEYzYkp0V2s2Yzd4bUk4ZFUvT0xZTUoxdGZSOXM2NFhKbTVNenk3VjdEQzBCNGdjcjBrL2dGaXRzT1BKVE5IUStzdGFpM25Id2x3TXRUK2RmaEVaditoMFZJZ3JjWXVnRks5Z1MwTUJHL0NMaDlhNW9ZTGdjbkI5cUI2azFPSWJGaWVGdE10THNMVXRaWnFXY2szWVJGYnlDMXRoV08zNWpud24wNFcrVC9PdzlUVkplaTd6Rnp6N2pOMWRwNnlRKy9wdGt1ekdTcDZkazhaVk90WmpjVlpMODZOdXJEZlhzbmFiTWU3aHZ0Mm5qbEZLQmlPV0lnWG9zRmFSZjZhNVFrcVRSZUlBNytCOW9iQmViK2VRTDdENGhQMy93OC91eElCQnp2WjVjVEwvRklaOWI1dkkyRkFjc1RIbXZSSTBxdnMxazEyVnNDTHJOTkkwK00yUDFMMS9MZ2oiLCJtYWMiOiIwMzczNjkwMWUyY2RlMWYyMTIyNDBiOGE5NjUyM2Q2Y2Q1MmY2NWM5NzhiMTJjMjdlOTQwYWI4NTA0MjFlY2NjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952394348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-461256242 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461256242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1933295326 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5lYk9RNjAyZG1qUEJrck1QcS9BM1E9PSIsInZhbHVlIjoiWkFtcTczd3M0R1NwYkhOUlhxUVcwYm9URU5ianBUVThNckJBNjBlM3l2cWQ3WDlHdGRHN09vMXVFL3N6TitNVVN1c0FqY254NnM4SnNHRFNhbVorTFNEQnRGWi9CdnpxN0JlalU1cFlYZHYwZGlBbWR4NXhTRXdxY3pqajdqUERHUnQxM0VLcnBhZ2krWkttckRQZFdLS08yek82S3NmbnlxVFVBMkJhYkJvcUdjUWdVQkh2b08rTDY1ejdZLzhsdHlQREc1UC81NDZza0ppWGw2UjlMaFZ6OS9ZNDVkc1RtVFNWb1BXenIvZjFBSUZOM1Jmb2w3SWxFUlJGdnhETjkrQkNHbkhjYXE5VXFrOGRDZVRqUjJpUEg0a3BoWjAvbldpUWxSYmlZKzBocm5SNWVaNUNsYzRiZ2FOVEpUell1SUtXbFMwR2JEUVM1M0tobWlJWVp1N3JaS1p2aE5wOXF5aEhSTkxUaFM2RW9paldPUWdxZkVHcHdjQTZMdWdVYzZ1cWZ0TFhiYUU5TTMrcS93Zmk0c25OQ0NkVXZ1Q0JOakFCOWNHOXE1KythRkFnTnMzUFY0a3U4aCtjNmRPNGZ6b25oV3pzMWlnYkxrQVpJMW9EZXVkT3Q5N3FRVXgzelUzQzdyemNjUHdKMWkwOENKcXNweVJMV3hxNWRxSXkiLCJtYWMiOiI5ZDg4YzY0OGU4OGQxZTM0ODM2ZGNhZjZlZWM4MTE0MDA4NDljOWNiMzBkYmI1MDQ5YThlOWI0NjJjY2M2YWUyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJReGRyQVUxQ3FIcW94NXFXS0tTMXc9PSIsInZhbHVlIjoiK0svMnFVV1FpZ01YdlBhaEVKc1B0N1dacXRGQTQ0TGJtUmtHQVJyWGVMMSsvTHEwSEFnbHFhOEY5UXAyUDRuVlI2WmpLRHZCcVk5OUlRcjF2a29IY2JwK0F3TmdwaFArRWsrcVhlZGtYd3YvbVpIQnF4cGszUVErL0RPNG9iL25Qd3NGZ05BNkl6UStFckFtQlNmeFZDZ1VCMnJCakt5cFVrVGd6anRWcnJSUmtuckpQa21tdUNVak4xMFhuSmljZjFCT2hUOFlrSXJuYW1QZ0h2N0EyWmJtUzVOanZaTXVNeUpQU0JJNnVrRTViSUJDcTdpYTMrZUNEdlQ0LzYxZWtWQUhBc3ZrYXNzQjY5L3FpVVBzcnlQN25CSWJOK25hamZHUjMwNGdUZThuQ1ZQVjZpc1I3YUMzeU0zMkVXZzFFWlAydU9pVDhQTTVBdFdzaEJib01CNlp0c1JnR0V6bjduQkhERzBkUjhjVEw3TlZpU1Nhd0RNOEJFY1RIOUZsalJPZ2xqUHBFVFJ0Wno0eDdRVWd2bGFyWXJFOTlDL0czY0VGcTBRV3FrSUZOSXNFN2tmZktERDVpQTJFV0lXS0ZpemZsdHc5SFZCNURDMThJdWo0L3ZQTm9MZjlqemV1SWxyNmF1VWRJbGhFdEY0R0l2V2ZTWXM4Y1dWcWZWbDkiLCJtYWMiOiI4M2Y5NzlkYmVmMDRiMjcyMmZhYzQ0ZWU0ZDY2MjMxOWZlMzNkZjU4MDQzNzg0NTUwNTY2MjYzZWIwNTM2YWQyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5lYk9RNjAyZG1qUEJrck1QcS9BM1E9PSIsInZhbHVlIjoiWkFtcTczd3M0R1NwYkhOUlhxUVcwYm9URU5ianBUVThNckJBNjBlM3l2cWQ3WDlHdGRHN09vMXVFL3N6TitNVVN1c0FqY254NnM4SnNHRFNhbVorTFNEQnRGWi9CdnpxN0JlalU1cFlYZHYwZGlBbWR4NXhTRXdxY3pqajdqUERHUnQxM0VLcnBhZ2krWkttckRQZFdLS08yek82S3NmbnlxVFVBMkJhYkJvcUdjUWdVQkh2b08rTDY1ejdZLzhsdHlQREc1UC81NDZza0ppWGw2UjlMaFZ6OS9ZNDVkc1RtVFNWb1BXenIvZjFBSUZOM1Jmb2w3SWxFUlJGdnhETjkrQkNHbkhjYXE5VXFrOGRDZVRqUjJpUEg0a3BoWjAvbldpUWxSYmlZKzBocm5SNWVaNUNsYzRiZ2FOVEpUell1SUtXbFMwR2JEUVM1M0tobWlJWVp1N3JaS1p2aE5wOXF5aEhSTkxUaFM2RW9paldPUWdxZkVHcHdjQTZMdWdVYzZ1cWZ0TFhiYUU5TTMrcS93Zmk0c25OQ0NkVXZ1Q0JOakFCOWNHOXE1KythRkFnTnMzUFY0a3U4aCtjNmRPNGZ6b25oV3pzMWlnYkxrQVpJMW9EZXVkT3Q5N3FRVXgzelUzQzdyemNjUHdKMWkwOENKcXNweVJMV3hxNWRxSXkiLCJtYWMiOiI5ZDg4YzY0OGU4OGQxZTM0ODM2ZGNhZjZlZWM4MTE0MDA4NDljOWNiMzBkYmI1MDQ5YThlOWI0NjJjY2M2YWUyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJReGRyQVUxQ3FIcW94NXFXS0tTMXc9PSIsInZhbHVlIjoiK0svMnFVV1FpZ01YdlBhaEVKc1B0N1dacXRGQTQ0TGJtUmtHQVJyWGVMMSsvTHEwSEFnbHFhOEY5UXAyUDRuVlI2WmpLRHZCcVk5OUlRcjF2a29IY2JwK0F3TmdwaFArRWsrcVhlZGtYd3YvbVpIQnF4cGszUVErL0RPNG9iL25Qd3NGZ05BNkl6UStFckFtQlNmeFZDZ1VCMnJCakt5cFVrVGd6anRWcnJSUmtuckpQa21tdUNVak4xMFhuSmljZjFCT2hUOFlrSXJuYW1QZ0h2N0EyWmJtUzVOanZaTXVNeUpQU0JJNnVrRTViSUJDcTdpYTMrZUNEdlQ0LzYxZWtWQUhBc3ZrYXNzQjY5L3FpVVBzcnlQN25CSWJOK25hamZHUjMwNGdUZThuQ1ZQVjZpc1I3YUMzeU0zMkVXZzFFWlAydU9pVDhQTTVBdFdzaEJib01CNlp0c1JnR0V6bjduQkhERzBkUjhjVEw3TlZpU1Nhd0RNOEJFY1RIOUZsalJPZ2xqUHBFVFJ0Wno0eDdRVWd2bGFyWXJFOTlDL0czY0VGcTBRV3FrSUZOSXNFN2tmZktERDVpQTJFV0lXS0ZpemZsdHc5SFZCNURDMThJdWo0L3ZQTm9MZjlqemV1SWxyNmF1VWRJbGhFdEY0R0l2V2ZTWXM4Y1dWcWZWbDkiLCJtYWMiOiI4M2Y5NzlkYmVmMDRiMjcyMmZhYzQ0ZWU0ZDY2MjMxOWZlMzNkZjU4MDQzNzg0NTUwNTY2MjYzZWIwNTM2YWQyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933295326\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-816349723 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816349723\", {\"maxDepth\":0})</script>\n"}}