{"__meta": {"id": "X7d438575c3a2e3af57b3e0950a403eab", "datetime": "2025-06-30 16:09:54", "utime": **********.485695, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.014576, "end": **********.485709, "duration": 0.4711329936981201, "duration_str": "471ms", "measures": [{"label": "Booting", "start": **********.014576, "relative_start": 0, "end": **********.427956, "relative_end": **********.427956, "duration": 0.41338014602661133, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.427965, "relative_start": 0.4133889675140381, "end": **********.485711, "relative_end": 2.1457672119140625e-06, "duration": 0.057746171951293945, "duration_str": "57.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027300000000000002, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4642959, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.399}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.476321, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.399, "width_percent": 17.949}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.479044, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 85.348, "width_percent": 14.652}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1615337026 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1615337026\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2044510831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2044510831\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1890864132 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890864132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299642499%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlXakJqVk0yejZKWjBCTFNXRWhCaEE9PSIsInZhbHVlIjoiSjRMN0hqakI2RDBHOE90OVNtUDBEekV6bHBuZjl0V0tEd0pYT2VXeS9JTkhDeENrNzJxNmwvdUdwMkx5WlFDMEk2dDV6QjNTRDJvRDZObFRHMzRzUnYycmdFS0l0QnF1azhLUVNaY281MDEyeS81R3oxdHFxMVcwUHVZdENSLytRdEMwYmlMbWpYZWh0d1Y0aVZrRnA1em1XR2o1bng5ZUhEQ3lGTFduNWVLMjVHK0lJeHoya05VMkxXSjVCUDVjckgrb1pkVXA2Qkgxd2pOVm4ycE1CUzIxT3dzcmQzUkoxaDY2NEVCbUV5cTVLRUJUeUV3YnBoTE4xSHNVeDc4SVQwTUV2cTR5eG13MEhEWHlPRy94dFhoR2dsellKYmsvd2FiOTVQSDlFSGRsR3JJUDlNaE1idmc5WjB0dnlJU20zcmZyVDZxR3lOKzgwMkM2MjVoWldUQ2NnODd4Y1pjK2lpNWRSUUJ4bVpUelF5bXBmUVNNb3ZVL1ZjcXU2VHdIa3drQVRsQ2twbFVFZkttU2kwQ2FXTkZWR1BlRUIwVFJQWEFiSFRPQTEyVzkydERNZUJ2bHlDVGVVTUQwbU5kYzNPbEh1OThuQWwvdDhzSzBoQnBRTFNYbUZhOW15MHZxY1I5RFp0Zk95N1ZHZUxhVmVjZ0VtOVpKaDU4K29uOWsiLCJtYWMiOiIxYmVmMTg1OGFhMGE3Nzc2ODU2NGJiMDQ3MGVlNTY0N2UzYWJiMTdlOTZmYTJjMjA0ZDllOWE1NWE3MzVhOTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1pRitFZDJOYTJpZWdwYk5Tc2tveVE9PSIsInZhbHVlIjoia1V4aGRDbms0R05RR2JlRlpSQWpzVi9WTlFzQU5hOFFmS3V2bVBOMUREZXJBQUY3M1NOMHJaUGdHcHB6SW52cGwxV2FjTFppVkR3dHBYZVFua3FmWEZEQlNWanlQYzVqOEtaRUpBYXlkL3FPbWZveCtiWTRPdGNxM2lXMU5zUmtlTDROODRLWVpXQUw5WGZpQTYzNGZnbFlRVFA5UVdlQVAxNklRMlFsbFl2czJqL3NoUlJBd1dYelhhYVZpUlpYN0kwVnZCNHd3clNxOFQ0RjdaTTFRRldjZDBKdCtYMlRPNXM5WVVYNVJWWFI3WUFSMWhvVERDTXBSaklJa09xYzY0WTdmYmF6T3RMK3FRUW9mOExqOW1wTVJkOFZYc2VPc1U4Tmp1VWpnQ09CbHNnN08rWXZTaUJrVi9ET0Nid1g0K2tlYVpIdVQ2ZXdoRmFpaStha3VjSjVRRFBJVUF5MllnY3lCK3B3MVVxTVU1bzVjNCswMEhqVVFJTlB0SHhVNzlacHhTaXB3UUVqYXRuU0NkQ3c5MU13TFREaDBYWHNtL0lDQ3g4WXZXN095VkNpUkhDMkE2M1Bld3VPdmYvakk3WHFrQkhXdW43ZmhGOC9pdTNRNWU2SFF1U3Y3dGpNaWFycFVRWjlISVkyNGFjanVCMnVRbXp4dWxJY0o2cloiLCJtYWMiOiI0OGRhYjMwYjU0Yjk0NzIzNzVjYTljYTZkM2ViYTZjZGYxZjQ3YzhkMWQwYWMwNTI4N2IzMTVjZjU5YWY4OWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1651919461 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651919461\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-420353035 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBIeVZBSXV1anY5bXM3UjVodDZocGc9PSIsInZhbHVlIjoiTHJjcXNZN1F4MUlMdGVwdWt6dnZBejRQOG1xYkRWcVI2NkVtb3hmOUhKaDRCdW1DQm1jMlJXVWVYc1RBcWx2ZzM0NkYzVVgyWW1yaXNRRnFPeWZoWUNTRWhGbVgwVGg2Y1hTTGpSai9tUDdZZFA1VW05cm9taUdvdjVaTGlYZ3Qzb1U5VHhvcVFEeTU4RjZmUnpDVDJSV1hYUW02NmZYQ3BESHJtUTd2WUdFNis5TEdnRERuMWxnN0hXYWlwQnBsQUdoaXVKWUhvQWpmc3FOdy9LeGdKOVpWUkViRE5xTVVNWm1vMURGbFVzTzRaRE93TDQ5K0ZxNkNIZERsdmR6S1dpRkNFWmNtQWNkWENIa1V6MFFrYjdMYkhzcXhEZDF4OGhxc2NRWk9LOEpncDgrRDJKTGhMblAzM2pQaSt1VEorVk9xODdTM0o5SmFuSmx6OWpOcVF4Mjk0VDNEb3BFQ0Z4YTU5RlQ4N01qQlhVSjRDcXh2RCszdXdibm13K2xBenpkQU5uRE1pN01FTzhBbkFwSHZ0eWJ3T2dwUzZTT3d1VDgvUnI3NHk5WGJlNWhrR0FUdlhla3FkV1NIekFDbzRrcWNDTEhmUGtRNjlzVlFsQjIwZjVzZ1oydncrN3lLN0tUektYKzhPcUsxN2wrSlpLZjBYZ0RhNER6UFlhSGYiLCJtYWMiOiJiZDY1ODUyMWU0NjJlMmQyZGRjOGE4ZGU4MjIyNzZjM2FmMzFjNTI3NDA4ZmMyNGIyYmUyNWYwYjM5ZDc0YmU0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5LNE5lbmtEWkg2V00zVENSbHJvM3c9PSIsInZhbHVlIjoiUzEzaXIyeDN6c0YvYTdxMkJTaEorWHVkWW91S2JJUm4xT0lmUVVxSk1va1M1YzRFRktHRk5xRTJhK2x2YWtOZXhGOXBnRGFxWWVXQjRBbndidXlpSE5ISGc4QWJ2ZjVnTElIUXdTaHRvaXZWR3gvQm9jZytVMllqNnY1OHhFUFVtOGV3Wlp6MzI4YS9DaFczQy9UTGhJa3Vlek42dklFUFRhOXh1Q0NweS9ud1gyai9HVkRKRWFsZWdqNnZsVWpMTkxHVkc0aG81SjFBNmhTcDBZSURxZlo2OUxQT2U5b0YyV1I1dWFnR3lsdkp1RTRoUko2Y0Y4YWdlYy93OHBSYk1nVHIzdlVhZWJOeFhWemhIODBkbXgydVd6M0c3Y2pNMlJueFkxZHNYay9Qc3hyT0NLMmtBQVc2ZWxkRnFhSm5CN2E2V1dUTzgydkgvbnBzdkk3R3JUV1BIZ1dwZnlUVnJYNHczWVo4YUFSdmUxK2Qxc3FkN3FQOFo3M1BGZHA5aWFyRk8rRUZ5dDRwRmRBeTJYRldibUtEMkNaOVA3VTc3RFlCTWRWaHN5VTlBdDZKNStwT1VPTEdkdjVuMW0vWkJhdnRPbWo5NWZmNW9wbzlpUlJ6Ym95emtBajZUbzEvWDAwVHdoQWY2anVpaDZKdFI1cW1xR096R1Z1VkpPNWciLCJtYWMiOiJjZTIxNTM1NGIxZDAxMDNiY2MwMzMwNmE0NWI5MmY5ZWFkODIyODVkMjNjNjc4NzJmNGYwOTMwNDU3YTQ3N2ZjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBIeVZBSXV1anY5bXM3UjVodDZocGc9PSIsInZhbHVlIjoiTHJjcXNZN1F4MUlMdGVwdWt6dnZBejRQOG1xYkRWcVI2NkVtb3hmOUhKaDRCdW1DQm1jMlJXVWVYc1RBcWx2ZzM0NkYzVVgyWW1yaXNRRnFPeWZoWUNTRWhGbVgwVGg2Y1hTTGpSai9tUDdZZFA1VW05cm9taUdvdjVaTGlYZ3Qzb1U5VHhvcVFEeTU4RjZmUnpDVDJSV1hYUW02NmZYQ3BESHJtUTd2WUdFNis5TEdnRERuMWxnN0hXYWlwQnBsQUdoaXVKWUhvQWpmc3FOdy9LeGdKOVpWUkViRE5xTVVNWm1vMURGbFVzTzRaRE93TDQ5K0ZxNkNIZERsdmR6S1dpRkNFWmNtQWNkWENIa1V6MFFrYjdMYkhzcXhEZDF4OGhxc2NRWk9LOEpncDgrRDJKTGhMblAzM2pQaSt1VEorVk9xODdTM0o5SmFuSmx6OWpOcVF4Mjk0VDNEb3BFQ0Z4YTU5RlQ4N01qQlhVSjRDcXh2RCszdXdibm13K2xBenpkQU5uRE1pN01FTzhBbkFwSHZ0eWJ3T2dwUzZTT3d1VDgvUnI3NHk5WGJlNWhrR0FUdlhla3FkV1NIekFDbzRrcWNDTEhmUGtRNjlzVlFsQjIwZjVzZ1oydncrN3lLN0tUektYKzhPcUsxN2wrSlpLZjBYZ0RhNER6UFlhSGYiLCJtYWMiOiJiZDY1ODUyMWU0NjJlMmQyZGRjOGE4ZGU4MjIyNzZjM2FmMzFjNTI3NDA4ZmMyNGIyYmUyNWYwYjM5ZDc0YmU0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5LNE5lbmtEWkg2V00zVENSbHJvM3c9PSIsInZhbHVlIjoiUzEzaXIyeDN6c0YvYTdxMkJTaEorWHVkWW91S2JJUm4xT0lmUVVxSk1va1M1YzRFRktHRk5xRTJhK2x2YWtOZXhGOXBnRGFxWWVXQjRBbndidXlpSE5ISGc4QWJ2ZjVnTElIUXdTaHRvaXZWR3gvQm9jZytVMllqNnY1OHhFUFVtOGV3Wlp6MzI4YS9DaFczQy9UTGhJa3Vlek42dklFUFRhOXh1Q0NweS9ud1gyai9HVkRKRWFsZWdqNnZsVWpMTkxHVkc0aG81SjFBNmhTcDBZSURxZlo2OUxQT2U5b0YyV1I1dWFnR3lsdkp1RTRoUko2Y0Y4YWdlYy93OHBSYk1nVHIzdlVhZWJOeFhWemhIODBkbXgydVd6M0c3Y2pNMlJueFkxZHNYay9Qc3hyT0NLMmtBQVc2ZWxkRnFhSm5CN2E2V1dUTzgydkgvbnBzdkk3R3JUV1BIZ1dwZnlUVnJYNHczWVo4YUFSdmUxK2Qxc3FkN3FQOFo3M1BGZHA5aWFyRk8rRUZ5dDRwRmRBeTJYRldibUtEMkNaOVA3VTc3RFlCTWRWaHN5VTlBdDZKNStwT1VPTEdkdjVuMW0vWkJhdnRPbWo5NWZmNW9wbzlpUlJ6Ym95emtBajZUbzEvWDAwVHdoQWY2anVpaDZKdFI1cW1xR096R1Z1VkpPNWciLCJtYWMiOiJjZTIxNTM1NGIxZDAxMDNiY2MwMzMwNmE0NWI5MmY5ZWFkODIyODVkMjNjNjc4NzJmNGYwOTMwNDU3YTQ3N2ZjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420353035\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1144440186 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144440186\", {\"maxDepth\":0})</script>\n"}}