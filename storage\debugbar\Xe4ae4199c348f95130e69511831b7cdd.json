{"__meta": {"id": "Xe4ae4199c348f95130e69511831b7cdd", "datetime": "2025-06-30 16:17:54", "utime": **********.987809, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.562842, "end": **********.987824, "duration": 0.42498207092285156, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.562842, "relative_start": 0, "end": **********.935536, "relative_end": **********.935536, "duration": 0.3726940155029297, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.935545, "relative_start": 0.37270307540893555, "end": **********.987826, "relative_end": 2.1457672119140625e-06, "duration": 0.05228114128112793, "duration_str": "52.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45227064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0021799999999999996, "accumulated_duration_str": "2.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.966934, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.431}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.977631, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.431, "width_percent": 32.569}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-256791639 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-256791639\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-188522092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-188522092\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1594690394 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594690394\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im54bnpqMmcrcUhpNUtlaW1iRFAvcWc9PSIsInZhbHVlIjoibldGWW9sbjBqS1JBMUZaV3hnczRLamJZS09ITXJOcnd3eDk3aTdhK3M5M1lKUlZ4UGl0VGFuNmF4Tmh2NjcxTTBtaEFUbk5qY253MGxmQ1V4OWFTVTY3enRnSk9KZ3J3U2pQYWlSdURmTXk2YWtuVHcydHdHTkNMcTBnb0lmeDBSWi9PeEdTdTlBZFdEc1RQZHh5Y1VwVmtPM1Zjd0FibE81RVlRZGNTYjFGRjJEVjh6bkRIV2ZuNGpiWklhQUJ6ZnorVkx0RWRrQ1RSS1FCU3FxOVMwUldQWmVod2t2dzRoM08zbWFHbktFd2lydEorSndqL2RTaFNTRUhUZGdXaWVmMjBFSXlSM2prdVZBMmQrekx0M1Vodk4xTjNiWFNuMGdENmZjK2daa013SDNXdXlEUUEvRFVWaHlOcUg3eDZBM3hhUkdVNU42NVdSZ3NoWDcxZU1KOWg5SmpqcGduQVUyYUxUd2FCY3N6MTMzVWN3ZHNZVU5aU1RyR3BIQmZXZjM0cEdSNEJOc1VaZ2JwbCtHbTVlRE5FTEQ0Qi9jc1NEQnBZbW43Tm5oYy9nNXhvY010TXJ0STZYblFjR29lMDNaRmJuU0lSMkJkYUw2MjdoNzBkSnBjb3IxTG5BbFBlUG5YU1VzNCtjdHRYeDlaYkpOWFBsUkZwbFVrQThKVkwiLCJtYWMiOiJhYjEzZGRiODllNjQwNmM0MDk0NmExMjgyMjEzNDY2NmI5YjRlOGI1ZmVhMTEyZjJhYjQ4ZGVhZTZmNzNhOGI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlN3YTBkNGFsZ21jWWVZZzBqMkZITlE9PSIsInZhbHVlIjoiUUdCWkIrS0QrODZ4K21RcldWRVk5SVN0M20yeXdMRkpxTzQwOGlzcXRaK1llSDM1RVlWSFplRDFRdXU4aU1EUmRGekJ3STRJUlU4TXk5d0xGeHFKYUFqVDFsaGhJakxBa1QrL0prNGlESUJHTUl3NW1Va29QUldGT2xjc1ozZCtDTmRCckRaTUFvSmNZaW9zTGtOMUI5RW1sZWJsVmRkblhwYXd1NWxDL2RCM0pJNGlHVUgrNGxrOUU2UlFOL1lXcVl0eFUzTE9rbVVDQmRUc1dKZnFNd3FaZ3gzMXNka3M5d25vU1RxRWd2OFBzWDB6cXh6Q1h4RjVtVE1qM0ZaMUVqUmhvMUtvR2pIU2VLNFlQOE42a0Z5eWJoQjVWc1h1SzJKa1MybHQ1dzBiOTA2ZTYzcDZlZGVUaFNhbVlqYzRIem9DOFVDYWVha2dQTjF2Uk1adjkwWmJPN1dPWE1KN2YrcGUyeVNQYWxQQ1FHT3k0TEh6VVZCcTBCMkE5VFZYamgxNDE5MjlYTGw0OE1RN3MyUG5XUGFrNHdVeEZWTGhkc0wzZ21ZTnpnbHlmU1hXaHZ6NEFHUnpJaTlweTVMUFVZeGZVY3MvT2R4RnpIT1ZIZVE1UkkxblVEbE1zWVZ0UjE1OExZcDlsN3lLdDQ2UVF3eGFJRWVPc1M0WUlQaEYiLCJtYWMiOiI3ODU3OWU3NDUyZTM5Mzc5ODhkMTk1ZTQ3NTg4YzFlNTZlOGJlZjRjOGY4YjA4Nzk2ODA3NGI3ODU0NjJkMGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-981017216 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981017216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-457775516 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkUycThWK0hMMTUySEtYUUlHUzNBRlE9PSIsInZhbHVlIjoid29jSTVrQ3pJWWtNb09odmNldk5wWnZTbjVYWGE0c0xVbUovN0xtdXViY1lhZ2dGWGplRUxrQW1MWjNnL1VzT0MvcDNlL3N4QkwvSUhjeURDekE2bVJ4OVdWOTVUeitUVkdub2lDMjVyS0p3T3NDTGQ1T0FtVzlxMC9SQWZDT2thUUszYTNrNjVXZTgwclpJVENtRnNEdnBsM2dER1gzeTlLY2FzVlYyQUF5Mm5NR2x5c3Bmc3JqZ09QK0dBZTV3S25aeHZNUGgvRS9GY0FWSVNzemU4dzUvSjhZL2UrOWJHekVZeDhRTURMMHhHSDlpOGVRM1JYQ2dENHIwNTFnYmhmajZzamc5R2Npa0ZQKzNlQmRsS2gwVFN2SkloQm1LNzRjc0hyRURHTWc4dVNic09FOTQrQ2JlcTdDNTdDM1VkM3BSY2ZuVkVGaWFVREQ3Q1dad3djNEIwOWgvbUI0NXBlTzJDclpDVUY0VlE5UkQvckoxQi8zdkRqa3BJYVI3aU5iLytaMlFsK1BYdm9NY3ozMkhaMW9raTQyTTdkbXBPUnVtTjBBNzN1L0ZtWWgyMXl4Nk1jRkJhZWlJamM2cXdvaTJGL1VrQ1E4ZHF6UEVrOUdsS25BWDJBV04wdFI3Sk4rNEJEZkxjR2d3d1dxK2xnQW5wRXl1QzlWU3BhMVYiLCJtYWMiOiIwMzY3ZTdiNTkwM2Q0Nzc0ODZhZWYyN2EzN2IxODk5MTZmMzVkZjBhYmI1MTI3Y2U1OGE4ZThkNmZhMzI3OTliIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZQVnkzdmkwUVVNVG1NZWhvU3NLTWc9PSIsInZhbHVlIjoicVdqNEczbHlqY3VtejRrbkJTWVVSTmdDd1hMYzkybGEvTUJ3ekdBSTZqQjgva3NkT2hyY2tjNW8vSUtBV1pxZ2tyZWhGL0ROL0xSby82V1FCR01RODJMdGdPR1NZR2hncnlqZFY2Ly9obFhINTRzcW9yVy9KZVhkYWpZY1J1UnN3NW8zSytOSC9kdFhYaXFBZmEwak9NblJMOXQ1SEhreUV2aGJ6MG56ZWU1WC84bk5Tdk9qYmVMOUg3S0V6VElrNzAvWjhBZWUzZjhLYmpWRXBLN3dPbDgydEVxcXdKWHpINnFsR29nL2dlckZTNHNreUJHczBTN1FkdDdSLzladm5pTnd6QUpBVnBuZ21vckVva0VNTTlTTXhQODc4WHJmZytkTWgzUHN0bDVMZzF3TFlEVEV1eGZBdXEvNllMeUY1LzJnRnh3OEpBVm92TTZKSENxWGZmdVVscm9hQWVRL2YrOGtUVEwvZ3R0ZHhYYmk0bTFSazBtaFUzT0hEU1RKNy9Xbnh5SlYxaU5EMzlMaDRjQmNONUl5NUN0M1B5bkpyRERPclArak9UK29KZjV5MlE3TE5xc0FHL3cxV2J3T1RjYlZoYmxvSThzTXlaRjBhc3ovNndWUTJHVUc3bk12eThuSElWblZVcThXR0dqSkNDNWJRS0YySDNmd0VPa2IiLCJtYWMiOiJhYTBiNmNlY2FhNzQ5ZTI4Nzk0MzYzMDY4ZWY4MGJiYjBlMWM3MTFiYzBhOGNmNWYwYjRiZmE5ODY2ZTEyMzc0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkUycThWK0hMMTUySEtYUUlHUzNBRlE9PSIsInZhbHVlIjoid29jSTVrQ3pJWWtNb09odmNldk5wWnZTbjVYWGE0c0xVbUovN0xtdXViY1lhZ2dGWGplRUxrQW1MWjNnL1VzT0MvcDNlL3N4QkwvSUhjeURDekE2bVJ4OVdWOTVUeitUVkdub2lDMjVyS0p3T3NDTGQ1T0FtVzlxMC9SQWZDT2thUUszYTNrNjVXZTgwclpJVENtRnNEdnBsM2dER1gzeTlLY2FzVlYyQUF5Mm5NR2x5c3Bmc3JqZ09QK0dBZTV3S25aeHZNUGgvRS9GY0FWSVNzemU4dzUvSjhZL2UrOWJHekVZeDhRTURMMHhHSDlpOGVRM1JYQ2dENHIwNTFnYmhmajZzamc5R2Npa0ZQKzNlQmRsS2gwVFN2SkloQm1LNzRjc0hyRURHTWc4dVNic09FOTQrQ2JlcTdDNTdDM1VkM3BSY2ZuVkVGaWFVREQ3Q1dad3djNEIwOWgvbUI0NXBlTzJDclpDVUY0VlE5UkQvckoxQi8zdkRqa3BJYVI3aU5iLytaMlFsK1BYdm9NY3ozMkhaMW9raTQyTTdkbXBPUnVtTjBBNzN1L0ZtWWgyMXl4Nk1jRkJhZWlJamM2cXdvaTJGL1VrQ1E4ZHF6UEVrOUdsS25BWDJBV04wdFI3Sk4rNEJEZkxjR2d3d1dxK2xnQW5wRXl1QzlWU3BhMVYiLCJtYWMiOiIwMzY3ZTdiNTkwM2Q0Nzc0ODZhZWYyN2EzN2IxODk5MTZmMzVkZjBhYmI1MTI3Y2U1OGE4ZThkNmZhMzI3OTliIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZQVnkzdmkwUVVNVG1NZWhvU3NLTWc9PSIsInZhbHVlIjoicVdqNEczbHlqY3VtejRrbkJTWVVSTmdDd1hMYzkybGEvTUJ3ekdBSTZqQjgva3NkT2hyY2tjNW8vSUtBV1pxZ2tyZWhGL0ROL0xSby82V1FCR01RODJMdGdPR1NZR2hncnlqZFY2Ly9obFhINTRzcW9yVy9KZVhkYWpZY1J1UnN3NW8zSytOSC9kdFhYaXFBZmEwak9NblJMOXQ1SEhreUV2aGJ6MG56ZWU1WC84bk5Tdk9qYmVMOUg3S0V6VElrNzAvWjhBZWUzZjhLYmpWRXBLN3dPbDgydEVxcXdKWHpINnFsR29nL2dlckZTNHNreUJHczBTN1FkdDdSLzladm5pTnd6QUpBVnBuZ21vckVva0VNTTlTTXhQODc4WHJmZytkTWgzUHN0bDVMZzF3TFlEVEV1eGZBdXEvNllMeUY1LzJnRnh3OEpBVm92TTZKSENxWGZmdVVscm9hQWVRL2YrOGtUVEwvZ3R0ZHhYYmk0bTFSazBtaFUzT0hEU1RKNy9Xbnh5SlYxaU5EMzlMaDRjQmNONUl5NUN0M1B5bkpyRERPclArak9UK29KZjV5MlE3TE5xc0FHL3cxV2J3T1RjYlZoYmxvSThzTXlaRjBhc3ovNndWUTJHVUc3bk12eThuSElWblZVcThXR0dqSkNDNWJRS0YySDNmd0VPa2IiLCJtYWMiOiJhYTBiNmNlY2FhNzQ5ZTI4Nzk0MzYzMDY4ZWY4MGJiYjBlMWM3MTFiYzBhOGNmNWYwYjRiZmE5ODY2ZTEyMzc0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457775516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1658257781 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658257781\", {\"maxDepth\":0})</script>\n"}}