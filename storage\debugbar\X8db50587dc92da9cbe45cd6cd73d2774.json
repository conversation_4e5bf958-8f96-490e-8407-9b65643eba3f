{"__meta": {"id": "X8db50587dc92da9cbe45cd6cd73d2774", "datetime": "2025-06-30 16:10:05", "utime": **********.677439, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.204782, "end": **********.677453, "duration": 0.4726710319519043, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.204782, "relative_start": 0, "end": **********.602527, "relative_end": **********.602527, "duration": 0.39774489402770996, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.602536, "relative_start": 0.3977539539337158, "end": **********.677455, "relative_end": 1.9073486328125e-06, "duration": 0.07491898536682129, "duration_str": "74.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45226720, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0254, "accumulated_duration_str": "25.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.635746, "duration": 0.02499, "duration_str": "24.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.386}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6700199, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.386, "width_percent": 1.614}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-36295229 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-36295229\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1701276182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1701276182\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-380071376 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380071376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-485788483 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299801140%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRwcEFoUDM3SGkyTkw4NFJ2bkluNWc9PSIsInZhbHVlIjoiSGRhb1htcnQwRmVWTlFzZGtyRFFPdlFEK1QxbEZpblUwRWFVSEFWN283bmlwNmJIbWowdmJRNzNxQldhdVhROW0rTTRhZWhvZ1NVdEZUQmw2UHYrekkvQkxSRWpmUFQ2M0FaSlI0eHpLUURDZUFpQjJGbFl4OUNLSWRYM2JpMFJSbURVeWN1RksxbWxlT2tuTGpVcCtNZHAwUjJYTlI5Mk5CdHlsUmUybVp6OE5zZ2IyaGd4TEIyd05vK2pPWTU4QUFCOFJvVVMyNURoeUdUQ2dVL25uUGl1aXd2a0F4Q3NMOFZlTkZPNWJydGphTTVOUFNoYTNWQ0NSalAreS85OFYvOEZGTGw1aUs1enNDRWhOMVJiSnBtZXN4MUlEMCtMZXBOZ3IyL0dkWFRSWWVpL0ljYUY3eWo1QkpYbVZvRldnZzkvN0lpMkNoSTNZR2tYWXNxUlNyNGJqQUlQNGxYbSt5VjNBVDc4eklBOXR6Ny9FK2E3OW9sWkxYVHNodFZZWEx6ZEhxS296ampYMFlwRk1zMHM5Z3dValk2ZXN5YnVTMEg0b2JNSWFGcnRqK05yek5UT3NNRDVnZ2hyUnp6Y1ovdzcvSDRZLzhSaGk1VUNORjZxMEhqMGZicytXNi9oTkZmZC90Q3R1eUFCdUlTRFV0SVdZUFlBdFA0WnZ3OEoiLCJtYWMiOiIxYTRlMjVkN2U2MTVjMTRkNDEyYjkyNTE1MWM3YzI0ZDVlZjdhZGNiNWIwNGY3ODU0ZjMxZmE4NTMzNjcxMGNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklrazU3b3FIMUQra3dVQ1JZclk1WUE9PSIsInZhbHVlIjoiRkY2UFVHUlJKT1BRdnh0d2Vnc2JYY2lvTHNzKzJUSXBLS05HeHNJc281alVLVnY1ckFyUmlFalFCWHVmQXpzOW52MXpPOW5RLzJWeHYyalhnU0trYk0rK0RxaFhqRW9Ma2RQRmFHaHBoa0NDalZlUlEzL08rd1EzUzBtMlowcWpjNTNwL1ltZGxSVFVOc0Yrd2cvWHY1K0s5eG5CWW5UUVRENEdUOEt1TWlCUUlaMlJOOVJzWGY4aHBKd0ZvMjE5R2w5cDBRYkgrZm5uZml2RGtRZ0hIQzVwM3lQS1dOU2NZV04wN1VidlVPcXNhWmFYcktmWnQxbERUS2pyTXhlMDBKd3BGWC9wWW5POUd5eGZkaFIzc0VNTVlTWnM3QlhWZ1FTSW5DVzBVS3ovdndGUitRLzVON0xzbUdYRHUvRlBsZFFNaHk0eEhJZzdwZFJWZEZ0cTB3RUp2WWQ5dWYrRWU5Q2RqSlNQemVYVFZMSFVzRHFCZVlYR21PL25ab09zbmVJSmtNQmc4VXhTMldmUFNxaUVYN0ZycGI3SzE5dnhJcWdQMlJaT251b0RUdHRRNXhuUktJYVhZS2VuMVN6TEdDZ0JNNFZEcmxtTDZaMlNBTWJsZmpKdmxOL2dEdUxNbzVGUFN0SUVJVTBRWHo3dkltdTJ1UWlkYm5YN3ByQjgiLCJtYWMiOiIwNTc3YWYxNDUyYmExYzYxMDRlMTBlYzVjNDVjMDQ1OWRiMjAwZjdmMDg0MzY3MjQ5OWU3MTRjNmFlZmI3MzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485788483\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-318890052 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318890052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNsaUZBU1RDWExObHVlaS9WeDhCZEE9PSIsInZhbHVlIjoiTUtDdFRwV2oyUVdmQVJJay9ROUlCd0JOaEQyR3BJemhKREpyZnhaWFZKSWcwQ3lKNjQyNTZWQjN4SWJFTzBiTUY1L0I4RkJTWGc5MUVNdzhKTmQ4M0dYbE5vZnhpR2pCUjFrOENwR0RPQldZb0hCM1BtdmdySEJsbVE1akFMR2FBQ29YaDdoQklsa293SHFMK1g4ZWk3UklpK3Fyd1pyRkRQRDE0SWhBZEFXSWhrcWRyRDRvQlVDUUdqMWt4a3R4UEJhSy9WV1hRUkpJdFlhaFJZQXI2WW1tbGh2TENJRm1PWitIVWV3WjdDYlpEYU41QUZSWVEreVMyWGhRSHVFL2djTy8wN0tYaFJxQ3dHWnd1OERteXFscEt2dUJJdFRBSnduVkRqb0xLdXZYRVhQd0JoeFJPRjFFa1Z0aWlkdlhGUVVlTm1OaCtRNVZKbGdQQjk3U2xDVUlHbnVZSHNqcHhCRDJ4NStSUSt6TnN4Mm5xSEpoaXIzUC9pRnZDa3NYTHQrU0tqRHZkd0pmQkw5YWtKTXVZMlliZE5yNlBseDBINW8vTGZoN2V6WjM2bXErblJBMEpKNlpySjBrY25IU0dxNk9ZRklXcHhqY1REdUgrMnNHc0lsQ1RNYnBDbzZKQ2pjMEg3VUpJNUdsT3lySmNLMjQ3WGhwTGNldEhHdHQiLCJtYWMiOiI3Y2QwODQyNDhlMmZjZjdmM2ViNjdiMGM2M2I0MzJjYmJkODYzOGYyMzYwMGQzMGNmNTRjOGQyZGFjZDJiZjVlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRXVmpVUnlsV01iQlhOampUWXBXZHc9PSIsInZhbHVlIjoiYk12ekxja2Y3WmFqeWRjcW94UC9qMnRJV3ZXdnVldlg2WW5QODhJakphRXdIR0FQSXF1OWZaRE9mOU5waXNweFBCK29GanVJcmNGbzVlUjgwcDhBV0Y5Qnp2Y1ZRelBOVjB1ckF2SjVzaDdjUTJZaFgzbitIUTlaaXdYalhERExrUzJFL25wYnh1VmhWd3dORVQzSHdXNVlsRjJaa3ZERnBySFgxODN4N1JrS0lqQklyUnM5eFoxSHNXZ2dxWi9wNGgra3Y1U3JESzIrcDM4NHFzYng1bHdVaHBuMHFIRWFQRFpxaGlNN2hxb3oyMFpzczZFRE1VeGlhdEZpN1F0eU5kUTZkSjRKT0ZjUi9weDZlZFM3MHhqazR4OW5weXVNVkdxOVhRenJnRWlUQkxSWVFxSERyTXdhcllVWkV6QjFDOENRcDlTRzZJd2xDY3BuU3d1S0l0allYci9SNDhUelFFZ1BBOUxITTZ4bUJYK0JJRFNSVkxrd1U1c0crdmJEdVFOQjJJbUtTS1RHcmJ2dHlSQjlYVndHOVg4QlFGWGROdGxQcEtXSkdQYnEzM01ORmNGZUNFRHVyUXl3ZU8rb29vL0IrSS9WZWVSZURzeDRvUU9RSERvOTFaZFNSTlFzdE1kYTUwYXd2SjFyV3FWZCtQVTZhV3VZUm1tZFJ5T2EiLCJtYWMiOiI5YjIwZDhiMzA4ZjFkMTc3ZTBlMjhkY2JhMzkyZGI3MjE0NTM5NmM4MTViZThiZTI0NDk5NmI1YzhkM2NhZTgzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNsaUZBU1RDWExObHVlaS9WeDhCZEE9PSIsInZhbHVlIjoiTUtDdFRwV2oyUVdmQVJJay9ROUlCd0JOaEQyR3BJemhKREpyZnhaWFZKSWcwQ3lKNjQyNTZWQjN4SWJFTzBiTUY1L0I4RkJTWGc5MUVNdzhKTmQ4M0dYbE5vZnhpR2pCUjFrOENwR0RPQldZb0hCM1BtdmdySEJsbVE1akFMR2FBQ29YaDdoQklsa293SHFMK1g4ZWk3UklpK3Fyd1pyRkRQRDE0SWhBZEFXSWhrcWRyRDRvQlVDUUdqMWt4a3R4UEJhSy9WV1hRUkpJdFlhaFJZQXI2WW1tbGh2TENJRm1PWitIVWV3WjdDYlpEYU41QUZSWVEreVMyWGhRSHVFL2djTy8wN0tYaFJxQ3dHWnd1OERteXFscEt2dUJJdFRBSnduVkRqb0xLdXZYRVhQd0JoeFJPRjFFa1Z0aWlkdlhGUVVlTm1OaCtRNVZKbGdQQjk3U2xDVUlHbnVZSHNqcHhCRDJ4NStSUSt6TnN4Mm5xSEpoaXIzUC9pRnZDa3NYTHQrU0tqRHZkd0pmQkw5YWtKTXVZMlliZE5yNlBseDBINW8vTGZoN2V6WjM2bXErblJBMEpKNlpySjBrY25IU0dxNk9ZRklXcHhqY1REdUgrMnNHc0lsQ1RNYnBDbzZKQ2pjMEg3VUpJNUdsT3lySmNLMjQ3WGhwTGNldEhHdHQiLCJtYWMiOiI3Y2QwODQyNDhlMmZjZjdmM2ViNjdiMGM2M2I0MzJjYmJkODYzOGYyMzYwMGQzMGNmNTRjOGQyZGFjZDJiZjVlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRXVmpVUnlsV01iQlhOampUWXBXZHc9PSIsInZhbHVlIjoiYk12ekxja2Y3WmFqeWRjcW94UC9qMnRJV3ZXdnVldlg2WW5QODhJakphRXdIR0FQSXF1OWZaRE9mOU5waXNweFBCK29GanVJcmNGbzVlUjgwcDhBV0Y5Qnp2Y1ZRelBOVjB1ckF2SjVzaDdjUTJZaFgzbitIUTlaaXdYalhERExrUzJFL25wYnh1VmhWd3dORVQzSHdXNVlsRjJaa3ZERnBySFgxODN4N1JrS0lqQklyUnM5eFoxSHNXZ2dxWi9wNGgra3Y1U3JESzIrcDM4NHFzYng1bHdVaHBuMHFIRWFQRFpxaGlNN2hxb3oyMFpzczZFRE1VeGlhdEZpN1F0eU5kUTZkSjRKT0ZjUi9weDZlZFM3MHhqazR4OW5weXVNVkdxOVhRenJnRWlUQkxSWVFxSERyTXdhcllVWkV6QjFDOENRcDlTRzZJd2xDY3BuU3d1S0l0allYci9SNDhUelFFZ1BBOUxITTZ4bUJYK0JJRFNSVkxrd1U1c0crdmJEdVFOQjJJbUtTS1RHcmJ2dHlSQjlYVndHOVg4QlFGWGROdGxQcEtXSkdQYnEzM01ORmNGZUNFRHVyUXl3ZU8rb29vL0IrSS9WZWVSZURzeDRvUU9RSERvOTFaZFNSTlFzdE1kYTUwYXd2SjFyV3FWZCtQVTZhV3VZUm1tZFJ5T2EiLCJtYWMiOiI5YjIwZDhiMzA4ZjFkMTc3ZTBlMjhkY2JhMzkyZGI3MjE0NTM5NmM4MTViZThiZTI0NDk5NmI1YzhkM2NhZTgzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}