{"__meta": {"id": "X7cac95b472838fa8dd188db2a21294f7", "datetime": "2025-06-30 18:07:35", "utime": **********.924967, "method": "GET", "uri": "/add-to-cart/2300/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.302851, "end": **********.924986, "duration": 0.6221349239349365, "duration_str": "622ms", "measures": [{"label": "Booting", "start": **********.302851, "relative_start": 0, "end": **********.802381, "relative_end": **********.802381, "duration": 0.4995300769805908, "duration_str": "500ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.802392, "relative_start": 0.4995410442352295, "end": **********.924989, "relative_end": 3.0994415283203125e-06, "duration": 0.12259697914123535, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48664120, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00886, "accumulated_duration_str": "8.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.855142, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.377}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.870843, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.377, "width_percent": 7.562}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.88908, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 38.939, "width_percent": 7.449}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8926702, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 46.388, "width_percent": 5.192}, {"sql": "select * from `product_services` where `product_services`.`id` = '2300' limit 1", "type": "query", "params": [], "bindings": ["2300"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.898872, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 51.58, "width_percent": 4.289}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2300 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2300", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.904738, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.869, "width_percent": 35.892}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.910282, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.761, "width_percent": 8.239}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-787941243 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787941243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.897573, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2305 => array:9 [\n    \"name\" => \"بيبيرو أصابع بسكويت مغطاة بالشوكولاتة كرانشي - 39غ\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2305\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2306 => array:8 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"id\" => \"2306\"\n    \"originalquantity\" => 52\n    \"product_tax\" => \"-\"\n  ]\n  2300 => array:8 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"id\" => \"2300\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2300/pos", "status_code": "<pre class=sf-dump id=sf-dump-2042353855 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2042353855\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1236216568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236216568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldUSmlSVHBVNFZqZTAxcFRPM0NmQXc9PSIsInZhbHVlIjoiaURsTHlHaHBLSlpWMDJFSW05T3BLdElrOXM5UVRIcDJtb2F4amJoQ1BTNFh2ck5WWmxvZjBZZkF2MEtNb1BCKzI1VHBreEtuNTZiUjVFOERnNER6dGx3QkpUWEFaZHVUQ1hRTGlEMURNN2c0bUx3UTZvbEFxTEFteXVUa0VMSVpYYk9Uclo4VDFmMmNXRjkvRUFaVzFkU2d0WW9NTllrcXVJWnVteVN4TnJDNWEwZXdLYnNOZmZheVVXZ21sTmRkekdkbkFRRWlXcUtRSnhKbWd0anNjY2dqSW5EcE5vNnMxUUNmTUkyQ3pRdGlwdWZpRFR3V3c4eHNSbSt5WFcxandCSnpVTU5Oa0hNTmNIT3NudkZhL2szQlROZG1aTElkV2VVUENTQ0dsbjJWekswcjUxcVpLejl6bWpKRWhTNVNzaGhxbmgzWTVWL1YrMXg5RkgreFlpUUtuR3FSZmwzVk5hSEFZK0RaeGlQWU1DNVBkZWoyaFdadkFuSThURFNwSTROb2g1Q0MveGkydUlDZnk1d2JQem5RTk9UZE9TSkp3Z2s4YXBZc3JMQlFRbUhXbzVFak9oMS9VdFlnNzdSakhyN1M1R1lxR1M1Qm1wVVJiV2cxdlhMUnZjT054dHBRS3BrRGMxaFZWZERIYXhwdVFjb2l6MTErRHI0bmtPSUoiLCJtYWMiOiIzNWYwMzFlZDQ5NmZjNDc5NTZiM2Y3ZjU5NTI3NzVmMjcwMTU3MDAzODNjYWJjNGM3MWQwYzZkY2EzMDA4YTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IituS3d2UllCY3hINklaV3dPb2Rjb2c9PSIsInZhbHVlIjoiUkJsbzZ2SmFRb3liRWhFdGVYTldlRkR2bWhMNXVYenIzd2tBQU9BWktCaWh4VmtBWEdSTk9YY3VwckJwV1RvOElubkUyYlpLWVZjYkNRckFLRGFsOEJ5bFhDVE1oR2hlSTRjTTdoWnBrZkNmc1JPZFpUV2dYNnM4MnR3WHFZUzBweWduVitLeWxzN3I2YTNZeXlZZ1dzbUNWS3B1YWxnQkdydkMzM2ZkOC9KeGlhU1prVE5icTBkZDZFSUFSUUNTWTE0SG5pN2lHOXV3M2w2eFRUUUFoVUVYVDZYaFZIVml6YTVMMUtvM0tEcFRqZ0M0QjBzalBBaWNnZ2xxb1MxUnJJRkQvUXgxbWRhcTdIampCaHMyOVBmd1Job2lxMG9CWXk0Z0pURlEvUzZOZC9LWWp3Z0lqNEpaY3NpazY2UkNXZXdlQ3lrYWhaTVRPMHlucWw1Rit6YkplcDlFZUFIRVhlbDVLTG5QRktBb1NqQ3g3M0U0RlM5YUxNem5HakQ4cGZKL2k1UldqaHhnSDhTTlY2L3dkZnpSdFpjYURWUFRlNEhNMTc5L05OSTVsM2ZTd3RiSnpCN2dTVDNqU09wZDVqa1Fmd1BkRG9uWXZ5ZWpKcFFON2piQjI0RkNVRmZ2bmZ0T05jZDZJZDg5UFFUZWJ1OGNiMXhpWXdmSWpJUVYiLCJtYWMiOiJkM2I5YTIzYWVjNGFjOWI1MmMzOWE0MDZmYTJmOGFhOTQ4ZGY2ZjdlNjA2MzA1Zjc1NjBmNjM1ZjM5ZDUyNjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1253020724 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253020724\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893141362 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:07:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1ocm9TN05HOEFDcUZ0REJUZzk1WHc9PSIsInZhbHVlIjoiRjJxd0E2MkYxQmZuSElKSEpSQW1QRWU5bFoxUjBza2FMNmxPTHZDbUV2YTlLWUJoS3QyaWhmUDkxZDdVeE9ZbzV3M1ZpVnRubXFvdkVzdUxlaDI0VjF2VVVSUHByUmR3eVhsbUhEWFFVVTdUM3E2WkcxMmdOVjd4QmFJSGRkcEZDQ3k4TTJFNmFHK0FuWjVXT2FSTTVwUkZZbmllOXRYenVwUzVhSFhhZ05TbEErQU9ZbmlpZHo3ZGlTNzZXaFVaZUwrNmFPOGUyQ1Vqb3BpcUt1L3h3L0ZXOGErNGZzZEdJbjkxZTVpcGVGOWFDOWJTVS9VZWUyMExSNDJlYmE5U3NaV092bnZCMEtmajlBbFQ4NkVyT2FqTTZSMHZTZUgzT2JLTTdmdFpHSEM5R214WXdoRFZZNVZlemY1djgxWnZJOG5vdVViMDRMWjJUZlp2WUV6d2RZSEVXZjI0TzJGWnBnK1RXVmZxcUhoajI0OXdpYnBNSm9tajVGSDFIdTdKQjlMT0ZrYjJMa0N1SlRZM2lBZFExSlNZM1BTRVNiU2xMQ0VmN1NNRzNWTmQ1TUsvNVRxbHpZbC9ZdEpJS2t6RUIrNzVjY2hTbHFFQi9PNDdoUnJDUVlHYVhIWnF1ZEdNemNtdFRqVnBxK21pcmRwU1Rhc3VMcy9qTzBYWkhNaVMiLCJtYWMiOiI0YmVkNjI4YWViZjhhZjhkOGQwZDliMTA2ZmVjZTc4NjhmMzM2YmVkZGI4YmJjOGNhNzY3N2QzMjFkOTY0NGUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InE5RmdFZ2t5bHNrU2pSc2M1WUxjQVE9PSIsInZhbHVlIjoiQ0Z3Mytjc0phSkEweFhZbkNLMGIwb2V4bFByajJCQndteG14N2tiejVPalVTSlVQbklvbFJDS1U3RUJjRFFRTERGUnRNRUM4QWZ2WG1XQWZTTW9JQUZObDh0Vm1nZTJxcHJiRWhhZVJnZ1RWZ3VWK0FxWUpJQlRwRXAvMUpFLzFEaUo3MHEyN3RiQVlEbEVCQ0dzYjVHZ0wreHJMcXFwSHRwVjB2OVZYV0NQVVlzNlc5VTR4VnJRYUZ2dHdMQTk4dkNUTGw4OTEzUll6S3lpSjRHZHBzLzlOVGl0NCtibER6cEpDQjdaeGJFeVBIR0JIMlliYXh3Y2xNclBSa3pQdlpZNXpPZXBhK0dJMEZvWEhNdUNFQkJoSnM4ODF2VFJ0bVYzcFYzOE5YU3RJTTdNcklmanJCTUdpbVlqcjk2SmNZZ3B0Rm5GR2FDZURCVW04dk1FaTgzWFhLSEFyZGg1K2pDYTBTL0JPY1hXeDJTMkh4K3NGM01LMFAzRWtWZTJDOGNvMllMOFI5cFA5WEkrMVo3Z2czbDhyVEdpV1dtbzhYQ1ZOay8reVY2WjYwTnVudFVxZXJlQmpsTGU1THRCVG9RNUtCdDNGQ2kyU3h0bE1kemJZTmpTZk9heVlhQ3NxRkh0MFRBL1lHeE9IK1lZdXBJT2QxMXpHeEdlcmo5aGkiLCJtYWMiOiIyZGZlMGYzZGRmNjcxNWZmMjNmYTk3ODNiOGY1NTRiZmQ0ODA4MDc3NDA1MzBkMjU2NGU1ZWY0MWFhMWI1YmFkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1ocm9TN05HOEFDcUZ0REJUZzk1WHc9PSIsInZhbHVlIjoiRjJxd0E2MkYxQmZuSElKSEpSQW1QRWU5bFoxUjBza2FMNmxPTHZDbUV2YTlLWUJoS3QyaWhmUDkxZDdVeE9ZbzV3M1ZpVnRubXFvdkVzdUxlaDI0VjF2VVVSUHByUmR3eVhsbUhEWFFVVTdUM3E2WkcxMmdOVjd4QmFJSGRkcEZDQ3k4TTJFNmFHK0FuWjVXT2FSTTVwUkZZbmllOXRYenVwUzVhSFhhZ05TbEErQU9ZbmlpZHo3ZGlTNzZXaFVaZUwrNmFPOGUyQ1Vqb3BpcUt1L3h3L0ZXOGErNGZzZEdJbjkxZTVpcGVGOWFDOWJTVS9VZWUyMExSNDJlYmE5U3NaV092bnZCMEtmajlBbFQ4NkVyT2FqTTZSMHZTZUgzT2JLTTdmdFpHSEM5R214WXdoRFZZNVZlemY1djgxWnZJOG5vdVViMDRMWjJUZlp2WUV6d2RZSEVXZjI0TzJGWnBnK1RXVmZxcUhoajI0OXdpYnBNSm9tajVGSDFIdTdKQjlMT0ZrYjJMa0N1SlRZM2lBZFExSlNZM1BTRVNiU2xMQ0VmN1NNRzNWTmQ1TUsvNVRxbHpZbC9ZdEpJS2t6RUIrNzVjY2hTbHFFQi9PNDdoUnJDUVlHYVhIWnF1ZEdNemNtdFRqVnBxK21pcmRwU1Rhc3VMcy9qTzBYWkhNaVMiLCJtYWMiOiI0YmVkNjI4YWViZjhhZjhkOGQwZDliMTA2ZmVjZTc4NjhmMzM2YmVkZGI4YmJjOGNhNzY3N2QzMjFkOTY0NGUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InE5RmdFZ2t5bHNrU2pSc2M1WUxjQVE9PSIsInZhbHVlIjoiQ0Z3Mytjc0phSkEweFhZbkNLMGIwb2V4bFByajJCQndteG14N2tiejVPalVTSlVQbklvbFJDS1U3RUJjRFFRTERGUnRNRUM4QWZ2WG1XQWZTTW9JQUZObDh0Vm1nZTJxcHJiRWhhZVJnZ1RWZ3VWK0FxWUpJQlRwRXAvMUpFLzFEaUo3MHEyN3RiQVlEbEVCQ0dzYjVHZ0wreHJMcXFwSHRwVjB2OVZYV0NQVVlzNlc5VTR4VnJRYUZ2dHdMQTk4dkNUTGw4OTEzUll6S3lpSjRHZHBzLzlOVGl0NCtibER6cEpDQjdaeGJFeVBIR0JIMlliYXh3Y2xNclBSa3pQdlpZNXpPZXBhK0dJMEZvWEhNdUNFQkJoSnM4ODF2VFJ0bVYzcFYzOE5YU3RJTTdNcklmanJCTUdpbVlqcjk2SmNZZ3B0Rm5GR2FDZURCVW04dk1FaTgzWFhLSEFyZGg1K2pDYTBTL0JPY1hXeDJTMkh4K3NGM01LMFAzRWtWZTJDOGNvMllMOFI5cFA5WEkrMVo3Z2czbDhyVEdpV1dtbzhYQ1ZOay8reVY2WjYwTnVudFVxZXJlQmpsTGU1THRCVG9RNUtCdDNGQ2kyU3h0bE1kemJZTmpTZk9heVlhQ3NxRkh0MFRBL1lHeE9IK1lZdXBJT2QxMXpHeEdlcmo5aGkiLCJtYWMiOiIyZGZlMGYzZGRmNjcxNWZmMjNmYTk3ODNiOGY1NTRiZmQ0ODA4MDc3NDA1MzBkMjU2NGU1ZWY0MWFhMWI1YmFkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893141362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2305</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"50 characters\">&#1576;&#1610;&#1576;&#1610;&#1585;&#1608; &#1571;&#1589;&#1575;&#1576;&#1593; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1605;&#1594;&#1591;&#1575;&#1577; &#1576;&#1575;&#1604;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1603;&#1585;&#1575;&#1606;&#1588;&#1610; - 39&#1594;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2305</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>52</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}