{"__meta": {"id": "Xec48bb7eef8c6f1b8973d9dc49f8b4e5", "datetime": "2025-06-30 18:49:53", "utime": **********.966545, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.526858, "end": **********.96656, "duration": 0.43970179557800293, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.526858, "relative_start": 0, "end": **********.917153, "relative_end": **********.917153, "duration": 0.39029479026794434, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.917162, "relative_start": 0.3903038501739502, "end": **********.966562, "relative_end": 2.1457672119140625e-06, "duration": 0.04940009117126465, "duration_str": "49.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45350680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00218, "accumulated_duration_str": "2.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9486392, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.101}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9581711, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.101, "width_percent": 15.138}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.960464, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 86.239, "width_percent": 13.761}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "pos": "array:2 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1545878502 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309391540%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRCU2NDc3lQbm0xM1lqK1E3WWRWRGc9PSIsInZhbHVlIjoiM2JKRzE3NVV4b3pJVHczZlQ4Z0dFSlYzUFJ5TndGRzVoY1lvc2VZSHl4WFNaZnAwaDFUR0pUcHJCblFNSThqcVZRWG91bjFjcm10bHZxT3o3Nk5RYUNuZ0QrWkNqY0JndG80dzd0R0F4YyszdXhQMDNmek5Bcmcra3k4d3RvZlVLN2JydnhJRm54cWFTY09KaVcybFJTZXdUSEZxRU91ZDdyd2xPVTBqV3k1V0VBUllUdzFOVXdnalcvVWtYbmJnVUFJRVFxK1VudXByTUFWQXZkK2VSSVhaVTMvWlRMaUpaWDhKbTVOTFNBa2hXSk5yeC9iUCs5SGJGeGhBTE5QUDhyMDJIQm1YVSsrZ2dFOEdqSVJqNHgrTjFlYjJTem9STU1YQ1B4S2FaU08xVmNraDBTZDBVSlN6ci84ak1tQzhrMGEreGJZcWZHdnNVZnByWXJlMGZCT3dDUVhnOUZ5SVpCR1hRMFMvR0NDaU5rRVk3NWNJcThxS1gxVmRoYWY5NnFVNHNtc3hRSGVqT0ZKc3hOSGNlU1IwZStTYzd5bkVDclRnUUZoejlWTmE0STkwS3FqWUIzUDlsSGE5eitqU1dFTFlUbWtPRWdkUlY4dEs1MnUxMWdVbUNmTzBtazhGeUlOUUJrSjJxZ0RjRmFpWkZKQVlsYWNMVnplSk9yS1EiLCJtYWMiOiJiYTZmMDBmY2FiMjE3MjlhYTVlOTUxMWI5N2Y3OGQ0MTcxZjVmOTU3ZDc1ZWM5YjcwMmY5MjEzOTc2ZDA1MGVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBCeEJESHlvZ2FrVEZMc24yN3dvbGc9PSIsInZhbHVlIjoid201VHJXVE1vNEtRbTltV0hXbi9LRzhFWERYNW1RalFiWTk5YTNBRzFwOEV5Z1ZJTk8yU2NjV3pSSk51Q3EwUE5ZczRhdGdWQVpZY0tob2lLbDJBNHhKTVhLWVhRbk05MHJqUTFBVldGa1d2cGpOMUdvVWVJZ2ZJMVlrVXpzNUtqVXBoQ2M1V050WU9SVldTeW1DZFAvdktKQlJWcXE2c0F2SlpIVVUrMlJqaGsyMFg5N0hOeUF6WW9vdjJZaEtnQWhHcEdGc2t4WGpBTUdWdzhlOVFkWk1DS25WTDFGVjhPa1Q2Y2l1dVF3WWRQWk0rLzZVN0cxY0k3Z1FWRXp6LytFQ0VPWWhSenNDdldJem4vM2NtVExrQnhPMzlqMVFZMk8zYldzQjh0Q2RualZvYmxFcHZSMWM5TWJqL1VMSlJ0clpnblBHZDhCdHZrT1ZxVXdEVndRYWRVMmt0TG5CMWhqNkVBTkpNK01od2dVVExuOHdnR05xMUw5MVlVVHJMU2lVU0c4WHhWZ0VEYmV5ZHh4NEY1WmVEMUVVK0YxV2d3RUx2Ymp1ZC93ajllZGs5cTdXRTBnaGFTOHU2VGlkSTk0VCswNWgxWnJXalJiSGE3OEpRcTRhYS9Icmt6Y3ZIR1BlNzVSZ3d2am9Vb25zdnR1RjNkdTN4YnlLU21SdTMiLCJtYWMiOiI0OWIxYzUyYmNkZDhkYWRiYjIyYWE5ZTgxOTA4OGRlNjg1ZGU1ZmFiNjY0NDMzMDUyMjMwMzliMDY5OTJlOWNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545878502\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350734445 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350734445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1450566447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImU4RXdoc2U2c0tQNFdlTmJEMXMyMWc9PSIsInZhbHVlIjoiMWI5c1kvYlRybmxaRjFDSUd4TkN1YjZjdXp0eGRERHplNENDUUhLUWdXK1BBSlFWTVh0cTJWN1BIV3BSaXptSW9mSDdaMTFZYitQUmY0alJBSVdoWlI0dTI5MlVyVVFBQkhBSGxuYjRmTStFVUZLQkdreVFmVmxFYzZoNFg0ME95WDZFQUw2M2E2ZUNzZ2YxVk5MdGpoSzZOSnVSbFBwaDVzYlJNMkVlRk5JaUNKVGxFZ1hkZVdxaG00RXNFVUtyelhTclFraldocGpFSUdIc3JadEN4T0ViVndkV21IVjNBdHgzNzgzeHJkakx0S2xxd1JkK2diRFNobXhlYndsTmpEQkVMNnIva1ZsZHp3N1gxZEVJK0tkclk0eUorMUhlOXNxT0h1M25EM0l1MnU2NVBuQ09LWjVBcVVHa3d0TXJVNmN3TzVWc29Rci9nTmhUTm1hbjBCMDFhSlYyQ1ZWeVF4L2ZoN2xVcVNjRWwxT3dlMGh0M2FFYk45R0JKeEFHNTVLYU5rOTVVRFNDeDRocGRpSGNsc1lYa09vMHdmdWU2M0diWmdrRW9xTEVqQ1M0M2hwTWxnVS9QQnlaWC9uMTgvUkVvQzdrM0EzZmtyck1pOHZkVUNpRlhlTE9YTVJZRmh6NGNjNmxoVENFRmRlSlJYYkFkSjlFWXZCQU41VTQiLCJtYWMiOiIxOWFjMDBkMTI4NjI4OGM0OTdmYWIyZjU3NmZmMjYxNzA0ZTc5YTljNDk2NWFmMjZjNGE0ZGVhNTVjZDRlMzhjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im0xeFpsVHdsa1Z5RVFjOXBaMjd4WUE9PSIsInZhbHVlIjoicGl5MzZrb1lKL1FwRVJ6RlAzeWQ1Vk95VFVqc2lVbllkeng5MW0vT0M4c3Myd1VJcjErZkdncjJkUXFpeXg1SmJXWUlzQmgzcnNLMWl5NjlsZkE0SkdUMThCTFZmWHFCdzBtL3dhRXV5bFdyeTJUQ0VrTnRIdnVoUmhEWEp2Q2Y2RnpFNGkvYnQ2TTU4Ri9GMThBZXBiM3Q0cGZlenlqL0d6TDM3RkRWZXJkYWN5QmJ4UEN5eEZIYnYwalFYK25NVnlnNGQ5WEhCaUJrcUxJdVpNNVZyR0Y5MXFBaVdjRUFPZUY1eVhGaXZRUHFqeDc2MWJGVDBxeHFMTUREUEppK1lyWDVXZ1dBMGJIbUxyaCtKaTgrZDB3Sm5XQ2tLNVhrVS92NWVXcjNTNVhEU1NNaEJ3bTd6cWlSN2dFNkI5bDRsZUZWS3lGWEhpQmVHL2UxY053NzBJRDRVM3VYa1FTd2RGT1Y2bTlHQ0t4ZlRBbEVtVmFhbUR6VXI0WTJNeWZjY2tVOGZxRDRYU0gwQmlic1lMa3VjdkFBalh1UU85Z0VVMlQ4SVl5ai9ScW56VXkxZ3dob0hkK0drdWw0bExUb1RoYWJVRlpzYk5zY3ZkZDVCUFJHMlFtK2UwNFh2WjRqN3FLcUdINk9JWmQrNXc2WlovbWE3KzhkR29GOVNpT2QiLCJtYWMiOiI3ZTMyMDUzZjZkNDEwYjAwZDZlM2IzYjBkZjU1Mjc3OGFlY2E4ZWUxNjQyZTU1NjEzYzIzM2Q5ZjRhYmQ4ZWE2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImU4RXdoc2U2c0tQNFdlTmJEMXMyMWc9PSIsInZhbHVlIjoiMWI5c1kvYlRybmxaRjFDSUd4TkN1YjZjdXp0eGRERHplNENDUUhLUWdXK1BBSlFWTVh0cTJWN1BIV3BSaXptSW9mSDdaMTFZYitQUmY0alJBSVdoWlI0dTI5MlVyVVFBQkhBSGxuYjRmTStFVUZLQkdreVFmVmxFYzZoNFg0ME95WDZFQUw2M2E2ZUNzZ2YxVk5MdGpoSzZOSnVSbFBwaDVzYlJNMkVlRk5JaUNKVGxFZ1hkZVdxaG00RXNFVUtyelhTclFraldocGpFSUdIc3JadEN4T0ViVndkV21IVjNBdHgzNzgzeHJkakx0S2xxd1JkK2diRFNobXhlYndsTmpEQkVMNnIva1ZsZHp3N1gxZEVJK0tkclk0eUorMUhlOXNxT0h1M25EM0l1MnU2NVBuQ09LWjVBcVVHa3d0TXJVNmN3TzVWc29Rci9nTmhUTm1hbjBCMDFhSlYyQ1ZWeVF4L2ZoN2xVcVNjRWwxT3dlMGh0M2FFYk45R0JKeEFHNTVLYU5rOTVVRFNDeDRocGRpSGNsc1lYa09vMHdmdWU2M0diWmdrRW9xTEVqQ1M0M2hwTWxnVS9QQnlaWC9uMTgvUkVvQzdrM0EzZmtyck1pOHZkVUNpRlhlTE9YTVJZRmh6NGNjNmxoVENFRmRlSlJYYkFkSjlFWXZCQU41VTQiLCJtYWMiOiIxOWFjMDBkMTI4NjI4OGM0OTdmYWIyZjU3NmZmMjYxNzA0ZTc5YTljNDk2NWFmMjZjNGE0ZGVhNTVjZDRlMzhjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im0xeFpsVHdsa1Z5RVFjOXBaMjd4WUE9PSIsInZhbHVlIjoicGl5MzZrb1lKL1FwRVJ6RlAzeWQ1Vk95VFVqc2lVbllkeng5MW0vT0M4c3Myd1VJcjErZkdncjJkUXFpeXg1SmJXWUlzQmgzcnNLMWl5NjlsZkE0SkdUMThCTFZmWHFCdzBtL3dhRXV5bFdyeTJUQ0VrTnRIdnVoUmhEWEp2Q2Y2RnpFNGkvYnQ2TTU4Ri9GMThBZXBiM3Q0cGZlenlqL0d6TDM3RkRWZXJkYWN5QmJ4UEN5eEZIYnYwalFYK25NVnlnNGQ5WEhCaUJrcUxJdVpNNVZyR0Y5MXFBaVdjRUFPZUY1eVhGaXZRUHFqeDc2MWJGVDBxeHFMTUREUEppK1lyWDVXZ1dBMGJIbUxyaCtKaTgrZDB3Sm5XQ2tLNVhrVS92NWVXcjNTNVhEU1NNaEJ3bTd6cWlSN2dFNkI5bDRsZUZWS3lGWEhpQmVHL2UxY053NzBJRDRVM3VYa1FTd2RGT1Y2bTlHQ0t4ZlRBbEVtVmFhbUR6VXI0WTJNeWZjY2tVOGZxRDRYU0gwQmlic1lMa3VjdkFBalh1UU85Z0VVMlQ4SVl5ai9ScW56VXkxZ3dob0hkK0drdWw0bExUb1RoYWJVRlpzYk5zY3ZkZDVCUFJHMlFtK2UwNFh2WjRqN3FLcUdINk9JWmQrNXc2WlovbWE3KzhkR29GOVNpT2QiLCJtYWMiOiI3ZTMyMDUzZjZkNDEwYjAwZDZlM2IzYjBkZjU1Mjc3OGFlY2E4ZWUxNjQyZTU1NjEzYzIzM2Q5ZjRhYmQ4ZWE2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450566447\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}