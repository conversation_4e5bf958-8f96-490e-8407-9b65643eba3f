{"__meta": {"id": "Xc9aac84bd206102fa6521b2e890e0f82", "datetime": "2025-06-30 16:06:20", "utime": **********.345563, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299579.901734, "end": **********.345583, "duration": 0.4438488483428955, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1751299579.901734, "relative_start": 0, "end": **********.293877, "relative_end": **********.293877, "duration": 0.39214277267456055, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293888, "relative_start": 0.3921539783477783, "end": **********.345586, "relative_end": 3.0994415283203125e-06, "duration": 0.05169796943664551, "duration_str": "51.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00294, "accumulated_duration_str": "2.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.321738, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.265}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3323948, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.265, "width_percent": 20.408}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3379111, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.673, "width_percent": 16.327}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1003560876 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1003560876\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1203044630 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1203044630\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1591135254 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591135254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-727059095 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299565610%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino5UVEvKy81aUQ3elFMN3I2Nyt1M0E9PSIsInZhbHVlIjoiODBJc3U0aWlEeldLRU9YWVFmeUcxV3dxVU9QMHlIVWM2clExOFloTFB0SnFleXFNeGRPRHptbXNBRlpBTHdZclRMZnJjeGN6bnNZWnQ3d2dXdXh3azRaNHFLNnZOWldEY0k0SUYxV00wTFJwK1ZoMm1uZzBnWVpGZnNUeEFKdGtOc3I4Y2tRREFHOFlXWHZDS3FKVWUxa1lwRk1pUTBHb1Nmbmc0YnBZeDRjejJrREhKVTQybDVlbmR6a2FEZWY1clBOOEZpbzdGMVN5cWNMWTFoSXlnemZDRTFQVFZkN2RFR3oyTkNQUmZsNWhydHhmRlM5bk0yeGdsak1UMDdXc1FReVRKbkJrN0dIU3Q4TUZ5QjJHUW5EVGRXUmVCYWFsY1JDa012UWRvMmNXdDFSaDdpV0Z1UWwzZ2R2VXRzcjhCTlNxMTlYOVVaTmd6Y0ZhSWVMTTl2ZmI5bUZ2ZHBiSTE0VEJmQzFSZ3ozV1dIU2QwWDVmTTlNYm52WlBiKzlKMmh0MXVHOXVoWkowblQ2MHlFcUlPQ1JOZUNPSzBHNktjbldkcFE0ZWZSM0x2RUJ1eXArdnhQZDNtNkd0OEJ4azZLSXdrRkEzMmZMaC9Rd1FFc0tsWnRzTFVlM3lEdmFvSm1vM2YrY25FT2dzb0k0bmlhbmEvOXFhMHNJb3lvSUkiLCJtYWMiOiJkNjQ2ZWU1ODkzOTI4NWU0MmMxMTg2ZWY3N2ZhZjRlMjNlZjZhZDM4ZDUzNmVhY2UwYTlhYjA2M2M1MjExNjMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitXdWU5enF4YVA5SC9ueWFTeWRzZ3c9PSIsInZhbHVlIjoiNUtLTXF5STdKMFRYTkU3clQraWRRamt0anhHR3NWeU5HK1NuN0xEOWNjakF4RkovbHAvVjQ2cENvZVBLTVppQjdyb2NlbjR5dkJVd3Y0VW9Dd2F3VnJwWEk0SjgzNWdnL0czSmVEdVpjTXBlbmJ0UnNyekVvWXlrQTlRWjRCTFlQMzF4bDdBc3RiT2tTOC9rb3VKaHBaOXd3cTFaNUFaV2VMZnpDdmZ4Z3NtNjRIQnZSOXBZekJDU014ZlBGN2FiaENZNzZQZk5KQzZVZzFzUXZUUitGTjkyMU1VZTFINVpBbGFKWVlEcnZ3RXdrSWpRbnJuWHVZTGRSdnB1LytlS2EwaEtMeWg2bGZZYllFRVB4RUM3VDhuRlVHbklrSTZJeW14V2xpUWlPS0g4TUJQMUVBNnJOZ1hqNGdSMmlOVERra3pPVGU4aVpEcUpVWlQyMWF2VGFjeWw0OEhrdldlUXk4Z0crMVFqMnprYURCY0FSYndqeGYreUpaZDBreEhvSE1QVkdIcWJRTW96KzYwZlI2RmNLdlZoNEZDMkNtbmtWRlhmaHFrdzBsbGNkNDA1Rm1vWlNQM2xMd3ZMMUJNRS9QbWVSdjZpSEd0V3p6QnNoajFDQ0JpZkZ4MTZrZmlFOXRaL3VYYmgwY0FaK3RvQWJ2bG1USm9YaDU1U1R2dGUiLCJtYWMiOiI5YjgwMjc3NDc3Zjg5MWU4NjMxZGQ1ZDlmMzE5OTdjZWRhZDdmMzU3Y2RiN2JiMTIwYThkNmFhYWVjYWZmYzUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727059095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1548652184 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548652184\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1288996467 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRGSXdGUHNQeDNPUkJxd3RHL3pzU1E9PSIsInZhbHVlIjoiZzNJMi9ZQzRncXZzWEFRRWpWeS9lR1c3WUZJek1wSnFwc0I5ZVhWS0UrV3U3NVNudElranVac2ppK293TXJwYVBCVmtENVFYTkpqZEFjVmZnc0hYUWQyRTlCaFNFWm1YajVaRFpKcHVUZVhveUkwOW5EVnNjNThTdjVpOURDK0lrQmtZekd5WlRRc0hLUS9NOWZyV3JLODNGc1NoOVBkd1JTeThSUHlCc3lYUzR2U0IzSGdsbzg4M0xITGYycGM2REhFZWFqRzhmTm0yb3QwbnZFTjExUmpISC9wbXBURkJEdTU3diswUFJSdE95azRaWGd5ekRIelZ0UlhTbW1Sa3lqOVVsQTYzVU1ITElubG5tdWhoVGlucjJ3eVdYOWo4czBrU1Bqc21qTm1ReFZaUHdYM1hnellRTGdWc1RmcC9pdDVPd29iM2w2WUxvZllMWHpVMk5uc2Z6THJGTzk5Z2RFNzJ1ellSSE5KbHRxRkNBaHV2OVAvcTVVL3hsZ1ZETzllcEtIWGRabS81bUdpLzhoT1VwMXF5eDQyZVhkUkNpQzNubGRhUDVTeDJwNUdSK01HeDZZbkExSTEzaUp2RGExKytoVUcvYXBQbnl4MC9DZ0pzWDRadXlsYUZvTU95c3orVStUdURnZlh3SXpHVmVVZCtxWjVGbUJNb0lTYnkiLCJtYWMiOiI1OGU4OWRiNWVmYjhiYjYwMGQxMDkzYTRjZjcyYjM2ZGYwOGUwOGVkYjQ3MDgxNTU2YjVjZTQ1Mzg4MzhjYmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJOaEdGT0ZLdjdOa0JjRU5aTlFGZ2c9PSIsInZhbHVlIjoidHJWTUJES3BpMitKRVNRVzZFbUNLQXJHaFBBT3ZpTFQzSXBKMHFUMlRKQy9QRnpSbUdjd0lzZ0FsNHJwUnlScjN2K1RmUzljVk9TQWR4b0FmNk8zT05XUGJ6STZ4eTBkaGYwdEhhWEpMWmprVkF2aGVpdHJscno4UW96em5HaWUwTkxiWTZySHBTenVuTm1JVEExMTRDVVhpeHMxejV4WU0wRTFic0puTnVES0d6U1JNUDlHZzB2MndxRGsrV25ScE9LdmNOajJWdUtEYml2UHlxdGlGL3JOTG1qeTFrOFhpUFFKcEhURkRnL05YNzUrRXRNbmMrdlE4RGpIbDNKbzBiemo0cGdSYmhSK2RXOWxPN01YQnJUbXQzSDdRcHI3OWtybDQvVlNtT3Q2b2p6NXE2Y0NrcnM4MlZ2ZHRQUFhRMjY3bmhRMzQ1dlp5ZEJnczBIUHRsQ1hJWm1zMUR3NkV5NmRhVzdqWjVhQ09ibTd5aE5SemF3ejBadkdTTzB3c2ZSeVY2aWUvZU0vcnEvZjRFcUZTUml2aGJRRStCdmg2ZlBQbncrYzJvbVVuSERxK3Rxd1VsMzhsNHhrdEMvQmVuSHA2R2E2OXgrZFRHblNWV2ZvNjIwM2x0S3hUaWtobWx0ZnUrZHdsVVA1b0JvaER3a01aZ2NmL3RJQXc5QjEiLCJtYWMiOiIwYTJiZjRiZjgzM2FiN2QzNDBlOGNkZTM3NjBjOTA1NTEwOWE0YTUzZTcyMzU4NGQwNmQ2YWU4N2RhODYxMjA5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRGSXdGUHNQeDNPUkJxd3RHL3pzU1E9PSIsInZhbHVlIjoiZzNJMi9ZQzRncXZzWEFRRWpWeS9lR1c3WUZJek1wSnFwc0I5ZVhWS0UrV3U3NVNudElranVac2ppK293TXJwYVBCVmtENVFYTkpqZEFjVmZnc0hYUWQyRTlCaFNFWm1YajVaRFpKcHVUZVhveUkwOW5EVnNjNThTdjVpOURDK0lrQmtZekd5WlRRc0hLUS9NOWZyV3JLODNGc1NoOVBkd1JTeThSUHlCc3lYUzR2U0IzSGdsbzg4M0xITGYycGM2REhFZWFqRzhmTm0yb3QwbnZFTjExUmpISC9wbXBURkJEdTU3diswUFJSdE95azRaWGd5ekRIelZ0UlhTbW1Sa3lqOVVsQTYzVU1ITElubG5tdWhoVGlucjJ3eVdYOWo4czBrU1Bqc21qTm1ReFZaUHdYM1hnellRTGdWc1RmcC9pdDVPd29iM2w2WUxvZllMWHpVMk5uc2Z6THJGTzk5Z2RFNzJ1ellSSE5KbHRxRkNBaHV2OVAvcTVVL3hsZ1ZETzllcEtIWGRabS81bUdpLzhoT1VwMXF5eDQyZVhkUkNpQzNubGRhUDVTeDJwNUdSK01HeDZZbkExSTEzaUp2RGExKytoVUcvYXBQbnl4MC9DZ0pzWDRadXlsYUZvTU95c3orVStUdURnZlh3SXpHVmVVZCtxWjVGbUJNb0lTYnkiLCJtYWMiOiI1OGU4OWRiNWVmYjhiYjYwMGQxMDkzYTRjZjcyYjM2ZGYwOGUwOGVkYjQ3MDgxNTU2YjVjZTQ1Mzg4MzhjYmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJOaEdGT0ZLdjdOa0JjRU5aTlFGZ2c9PSIsInZhbHVlIjoidHJWTUJES3BpMitKRVNRVzZFbUNLQXJHaFBBT3ZpTFQzSXBKMHFUMlRKQy9QRnpSbUdjd0lzZ0FsNHJwUnlScjN2K1RmUzljVk9TQWR4b0FmNk8zT05XUGJ6STZ4eTBkaGYwdEhhWEpMWmprVkF2aGVpdHJscno4UW96em5HaWUwTkxiWTZySHBTenVuTm1JVEExMTRDVVhpeHMxejV4WU0wRTFic0puTnVES0d6U1JNUDlHZzB2MndxRGsrV25ScE9LdmNOajJWdUtEYml2UHlxdGlGL3JOTG1qeTFrOFhpUFFKcEhURkRnL05YNzUrRXRNbmMrdlE4RGpIbDNKbzBiemo0cGdSYmhSK2RXOWxPN01YQnJUbXQzSDdRcHI3OWtybDQvVlNtT3Q2b2p6NXE2Y0NrcnM4MlZ2ZHRQUFhRMjY3bmhRMzQ1dlp5ZEJnczBIUHRsQ1hJWm1zMUR3NkV5NmRhVzdqWjVhQ09ibTd5aE5SemF3ejBadkdTTzB3c2ZSeVY2aWUvZU0vcnEvZjRFcUZTUml2aGJRRStCdmg2ZlBQbncrYzJvbVVuSERxK3Rxd1VsMzhsNHhrdEMvQmVuSHA2R2E2OXgrZFRHblNWV2ZvNjIwM2x0S3hUaWtobWx0ZnUrZHdsVVA1b0JvaER3a01aZ2NmL3RJQXc5QjEiLCJtYWMiOiIwYTJiZjRiZjgzM2FiN2QzNDBlOGNkZTM3NjBjOTA1NTEwOWE0YTUzZTcyMzU4NGQwNmQ2YWU4N2RhODYxMjA5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288996467\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-290471560 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290471560\", {\"maxDepth\":0})</script>\n"}}