{"__meta": {"id": "X892b060a3871a782c1c960a1ab263857", "datetime": "2025-06-30 17:58:14", "utime": **********.821881, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.289736, "end": **********.821895, "duration": 0.5321588516235352, "duration_str": "532ms", "measures": [{"label": "Booting", "start": **********.289736, "relative_start": 0, "end": **********.715669, "relative_end": **********.715669, "duration": 0.4259328842163086, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.715683, "relative_start": 0.4259469509124756, "end": **********.821897, "relative_end": 2.1457672119140625e-06, "duration": 0.10621404647827148, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45938208, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.787543, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.793675, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.812514, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.815153, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.02888, "accumulated_duration_str": "28.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7462249, "duration": 0.02305, "duration_str": "23.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.813}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.771487, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 79.813, "width_percent": 10.665}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7766201, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 90.478, "width_percent": 1.004}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.788207, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.482, "width_percent": 1.731}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.794368, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.213, "width_percent": 1.177}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.804615, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 94.391, "width_percent": 1.801}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.807691, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 96.191, "width_percent": 1.108}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.809388, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.299, "width_percent": 1.143}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8134081, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.442, "width_percent": 1.558}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEXyoiTANh8qPLaXlQ5WQSdt8eQQWYvkJCqk22Te", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1270565097 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1270565097\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1053483492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1053483492\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1149675389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149675389\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1501554147 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImJ2VGNNaDMrek1lcUFqUEp4N0pkK3c9PSIsInZhbHVlIjoieEVZV09DeFZSYjAwM3dLMmxtaVdFZ2lmdDVlWG43cW1ySHZXRkxJVCt3YWJ3OVNyWXRVOXQyZlF6UzdCTVNYbDZPekhWeno4cFZjVnpkWjlFNEtSNVhvcXUxRmxZNDhCdjJRNG1UTGliOXJ6bDZoanFOV1JpWW1wa1JpM0p2emFZbGc4U3ZVeEllemF1eGM1RWVXeGFNdUpiL3lvM3RMeXY0Qzl1dytaMWV6VUM0ZGVRR1NhTjY4cWFJeUtoNWZ6R1FtS2RDbFRZRm0vbG40UUZ6ZTh0UVpjV1ZEUkNib2NyNjVmbjdYbWRpQ21ZNVZldUM4YmJRbDI4VU0wUEFIVXYzMWRlbEFjUk01bHl1U0U5NjUvLzM5SGpaZXFJYXB2bjhhOEJLQWlCMCtDRmZWd1hwcVRIcnM4STZlWUtrR1FFUHc1ZW4rWGNzOTRVQ1ZJR2dQU1hNdUdZQ05aRThPZ0E1OHlRNEN4L0hpVXhVWWJNT2QzN2h1TVVOWGZlblBKRk9zSXZXOWdvZmtnV1pYaHhsNmN5a2dWeGNYRVVpd1pVZi9HYzR1TUk5OHBKR1dWb012TUx6QTBZL0kxZ29PQnRYcnZkMXk1SXZsaVh1WlprSDlEbWVCeWRXd3hBUURZalpHU0VVMG9wUzhzcWxhSHA1elZod2xBV1NhUUlrRUIiLCJtYWMiOiIyZWY1NjkzYWVlZWI5MGJiOTAzMTU2NjYwZWRkZmM5NmRiMzM4MWQ2N2M0ZjdmNmFmNDdjZmY0MzEwZDk4YmFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZqSmVTMVlUelVhKzRKMGRuUWtQTFE9PSIsInZhbHVlIjoiOTBqOThHUzZXaC9mb1J0dER3bG9lMjZ6SUVManNieTdqLzFwT1JhZENuTElNNUNDQmFMZHQzVnJYbDRhVkdhL0NMZFcxRnNRVlloTEIrWEk2L2NuU21uM0ZpVkpZcnhKanlucE03YVdVU3pWa0dhbHJnbUpBTVJOR0xGR0RHR05GZWZPZ0JpQUY5ZTRkejA3TzhFdVJvU2k1RHI0OXhQTlUwY09CcW92d2htelY5OXViV1JOc1RtblQrVVlUTE9va2RydzZheDF2WkZyODh3YXZ3NEd2K05uN0p1R0pEYWhpVWo2alhWNDl4QmladDdwVExJa2FqSjducm95N1grSTlQdjlYckdKTkQvMGxORXJSNzlYbmFwNHZYdWJqOFgxU1NmWWUvQ2NRMGkwNkhIYnVQWkhQbXpkVjV3a3JpbzBwUDNSTVdRSHF2OFhoNW1zaEdRbnV6WFRKbWhFam1OWWZPSTYyZWJ5eXNNS3llUGc0TnhpR3AraFhQNk9KK3NjU2pFaFBJWUo1NHdsajdyK0o0Q1ZLZEpreHlYbkgvbFhRbGFFMStabEZyNExWaDBDd0xRVkNybVBCU01Wa3JyS0ZZMVF0a3pITklKbjF5eS9jTjRwSHFhaUQrUUFqRUx2NEcvOXNrU3JjZU1laWYxdFdYV0F3VWtVbXhyaE1MWXoiLCJtYWMiOiI0ZDQ4YTA0OGQxMjJhNGViZDdiNWVkNTE5YjAwZDk3N2VjMzBiMTNiNmJmYjUyYWRkZDg5MTQwZjU3NWRlYzljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501554147\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1276369033 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEXyoiTANh8qPLaXlQ5WQSdt8eQQWYvkJCqk22Te</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JzYputEVuaoa74I4XiI6ieiBgMnKG500coc1DEkP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276369033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1393346795 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:58:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVIRU91Q0NPbmU5YzhubE5ueHVJUHc9PSIsInZhbHVlIjoiNnA1SWRXTnVROVFad2pMY2VLQW1wWDFWVW1ZR0FXVit5b2FwYXhkRWlkNFc2TG0va2EyS3F1V2JiajJwbVNSRDhwN3crdGZRbHRzQVVzTmdDR281TkF1OEVOdzI5NGZscExmQmNHdndJdjgrWFZvd0VPRG5wMm44bFFDTmpodXk4RE9OZFhDS1Q1UDNlTW4vek9ZcHdpNHcvZHJiZnRqNjZEVWkxYWJNbE5maSs0K1hoTnRUU3A3V1JBVUxkbitxTnZsRzhVWFlWWXlpaXd1SjZNMW9HUmdycDhxcGRmYlRhRGdhNjAwbUpWTHVoZWN6MXB3MzUwdWxFcUczZFZQaDlKbnBRdWJsT0p2cTl1eUZnVUswV0JxT0N2NmJBbHV4M0FSSGRSM3k5ZmhOVVRYbHBKZWh1dXJSbjYvbDUxaEd1S0NCb2tsSXM0OHpYUTJmSFovYSt2dlQyVE9jQUp6UnJldW8xaDE0TTV2WU9rTXIrOGdlY2VwOGdQT09iOTgrc3ZRQmw0R0NIN0NhNTdNdStGMmd2K2IvVmF4eFgrZzhCbXBnQ2xGV2xINlhIckpTZEdVeFJUYUxySXAwUW9YOWhaUU5UTSs1RzVodnk0aCtuVWpSbG1OTEhHOGVqMExpZm41K0hNUWVZb0NxeVZyM3BxVFhEdnpxOEovUmVpSUoiLCJtYWMiOiJmN2FjMWQyYjllNmYzMmI3ZGJlOWUxYmFiZTUyNGRiYzRmNDU2NzAyNDFhZTVhODQyMjVjMzY3NTZmMWUzYWY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBFbWNydGM5VW9zRnhOSHFnMHBuMFE9PSIsInZhbHVlIjoiUERQQzEzRWZQQ0pzRHBNRU1GN0J3WUZiZm81QlRpSUlOVGdaQ0gzaHBMeFJHaDhYdmUvUEFYUHFGdWxTVlc2K0FTYTRFMTlYT1lNd2czWWNHcENxcUJkdC90eTM5aXBTOTNJZm53MDZSU0RtM2gyRk5BeXg4ZHpwWjA2YjVicG5zOStoeGlOMUNneWFxU28rS1JtNHVIcDRBOE9YRnRXYlcvSWtKc1JadndKNFB1c3VlRnpseTdyTEErc0RnOC9RNmY2Vk9handySlppNmQ0ZytnSTN2cHE5MW9PZVh0RDJHeDYrYXcyUlZ1NGorV0d5NTR3RlpBRXVJZGNMWlpEdm9kUVBXaWt4VlU4cVl0UnBNQlcyaXZtREdFMk5sSXhCdHRVbEdxQmlBN2dQSXpsM2x1czV1c21GdzNVYjZzOHVBK2JGV3c5MHNSN3F2WW41ZGJweHNSSWpqOUYvZlNNenhtUTJ5c1p0dG41dm80Q1FTYkIrUU56K00zZzM5cStsUTZqNmJJbXI1cGZGT1RZV3hWdEtTd2F1V05FcThoUWF6MEFFUkg3T055RjZHRjhYR1pRbGloMWlhSERuKzFXY2YyemIvbG1hSE9UQU81bEdESHN3SFFEZkhOVUpyMzd1M3FlZkVhRDF5dFBSemlEYnVjODE0clhham96c0RBeXkiLCJtYWMiOiI1MDhjYjg2NmI5ZDI1YmZlYzUzNGFlNDY1YWQxZGIzMDYyMGQ4NGExZTg2OTE4NjIzYjA2OWM3NTExNTE5ZTJiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVIRU91Q0NPbmU5YzhubE5ueHVJUHc9PSIsInZhbHVlIjoiNnA1SWRXTnVROVFad2pMY2VLQW1wWDFWVW1ZR0FXVit5b2FwYXhkRWlkNFc2TG0va2EyS3F1V2JiajJwbVNSRDhwN3crdGZRbHRzQVVzTmdDR281TkF1OEVOdzI5NGZscExmQmNHdndJdjgrWFZvd0VPRG5wMm44bFFDTmpodXk4RE9OZFhDS1Q1UDNlTW4vek9ZcHdpNHcvZHJiZnRqNjZEVWkxYWJNbE5maSs0K1hoTnRUU3A3V1JBVUxkbitxTnZsRzhVWFlWWXlpaXd1SjZNMW9HUmdycDhxcGRmYlRhRGdhNjAwbUpWTHVoZWN6MXB3MzUwdWxFcUczZFZQaDlKbnBRdWJsT0p2cTl1eUZnVUswV0JxT0N2NmJBbHV4M0FSSGRSM3k5ZmhOVVRYbHBKZWh1dXJSbjYvbDUxaEd1S0NCb2tsSXM0OHpYUTJmSFovYSt2dlQyVE9jQUp6UnJldW8xaDE0TTV2WU9rTXIrOGdlY2VwOGdQT09iOTgrc3ZRQmw0R0NIN0NhNTdNdStGMmd2K2IvVmF4eFgrZzhCbXBnQ2xGV2xINlhIckpTZEdVeFJUYUxySXAwUW9YOWhaUU5UTSs1RzVodnk0aCtuVWpSbG1OTEhHOGVqMExpZm41K0hNUWVZb0NxeVZyM3BxVFhEdnpxOEovUmVpSUoiLCJtYWMiOiJmN2FjMWQyYjllNmYzMmI3ZGJlOWUxYmFiZTUyNGRiYzRmNDU2NzAyNDFhZTVhODQyMjVjMzY3NTZmMWUzYWY2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBFbWNydGM5VW9zRnhOSHFnMHBuMFE9PSIsInZhbHVlIjoiUERQQzEzRWZQQ0pzRHBNRU1GN0J3WUZiZm81QlRpSUlOVGdaQ0gzaHBMeFJHaDhYdmUvUEFYUHFGdWxTVlc2K0FTYTRFMTlYT1lNd2czWWNHcENxcUJkdC90eTM5aXBTOTNJZm53MDZSU0RtM2gyRk5BeXg4ZHpwWjA2YjVicG5zOStoeGlOMUNneWFxU28rS1JtNHVIcDRBOE9YRnRXYlcvSWtKc1JadndKNFB1c3VlRnpseTdyTEErc0RnOC9RNmY2Vk9handySlppNmQ0ZytnSTN2cHE5MW9PZVh0RDJHeDYrYXcyUlZ1NGorV0d5NTR3RlpBRXVJZGNMWlpEdm9kUVBXaWt4VlU4cVl0UnBNQlcyaXZtREdFMk5sSXhCdHRVbEdxQmlBN2dQSXpsM2x1czV1c21GdzNVYjZzOHVBK2JGV3c5MHNSN3F2WW41ZGJweHNSSWpqOUYvZlNNenhtUTJ5c1p0dG41dm80Q1FTYkIrUU56K00zZzM5cStsUTZqNmJJbXI1cGZGT1RZV3hWdEtTd2F1V05FcThoUWF6MEFFUkg3T055RjZHRjhYR1pRbGloMWlhSERuKzFXY2YyemIvbG1hSE9UQU81bEdESHN3SFFEZkhOVUpyMzd1M3FlZkVhRDF5dFBSemlEYnVjODE0clhham96c0RBeXkiLCJtYWMiOiI1MDhjYjg2NmI5ZDI1YmZlYzUzNGFlNDY1YWQxZGIzMDYyMGQ4NGExZTg2OTE4NjIzYjA2OWM3NTExNTE5ZTJiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393346795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-870550243 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEXyoiTANh8qPLaXlQ5WQSdt8eQQWYvkJCqk22Te</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870550243\", {\"maxDepth\":0})</script>\n"}}