{"__meta": {"id": "Xee318631a45d7c3fdcf6d83831d28c1d", "datetime": "2025-06-30 16:06:01", "utime": **********.220452, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299560.725842, "end": **********.220467, "duration": 0.4946250915527344, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1751299560.725842, "relative_start": 0, "end": **********.135092, "relative_end": **********.135092, "duration": 0.40925002098083496, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135102, "relative_start": 0.4092600345611572, "end": **********.220469, "relative_end": 1.9073486328125e-06, "duration": 0.08536696434020996, "duration_str": "85.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045104, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025439999999999997, "accumulated_duration_str": "25.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.163626, "duration": 0.023149999999999997, "duration_str": "23.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.998}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.196281, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.998, "width_percent": 2.594}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2054448, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.593, "width_percent": 3.656}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.212091, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.248, "width_percent": 2.752}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1071448339 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1071448339\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-570518133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570518133\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-306696430 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306696430\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-489578376 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299555850%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZGc3BLTW05YXcvUzJmY3g2N3pJSlE9PSIsInZhbHVlIjoiZjdTRjVFd2dJSnlKVDRCc21NdE1taG55ZUU3QkYrbjZuVkZOdDNvUlZSWW9kaWJaVkJWVUVZeWFJK3FyOFJWVktab29waWMranVmVWVLQmJoYTM2dlJPMEt1a2NVSWNUUE5kLzFobGNNalB4T0Rpb1pJcitiSDl3YkpuWkl3emRqeXFMeGUwb3lyOURuU1p6cGJEaDJXWUo5NmhXK0tGczdiS2g3citwVS93ZUc3OEoyR3lGM0N1ZEFpeFVaYlgwbHRNc0lWc21KbXZ1ZktOQUpKaG9xTFdWZzMxb3ViV3d1M2FjMGZlbWowUmlZb2p2Z0NLYXZsQkRmZjNmSEJLcDE2emhEYVRxUUF0d29tTUNhYjJ4c0E1MHZRR0pUVytTYWtDUTFKOXZ4YjN5bEpNWUd6VGg5bUpzOGM3VlZWakF4Y2RVbUFlL2dQeCtLL0N3ZEpJZmdMMWxPcUdVRlhXZDErbVcrNjVYd004TkRndFBSZ2s2WllIM1hBUnJQVUJjUEk5TExtUGlqbnZGTDRuV0VNdXBxRXlCMjdlRjdXTkZXb2NlSWk1bzdya0RsN01QbytqSm5SNjJxVXhhZGhxY1NKU0lrN3N0ZkhuVy93ZXZ6em00VFUyZXhXTXlnSkNXcmFjNEdMWWh4dUo3cEd5RFFXcGtoWThYY3ZjcGdtSG8iLCJtYWMiOiIyOTdhZGE1MTY3ZjMwZGVjYTUyZmE0YTkwZmZkYjcxYzg4NzBjYmIyMzU4NWM4MzUzZDJiNmQ5ZWQyYTBhODIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii85dEFiTTlQWDRrOHh4QWtJZ3JONkE9PSIsInZhbHVlIjoiU2VEY2Fha0Fyc3lQbmhoZFlmYzZMVlc4SGJveDMvbVdlYlg3Q0tuR3loT093dHUvQWtkcnc2ZCtaV1FNLzRwT2RLYUswbHo3ZGJCSVZBZFBpdEMrNUNkanFIWkIrNVY1RjB0dG53YzBiQVhCNnFKaE1uQ3I5RnNLc2Z1bE5QU05uT1VySVJwK2p1NG9DeU0wV3l4SjlpZ05LYTBURVgvdGhmY0dMMmVyemE4M3BlMXFzcG5tODhjQTkzanVKeGs1T2wxNlJtNWlOTVEwRWM0c2ZhRkw0RGc0K3pUeEFQY3dqTjVENHYwQ29MQlNLdmVob1M5ZXhCUEZQUHVTN0FUb2VGbEdaVy9IcFpIaFA0UXVtTHpUMnk2aXUxQnJCYjI0YUx6UmlDZ25SaTVJQU82eVFiV25FbGpmd3BPV01CWmR0b2I5MkQ5TzVha2RrUHNPemoreXdIOWd5MXdwWlk0TEJLbGFHMkxUUGgyU3Zsalluc2RzZHVoeDBXbk14dnVvT213bUYxek9QSmN3OFdkSnRVY1N0MEEvVVAxY3REMndnMHZOQmMrZXAzWGNEUlI1b2k4UkxTMDhoTHFnL0lYb0pwNjZydWhSdlJVWW9MN3dxSVM5emprbGRvb3ZEUktCUHN1TXVjTVhyNDVqZkY4RlE4MVYyU3g2cndDV2lmbEoiLCJtYWMiOiJmNzE5ZTgxNjJmZjFiMzRjMTE0ZWZjNDgxMzAxMzk5NmQ2NjVkN2E0YWUxZmQ4YWQ1YTBmM2JiYzM5ZGM5MGY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489578376\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-862249950 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862249950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1363515428 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdYeFRXK1MxVnZPYWFCMkxBSGZHdmc9PSIsInZhbHVlIjoiOUlrT1BOWkxCenpzekRpcWMvNW1JRVZKVjFoditpYzloZmh5TTZWOFJmVGhERnRidjFwbGxVd2hMbHl5clpCOTdBUElReWFtNHVOY0tPcWhFWGZ0Y3d5VGxNL1ZTWWVpK1dKbWVzWTFFZUhRUGlyYkRlOGo4d1NIMG9WSWQ4cS9iZWpjODRocEpMUGVCekRFM2QwY1pCcDlLaUROLzlDdHV0Z0pKOURFUnZwRGh1Yzl4dzg5ZTR5aXZScE5MTEpEWWR4Z0FPOUdMdkpyZ2dXWFVwRDZMT01qQTZoWFJMOEd2Nnk2dHpwc01yTS9lYjhsTWJINmordjRVRkFiSENycG81V3Z0cm94RUx4NXlHWWxWR3JWSXlmS21aa1dXeU91Y1N0QVl0RHBSVzdEaTJWTmZxTGJ1NE81RG1XYzFjYXB3a0dGVy9BZDlBVWN3eVcvM1NJUkg0Mi90U3pRT2tqSlJxaERRQmtKRThUS01vTVkwc3I1UFhwb0VxbTJpN3FKS3IvVkdqT05hM2JTdUt0V3FpSWs1NFI4K2d4ajBhM1ZvQWZ6NjFTUTNSTjhOcUZMS2t3VDJpZzRvYW54bjdNYmJma3NPdVRIN1B2ZXRDRDdqQXo0K3pENEpTYUR4S0YxS1d0R3AyRmtMY0czRFRGRExwME9PeUJrZWZjOHFEZEYiLCJtYWMiOiJkY2Y2ZjY0ZGZmMzE4ZjM4YmY3MTI4MDVjY2UxZTE5YTEzNTU2NmI1MjFiOTg0NzY0M2M1ZDlmMWZmZmIwZWQzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNpVE9IUk16NVhKaDNwUnpTRmJhdFE9PSIsInZhbHVlIjoiWG13c0RDbzh6bXFKbVZKenNhOUVvQVJJU2phanBDTUZwaWNUNzRDcE9pRTJpY25BSEtZM01BeXlQb1NnN0Yvd1l0MDBKQU1MVzd1MWFkNDhHMmxYNkx1NC9ZMytCR0hPbXJwTDRwTEkvalh4bzBlcXVzRDdhWkp1eUY1WkFFeGVlWFlFQkhCQWV4MWt4RklnV1h3SmRwZFFEeWI3dDlCUDJXZTJ5RmZVNFQwV3ZJeVNJZ0VGTVUyMUIzM2hkUk5JRFFPcENuQ2J4NmVqTVdmRTE5ZmVBWmY3VnkwNUFod0ROTC9hbDFzaEVnWGRTc1lNT2NEL2d6VTJzNnFpYURDcHY4RE1BMDBRMGVqeGRUWnp4SVFTQmdDelBMY0hqQS9HNWlQQ1dCUFJueERCZUJSL0dUK1grY0MwWkVCVU0xZGR1Rm1oM05MWXBPeTlBTFplV0RxOFZDN04rbUpwaFd5M3A3a1JlUE9ZY01mZ1htSlk3K2tzRDhQa1k2MVpCUjUvMkxHZWNORjRsNlZSNTI2WXhPM29XRGd6MEtuS3NFbTBqV2xFbkVJN09lZmJaZmlYNmx0WndVWTFaREh5Zk5YL2UwVnNJalhXVVNMSFJxTnlQcGZEYUlMUUxhWDYvcjVaVDhwMTFpN29QWjlNTXB0ZEhHbGdwOThoZHZ4YXcrK3oiLCJtYWMiOiJmNDYwN2JiZjI2ZWRmMjk2N2UzZDViOTdkZDQzZjlkM2IzY2I4Nzg2YWFiOGJlMTUyY2FkZjIzMTc5ZGE3ODQxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdYeFRXK1MxVnZPYWFCMkxBSGZHdmc9PSIsInZhbHVlIjoiOUlrT1BOWkxCenpzekRpcWMvNW1JRVZKVjFoditpYzloZmh5TTZWOFJmVGhERnRidjFwbGxVd2hMbHl5clpCOTdBUElReWFtNHVOY0tPcWhFWGZ0Y3d5VGxNL1ZTWWVpK1dKbWVzWTFFZUhRUGlyYkRlOGo4d1NIMG9WSWQ4cS9iZWpjODRocEpMUGVCekRFM2QwY1pCcDlLaUROLzlDdHV0Z0pKOURFUnZwRGh1Yzl4dzg5ZTR5aXZScE5MTEpEWWR4Z0FPOUdMdkpyZ2dXWFVwRDZMT01qQTZoWFJMOEd2Nnk2dHpwc01yTS9lYjhsTWJINmordjRVRkFiSENycG81V3Z0cm94RUx4NXlHWWxWR3JWSXlmS21aa1dXeU91Y1N0QVl0RHBSVzdEaTJWTmZxTGJ1NE81RG1XYzFjYXB3a0dGVy9BZDlBVWN3eVcvM1NJUkg0Mi90U3pRT2tqSlJxaERRQmtKRThUS01vTVkwc3I1UFhwb0VxbTJpN3FKS3IvVkdqT05hM2JTdUt0V3FpSWs1NFI4K2d4ajBhM1ZvQWZ6NjFTUTNSTjhOcUZMS2t3VDJpZzRvYW54bjdNYmJma3NPdVRIN1B2ZXRDRDdqQXo0K3pENEpTYUR4S0YxS1d0R3AyRmtMY0czRFRGRExwME9PeUJrZWZjOHFEZEYiLCJtYWMiOiJkY2Y2ZjY0ZGZmMzE4ZjM4YmY3MTI4MDVjY2UxZTE5YTEzNTU2NmI1MjFiOTg0NzY0M2M1ZDlmMWZmZmIwZWQzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNpVE9IUk16NVhKaDNwUnpTRmJhdFE9PSIsInZhbHVlIjoiWG13c0RDbzh6bXFKbVZKenNhOUVvQVJJU2phanBDTUZwaWNUNzRDcE9pRTJpY25BSEtZM01BeXlQb1NnN0Yvd1l0MDBKQU1MVzd1MWFkNDhHMmxYNkx1NC9ZMytCR0hPbXJwTDRwTEkvalh4bzBlcXVzRDdhWkp1eUY1WkFFeGVlWFlFQkhCQWV4MWt4RklnV1h3SmRwZFFEeWI3dDlCUDJXZTJ5RmZVNFQwV3ZJeVNJZ0VGTVUyMUIzM2hkUk5JRFFPcENuQ2J4NmVqTVdmRTE5ZmVBWmY3VnkwNUFod0ROTC9hbDFzaEVnWGRTc1lNT2NEL2d6VTJzNnFpYURDcHY4RE1BMDBRMGVqeGRUWnp4SVFTQmdDelBMY0hqQS9HNWlQQ1dCUFJueERCZUJSL0dUK1grY0MwWkVCVU0xZGR1Rm1oM05MWXBPeTlBTFplV0RxOFZDN04rbUpwaFd5M3A3a1JlUE9ZY01mZ1htSlk3K2tzRDhQa1k2MVpCUjUvMkxHZWNORjRsNlZSNTI2WXhPM29XRGd6MEtuS3NFbTBqV2xFbkVJN09lZmJaZmlYNmx0WndVWTFaREh5Zk5YL2UwVnNJalhXVVNMSFJxTnlQcGZEYUlMUUxhWDYvcjVaVDhwMTFpN29QWjlNTXB0ZEhHbGdwOThoZHZ4YXcrK3oiLCJtYWMiOiJmNDYwN2JiZjI2ZWRmMjk2N2UzZDViOTdkZDQzZjlkM2IzY2I4Nzg2YWFiOGJlMTUyY2FkZjIzMTc5ZGE3ODQxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363515428\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-242221987 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242221987\", {\"maxDepth\":0})</script>\n"}}