{"__meta": {"id": "X870e109aa8dbbaa015437c302a8f90c9", "datetime": "2025-06-30 16:21:59", "utime": **********.155573, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751300518.739543, "end": **********.155589, "duration": 0.416046142578125, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1751300518.739543, "relative_start": 0, "end": **********.10099, "relative_end": **********.10099, "duration": 0.3614470958709717, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101, "relative_start": 0.36145710945129395, "end": **********.155591, "relative_end": 1.9073486328125e-06, "duration": 0.05459094047546387, "duration_str": "54.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032500000000000003, "accumulated_duration_str": "3.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.132869, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.144504, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72, "width_percent": 16}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.147578, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 88, "width_percent": 12}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 2\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 21.98\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2110834259 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110834259\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1907496643 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907496643\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-811136864 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB1ZTYvZ3ZDalBCVFk4eC9DRDN6Vnc9PSIsInZhbHVlIjoiZUJwUWNwUEpzM0UxSFpRN0RpeW5lM3doTXBZQ0plY245a0RrYy9SR3g3amk0SWlNMUd2aUl0cjJXVm01K2Z1UldNRTB1RktNeTVCMmNXZlZEN0pNZ1JKL05aZkJnZ0ZuWEs3c0c4NG9qblZVL1lrTGdJZzExT2s5emZGa3lRdXBUcisycjV3RERTc3owK08xOXI2ZWZ5MXFsV1ZYam9oS0NTdUc3bnp2R0pTM1Z0ZG5udTA3V1JkYnlubFAxSDJkRkJaMDE5Smk5NjJyd2Zkdlk4aW5CTEY1MkRYTVRlWVo1KzM5N1NvdGE0NGVIZDRVSUR5WUx2SGdtb0cxd1hlTlgxbGFYMlh1TkMyK3FibU9nYVZmL0dFR3JNdTY5QVhDRXA4MFVCWkkwN0pTalhUVGtqMU5pL2sxQzAyRVM2R3RHbHpWcFNZamN5MzdMVENxUklzUW5jUVgwMnhjNkJQL3Mxd0UrTzFkVktldGNZUUxyTUNFODJvZTFIOWt2SUZYNjljSExmNmdWd2EvZ0xDU3hPeFBILzQzcHZmSTVUUHhvZkwwblpIbEtNeTh5c1dVWkhNVUQ0TUFWbWVMTFVsQjhQZEJDa2FJSDFjRHNiSFZaK3kvWVJKOTZsL0xJVUtQVTFFSXZ3LzFhaFk4TDRrVnU1UkRrdkRWUzRpN2w3QnAiLCJtYWMiOiIzZWVhOWNhMjFkYTY1MDQ0NWMxZDkzNjQ5OTg3NzMwN2M4YjI4YzJjMDFhMjliMGI0ZmNjNDk4OWQ3NGFhMzlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkloSW54TDBZWXhMZmd3WC80Zm43Qnc9PSIsInZhbHVlIjoiK21lR0Q0QnRsNVVXYStLb3JNRDhnS3BEYlkxZVVYZDF3cS9GNUxiZ1NNcjRMQkVjL0xiOEdpOXFDWC9HQ21GbUcvbHFhZ1N1dHFFU21wOHhJMmx3QWVIVTZXWmlKNm1hcVhtdmJYQW1NVGN6dldoWXVvQ2d5MkVYOHdLLzJYUU1jYlRYTVBXNGNqTlBwNFFkSm1aL251RmNIeEFSY3hoVm00RTlzdXRsT3pWVkFoWUZEajZPcExsbkh3UnE4eGxTQU5hOVl4WGxrMEZHZXdhT0dpQlUvTmRZZVhMMDdodWJNOWtrZTk0WVQ2Y0cwWEN6bFo4VjBKc2R2SzdrL1diMGZHc1g4NWtSUlBMOU5yZjdER01sd3M2anFBd2VELzJ2MzkrQkE1eFNUclB3Y3JuQklPVkxlekRwVjI2TUtJeWlXMDZUUW50SnhZYm9Eem9ZL0tCT1ExWUdBU0N4dTR4UktPVkN3NTNoc0ZRdVc3NHBRTnZBNnRETy9id3lQNG01bk5SSUNhRTcyQWJsS3dESUZmOFZGdjRUUWJFUEtkVDQwWnZ5NFplK1ljNmI5NU8yb0xtRExuMklKNXVyMlRoTXQrZmFHbnlXREhCMDcxR1djZHpYQ2xTMHg3WmM5ZU4yNi9rWDh5cy9La0I5eUYrb2ZNNWtZbUMwTkZFdFhsczciLCJtYWMiOiJkMzJhNzI3N2VhOTg0ZTQzZjZiM2U4ZGI0OTM0MjA4Y2Y0NWVjOTJiOTUwYWM1Y2ZjNDE4ZmNiZDdkMDBhNzQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811136864\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-976455415 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976455415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:21:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im95OXk0cldSQ01VeGhLTlUwMFg2VWc9PSIsInZhbHVlIjoiSm4xaUF5V3FPeHFwRkNJUnZIQTN1d0s1SjRWNmZmaFUrcjZaR3NuWjlEcXd4ZGZtckJVbTlQRHZyVlJycDBFOXlCTlR6b28wVCtYSFA5dDcvRTZwQWptTnk3VHp3akowVzV2YUZ4MjlwSmVzMGxKTG13S0VRbFRWeW9BUXRQMjJuMDhlUjRSZjhibzI4NDljclNoMHo0cFVSajI3L0k5T3h5aGl3UzR3eTRvRXFxVWlrQU85SEoxTEx2eXZmckthRnpmNEFGTUhaZjFKZG95OVVsVGhOYkxUQTUxZ2g1RTlUd1ZlWVR3eFdaRFFlcHU1SHh6T3U5anY1Ym5obEhBR0NpZGFhU0s4d1NPMkRtSDFINVB1OTJLZ3gwN1NrZ0tDcElBVC9SRHNEVlVNcHVvL0dvRGxGdlNnZGFqblRkdWVTMWJEZGlYVWR0NW95UC9pRmdzdmlNcmlaMTZNck9RbVQvNmpudlNNSTB1WnhWUVMxb25uZk1iRDZFMGFmVE9Lb0REME4xZEdPYjFDeG9oSzhrRWVJM2tXRnBKaU5FNFNuQjlZRFBoUnRhNkZyNkl6a3NYeng5VDA2SXVDSFM3bXZxdGN1SWF0czMwWng1cFp5NXRaa0I4K3I3eFpKeXEwc3FkSUpaN1prNEd6OXUyakIvaTNXWGpSbmUwSEV3WW0iLCJtYWMiOiI5NzE0ZTJkMDg5OGQ3NTBjMjgzODUwODNhMDAxZWY1NzM4M2YwY2M3NWMxNjQ5NDlmNzg2NjM3NTFiZDViY2E1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:21:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjAzdXp3eVpGcitNQTlnTjQ2d1k4UWc9PSIsInZhbHVlIjoiU1ZsMlJnVW1RMnp1Yi9TdTNINHMzYnJiVEVZL0JXa1VuUTUrcWYzSkphbVpEL0lBdEo0YXAyeng2b0srelRTZTFES1pialB4N09JWG84cVVlQVlma1dyckxSNURsY1lTWTlsbG5aa0pvYVZNd2IvTUZ3NDRobkZOVW5sYzZESXFvQmJXY3FtQlkzVkNndkZDTEoraHhQQVhzNlRYVmp0OHJBUVVFV1Q4VmVUaGM0dlVSK0Ftd3N3N1Zya1dYQ1YvdlZXRExML3lMZDM0WmxNRDFMa29IN20xVk1oMWdsM2dHR1dscVlUNnMzS05HTlJ4MzZNTmNEeGo0SHFTeFVYdmk5Rkl3ZklRVm0rYURSWEtqSVZmaFd2bFZoZENwOG9hejY0YTVUQnhzRzl5V2dpNStXeEZiMEFYRHkwNWl3b0tBUTJGRDlwdEtudVBsdHJYL3RNRkRhek5VRlpDTDQ2MXpvY3JaaDBDQW84MlFNM1A1cUYvbmFwNGNFZ3dNcktRTmdhT3luK0ZQTlM1QXZUN2tZVlBoam5aUDhyT0Rkc3pRVW5DZkNkbTFnQTJnMDhzamJyVmNJam51RDMyZ2FFTkRtRDduckxvTXI1SExYb3I0Z21nVkxJR3M5OUNyclJjRTdJUkFEd3hwWWt1TDljK3pwMkVwdjBUc3dBd1Q4MUkiLCJtYWMiOiI2YjQ0ODRkYWVlZTNmMjZhODVjYjk0NzI0M2ViN2FkNGFhMzM3MmM4ZTI1NjA5MDdlZDAzZDYwMDMwYjYzZjJiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:21:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im95OXk0cldSQ01VeGhLTlUwMFg2VWc9PSIsInZhbHVlIjoiSm4xaUF5V3FPeHFwRkNJUnZIQTN1d0s1SjRWNmZmaFUrcjZaR3NuWjlEcXd4ZGZtckJVbTlQRHZyVlJycDBFOXlCTlR6b28wVCtYSFA5dDcvRTZwQWptTnk3VHp3akowVzV2YUZ4MjlwSmVzMGxKTG13S0VRbFRWeW9BUXRQMjJuMDhlUjRSZjhibzI4NDljclNoMHo0cFVSajI3L0k5T3h5aGl3UzR3eTRvRXFxVWlrQU85SEoxTEx2eXZmckthRnpmNEFGTUhaZjFKZG95OVVsVGhOYkxUQTUxZ2g1RTlUd1ZlWVR3eFdaRFFlcHU1SHh6T3U5anY1Ym5obEhBR0NpZGFhU0s4d1NPMkRtSDFINVB1OTJLZ3gwN1NrZ0tDcElBVC9SRHNEVlVNcHVvL0dvRGxGdlNnZGFqblRkdWVTMWJEZGlYVWR0NW95UC9pRmdzdmlNcmlaMTZNck9RbVQvNmpudlNNSTB1WnhWUVMxb25uZk1iRDZFMGFmVE9Lb0REME4xZEdPYjFDeG9oSzhrRWVJM2tXRnBKaU5FNFNuQjlZRFBoUnRhNkZyNkl6a3NYeng5VDA2SXVDSFM3bXZxdGN1SWF0czMwWng1cFp5NXRaa0I4K3I3eFpKeXEwc3FkSUpaN1prNEd6OXUyakIvaTNXWGpSbmUwSEV3WW0iLCJtYWMiOiI5NzE0ZTJkMDg5OGQ3NTBjMjgzODUwODNhMDAxZWY1NzM4M2YwY2M3NWMxNjQ5NDlmNzg2NjM3NTFiZDViY2E1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:21:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjAzdXp3eVpGcitNQTlnTjQ2d1k4UWc9PSIsInZhbHVlIjoiU1ZsMlJnVW1RMnp1Yi9TdTNINHMzYnJiVEVZL0JXa1VuUTUrcWYzSkphbVpEL0lBdEo0YXAyeng2b0srelRTZTFES1pialB4N09JWG84cVVlQVlma1dyckxSNURsY1lTWTlsbG5aa0pvYVZNd2IvTUZ3NDRobkZOVW5sYzZESXFvQmJXY3FtQlkzVkNndkZDTEoraHhQQVhzNlRYVmp0OHJBUVVFV1Q4VmVUaGM0dlVSK0Ftd3N3N1Zya1dYQ1YvdlZXRExML3lMZDM0WmxNRDFMa29IN20xVk1oMWdsM2dHR1dscVlUNnMzS05HTlJ4MzZNTmNEeGo0SHFTeFVYdmk5Rkl3ZklRVm0rYURSWEtqSVZmaFd2bFZoZENwOG9hejY0YTVUQnhzRzl5V2dpNStXeEZiMEFYRHkwNWl3b0tBUTJGRDlwdEtudVBsdHJYL3RNRkRhek5VRlpDTDQ2MXpvY3JaaDBDQW84MlFNM1A1cUYvbmFwNGNFZ3dNcktRTmdhT3luK0ZQTlM1QXZUN2tZVlBoam5aUDhyT0Rkc3pRVW5DZkNkbTFnQTJnMDhzamJyVmNJam51RDMyZ2FFTkRtRDduckxvTXI1SExYb3I0Z21nVkxJR3M5OUNyclJjRTdJUkFEd3hwWWt1TDljK3pwMkVwdjBUc3dBd1Q4MUkiLCJtYWMiOiI2YjQ0ODRkYWVlZTNmMjZhODVjYjk0NzI0M2ViN2FkNGFhMzM3MmM4ZTI1NjA5MDdlZDAzZDYwMDMwYjYzZjJiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:21:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2075526553 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>21.98</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075526553\", {\"maxDepth\":0})</script>\n"}}