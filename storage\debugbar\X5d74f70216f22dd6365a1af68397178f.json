{"__meta": {"id": "X5d74f70216f22dd6365a1af68397178f", "datetime": "2025-06-30 18:07:41", "utime": **********.129263, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306860.694644, "end": **********.129275, "duration": 0.4346311092376709, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1751306860.694644, "relative_start": 0, "end": **********.086502, "relative_end": **********.086502, "duration": 0.3918581008911133, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086511, "relative_start": 0.39186692237854004, "end": **********.129277, "relative_end": 1.9073486328125e-06, "duration": 0.04276609420776367, "duration_str": "42.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43871136, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0024300000000000003, "accumulated_duration_str": "2.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.117598, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.597}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.123002, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 85.597, "width_percent": 14.403}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1216875263 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1216875263\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-789463050 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789463050\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1793337076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1793337076\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-287052340 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxuVTMrdDZpVk93U01DTUc1Ui9DaFE9PSIsInZhbHVlIjoiNTR0VUJzaFpsRUVMWVJhL0xUVVNkOUVBMnpacUlQWW96UG1pWGViT0lGWjJib2hOcW1aNUdmdWoyenhjWTQ1d2pUbUI1R0xHVWlmOEhKd3dxSmFzU1JSdFg1Y2NpdmZFekJYN2o1Yjdma042UjBlc2I2bk1PYmJka2hFelBhckZKWkJtTVpZSk9lcStoNWR4Z3hxS0orMGoreFRFcmRTZkN0OXQyVFl3ZWJ2dkJCeXlwaU5uaXlHR2xEZmhOMDY5ZkM3L1pUOVFWNFdDdUhVWnQyUmIvQ0tvMWlScUkvaG51eE9EWUlBTldFblF5MzhDOC9ab29nUTkyU0ovTkl6MVRBdGxJUFFNdzc5QXhnLzRrTXhmaUgwVlJaazJhc2YwTHpsNVRDaG9MUmhVTVVXbk5ITDQ1S01TZXhwM3FSaXF5enZMNVk5WGpadFRyYnNpVGw0ZmhtVEg0STMra1FsenBlUFJ5dDlLKzRNbjJnTUttSnBjWS9tb3krajF4M3RpSnJxckpLNk1scFVsT2JxTTUrNGtBbldWSU9EUkYxcFBoL3JCek5qaXI2ekREOWZoelZOYzVaZlBBa1NoS2xESjBibXplL0RJMklYbFFVc1JmN3FkdjhiOXU0elBHY0w2UWlnTDRRNFYvYmozc2FobjVOdjBBZWw3d3pkQVBEOTQiLCJtYWMiOiI0NDI4OTZhMjI2YTYxMWQ4N2ZkMTkyM2JjODg0OGUwYjVhZTEyOGJmZmQ3ZjExOTQ1MDk3YmZlMjVjNzY0NjUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdQK0w4N3dWYWV0d0h0dnpNeHZyUlE9PSIsInZhbHVlIjoiRlBheHpiYVV2K1lIaEFjSWE3L3NqaGRqOXdadGJ4K3o0WklQdmNBcmM3cm1qY2tRSHlIZUNUMG9VWC9XYlF6WHNraS9vTzZNTzZQK1N1c0RHVzFDWHUzdForbVJGMllRMWkzcnRtL3pXbmtEdnpUVFJIWjMvQ1RDTWlYNm9PT1U3eFJyaUZsWkFaN25ybW9vSDZBMjJ3S01RcUlsb1hzbkk4dVBveEcwNU1VZXVXVlAvUC9ma2RJNnUzTXdjblVaeGprQSsrOUo0ay85WGVzbGRDdFNnekx6REUrTVBKSGsvcUlIUWFmd3lpYnpsdnMvcmg2a0JuTjJMUWlsV2JUOElSK0UxQ0I0NXk1RlRMSjR0dEd3RHFVdE9BdmVMTEpYdWZTTUVMdzhCa1hCVmZFNmxaNjJiOWlHQjYvUjVIZ1ZydEZHMVF1NlNpdU9iS05EUW90cklhL0Y5dytVUFAxMUNKZ1p5RlN5MjNFbkIxQ1BvVEdteW0vdHk3SmtJcW53UHBpdWlyZ05tT01rK0NTQ1ZxZkNoSFlKN1RXQUVURzI5MUUyZUlHK1BtRVY0K2FvTXlrTkp1MG52elptcS9ZRHk5eVY5Z1VkOFkwZ2JwZndGdEhCWndWSHRvSzcxbStDM2F3N0c4ODFSbHRvU25WL0NtZStEWUI3NEJuYTZEYmQiLCJtYWMiOiJmYTUwNzgzOThmYTZmMDE3MDY0ZmQxZWI0NDkwNDczMzA4Zjk3NzA3NTg0ZWVhMjExZTQ2MGNiNTAzN2U4ZTEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287052340\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-535554231 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535554231\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-861735163 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:07:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh1NjdsOTdVOHNNRnhXUVlhWFYwQkE9PSIsInZhbHVlIjoiR2Q5QWl3Mk5GbW9XSmZ5cDZqTEJOTDVXVURIVHhWU1BOVkNRZUJURmRWcWFPekxVSlZxdTZkQzhXbXlxVDhvcGx1NjdrSHEzUFI4Q29tVzMzSklDeTNsU0hFMk1ibUwyWUdCeTliMmpvVituUTVWNjBjSi9RRW9FcGFVQkhIYjErSFAzL3JnSWhPaU82V1dmQ1BGbFp1Z3UvR1dEMkU5cTl5VGFPR0FlSU9HelhvOTY2WVlTTWxzSmVxazMwb3NhSDVDYTdRTTg1REx0cnAyL2RaYktjUTVMcmllT0ZJL2NFdXFobWZhbC9mUTdER2NDbTJLYk5TeldxRVVDV3JMVjFoZnVyeDloQTlQWVFRdE1RUmw3dElXN0RHNGJjQ3VnaG5CNWpEWTUyK2djbEhSaXZQUldHYlV4SW9jdkNza0NjZ3p2RHFLNXNSbnJFdk9sT1RiaENGdVF1dlpramxSUlQ0UmV5eDliblg2TmJ2aDd3Q0xYaHphZDJLdGxuY01RUnd0S1FxaHp5MmtnVVlQOWxKTS8xM3FKWnRSaSs2NlR4NFBVSlo4V3p4OGYxem9CQlpWbm5zbG1mWFpxM0dqQnR1aTlQNTZkN01kdVV3a2RaTk4xSVBURDg4STZuZVFOMEJEbDVKNHFCM3F1dWVETU9qVDl4UXhhSmZqaENYVjAiLCJtYWMiOiJkYjA3MmZjMjI5MTI4YzQzY2Y2YzA1MzFmNDA3MDg0MDA3MWNiOGFhYjg2MmE0NGQyNjEzNWFmZDMwODE1NGQzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRsTldYWTQrSTdZbE1BbkQxam5PdGc9PSIsInZhbHVlIjoiNDdJbTZmWkwrbnJDRXRqcnAyUFpHTmFjbVdEOU5CTW84RG5HMmp6UTJSaStDajlpYkpFcUt3eUdMemxteTRhTHZOWFN2WWdJa0hmdjBmT2Z6V0Z1Wkk5VmFyMS9Ha3JiRDBHTnVKaVIvRkFrUHdMU0tPRllGWk9DZjhIRzRNY2NQbGplMjdSb1JRS0hNUjU0OVNjRVRKYk05QmZFdEJubEV2Y3FOWE9YMXNYbWtHL0R3ZHI0VmVzNWpGaGx6VjR3dGFNZHQ1dVZUR2JWWTJWUXRnMHVaK3I3WTJHNG1pdytvWk1vKzdIeEF1dERiaE9vL003bFdiL1JsWmxVZklKb2hUQTIxZUdUMjR1MHcwUFVjV0g3UzFPclhTcnJZMnVveXVCTGo0Z1piUGE5emxEMktETmEvLzdqTU5VYmJrUzhoK21GMzJ4Q1VDRWZTam8yeXpyMmg2ZXIzbHhjaUd0WmpUU2FnTXVZY0t0UjVmamdmckQwZ1pGR3VpSGVKcG4vNnowUWx6WnBCOFhibHRMK2FFbVRCcDFtenFUV29IdjRYd0hDd3pZL2huTjQ0ck9hRy92Z3NWd2RPbUlUNFVLRWxyTGl0Z0d2cWhHMkVNdFg4RXp4TVZyK0dQNFc0UW5WUjVLd2NLckFIMWY3YWo4MFdaR0JpcmhiTVpBYnMzOUwiLCJtYWMiOiI5MTQzYTlkZjJjMzU3MzNmZTMyNmU0YzgzMDg2N2E5ZGRjOTA2NmJmMTAzZTU3MmIxZjg1NzdhMDQ2MzMyYTBiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh1NjdsOTdVOHNNRnhXUVlhWFYwQkE9PSIsInZhbHVlIjoiR2Q5QWl3Mk5GbW9XSmZ5cDZqTEJOTDVXVURIVHhWU1BOVkNRZUJURmRWcWFPekxVSlZxdTZkQzhXbXlxVDhvcGx1NjdrSHEzUFI4Q29tVzMzSklDeTNsU0hFMk1ibUwyWUdCeTliMmpvVituUTVWNjBjSi9RRW9FcGFVQkhIYjErSFAzL3JnSWhPaU82V1dmQ1BGbFp1Z3UvR1dEMkU5cTl5VGFPR0FlSU9HelhvOTY2WVlTTWxzSmVxazMwb3NhSDVDYTdRTTg1REx0cnAyL2RaYktjUTVMcmllT0ZJL2NFdXFobWZhbC9mUTdER2NDbTJLYk5TeldxRVVDV3JMVjFoZnVyeDloQTlQWVFRdE1RUmw3dElXN0RHNGJjQ3VnaG5CNWpEWTUyK2djbEhSaXZQUldHYlV4SW9jdkNza0NjZ3p2RHFLNXNSbnJFdk9sT1RiaENGdVF1dlpramxSUlQ0UmV5eDliblg2TmJ2aDd3Q0xYaHphZDJLdGxuY01RUnd0S1FxaHp5MmtnVVlQOWxKTS8xM3FKWnRSaSs2NlR4NFBVSlo4V3p4OGYxem9CQlpWbm5zbG1mWFpxM0dqQnR1aTlQNTZkN01kdVV3a2RaTk4xSVBURDg4STZuZVFOMEJEbDVKNHFCM3F1dWVETU9qVDl4UXhhSmZqaENYVjAiLCJtYWMiOiJkYjA3MmZjMjI5MTI4YzQzY2Y2YzA1MzFmNDA3MDg0MDA3MWNiOGFhYjg2MmE0NGQyNjEzNWFmZDMwODE1NGQzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRsTldYWTQrSTdZbE1BbkQxam5PdGc9PSIsInZhbHVlIjoiNDdJbTZmWkwrbnJDRXRqcnAyUFpHTmFjbVdEOU5CTW84RG5HMmp6UTJSaStDajlpYkpFcUt3eUdMemxteTRhTHZOWFN2WWdJa0hmdjBmT2Z6V0Z1Wkk5VmFyMS9Ha3JiRDBHTnVKaVIvRkFrUHdMU0tPRllGWk9DZjhIRzRNY2NQbGplMjdSb1JRS0hNUjU0OVNjRVRKYk05QmZFdEJubEV2Y3FOWE9YMXNYbWtHL0R3ZHI0VmVzNWpGaGx6VjR3dGFNZHQ1dVZUR2JWWTJWUXRnMHVaK3I3WTJHNG1pdytvWk1vKzdIeEF1dERiaE9vL003bFdiL1JsWmxVZklKb2hUQTIxZUdUMjR1MHcwUFVjV0g3UzFPclhTcnJZMnVveXVCTGo0Z1piUGE5emxEMktETmEvLzdqTU5VYmJrUzhoK21GMzJ4Q1VDRWZTam8yeXpyMmg2ZXIzbHhjaUd0WmpUU2FnTXVZY0t0UjVmamdmckQwZ1pGR3VpSGVKcG4vNnowUWx6WnBCOFhibHRMK2FFbVRCcDFtenFUV29IdjRYd0hDd3pZL2huTjQ0ck9hRy92Z3NWd2RPbUlUNFVLRWxyTGl0Z0d2cWhHMkVNdFg4RXp4TVZyK0dQNFc0UW5WUjVLd2NLckFIMWY3YWo4MFdaR0JpcmhiTVpBYnMzOUwiLCJtYWMiOiI5MTQzYTlkZjJjMzU3MzNmZTMyNmU0YzgzMDg2N2E5ZGRjOTA2NmJmMTAzZTU3MmIxZjg1NzdhMDQ2MzMyYTBiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861735163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-764810200 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764810200\", {\"maxDepth\":0})</script>\n"}}