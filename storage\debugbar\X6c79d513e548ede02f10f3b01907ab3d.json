{"__meta": {"id": "X6c79d513e548ede02f10f3b01907ab3d", "datetime": "2025-06-30 16:05:47", "utime": **********.149934, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299546.66152, "end": **********.149948, "duration": 0.48842787742614746, "duration_str": "488ms", "measures": [{"label": "Booting", "start": 1751299546.66152, "relative_start": 0, "end": **********.049724, "relative_end": **********.049724, "duration": 0.38820409774780273, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.049735, "relative_start": 0.3882150650024414, "end": **********.149949, "relative_end": 1.1920928955078125e-06, "duration": 0.10021400451660156, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48094936, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01823, "accumulated_duration_str": "18.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.090988, "duration": 0.01249, "duration_str": "12.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.513}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.113062, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.513, "width_percent": 2.962}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1277251, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.476, "width_percent": 3.511}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.130546, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.986, "width_percent": 2.743}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1357129, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 77.729, "width_percent": 14.646}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.140701, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.375, "width_percent": 7.625}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1874938296 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874938296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134673, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-595939256 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-595939256\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1566417185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1566417185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-973838605 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973838605\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1996325770 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inpoazk2eUw4N0ROTXhUSGZpSFF3Wnc9PSIsInZhbHVlIjoiZFdNQWEyUUVINUd6blJlVHVzOUVxUk5tNHkyMHVqZ2tJaU1JYyszbFU4L3dHcFNCSTRPcTdHQ2xLRnlFNmJNK0xYSzkydGFpZTBwdTdjRE95ZmxkSVkwb0N6RW5TUEhSbmtzU2lzaUpEZk5aTVQzdFBiTndyWDhUVFdpbWordDZoMmU5SVBWeUxXRUo0N1NRaTdPcm15NmRBckJQajMydnBZdkR3b3loc05oYitRNExwWllhYlZyQ3lhTTNlSE1ETS9aNXJ3MFFJdDBIYjdDZnRZZGlKM1UyQTRBQmtCS1VuUnZUTTZuOEhnNWhONWFNSXVSTnlOMDlFQ0ZpZDRMQnB4a2w0WGd2SEJQYlpTVWtrQlFhOVZNeWxGeEhTcUlxbmJPaXJaTElhS24vTytDVlNLekxRbk8rRkRkSzVMTDk3VUNRTkE5b2hhMVFtWHVQM0k1Q0YrTVIwajlGVEFZbzhRb3pSbWdvQmJ2cFhFZWdWdW5wMzhTWGtuWDZ3T3hNMlVpcmVCczkrcSsrWDUzd0dqRXNiL2w0eTR3aEhHUmY3QzBwZDMwc0xoUE5ybUxNZ1lCRjFrcmdvcVJLUE5HTzFMSXByMzJZcnlvN0dsSUdjQi9FTDhURU16ei8rc3M4Wjg1VjMxb3ZBMnVURGdua2JOakEyZ0VnR2E3MVIwNnciLCJtYWMiOiJlMzVhNDJiYjk4NzFkMTI4ZmEzZTM5MmZjZDYwOTcwNGM2NDI1NjJlOWEwNThkZTIyOTZmNjQ1NGU2ZTJmZjc1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlFckhYK3hCRzh0NjhtdWp5WTlvcUE9PSIsInZhbHVlIjoibHBWUlRyMGV3VFpkS2tYQkJoOGwvZzV3dzdtZlh2MWUxaGp0M2NTY0N0cmtUdkNZV3IxR0oxcnBHcFJLeDB6eTA2RUcxNXlMOWUycXI5Qmx1V3lHcndCK0VUQmdUZWNvNWh4anJZZDBLTklPNTdlakpBRkFidytjb1RhdktXb2VoMXYwa0dGUmhCYU95UWFtT1NmTVRXSDl2LzFUUENoaGJCNnMvZVpQZjh6RHUzZ3p6SkNtUUNEM21tcklFcnFJT3RScjM5MXBoMmVwWGJvaFhxYVV2WkNxclpqdUNwMnpBVUVlOVlOdnVuTCtxbDNTRDJaWGRGUVNSRGxRbUJsalRWUk9RUVVZUWp1a0NFT2dHTm01Q284czR1WThuQ1h5ZHRGZ04wTm95dzZkMlBUQ2VxQi8xakdWWEk5R0NqcXUxUk5NSmlNSkwvd1lYU0dzNFowZDE4VXN0dVVuejM0ZEhmeGJVc2hZbW4xckhQL1BzZmFldy9GbnV5bjg4NTNueWs5cHJlOTZLdzZ3alErZHFJemdiK0o4SHBZZFpobEQvMWtFUEh5b0xmSGFUeVVLMU1jKy9XM1EwaFk4d1djQW5DR1RYa3dWN01yZi9ORzhhQjJjVG9UY0YvY0lYcWV3R05QVDh0TWUyZWNPSGtnMmVxdHdLRWpIWTNObjE0UG0iLCJtYWMiOiJmMTMwZmJjNTZkN2NlMzNlNzQ0ZWFiODlhZWU4ZjBjMmU4ZmEzNjk0ZDk0ZWUzZGZiNGZhMmQyMmYzYjYxOGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996325770\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-324931602 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324931602\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-123167021 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhJTVJ6R2FJdU94Yjk5c0k4ZzZDN3c9PSIsInZhbHVlIjoiQjdSb0JiUkx6QnpqMUwvQXdFbFZLZXZrL0tabkZJOVZEZVl0elJHSFdabUhHN3JEbVdieStMWE81K1JmQWtBcXRFSVRsSmZQM1lmN0tHdVhFZ2FOLzVKaFBLWkM0MW9jS2c4eHZ6bUFJRmZoRE9YcTNIcVV5R1ZRczBIL1dlVzk4czJabWFGQXVDNDI4eTdzTXJBQTB2QVZrb2Z4NjEvMGNuQS8xWGpRcXYrNC8rSjM0NUY3QlpuTTVGcFY2bjVvK01rVTNlZ29QUFVXQXh0OEN5KzAwcVg2MlkvcDFnQTFLZW0xclBDdDF2dnJCVDZwbWRGaFlPb01pQmZSckZaL2Nka2ZwaU1OcW5YU0x1VnE4V1JRNDU3N0RXM3lEWFFFeEZmemFhMlBGZlA4Zmhkei9KWVpSMWp1ZFBPa2V6WFVVZU5Qem9yRXBzM3UwbFBoU2hRZWRwMXM0cHNxd29WaHZBSlNrVzYraDVQbFdrQnVqQVNHL3hLMWhRUVJTRVZyb1lzTlNmYlNGdnlONkxmQjZBZUltd1pvaTBnMlFVZExQaFNhWXhNamVTZ1RIUUk4OXpPb2VpaUlIZ0RVcVRPWkVHMnVUNFYwZVhjS2tXMXlmdFpvMHBxdXN6R3RBajRPVVF4SVRhaUtLdGVBaVVRMGxHdlVDZjlWYUhhdGlDdVYiLCJtYWMiOiIxYTJjMjA5MmE2ZjQ3MzM5MjcxNmQ4ZDM0NzgzMGIwODM5NGMwZTZhNmFiZDc4OGQwYzhmZTViNzY1YWE1ZTFkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhBR0NvMDlpR3pSeW9qQVA2elpvR3c9PSIsInZhbHVlIjoiSkd5VlA0Q3JSN3dYa2Evd0hUTUJQSkZYekx4b2RHQThCMS82Z2pLa1pmQlNXTTkwYndGOXE2UVRpMktuUnZLaTQzczh1N3BRR2RrLzU1a3M3c3JwM1NUYThoNzVhbDJPV05LdlRNMjhJdURoV1hGS1pYMkpYb2lsVE01N0hqMldUazNVSVNhekl0aTVKaXV1MExTdTVsMS85Vy9jWlV0c2tLY2VIOXZRMi9pOHNzb3k0NmVsSWNuVjB1Z2dwbUNBekRFMldEM3FCNWVvQWlrbzNkZUY5T0Q1Zlg2NmZJLzBMTjlNUjVDalQ5WnJCMFppQVVuVUp5ejFHY0tDWGJUSlpKbGlKbEpyeXMwZzAyNmRaenNtczlOVDdhOFg1dG9PSXA2cG5aNGNFMzlqc3YvaUxDdTMrcWYrU0pDbkp0YzhMb1JwSTErWFVuMGVsb0lKS25UVkRDdE9UbDZnaXB0QTgwOUpVbFJPWERYMWVPN1RsdlJjd3FhaXIxTHBRald5T3p3OXR6ZGt0dHlMbXlpblZ2ZjZaaThObnRRdFE5dllyQjdDZWZSakU3ZnN0RjFZZlMzb3UvdnpZWTRNVDNyTThjenlxMHBVd3krNXhPcjhwT0pSSnc4N3orQXJIZjJGNHhqbk14cG9hbkgrTi9QQlJ6QitYYTNDcVltcWIxTloiLCJtYWMiOiI5MDY3ZTFlOGU4ZjI5MWRkOWU2ZGFmYmYxYzc0M2YzOWZlYjdiZGUwNWZjYzFmYTBmZTQyMDdkMTI0ZTk5NWIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhJTVJ6R2FJdU94Yjk5c0k4ZzZDN3c9PSIsInZhbHVlIjoiQjdSb0JiUkx6QnpqMUwvQXdFbFZLZXZrL0tabkZJOVZEZVl0elJHSFdabUhHN3JEbVdieStMWE81K1JmQWtBcXRFSVRsSmZQM1lmN0tHdVhFZ2FOLzVKaFBLWkM0MW9jS2c4eHZ6bUFJRmZoRE9YcTNIcVV5R1ZRczBIL1dlVzk4czJabWFGQXVDNDI4eTdzTXJBQTB2QVZrb2Z4NjEvMGNuQS8xWGpRcXYrNC8rSjM0NUY3QlpuTTVGcFY2bjVvK01rVTNlZ29QUFVXQXh0OEN5KzAwcVg2MlkvcDFnQTFLZW0xclBDdDF2dnJCVDZwbWRGaFlPb01pQmZSckZaL2Nka2ZwaU1OcW5YU0x1VnE4V1JRNDU3N0RXM3lEWFFFeEZmemFhMlBGZlA4Zmhkei9KWVpSMWp1ZFBPa2V6WFVVZU5Qem9yRXBzM3UwbFBoU2hRZWRwMXM0cHNxd29WaHZBSlNrVzYraDVQbFdrQnVqQVNHL3hLMWhRUVJTRVZyb1lzTlNmYlNGdnlONkxmQjZBZUltd1pvaTBnMlFVZExQaFNhWXhNamVTZ1RIUUk4OXpPb2VpaUlIZ0RVcVRPWkVHMnVUNFYwZVhjS2tXMXlmdFpvMHBxdXN6R3RBajRPVVF4SVRhaUtLdGVBaVVRMGxHdlVDZjlWYUhhdGlDdVYiLCJtYWMiOiIxYTJjMjA5MmE2ZjQ3MzM5MjcxNmQ4ZDM0NzgzMGIwODM5NGMwZTZhNmFiZDc4OGQwYzhmZTViNzY1YWE1ZTFkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhBR0NvMDlpR3pSeW9qQVA2elpvR3c9PSIsInZhbHVlIjoiSkd5VlA0Q3JSN3dYa2Evd0hUTUJQSkZYekx4b2RHQThCMS82Z2pLa1pmQlNXTTkwYndGOXE2UVRpMktuUnZLaTQzczh1N3BRR2RrLzU1a3M3c3JwM1NUYThoNzVhbDJPV05LdlRNMjhJdURoV1hGS1pYMkpYb2lsVE01N0hqMldUazNVSVNhekl0aTVKaXV1MExTdTVsMS85Vy9jWlV0c2tLY2VIOXZRMi9pOHNzb3k0NmVsSWNuVjB1Z2dwbUNBekRFMldEM3FCNWVvQWlrbzNkZUY5T0Q1Zlg2NmZJLzBMTjlNUjVDalQ5WnJCMFppQVVuVUp5ejFHY0tDWGJUSlpKbGlKbEpyeXMwZzAyNmRaenNtczlOVDdhOFg1dG9PSXA2cG5aNGNFMzlqc3YvaUxDdTMrcWYrU0pDbkp0YzhMb1JwSTErWFVuMGVsb0lKS25UVkRDdE9UbDZnaXB0QTgwOUpVbFJPWERYMWVPN1RsdlJjd3FhaXIxTHBRald5T3p3OXR6ZGt0dHlMbXlpblZ2ZjZaaThObnRRdFE5dllyQjdDZWZSakU3ZnN0RjFZZlMzb3UvdnpZWTRNVDNyTThjenlxMHBVd3krNXhPcjhwT0pSSnc4N3orQXJIZjJGNHhqbk14cG9hbkgrTi9QQlJ6QitYYTNDcVltcWIxTloiLCJtYWMiOiI5MDY3ZTFlOGU4ZjI5MWRkOWU2ZGFmYmYxYzc0M2YzOWZlYjdiZGUwNWZjYzFmYTBmZTQyMDdkMTI0ZTk5NWIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123167021\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1085112925 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085112925\", {\"maxDepth\":0})</script>\n"}}