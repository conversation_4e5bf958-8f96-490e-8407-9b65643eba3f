{"__meta": {"id": "Xcd5356bc36217fcfd0e62d432c0e7457", "datetime": "2025-06-30 18:42:11", "utime": **********.144836, "method": "GET", "uri": "/add-to-cart/1287/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751308930.71043, "end": **********.14485, "duration": 0.434420108795166, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751308930.71043, "relative_start": 0, "end": **********.057742, "relative_end": **********.057742, "duration": 0.34731221199035645, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.05775, "relative_start": 0.3473200798034668, "end": **********.144852, "relative_end": 1.9073486328125e-06, "duration": 0.08710193634033203, "duration_str": "87.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48658952, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.007149999999999999, "accumulated_duration_str": "7.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.096479, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.615}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.106591, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.615, "width_percent": 8.112}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.120511, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.727, "width_percent": 11.189}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.122799, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.916, "width_percent": 7.133}, {"sql": "select * from `product_services` where `product_services`.`id` = '1287' limit 1", "type": "query", "params": [], "bindings": ["1287"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1272771, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 51.049, "width_percent": 6.853}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1287 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1287", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.131649, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.902, "width_percent": 35.804}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1357121, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.706, "width_percent": 6.294}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1668946130 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668946130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126458, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1287 => array:9 [\n    \"name\" => \"KDD حليب شكولاتة 180مل\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1287\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1287/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1279594686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1279594686\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1185114702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1185114702\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijd4RC9NZFFaRUdvajZJNTlpV3J0TUE9PSIsInZhbHVlIjoiMTZIc2dWMENzTU8yKzFxRFJscWpac2QyeFByUWdIMGh6T2ZQc0M1MEJ4bkxpeFVFSXdrRnFPbnB3UHFCNFpRZkV0NHB2dGx4SHB0NFJQS1N0aVV5S2tNYWdyS3dwT1dhV2pacDhSVXBWYkFKUDg4RC9LMGJSYXNzOHcyQWc0QUJxSEhLNzgra3NUU3A1WmhFdVBDV3VpV1dnZXpWVE8vUlNOK0locURYZGNyakZVTC9pejk5dTdER1F2NnJTTWhGUWR2L3ZrYTBRcHlhaU1POHpVRlJtaEVjNFhzcWZBRTVFbEo1NCtGdWh1T1NvcHFTVG5zT3BiZWIwNEhwT2dtVzVqR2czSk5ZWjdKVlJsU2NEdVZHclVXQmgwM2h1OHI2M0RtblpoQ04vVGNDR205dGRQWFBQSWRLanhJRlRMZjdmdmxTck03S1NNQWpDeTdHeCs3ejBkTGJMTFhFQnZzOEMyb2s3RlRSRzZ3aXRQNGJLNElxYzJFU0w5aVpTWUx1cjluSFRTQk9Jb3RtVFdMZWhOQmE1bFU2Y3cyZ1VLR0NrOG9peG4rd3JKTXhTVFJBeS9KR1BGcm1vTTBiSko3cVdIbEl6MXVYU1MrVVA3dXVKRERzbFB0SVdtWDIzR0pKd3VoYnlVQnNGQ0x1S0Q4YUx0eFl1MEk3WDBMaVN4Sm0iLCJtYWMiOiIwOTEyZDRjZGYyNjBjZDFlMmQwNmFhNTU1ZDQ5NzgzNDljNmE1NTUwMzY1YTZmNmVkODk5YTNjYmFkMzI4YTFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFnRWVsc1J4VGxvSnp3MnR1VjhHaUE9PSIsInZhbHVlIjoiMTFVZGsyTmxmQXd4NGVwZnpuSEZnMmhveUcxWFNBSjBmNFZzTVJWN1ZiSzdMZkJYZmdQVnNUeUU5SDcyZHozNFVlaHgrNUkxVWYyenRpekNFS3JhbkZ1R09FNmY3UVBycFk4Nk5wa2xkVHdxMnU3NTg1cTFtRGhsQVVZOHRtK1RTMUtwMGV4U1lHdXY5NEIyQ3FYVmVTV1R6Zk9uUktsWUJJQUtvZ3QyVFJvRDhkdk9iVTlOb2l5WVo3VFNMZ0taZ2Q3SkJTRWFtalluTUMyNjA3cHF5eWN0ZGp6WmpLSEJlckZSNTV3V09HN1VXeXhveWtBUTRublNBYUpSR1BnSFJOWGZtVjY2cjRNQ2Y1QVlOZW16c0FkbGFOMlFHL0ltR1FmN0FjTFl5SWx4bUg5QStCaUN1eXZUNUlnSWpWUFIwa0xKZWRsVGxsY0pwdjFGdVoyOTNoS0pVMW12TDVjdEdIZ3V1cTdGSGcxRGRkTUZRdEYxWktjSGF0RVZEOVR1S2ZDSVd3UTNGUDBiN0tDUmxVOFBNTzN3clZrbFBONWllYnVxSFY4NXY3ZzJyRE1xWGw2Nk52Rm9mRGswSUFGQXU5YWsrMTJ4Z2t0T0tjaTk0b0lRbDdnQUI2aTAxTkIrdmxrYzdMdDAwQjN1bjNIQ3ZKWHY3cjdWQytGb3dzaHAiLCJtYWMiOiJmNDU1NzYzNmQyYWY0YmIwNWRjNDI0MDgzYmI5ZjBjNGIzMGYwMDg4MTE5NDNkY2Q0YmFmNmFmYjA5MjI4OTdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1467053963 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467053963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231749319 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:42:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZjSG1RZGs3anprK25OUy8xekorTXc9PSIsInZhbHVlIjoidTYyclR0aDlhSElEdjBGcWpjTU9mN3FHK2l5MEZNQkJlRVJZczlWNll1VGNoMHlaMXMxd2JET002WHdtYlBZb2tQSURUT01MV2hDQUZtT2hZZEpBQkRLMEdERkJ1dFJtalI4YTRlSHZjUDdQdFhMNW5MR04zU3l2Mk1iNXVac0tXcTFWK3VJNWpSRDZkcmVKQ1MxVVdNSlNmL0pPaE1jUjQwZWhJWVl3SEJSb2ZBR29TYzRvaVRCY0hpam9FbmppREZMQkdhUmprcHhOZmxDUlFqdis5SkJMUGtKNExsM2J0QTUrL25YODdXNzVCVXRRTmJRc1dJWWVmWDJzS0dVTzRsa3dydDJ6cXN6cEV2WVozY3VkSmZZaUtyNDBZWUJBMnhsWE15cnozS1F0aFMzMmtISzJSTUk0azlrUDFCL0w4dUhsRWxlOXRNTU9EZ1dpMldxTDFQNndDL2NMUmx3bDFveVYvTkN0QWZvZmhEdUU0RVp0cDdlZEdCWFdSZGJXeDA4ZDlacUlwcnV2ZWQ4U3VsVWd4bFRzVm9CTXFuYTQ3V2ptclZySmV2T3hhdTZFUzF5dGZNYWIrYU9QNnJHSlRxdGR4TmdlK2l5L0JQK21vTDVnUkFCTFR1ZkVhTU5pN1VTVERrV2ZWcEhubk9wbkx6UTJSU2IzeTc3ZTR1RGYiLCJtYWMiOiIzZDlkMjcxZTdiMjNkYzM0NjFlZjBhNTQ5NTIwNmJhMzU5MjA0ZTVhOGFkN2Y3OWRiMTZhODBhODEzMzU3Y2ViIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1Kc0pvUkhsZ1UyK2xjQkdFaHpkVUE9PSIsInZhbHVlIjoiVjFVazdYZGZMRnpvSU53TC9ibDIyaTloSldSYWZjQUdGVTcwN01rRldCdXJuSjBMY0xnb1BUeDQ5VTFKVGIwVHJkZFVOTUFoNTdlOGlua0QzQ2Q4UmhtVjFpWDVMN1B0a2JzZlI0VGQ5R2d0djJJQXY3QVF0ak9OV0N0UGprUnhLVzc1djBxZUJYVlVWTXQwWE5MamgzMENaclByUWs3WWc2bFYxdzBMM1U3ZmhidzI4YjR0L3NJZm81TDBmcW1NZzg0dStZdjFZUGZSVEFpLzR0S2x4NlRwSHFFMEM3TmdqeXRoZ2JoT2d1MFczTGVTTXdqMUpDQWpIWGx2NVJSMlluOGJBclNFTEV5ajFLUTFjRVFxQU8yZXBEVW0wQVR3M2UrSjFPbFlHbklLVHQ5M3Vrb29IejJjYjhSUHIzYlNjQ2xlSzMvdGxveFRkMDVrT0pJTEdIY1pVNUE1OXFuRSszdldLSVpuZEViZis3MWlJaVAzRm80SGVVNGRYSFNkTmNMeUJ6SktKdjNLaXJLTWkrRlBka0svKzBXVnE5NUdCNG9ZbkFsTmZhYUo0bVE5Z05yektHZTJOZ1ViaWxudjdlbEJ0UVBTUnBhWFJSSzRNWkZmMHduSzB3Uy82a3ZHOXdvcnh1WXhrTlhpbHNINFROenRCRHY5Q1JLbGVKYmgiLCJtYWMiOiIyODgzMGFmNWNiMDI3M2NkMTk1NmVhZWIxYjAyYzEwYzRkNzMyZmNjMTY3MjkxZWUxYWE0MDNlOTBjMzg4NDAyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZjSG1RZGs3anprK25OUy8xekorTXc9PSIsInZhbHVlIjoidTYyclR0aDlhSElEdjBGcWpjTU9mN3FHK2l5MEZNQkJlRVJZczlWNll1VGNoMHlaMXMxd2JET002WHdtYlBZb2tQSURUT01MV2hDQUZtT2hZZEpBQkRLMEdERkJ1dFJtalI4YTRlSHZjUDdQdFhMNW5MR04zU3l2Mk1iNXVac0tXcTFWK3VJNWpSRDZkcmVKQ1MxVVdNSlNmL0pPaE1jUjQwZWhJWVl3SEJSb2ZBR29TYzRvaVRCY0hpam9FbmppREZMQkdhUmprcHhOZmxDUlFqdis5SkJMUGtKNExsM2J0QTUrL25YODdXNzVCVXRRTmJRc1dJWWVmWDJzS0dVTzRsa3dydDJ6cXN6cEV2WVozY3VkSmZZaUtyNDBZWUJBMnhsWE15cnozS1F0aFMzMmtISzJSTUk0azlrUDFCL0w4dUhsRWxlOXRNTU9EZ1dpMldxTDFQNndDL2NMUmx3bDFveVYvTkN0QWZvZmhEdUU0RVp0cDdlZEdCWFdSZGJXeDA4ZDlacUlwcnV2ZWQ4U3VsVWd4bFRzVm9CTXFuYTQ3V2ptclZySmV2T3hhdTZFUzF5dGZNYWIrYU9QNnJHSlRxdGR4TmdlK2l5L0JQK21vTDVnUkFCTFR1ZkVhTU5pN1VTVERrV2ZWcEhubk9wbkx6UTJSU2IzeTc3ZTR1RGYiLCJtYWMiOiIzZDlkMjcxZTdiMjNkYzM0NjFlZjBhNTQ5NTIwNmJhMzU5MjA0ZTVhOGFkN2Y3OWRiMTZhODBhODEzMzU3Y2ViIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1Kc0pvUkhsZ1UyK2xjQkdFaHpkVUE9PSIsInZhbHVlIjoiVjFVazdYZGZMRnpvSU53TC9ibDIyaTloSldSYWZjQUdGVTcwN01rRldCdXJuSjBMY0xnb1BUeDQ5VTFKVGIwVHJkZFVOTUFoNTdlOGlua0QzQ2Q4UmhtVjFpWDVMN1B0a2JzZlI0VGQ5R2d0djJJQXY3QVF0ak9OV0N0UGprUnhLVzc1djBxZUJYVlVWTXQwWE5MamgzMENaclByUWs3WWc2bFYxdzBMM1U3ZmhidzI4YjR0L3NJZm81TDBmcW1NZzg0dStZdjFZUGZSVEFpLzR0S2x4NlRwSHFFMEM3TmdqeXRoZ2JoT2d1MFczTGVTTXdqMUpDQWpIWGx2NVJSMlluOGJBclNFTEV5ajFLUTFjRVFxQU8yZXBEVW0wQVR3M2UrSjFPbFlHbklLVHQ5M3Vrb29IejJjYjhSUHIzYlNjQ2xlSzMvdGxveFRkMDVrT0pJTEdIY1pVNUE1OXFuRSszdldLSVpuZEViZis3MWlJaVAzRm80SGVVNGRYSFNkTmNMeUJ6SktKdjNLaXJLTWkrRlBka0svKzBXVnE5NUdCNG9ZbkFsTmZhYUo0bVE5Z05yektHZTJOZ1ViaWxudjdlbEJ0UVBTUnBhWFJSSzRNWkZmMHduSzB3Uy82a3ZHOXdvcnh1WXhrTlhpbHNINFROenRCRHY5Q1JLbGVKYmgiLCJtYWMiOiIyODgzMGFmNWNiMDI3M2NkMTk1NmVhZWIxYjAyYzEwYzRkNzMyZmNjMTY3MjkxZWUxYWE0MDNlOTBjMzg4NDAyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231749319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1287</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">KDD &#1581;&#1604;&#1610;&#1576; &#1588;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; 180&#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}