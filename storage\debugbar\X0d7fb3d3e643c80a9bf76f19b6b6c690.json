{"__meta": {"id": "X0d7fb3d3e643c80a9bf76f19b6b6c690", "datetime": "2025-06-30 16:07:17", "utime": **********.618653, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.142556, "end": **********.618671, "duration": 0.47611498832702637, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.142556, "relative_start": 0, "end": **********.501535, "relative_end": **********.501535, "duration": 0.3589789867401123, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.501547, "relative_start": 0.3589911460876465, "end": **********.618673, "relative_end": 2.1457672119140625e-06, "duration": 0.1171259880065918, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52198240, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.581116, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00392, "accumulated_duration_str": "3.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.536341, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 49.235}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.547275, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 49.235, "width_percent": 14.286}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 9 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.550479, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 63.52, "width_percent": 13.01}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.564944, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 76.531, "width_percent": 14.031}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.566956, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.561, "width_percent": 9.439}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2099967740 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099967740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.570576, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1578418105 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578418105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.571789, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1181515025 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181515025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.572604, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1581968023 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1581968023\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1241297221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1241297221\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-466533825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-466533825\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2053933607 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6InN3ODVCeUhNTUZvUFZ1QUhpTWNsT0E9PSIsInZhbHVlIjoiZFlNT2FES1NsVUpSN3BnamZ3bGlZcHdPWnFVWWNacnhseUFDL3kzdW1qODVod3Znckk1SGhQQkFtMG5RU0FIYzREREo4bUFGWVUvRlZVK2FkK1BoUWF3bVphKzVDWSszV2VIeWx1NzUrOHNFVHY5OUZrMzZwQmpuUTY1cyt3T2FPdDNjWHl0NnhaSlNLY0hWMWt1Y2ZwTVVjTG9OVFNiQ3piMVkyc3NwSHg5aStOSHFSOWlUMUUza05DSEJpSWFiUGNCaFNURGRtKzNyL0pNR1hQaEp0cWt1eVFqcnZpL1Avdy9xbGNmZGVMZFBUeVA4Nm9DcTY1NHUvdVlBeEpXcm4rUmxzYU9lZXIzcTFzRHFocG9oOWdUSHNDWUpqV3lNMm9uL2dLZkNvQlltcU9jNGtrUlpuZk1KR2NPQjRuTFpoV0hTc0VnblkzcVA2Z1l3SkF5NUc5T2dzZWRtT0E4Z1JPZmFqVUxxdTBSa2NjRG9kVlpBdUFvWGxpNDBhZjZiSWhHYXV1VEFLNnpCRkVsSmo0b0c3VnZkdFpUQ3JOcDI5eVJ2dlVBWlJTeHNpUXUvVTF4VkdRUUhOb0p1MUtrR0Z0dTJrOXN6ampQRnlDclRCVEJvK3dPT2ZmSk5BUmR5Tjd2a2x2UUpQb3p0dmx4YVIvek1zcEZvRGNTQzJ1bDkiLCJtYWMiOiI1NWRjMTA3OWVmZTY3Yjg1YTI5ZDY2Y2IyNjMxZWIyNjRiMGVhNmU0ZDk0NmI5YmU4OWJmMzQzNTY0YWY0MTM3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZHNkZZT2laTEhHbkZtSDdycVhkakE9PSIsInZhbHVlIjoiT2JOc2E2WG1TSk5TcTE0N211Si9QSzNFblNIY2k4YUMyL1Q2LzU0RFRVQjZGRFZiRzVCaXVPVE55aUpqNEJoMEUzZ080OStocGQ0YVN3bGhxc0FyMEl3aVFaQTFDZTVGaURkaERzRDhoR0xSRW9BOGNocHRVK0JySEZScHZKWHFNQXVSc2VUKzZTZ2lURGluV3ROYXY0QTdnWWRZaldhR04wVjJ1UkFGUGRiNEF1YWp4azEyOUZpMTVwNnBRK3VncWYxeEdQMWs2V29hTUtaMnozRVRKMzNDRnU3dWExaE9QYjdQS0hDaWJRNHphUGVpeWZmdzBEMTVWWEVmZDF2YWVJTHkyRzRLN1p1ZkNXTDhXTmM0cHA4cUdhT2RDZW5zZlZxamtNUVhhU3hrYnQxTEJ4ZWdvMElVYko2alQySkIySmFpZENSRTkzSUxPaE9pSTNWMkNIaUhTRUxzcEpGc2NoMCt6VDB5a1FTZDllc1orSjdONWVBVkVianlUcEZTeDJYclpOY2x6WTBpajRjYUJ4UVlRck10R3VGdkxxU3UzekdXeGwrZ0RFR0EzRVpvaGVXcFJ5NXhIWVZyQUdxVU1jUDUvYWdpR3pXVFZnNXlka1IwalhhT2RmUXQ5UVVERHRtblppSERZV0grYjhOOWVXTnMxOFJiYzloMHEwM2oiLCJtYWMiOiJiYTdmMTI2YWNiMDgwMTJmZmZkMmU2MDE2NjZhN2IwNWQ2NjcwOGRiZjc2ZDhkOWMzZjhiZmQ3ZjkxZGJiMDEwIiwidGFnIjoiIn0%3D; _clsk=16h7wx3%7C1751299634011%7C3%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053933607\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-760437838 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760437838\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1051105198 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw1dmNVanYrVDZ2eU15ZVVwSkViOEE9PSIsInZhbHVlIjoiRjJpNmdCRDdpbjdhaTJnOVJWb29sOVFCSlNhd0RPQTdRQ0NsamhTSEd1SlBFMERXOHpMazVVaFZiclBvcFJIZ3pKa0JFUGNneEptK3BabG5PNkRPTENhM3lRdmNvOWhReXUzWm9oclMyRWh2cHlEZXg4bTlacXlrUXdDV0xRRWFQZCtPNWgzb2RBZ2FYeDhkQk80dlBvRzQ0ekhRTDlnYW1wcE5zRldiOGUwUjM3K1I0ZGx4ZnQ5NVZtU3ZpOHVSaXk3SzJpTHMyUVA0ZEtPeC9SdE5oY09OVm5NdVZ2c0sxd3FrMTZEbHovNkVNZGVYNmVMaTlPWDVnZy9hTFVMeis1cWlYVkRuOThwZWlUSDFjSGJRRTQ3R2RpZHAwMHE4MHVxa1hIaWZlekY2dUxqMU90S3FJbWJ0WDFTTmYxQ1F4d1Z2azJjdFNvaU9NUDk0dDk1eW1nMTNWY1l1Tk5uTVR4SzE2Y1hHY3pQN3d3T3ZmK0JaVGFNWGcwZXFBdE0yMDhlcUVYWEh6Nm5wZVNZdURucXowY3hGU0xFYmdJc0diK3M3blpWdkI2UXB2RUlDSy8zOGR1V1llRHBwYXdvTEdWVHM1R3RBZVpKeEg3L1FYSUxFalk3WXk2bmhvTnpaRjNhRWtKL2Y4TXJIYzJqOXRTOWt2N0d6ZkpEMEI3YVQiLCJtYWMiOiIyN2E2Njg5MWEwN2RmMzc3YTRlY2NiZmIwYTI4MzkxYWZlZTY2OGNmNzkxMGJkNGI0ZWMwMzcwYzBkMzAzOGMzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRxa2xkZ1ZaUWIyZWk4OExzVHRpcXc9PSIsInZhbHVlIjoieXN1eUdZTU9xQjZ0d2o5UDYxUDZnYXlpekU4aUtWM1hUYmhUNFZ3anJsUEFydDBENGJSTHpQOHhlN1pnOHFkU2xKR1czLzhpMDlPTTZKNktpQXV0Qy9EOGhiQUFMeCsyanpHWWVORCtYUWJjc1ZEOHRJMXRXMmxna0NsQzV3dEFYaFZSVW01SkJBMG9hM0V1TVFoaHNwRmpacTJnTzI0Lzc0cEQyZGJzbTRaemFjRWUxeE0zZmgwRGxVWEtsN1FaQlJDeVdNd2p1djZkNlQxTEY1emJXa2xsaFNWT3FjVlBuOWV6U09WaXpabHVITUliVVZqVzNGOWZYYWtHWHdXRS9OOWZuTGFIUGNSYXN2eHQxL0ZiTDhpVzFDVWNHYmVjWTQ1Q0g0QmlRSzhzY3dwbHRUK0RuZ1dyT1BFK0NIY0NyUWx2Y3NRbTNsZnBablFhUWJPMUNaZDZpRDMvR2YrcGh2WEdYcFNBQ0NCWVdzNTFHQm8xUWxpUFhKV2crVThjMCtlNlZRcFlUZGREK1cwazB3dnhDQzJBREk0a2lwZDh5bm5tcUVHVENOTzVjVTdjWUhLVjYrNGlwdTQ5bkM5TnpWeVQ0MG5BMmcxVXAwcUh6bzRKZUY5MkQxOUpLUmtOQTRubkdBakcxQnR1b1NENUhZZWVqVnpsbFV3VmU2ZEQiLCJtYWMiOiI4NzFhODZiNzdmNDVlZDQzYTI2MjQzNTc4N2I1ZDcxOTZiNjg4ZTBlOWY3YTA0MDM1MWY0NWYxMTlhYjU0MGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw1dmNVanYrVDZ2eU15ZVVwSkViOEE9PSIsInZhbHVlIjoiRjJpNmdCRDdpbjdhaTJnOVJWb29sOVFCSlNhd0RPQTdRQ0NsamhTSEd1SlBFMERXOHpMazVVaFZiclBvcFJIZ3pKa0JFUGNneEptK3BabG5PNkRPTENhM3lRdmNvOWhReXUzWm9oclMyRWh2cHlEZXg4bTlacXlrUXdDV0xRRWFQZCtPNWgzb2RBZ2FYeDhkQk80dlBvRzQ0ekhRTDlnYW1wcE5zRldiOGUwUjM3K1I0ZGx4ZnQ5NVZtU3ZpOHVSaXk3SzJpTHMyUVA0ZEtPeC9SdE5oY09OVm5NdVZ2c0sxd3FrMTZEbHovNkVNZGVYNmVMaTlPWDVnZy9hTFVMeis1cWlYVkRuOThwZWlUSDFjSGJRRTQ3R2RpZHAwMHE4MHVxa1hIaWZlekY2dUxqMU90S3FJbWJ0WDFTTmYxQ1F4d1Z2azJjdFNvaU9NUDk0dDk1eW1nMTNWY1l1Tk5uTVR4SzE2Y1hHY3pQN3d3T3ZmK0JaVGFNWGcwZXFBdE0yMDhlcUVYWEh6Nm5wZVNZdURucXowY3hGU0xFYmdJc0diK3M3blpWdkI2UXB2RUlDSy8zOGR1V1llRHBwYXdvTEdWVHM1R3RBZVpKeEg3L1FYSUxFalk3WXk2bmhvTnpaRjNhRWtKL2Y4TXJIYzJqOXRTOWt2N0d6ZkpEMEI3YVQiLCJtYWMiOiIyN2E2Njg5MWEwN2RmMzc3YTRlY2NiZmIwYTI4MzkxYWZlZTY2OGNmNzkxMGJkNGI0ZWMwMzcwYzBkMzAzOGMzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRxa2xkZ1ZaUWIyZWk4OExzVHRpcXc9PSIsInZhbHVlIjoieXN1eUdZTU9xQjZ0d2o5UDYxUDZnYXlpekU4aUtWM1hUYmhUNFZ3anJsUEFydDBENGJSTHpQOHhlN1pnOHFkU2xKR1czLzhpMDlPTTZKNktpQXV0Qy9EOGhiQUFMeCsyanpHWWVORCtYUWJjc1ZEOHRJMXRXMmxna0NsQzV3dEFYaFZSVW01SkJBMG9hM0V1TVFoaHNwRmpacTJnTzI0Lzc0cEQyZGJzbTRaemFjRWUxeE0zZmgwRGxVWEtsN1FaQlJDeVdNd2p1djZkNlQxTEY1emJXa2xsaFNWT3FjVlBuOWV6U09WaXpabHVITUliVVZqVzNGOWZYYWtHWHdXRS9OOWZuTGFIUGNSYXN2eHQxL0ZiTDhpVzFDVWNHYmVjWTQ1Q0g0QmlRSzhzY3dwbHRUK0RuZ1dyT1BFK0NIY0NyUWx2Y3NRbTNsZnBablFhUWJPMUNaZDZpRDMvR2YrcGh2WEdYcFNBQ0NCWVdzNTFHQm8xUWxpUFhKV2crVThjMCtlNlZRcFlUZGREK1cwazB3dnhDQzJBREk0a2lwZDh5bm5tcUVHVENOTzVjVTdjWUhLVjYrNGlwdTQ5bkM5TnpWeVQ0MG5BMmcxVXAwcUh6bzRKZUY5MkQxOUpLUmtOQTRubkdBakcxQnR1b1NENUhZZWVqVnpsbFV3VmU2ZEQiLCJtYWMiOiI4NzFhODZiNzdmNDVlZDQzYTI2MjQzNTc4N2I1ZDcxOTZiNjg4ZTBlOWY3YTA0MDM1MWY0NWYxMTlhYjU0MGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051105198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1832409124 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832409124\", {\"maxDepth\":0})</script>\n"}}