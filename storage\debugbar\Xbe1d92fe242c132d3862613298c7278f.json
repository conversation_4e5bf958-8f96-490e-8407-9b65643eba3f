{"__meta": {"id": "Xbe1d92fe242c132d3862613298c7278f", "datetime": "2025-06-30 16:06:51", "utime": **********.716007, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.23846, "end": **********.716024, "duration": 0.47756385803222656, "duration_str": "478ms", "measures": [{"label": "Booting", "start": **********.23846, "relative_start": 0, "end": **********.659743, "relative_end": **********.659743, "duration": 0.4212830066680908, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.659754, "relative_start": 0.4212939739227295, "end": **********.716026, "relative_end": 2.1457672119140625e-06, "duration": 0.056272029876708984, "duration_str": "56.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027300000000000002, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6887128, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.527}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.70199, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.527, "width_percent": 13.187}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.708217, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/59\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-18926038 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-18926038\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-57206387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-57206387\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1016117021 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016117021\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-572332219 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299609712%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InU2c0dZOWNmMUJyRnhVQjZjMU5lV0E9PSIsInZhbHVlIjoieUs2WURwZ3hhRzJLb01MUGNQMHhjbHdwRExtaEtsbjRKWGhscDlRT0xjRkl4Kyt2dGhIR3lrOGhUYWk1cCtLeVZjT0JlV05MY0JYZWw1ZmRxd3FXamhwMS9SUXdITWhXQmgvcFpCcjhweHBNeXhsOHR5RlV4aVd0VUE0Y0ZVVDE4RG9mZkk1cDZHUEdtaW91L1B1WGRkR0UxVTRUb3A0VFJkcm9xQ1R2akNraExYbE5HVUpWTFlSdHBDYWxIZ3MwVnNFc3JydmFCc09lbmwyb3ZSeFcwYXF6U1pQMVRmckZjaG9XdmtLVFVxYklqNXJlS2NVRElYUXZBZjd3QjNPQ0swTzhhK1lNVkd1UDZlWm4weTJPREVBbEw4Mkl4UnpQcmpTcmVHYmZBM2djeHpHbENKc2paalYzYkdBYlRjcmR2TkRkanNaTnN4R1RVQTZqc1VtcU9Mb2RaK3FPZGJObTd1czZCWHFvNVhFOUtzd2VuMlpBeUx2VlNjeVpVeTdjcU1nc09PRVpOWjNiS0JVVTU5MjhDaldtbFZkbXp3aDBvaTNhZDZCQVc2aFd0YzRGNGhPZ29jdTlxRnVGSHkzalgvNWdkM2VNQVUyb0lzeXhhbm5pWlMreUY5emszb2xqYjFKZ2c5ZzcrOW42TWloU0ZzbldiMWQrSnVqeHQ3QW0iLCJtYWMiOiI2Yjc3OTdhMjg4YzMxNTEwZjM2NzhmNGFkZGQ3Y2EyZmQ4ZTdjMGZkNGIyMzA0MjgzM2Y2YTNhMDc3ODMxYzQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im0veXIrbnJ2ODQvekpzYkwrQmpYNkE9PSIsInZhbHVlIjoiWXVZcS9pSkpoL250SFE2bEdLdm5YQStzdWgycGN2QWVGZnhCb0Q3UFBUUi82NDlVSFBPQi8rMXkrSzcxUG1TNWF3VnRZeFB5bjAzS2NMMGNaRzVvYmJESTVtOGJxa3J6VFM2cVI0S25iU1RsN0dxeFBrS2dwSHlCanJiemphT1E3T0J5aFFYR25SVUREWDFpU2pYMTB0b3YxNEo4QlRRNGVhejF2aEd1OUhvdTJIZEpCUGF0cW80azkzekJ6eE4xa3JJMGdiYkYzMnpHTktjUHU0VUMrQ1VhYU90VXFFZzYvL3UrQWVWRVcyLzdJK3FETGFlcU4rMWpscWk1ZW1zcGdNWmZzd2tYcGZNbFE1UHB0U2JCdUp1emlWUzVtSnByUG4rRWpzQWdtd0VtWTRMQzJwRE1RcDVQL0wrUkh6L3YzWWtXWktlSFI3SnJGQ21paFNDenFNR2hLaDlhSXFPYklpbVV4ZlhTS0FBR2RSbHR5UllJMjJJK0g5dHl2Nk5sS2JhWm9EeERlMzNsV1dXVlNFQjF4aGt0Y3JGd2JvZUkzVkxJOTR1Z3pIVXBPcG9NcFZSVFNnWURFaFBsV012bzJhYUxSWUUrWjVPR1dzdHpTclNZRzFWQUJnU1FDRkU4VU5CQmFmTHJCU0JLZXkyRkF6N0pRWWt1Qmc5QTFRQjMiLCJtYWMiOiJiOGRmMjRmYzMwN2RmZGNlMThhNTg5ZDJjZGNiODBjYjkxNDk3MGU0ZmY4ZmFlMzlkZDNmYzY2MmQ2YThmOGEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572332219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1957440228 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957440228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-62025169 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlB3YjEyTEhXUVMxMVVLQ2pUMVB3T2c9PSIsInZhbHVlIjoiSytwbGxWeXBGMSs0UlFQakRQdUJ6K3l6T0hUMEROWjRac01HY2xwc0dGVEUwOEZidFRoc2MrakpvdW1UK2VLdFNwL1FUQVhjS3c5QWN4ckRzbzdwMUtEZzJPSU0yWStPZmVnbEI3eThpeG9QREowdnVsOVlWQkxWNVJsbnR2Ukl5aXU5M2NmcTYxQUExWG9hbjd6WUcxRTFlZTRVSzNjV0o2NW5oRmhRYVFLSmhqbDNRcUYyNTVvT0l4RXNYZnJkQnRaUzI4czhSdHZDcGx0clVmVHZQNVVVV0k1dnNCTnJpOG1nWFNHUC9iSElURXUwZEZmZElnOHlJeW5JVHovdHJNOVdrcDgrWHBaYXNQS2o2cjhTSFE2cEhlWDcwTFNpMDRORnNYbWhOSEQ3cVdzUHpvYUlJbTR1d1JGaUFKSURtWnl6RG5MRHdaekRYR2ROMFQ2bUU5VzR6cm9wNkhxRTJIaDkrcG1OQ2k2QUFOTzdIam5hR2NUczdxWFFIbU1xWjdyendlR0NtVXhOSFVVL1JHZVJtK0lCTVJhcjB2MjAxaUptU0FpMnVsWnpRVU5IRmJrS0VMd3gzc0xtNXZFS0IyblBBdWgxbll2bUNreTRRT0JIM1NUV2dxSzVEOG9TYXdrZHhFZFA3L3BqbXRkT04xSk1UemRqREhqZkl4MHgiLCJtYWMiOiI1MTI2YTM5MGU2YmI5YmQzMTcyYzllYmVhODc4ZDEyNGExZDI1NThlNGZlZjA0YWY2ZmMyNGVkYzk4YmEzYjUyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9tU1BWREZuMUgySnBiT1Q5MlEwY0E9PSIsInZhbHVlIjoiTXVqaUp4V2NWWUZ1SFVidzV0K2xWbzJMTTVWalVUbjA0c3FwQld2SEVQSVVjNUlRbkhjZ0o4dW9xbmc0L2tSaU1NL2NtUFVVdC90RVU2VkV6QVBIdXBSbldjQVFwMXBLSm5TOUptR3kydGRldVhBcFk5RDIveGpKc0lQOVJwVTJIQ1BFd3FodWVNL2xrTUVMWFdXMkV3emt0MlNJc3djY1RqOXZSQW5xSVhabVBFMDB2eEdpbklFcnFCTCtYZVQraUJ5dCs5T2VTUWEwTmZVZFZISENmbjRiY2lIbHlUVFppTkoveWZzTFc1MlVFb3pBMjBkWWE3elU2YlYxYnhVYUZCZGo3U3hMRFc4Vk5zRS8zN2ppZmYrYXRtazl0V0YwUTJ3VnNaQ0tzTE1CU3BCcWJ0MGIyd1Z5ZHMyMzYyOEVPb0dXWWZ5dWNQcW54SUVzcHZ5bzhvZU1BZ0NoWWhrb0VDQ1RjM1lRaFJHaFZ5NTgzSmc1VktWYUE4eDhSc29hMGN1R1F3ZFAzS3dSck5DaDI1YWI1dWswZG84Wk0xNG9YRXNJaXZ3ektpNzBnck5DTGFhRS9NbFhyZ25iNTNOQ3B0eFNML1hjYk9uTklORjIzeC9vL0JCSlZpdUtNUlRlOE1WTi96akllR2FHOU56Y0cvY3NlcHhnZHBFYzQ4YWkiLCJtYWMiOiI5ZGUxYmY3NDFhYTdmZGNjMGFhY2JjYjMwZjUxMjc3NTU5ZjMwM2ZmNWJjYWU3YzE5NjdmYzBjYWJjNjZiYjdiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlB3YjEyTEhXUVMxMVVLQ2pUMVB3T2c9PSIsInZhbHVlIjoiSytwbGxWeXBGMSs0UlFQakRQdUJ6K3l6T0hUMEROWjRac01HY2xwc0dGVEUwOEZidFRoc2MrakpvdW1UK2VLdFNwL1FUQVhjS3c5QWN4ckRzbzdwMUtEZzJPSU0yWStPZmVnbEI3eThpeG9QREowdnVsOVlWQkxWNVJsbnR2Ukl5aXU5M2NmcTYxQUExWG9hbjd6WUcxRTFlZTRVSzNjV0o2NW5oRmhRYVFLSmhqbDNRcUYyNTVvT0l4RXNYZnJkQnRaUzI4czhSdHZDcGx0clVmVHZQNVVVV0k1dnNCTnJpOG1nWFNHUC9iSElURXUwZEZmZElnOHlJeW5JVHovdHJNOVdrcDgrWHBaYXNQS2o2cjhTSFE2cEhlWDcwTFNpMDRORnNYbWhOSEQ3cVdzUHpvYUlJbTR1d1JGaUFKSURtWnl6RG5MRHdaekRYR2ROMFQ2bUU5VzR6cm9wNkhxRTJIaDkrcG1OQ2k2QUFOTzdIam5hR2NUczdxWFFIbU1xWjdyendlR0NtVXhOSFVVL1JHZVJtK0lCTVJhcjB2MjAxaUptU0FpMnVsWnpRVU5IRmJrS0VMd3gzc0xtNXZFS0IyblBBdWgxbll2bUNreTRRT0JIM1NUV2dxSzVEOG9TYXdrZHhFZFA3L3BqbXRkT04xSk1UemRqREhqZkl4MHgiLCJtYWMiOiI1MTI2YTM5MGU2YmI5YmQzMTcyYzllYmVhODc4ZDEyNGExZDI1NThlNGZlZjA0YWY2ZmMyNGVkYzk4YmEzYjUyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9tU1BWREZuMUgySnBiT1Q5MlEwY0E9PSIsInZhbHVlIjoiTXVqaUp4V2NWWUZ1SFVidzV0K2xWbzJMTTVWalVUbjA0c3FwQld2SEVQSVVjNUlRbkhjZ0o4dW9xbmc0L2tSaU1NL2NtUFVVdC90RVU2VkV6QVBIdXBSbldjQVFwMXBLSm5TOUptR3kydGRldVhBcFk5RDIveGpKc0lQOVJwVTJIQ1BFd3FodWVNL2xrTUVMWFdXMkV3emt0MlNJc3djY1RqOXZSQW5xSVhabVBFMDB2eEdpbklFcnFCTCtYZVQraUJ5dCs5T2VTUWEwTmZVZFZISENmbjRiY2lIbHlUVFppTkoveWZzTFc1MlVFb3pBMjBkWWE3elU2YlYxYnhVYUZCZGo3U3hMRFc4Vk5zRS8zN2ppZmYrYXRtazl0V0YwUTJ3VnNaQ0tzTE1CU3BCcWJ0MGIyd1Z5ZHMyMzYyOEVPb0dXWWZ5dWNQcW54SUVzcHZ5bzhvZU1BZ0NoWWhrb0VDQ1RjM1lRaFJHaFZ5NTgzSmc1VktWYUE4eDhSc29hMGN1R1F3ZFAzS3dSck5DaDI1YWI1dWswZG84Wk0xNG9YRXNJaXZ3ektpNzBnck5DTGFhRS9NbFhyZ25iNTNOQ3B0eFNML1hjYk9uTklORjIzeC9vL0JCSlZpdUtNUlRlOE1WTi96akllR2FHOU56Y0cvY3NlcHhnZHBFYzQ4YWkiLCJtYWMiOiI5ZGUxYmY3NDFhYTdmZGNjMGFhY2JjYjMwZjUxMjc3NTU5ZjMwM2ZmNWJjYWU3YzE5NjdmYzBjYWJjNjZiYjdiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62025169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095735698 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095735698\", {\"maxDepth\":0})</script>\n"}}