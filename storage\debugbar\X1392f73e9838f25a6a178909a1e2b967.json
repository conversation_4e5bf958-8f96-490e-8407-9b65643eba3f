{"__meta": {"id": "X1392f73e9838f25a6a178909a1e2b967", "datetime": "2025-06-30 18:08:25", "utime": **********.715913, "method": "GET", "uri": "/add-to-cart/2142/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.265335, "end": **********.715926, "duration": 0.4505908489227295, "duration_str": "451ms", "measures": [{"label": "Booting", "start": **********.265335, "relative_start": 0, "end": **********.616809, "relative_end": **********.616809, "duration": 0.3514738082885742, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.616818, "relative_start": 0.3514828681945801, "end": **********.715928, "relative_end": 2.1457672119140625e-06, "duration": 0.09911012649536133, "duration_str": "99.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673632, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.023940000000000006, "accumulated_duration_str": "23.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.651376, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.451}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.678905, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.451, "width_percent": 1.838}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.692808, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.289, "width_percent": 3.049}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6949642, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.338, "width_percent": 1.838}, {"sql": "select * from `product_services` where `product_services`.`id` = '2142' limit 1", "type": "query", "params": [], "bindings": ["2142"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.699378, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 87.176, "width_percent": 1.671}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2142 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2142", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.703559, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 88.847, "width_percent": 9.816}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7073321, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.663, "width_percent": 1.337}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1113870056 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113870056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6986, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2142/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1917839053 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9RZ2RPMkNCR0xKQVJVcmc5SDMxOGc9PSIsInZhbHVlIjoiRVFvTndnSzFuQ0RiZnd5ZTg2SUZGR292VFJCVElHQzVUZ3pPcCtjTEx5TUh4WFRXVnJtTHVvcTl0bzBNTmwxSG5IVnJHTTNEbStGVHJsZUFVaEdJWVAwUkxjVmZnSCtXdktQZXFweXViNmVVWFZ5bjFEWWIvd2lpbTI5QkZIOVpsejk4YWdLSGZyK2hqbEU4T1N1S3N3bmZ3UnhCZVRzb09MQXBQeFlrdjdDZWlJdmNFL25IdzN2ZEN4Uk5QVFNSNEdQTW16cUJqNXVBVXpOWFVRTGZWK2hLSEZqZEYzNGlNM2Z5L0lpKy9iQWsrc0ZZSVcxWnZGMkxlLzJObjRxa1hEYm1MWnNibTZQcThJWEZ5NTFnVDFtK3QwTEg0S213aVoxajVvbUFKMUZKZnlReFVwUzNqbWV2ZzZkNlNKK3RDNDJEZnlFVlRtU0NUdlpBSUNGaC9Ickw5b3RYWDZmYlUxWHRiTGZGSno3b3RVQ1ZhQ09EWUQ2ZUYrSFZDQlloR3FtYmx0NENLd1UvZW11Tlk2aDYzSWlyTFQ3UTh6dGdIN09WNUx0bDBnQXpVbXJQb2s3cXYzYkErMmNOZGNxUC91V2gybUNudUdzdE9haTdvdk1jK2hqais4bmNuVGlYNG9ZdTlSa0h6cTNJbk01ME5BSXkyQzdyaDJvQU90TkoiLCJtYWMiOiI4OTE3MjkyMGFlZjAwNGJjY2QzMWQ4OWQ5ZTkwODdlMTU4M2UzYTE0ZWRlNTNmYzBhYzQzMTQ4NGI0MDg1MGQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFTYkoyWWd3dkJtNU90WjhYYmNsNHc9PSIsInZhbHVlIjoiRVVKeFJQdGlxTlA4RjJpb2VoUlV6R1pvdEVsbUkxajNTUTVZNmtmMG5sK0kxeDJ2SVpHV1RqMlVQbmN0YkYxNUFjUjU1Z2lXZlh6OVd6cUhjYjJjL0ZPRGREZDZhTHZraUhGMjhENGIwRm4rQjJSUlVQbGZkSFhuWERUeXl2ZXFvWHhNTURXRTd4b2lHT0J4MzFEVzNwOGNCUVBIR1VyMkJyOXpHY0MzRGVYaVBDWFlGMFFwNGZhNGJiNldjTWl1aGMzMXgvanhDYmhUL1htQmRLRjFqSVp4T093aW5BaGVhMkx2V05rYmtFbnJmdzE1WGN6N2hxM25ieWtxWXRXT3A2NTdpVUg1REp4VEFOejA5YS9qT2JPRTh4Z2lTK1BXS2c2Y1crZW1LRnE5N0NvZFhjUDMxdm1nWnljeGIzTVpabVdDN3N5cXRpaU55K0ZZd3hkbSs3dXBFOElYQmE2bkRYU01xRmVqWU9kUDNPazlMdllkTm1BSkpsUmlwdFovKzVEYnh5R2tKVnBiZ1hlcFhVT0c0VjZXQ0F3bkxNbFZhamZ4VHVEam5VSE10YVRnUkJqam1YRnlySUxUMm9xNnBuS04rd3E5VWFhbFZEeDExTkZGbWJ6cDBud1lxUmlGcC9USUFaRzh4YWc4aFZqZkFndWRvcTNWa1RxUjg4Z2UiLCJtYWMiOiIwZGRkYzYwZmMxNDU0OThjZjdiZmVjNTEzY2U3YjRiY2E4ZjA1ZDYwNWU5NDZmZGY4YzFjODllNWY4NjM5NTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917839053\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1298472728 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298472728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1996577580 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:08:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklmMUhNR3FOYWJFRDdjQk5hN3BCVWc9PSIsInZhbHVlIjoiK3g5N2UrYkVhYWhRYkFXODZ4VVVUWk5tNmtBNDlMZlRWSGIxaWo3cC9kcEFQWWdiTk5ubkFidW9QTDBXQnkxbENpOVdyMWF3T3k3MktmOE1DM2JDQXZKdnlGY1NiVnBkbmxTSzNXVUhMNFpWQjRPQncyOG9yZllMTDByRFl2T0dBNmJSZjl5NTZqVStwMVYwWXpZQnpUZzJHeTdTZnlnUzJXRjMxeGNpWi9qK3E3RW8yeGViLzhlZThqLzB3S0FNOGI5SC85MHh4MVhpMHhJV2xYbWw1aWVQeWVBOXdTMk1HNGU0QVB6ck9ON0tMVi9ReFFTbDFyK3JPdjNuT3ozVnQ2TWtzNW56Vlpuei83L1VEY1AzK0tma3ZMOWtDT1JNWjRMK1NCUjRiTEVXenFML3l2RmV6UC9sZzUwbDIvdTVGVGpjUHk2YlhiOSsva0RQazhaemxVUC9SMlhpbU9sVG5uTm1BQW8vQnZqNmpGK0M4WjVXdWRmKzRxamxXZUUwU2R0UklQSVFrSnFYTUZDOGUzMUNuM0ozNFZydU5kQWxzT0pzZ0JkNDh4VmFnZ2lVKzRFN09wbUpUS21ReHhGSjJoMlkzNkJGejRIOTRYYXNjZlNKekUvTjlidkt5bm1pYUtCSDV6cVFmRnFpdnF3UHBOL25XYWZaNkhPUFovMVoiLCJtYWMiOiJhZDJiNmY3YjQwNzNkMjRhYTMyMjFjYWIyMjM2YmRlYjJkNmJkZTljNTIyMTExOTRkODFhNWU0MzM3YTY3N2UyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlplSXlFWnVhZnZRSURpMjYxSWQzbWc9PSIsInZhbHVlIjoiTXVrNzFySk9ycldLeFNyNHhkdVhLMzZDTXZXZVY0S29kZHM5dWU3ZnFNd0hmOHEzcndYY2pDRU1GdzVSZ25sY2ZzdEtQSVN4ZlJGQlRKWFJpckFLczNFY1BIUUE2YUJLR3RuNVpOdWJpY1JJV0thS1BGMFh5VXFxSmFJUVdMOGFjSmcvbTVvVEZEYlllaHJ4MmpGdElMUE1JVHl0L2hiWWptb2I5R0FOK3VHZEZVWVJqeEZLZ1N6NjEvRmxCZVV1RnFmQjh5SEdZVTl3OEZCNVFyWS9WOEFURUd4anlqY1hhc0EvZjY3ZEdZZ1BBRmZ0UllwbS8rcVhIdHMxYWZKSEM0dEZEcHU3M1dITlB0T1ZBeXZJdzlQUTVXQ0JlR3k0WEc5L0FmM3ZNMlZTT3Jqa09nK0JEc25PTG84NkNyUEhCU0kyWUQzak0rUDZOVTd0cGpNaXhqckNjdXBvNlh4RXREUDFRbTdjM2o1S0pSNFJpZ1QvaEVTTkhSWDRpNUdzYkdEZ2RuNVROWCs0RGpIRGNZSm16MUl4Sm9lSS9QS3J4ZFNjZ1VnV2lPclpSdktTaWI3c0l3aysxR0p2YzY3OVJYNjJLM08veTRDNnFRNVdsMElEcTF4K2gwT0sxOTJYVjFxbThzZ2ZtR210UjlDL0VxN3A3R1Jmdk1SdFNJR04iLCJtYWMiOiJmOTg1MWY0NWRjNTI4NGNjMTg5ZGE0ZDA5ZDBlY2E5MGE4OGYzMDc2MTA2NzQ0Y2I3M2E0YjQ4ODRjNDVhNzljIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklmMUhNR3FOYWJFRDdjQk5hN3BCVWc9PSIsInZhbHVlIjoiK3g5N2UrYkVhYWhRYkFXODZ4VVVUWk5tNmtBNDlMZlRWSGIxaWo3cC9kcEFQWWdiTk5ubkFidW9QTDBXQnkxbENpOVdyMWF3T3k3MktmOE1DM2JDQXZKdnlGY1NiVnBkbmxTSzNXVUhMNFpWQjRPQncyOG9yZllMTDByRFl2T0dBNmJSZjl5NTZqVStwMVYwWXpZQnpUZzJHeTdTZnlnUzJXRjMxeGNpWi9qK3E3RW8yeGViLzhlZThqLzB3S0FNOGI5SC85MHh4MVhpMHhJV2xYbWw1aWVQeWVBOXdTMk1HNGU0QVB6ck9ON0tMVi9ReFFTbDFyK3JPdjNuT3ozVnQ2TWtzNW56Vlpuei83L1VEY1AzK0tma3ZMOWtDT1JNWjRMK1NCUjRiTEVXenFML3l2RmV6UC9sZzUwbDIvdTVGVGpjUHk2YlhiOSsva0RQazhaemxVUC9SMlhpbU9sVG5uTm1BQW8vQnZqNmpGK0M4WjVXdWRmKzRxamxXZUUwU2R0UklQSVFrSnFYTUZDOGUzMUNuM0ozNFZydU5kQWxzT0pzZ0JkNDh4VmFnZ2lVKzRFN09wbUpUS21ReHhGSjJoMlkzNkJGejRIOTRYYXNjZlNKekUvTjlidkt5bm1pYUtCSDV6cVFmRnFpdnF3UHBOL25XYWZaNkhPUFovMVoiLCJtYWMiOiJhZDJiNmY3YjQwNzNkMjRhYTMyMjFjYWIyMjM2YmRlYjJkNmJkZTljNTIyMTExOTRkODFhNWU0MzM3YTY3N2UyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlplSXlFWnVhZnZRSURpMjYxSWQzbWc9PSIsInZhbHVlIjoiTXVrNzFySk9ycldLeFNyNHhkdVhLMzZDTXZXZVY0S29kZHM5dWU3ZnFNd0hmOHEzcndYY2pDRU1GdzVSZ25sY2ZzdEtQSVN4ZlJGQlRKWFJpckFLczNFY1BIUUE2YUJLR3RuNVpOdWJpY1JJV0thS1BGMFh5VXFxSmFJUVdMOGFjSmcvbTVvVEZEYlllaHJ4MmpGdElMUE1JVHl0L2hiWWptb2I5R0FOK3VHZEZVWVJqeEZLZ1N6NjEvRmxCZVV1RnFmQjh5SEdZVTl3OEZCNVFyWS9WOEFURUd4anlqY1hhc0EvZjY3ZEdZZ1BBRmZ0UllwbS8rcVhIdHMxYWZKSEM0dEZEcHU3M1dITlB0T1ZBeXZJdzlQUTVXQ0JlR3k0WEc5L0FmM3ZNMlZTT3Jqa09nK0JEc25PTG84NkNyUEhCU0kyWUQzak0rUDZOVTd0cGpNaXhqckNjdXBvNlh4RXREUDFRbTdjM2o1S0pSNFJpZ1QvaEVTTkhSWDRpNUdzYkdEZ2RuNVROWCs0RGpIRGNZSm16MUl4Sm9lSS9QS3J4ZFNjZ1VnV2lPclpSdktTaWI3c0l3aysxR0p2YzY3OVJYNjJLM08veTRDNnFRNVdsMElEcTF4K2gwT0sxOTJYVjFxbThzZ2ZtR210UjlDL0VxN3A3R1Jmdk1SdFNJR04iLCJtYWMiOiJmOTg1MWY0NWRjNTI4NGNjMTg5ZGE0ZDA5ZDBlY2E5MGE4OGYzMDc2MTA2NzQ0Y2I3M2E0YjQ4ODRjNDVhNzljIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996577580\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1869571867 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869571867\", {\"maxDepth\":0})</script>\n"}}