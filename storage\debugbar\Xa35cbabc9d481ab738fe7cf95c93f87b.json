{"__meta": {"id": "Xa35cbabc9d481ab738fe7cf95c93f87b", "datetime": "2025-06-30 18:42:10", "utime": **********.66888, "method": "GET", "uri": "/search-products?search=*************&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:42:10] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.655552, "xdebug_link": null, "collector": "log"}, {"message": "[18:42:10] LOG.info: ✅ انتهاء searchProducts - الوقت: 9.36 مللي ثانية - المنتجات: 1", "message_html": null, "is_string": false, "label": "info", "time": **********.664716, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.252363, "end": **********.668896, "duration": 0.4165329933166504, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.252363, "relative_start": 0, "end": **********.575389, "relative_end": **********.575389, "duration": 0.3230259418487549, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.575397, "relative_start": 0.32303404808044434, "end": **********.668897, "relative_end": 9.5367431640625e-07, "duration": 0.09349989891052246, "duration_str": "93.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47771000, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02821, "accumulated_duration_str": "28.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.607464, "duration": 0.0217, "duration_str": "21.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.63672, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.923, "width_percent": 1.524}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6498559, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 78.447, "width_percent": 1.453}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.651558, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.901, "width_percent": 0.886}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`sku` LIKE '%*************%' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.656228, "duration": 0.00513, "duration_str": "5.13ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 80.787, "width_percent": 18.185}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.662873, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.972, "width_percent": 1.028}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1956726852 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956726852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655012, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-967960496 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-967960496\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1419448107 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419448107\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1331079820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1331079820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-833797650 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRFSmx4MmdocmVWZFBoVG91dWJuYVE9PSIsInZhbHVlIjoiYmF4Rit1YmhPNzdySXVIYm5RSmN5NVp3Y2Jac2JkdkpuUm04d2lmMUdDZW5jd2dxWncxTlZybUVWdms4REx0Vml4U3hGYTBuVXlQeGZkMnR0QkxJcmY2cUNYV0JlYkZ4TEtjN2V4RUdHOEpRU1RJeWtkZUpvS3BmVlM2SEhIdXlCV0ltMHUxdmF2TTFxS3Q5K1Z4bmpKT2NMSjM3Q1grQkJ5WVFMQy8wS2FTZFZKbGE0SWlqRm0wN2N0RXdjbFU3WHJDZnNMQVc2YjJjMmZjNjZhZVl0NWZwbGhQSkhOTDJUSWtFdVpQNTZCZTZqZ3hLaHF0NStqWTZtTmdmYlp3U3RONnppNHA2REJZM1R5ZWszRFp2MjA0RlFZTi9HRHkzZ2ZxVFIycFZrekdJeFFJMEphcGpETDJQdEVYaDdRbnZrbnNrZHVUV05BeUJwL0drM3AxR29mc3VMNzZXTWx1RDRra3lJSDRjOFVub0hXUzlvRWxiVi8yMGVqV1BqNlA2MGg1cnUzaS8wODR1Y3Z4U25TYWFTU1F2WnRBSXJmaWxrUUVhODBGNEZrNlo5Wk53U0I2bkhvc1VjWHA5T1JZMUhxdC9hRjgyVE5JK1FlVXBuNkZ4bEZtNk5rdlRCWEVYbE0vWWtVcWhaekZPeWg5RzVXNkFSUzFTSk9MY0NXY0IiLCJtYWMiOiI4NzRmYzJmNmFmN2NkNjIyMmM2ZTBhMDE1ZWVmNDdhYWYwM2Y0OTlhZmFmOGQxZTU1NDUxYjUzNTU5NTgzY2NjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkM1bmdYQVUweFhVWGsvOGp3endLUkE9PSIsInZhbHVlIjoiQXlzL0VFUDhhbG9oQndjLzE0Y3FMZHE4Z2lRMHdpandJdlJTZldkMlVwYzkzeU9lelJNbzFMNml1ZmNiQ3RtS0VTZzlna1Y1MCtRLzE1enZxNisrb1RhSDVjejlFT1NSVTRlQ25RTEsvdWdESGZTUEtBMEVVbldNemdLTjJUdVVmaUozQjlMdXNRWnVVbmdTMUx3aGJYTGZKVlJ6ckhyVVFVS1VCWDczclA5SnROZEIwOHJhVVVyOWg3ejQ4ams4UW1hZWJGWnMrQWFrOFRRS2R5Um51Qzl6NHVadUgxNkF5RFF3Q010V2QxY29pZUFxOXd3UVMwS0FMS3I1RzA5cERzVXk5TWFDZlFhbHRUeWxITExwREtkeXRESXZEaFlVTlBIdFZTT0VMamxGbnFlRmZRM1pZVE9tcDQ5d0tzTnVIdjZ4NEdmdWdmM2hjZU01aThiYUMrUDNNeVFPaHJxdWtwMjFOWEx1K2VUc2hXbTVYVEhQS3F1R3lSQmxaTllRUjdzaGJ3OWhHQ0VoSnlSR3g4U2g5UithQm1KT2owWmI0T3BJalBlRzFISUFBRHVwWmdVdGkybnRVaWN0b3lEelk0ZVczK3hiQ0licU0yY200U3VwVzJ0R3NtK1BIVVZyTzVsb215WU0wWm04UDR6eWVwbitVUFZOS2xORTlZd0giLCJtYWMiOiIzODE2M2NkNmU0YWYwNDZjY2MxN2JlNzhlNzI4NGEzZjIzNDA5MzFkZGIyZWVmMWQ2ZjRhNDBiYTcyMGE1ZWI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833797650\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-113465331 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113465331\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1510652464 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:42:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd4RC9NZFFaRUdvajZJNTlpV3J0TUE9PSIsInZhbHVlIjoiMTZIc2dWMENzTU8yKzFxRFJscWpac2QyeFByUWdIMGh6T2ZQc0M1MEJ4bkxpeFVFSXdrRnFPbnB3UHFCNFpRZkV0NHB2dGx4SHB0NFJQS1N0aVV5S2tNYWdyS3dwT1dhV2pacDhSVXBWYkFKUDg4RC9LMGJSYXNzOHcyQWc0QUJxSEhLNzgra3NUU3A1WmhFdVBDV3VpV1dnZXpWVE8vUlNOK0locURYZGNyakZVTC9pejk5dTdER1F2NnJTTWhGUWR2L3ZrYTBRcHlhaU1POHpVRlJtaEVjNFhzcWZBRTVFbEo1NCtGdWh1T1NvcHFTVG5zT3BiZWIwNEhwT2dtVzVqR2czSk5ZWjdKVlJsU2NEdVZHclVXQmgwM2h1OHI2M0RtblpoQ04vVGNDR205dGRQWFBQSWRLanhJRlRMZjdmdmxTck03S1NNQWpDeTdHeCs3ejBkTGJMTFhFQnZzOEMyb2s3RlRSRzZ3aXRQNGJLNElxYzJFU0w5aVpTWUx1cjluSFRTQk9Jb3RtVFdMZWhOQmE1bFU2Y3cyZ1VLR0NrOG9peG4rd3JKTXhTVFJBeS9KR1BGcm1vTTBiSko3cVdIbEl6MXVYU1MrVVA3dXVKRERzbFB0SVdtWDIzR0pKd3VoYnlVQnNGQ0x1S0Q4YUx0eFl1MEk3WDBMaVN4Sm0iLCJtYWMiOiIwOTEyZDRjZGYyNjBjZDFlMmQwNmFhNTU1ZDQ5NzgzNDljNmE1NTUwMzY1YTZmNmVkODk5YTNjYmFkMzI4YTFmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFnRWVsc1J4VGxvSnp3MnR1VjhHaUE9PSIsInZhbHVlIjoiMTFVZGsyTmxmQXd4NGVwZnpuSEZnMmhveUcxWFNBSjBmNFZzTVJWN1ZiSzdMZkJYZmdQVnNUeUU5SDcyZHozNFVlaHgrNUkxVWYyenRpekNFS3JhbkZ1R09FNmY3UVBycFk4Nk5wa2xkVHdxMnU3NTg1cTFtRGhsQVVZOHRtK1RTMUtwMGV4U1lHdXY5NEIyQ3FYVmVTV1R6Zk9uUktsWUJJQUtvZ3QyVFJvRDhkdk9iVTlOb2l5WVo3VFNMZ0taZ2Q3SkJTRWFtalluTUMyNjA3cHF5eWN0ZGp6WmpLSEJlckZSNTV3V09HN1VXeXhveWtBUTRublNBYUpSR1BnSFJOWGZtVjY2cjRNQ2Y1QVlOZW16c0FkbGFOMlFHL0ltR1FmN0FjTFl5SWx4bUg5QStCaUN1eXZUNUlnSWpWUFIwa0xKZWRsVGxsY0pwdjFGdVoyOTNoS0pVMW12TDVjdEdIZ3V1cTdGSGcxRGRkTUZRdEYxWktjSGF0RVZEOVR1S2ZDSVd3UTNGUDBiN0tDUmxVOFBNTzN3clZrbFBONWllYnVxSFY4NXY3ZzJyRE1xWGw2Nk52Rm9mRGswSUFGQXU5YWsrMTJ4Z2t0T0tjaTk0b0lRbDdnQUI2aTAxTkIrdmxrYzdMdDAwQjN1bjNIQ3ZKWHY3cjdWQytGb3dzaHAiLCJtYWMiOiJmNDU1NzYzNmQyYWY0YmIwNWRjNDI0MDgzYmI5ZjBjNGIzMGYwMDg4MTE5NDNkY2Q0YmFmNmFmYjA5MjI4OTdiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd4RC9NZFFaRUdvajZJNTlpV3J0TUE9PSIsInZhbHVlIjoiMTZIc2dWMENzTU8yKzFxRFJscWpac2QyeFByUWdIMGh6T2ZQc0M1MEJ4bkxpeFVFSXdrRnFPbnB3UHFCNFpRZkV0NHB2dGx4SHB0NFJQS1N0aVV5S2tNYWdyS3dwT1dhV2pacDhSVXBWYkFKUDg4RC9LMGJSYXNzOHcyQWc0QUJxSEhLNzgra3NUU3A1WmhFdVBDV3VpV1dnZXpWVE8vUlNOK0locURYZGNyakZVTC9pejk5dTdER1F2NnJTTWhGUWR2L3ZrYTBRcHlhaU1POHpVRlJtaEVjNFhzcWZBRTVFbEo1NCtGdWh1T1NvcHFTVG5zT3BiZWIwNEhwT2dtVzVqR2czSk5ZWjdKVlJsU2NEdVZHclVXQmgwM2h1OHI2M0RtblpoQ04vVGNDR205dGRQWFBQSWRLanhJRlRMZjdmdmxTck03S1NNQWpDeTdHeCs3ejBkTGJMTFhFQnZzOEMyb2s3RlRSRzZ3aXRQNGJLNElxYzJFU0w5aVpTWUx1cjluSFRTQk9Jb3RtVFdMZWhOQmE1bFU2Y3cyZ1VLR0NrOG9peG4rd3JKTXhTVFJBeS9KR1BGcm1vTTBiSko3cVdIbEl6MXVYU1MrVVA3dXVKRERzbFB0SVdtWDIzR0pKd3VoYnlVQnNGQ0x1S0Q4YUx0eFl1MEk3WDBMaVN4Sm0iLCJtYWMiOiIwOTEyZDRjZGYyNjBjZDFlMmQwNmFhNTU1ZDQ5NzgzNDljNmE1NTUwMzY1YTZmNmVkODk5YTNjYmFkMzI4YTFmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFnRWVsc1J4VGxvSnp3MnR1VjhHaUE9PSIsInZhbHVlIjoiMTFVZGsyTmxmQXd4NGVwZnpuSEZnMmhveUcxWFNBSjBmNFZzTVJWN1ZiSzdMZkJYZmdQVnNUeUU5SDcyZHozNFVlaHgrNUkxVWYyenRpekNFS3JhbkZ1R09FNmY3UVBycFk4Nk5wa2xkVHdxMnU3NTg1cTFtRGhsQVVZOHRtK1RTMUtwMGV4U1lHdXY5NEIyQ3FYVmVTV1R6Zk9uUktsWUJJQUtvZ3QyVFJvRDhkdk9iVTlOb2l5WVo3VFNMZ0taZ2Q3SkJTRWFtalluTUMyNjA3cHF5eWN0ZGp6WmpLSEJlckZSNTV3V09HN1VXeXhveWtBUTRublNBYUpSR1BnSFJOWGZtVjY2cjRNQ2Y1QVlOZW16c0FkbGFOMlFHL0ltR1FmN0FjTFl5SWx4bUg5QStCaUN1eXZUNUlnSWpWUFIwa0xKZWRsVGxsY0pwdjFGdVoyOTNoS0pVMW12TDVjdEdIZ3V1cTdGSGcxRGRkTUZRdEYxWktjSGF0RVZEOVR1S2ZDSVd3UTNGUDBiN0tDUmxVOFBNTzN3clZrbFBONWllYnVxSFY4NXY3ZzJyRE1xWGw2Nk52Rm9mRGswSUFGQXU5YWsrMTJ4Z2t0T0tjaTk0b0lRbDdnQUI2aTAxTkIrdmxrYzdMdDAwQjN1bjNIQ3ZKWHY3cjdWQytGb3dzaHAiLCJtYWMiOiJmNDU1NzYzNmQyYWY0YmIwNWRjNDI0MDgzYmI5ZjBjNGIzMGYwMDg4MTE5NDNkY2Q0YmFmNmFmYjA5MjI4OTdiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510652464\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2080100345 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080100345\", {\"maxDepth\":0})</script>\n"}}