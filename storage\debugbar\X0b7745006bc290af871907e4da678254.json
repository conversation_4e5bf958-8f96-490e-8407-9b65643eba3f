{"__meta": {"id": "X0b7745006bc290af871907e4da678254", "datetime": "2025-06-30 18:05:55", "utime": **********.399577, "method": "GET", "uri": "/add-to-cart/2303/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306754.926223, "end": **********.399595, "duration": 0.4733719825744629, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1751306754.926223, "relative_start": 0, "end": **********.28386, "relative_end": **********.28386, "duration": 0.3576369285583496, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.28387, "relative_start": 0.3576469421386719, "end": **********.399597, "relative_end": 1.9073486328125e-06, "duration": 0.11572694778442383, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673808, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0309, "accumulated_duration_str": "30.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.323217, "duration": 0.02477, "duration_str": "24.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.162}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.356318, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.162, "width_percent": 1.877}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.370418, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.039, "width_percent": 1.748}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.372381, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.786, "width_percent": 1.133}, {"sql": "select * from `product_services` where `product_services`.`id` = '2303' limit 1", "type": "query", "params": [], "bindings": ["2303"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3769178, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 84.919, "width_percent": 1.294}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2303 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2303", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.382616, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 86.214, "width_percent": 11.65}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3879201, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.864, "width_percent": 2.136}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2073559316 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073559316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.375951, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2303/pos", "status_code": "<pre class=sf-dump id=sf-dump-1931692415 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1931692415\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1506656919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506656919\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1112965968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1112965968\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1822651662 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJBdXBGKzVzN0VpMGtQajNJaXlOYmc9PSIsInZhbHVlIjoiVEZiSjN3L2l0WFMyMlNWSjAwanhlQ09DZ0hhbkgxQmtQREJUaXpmdE1WQS91VllUakcraEh0V2Z1TFdibTRxbWpBUWhkcVV2WnZSU0Z1NERIejZXWmJvNWdueHZ5RUVtb0x1VzhEVTFIN1VLQjBaTlJlcG5IMWlkdzNFM1hmalQ0OU42Z3FVZnNHQjNycEwxK2pLbXFSOEM4YkxJUzNXUkE2NWpUUnJKaGkzN3lJVGNEVTBWbWdOQjlqTzBBazRDaHVrV3RFSzMxRHlZSWRJeUdwVEdrNGx0ald4WERmOUZFMG5MZ25Ja2kvMExVanJxZmV2QzVoVkVYM21HdjJKV3FqTTQ5VTRIblF1aFUwcWg1dVQ4V1Q5b1FsQThOSHB0ZkFvdkdHWGxIaVdIemR5alRadTFpK1p3UGNpRjd0ek1jSHNxNjRnVXhudHRSSG50VzRLcmN5MmtKWXB5Um5sdWxWL1VsK3haNllWV0s5SFo3WGVnZE5XNm1sWlFWNzFoZ1ZkaW5xWFJjWkhRVmxQQTZHbUtmQXY5YVRVYVhkMlpWZy8vTmtQVzZ4Ull2NXVTK2thaU9HQXp3a3BWbUxjL3lYS2tHK0F0N2ZubVRoVUdibjNObEc3UUxIdUhoTmlzZkZwSnlHc2FEZitGaXEwSkZTV0YyVGVYajF3SkFFWFYiLCJtYWMiOiIwOTE3MzVjNGJkZDQ3M2VhMmEwYjIwYWVhYmE3ZTBkMDcwZGJhOTg3ZGY4NGYxMDc2NjAyNWY1YTk0ZThlMTk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldKWkwrZUt4ZDZ0MytYUjVqM2VJZ3c9PSIsInZhbHVlIjoiRUdvZnFrQVlnS3g0OWEzL3NLcWN5VTVraGlPcFlyenlOLy9XcnRaY0Zobk10SURYOWE2RzhiTDFXdnBFZjBvUkduK0UxblhyZUZ3ZlY4cTBPZVZXaXMrNkNRZmhvSmhOL0EybFN1NTZWT1VWalpnSjZZUXpKaEhCaWJrN04vQ0M2K0kxTjBWeDUyQlE0ejJSVllyT1N2QTVZRXBCSHBxd2lUYk0xY1R3YmlraEJ1TFVMRDM3UTk0OGIzTE8yOGlkQjJaRjAvaFpseWFiQXZ0ZWdFUFl5ZitSVU9PL0ljVks2UlpST1lkZVk4aHpKSC9acGF4ZWJwamRRNzl2Rlk0Y1B3R3pNMzE1dGpoakF4Nk1RT3JtRjlhb0JwcDlPOWwzbGtnc21vV1U3K3ZuVkFlSjY2V3pubktiV2lmMVRsdG5oeHJYTi9qeVVoc2twd2t3YldZWXU0bGxFNWZSeXlMcFRubjB2c2g0bmxKVmxVWHE1WE9MOEtsOGZ1QkpSN1RCbGVKU3JoMzBiUWdneTViUWRhTXY5QWVhbGxvck1NMjF4N0RDYzc3dENpU3JYT2lEOGt3ZysrbkhGNFVNTVBNQVNRVVdtS1JRNjcyYXpsMDZVZ1prNG0xcXcxcmZRalcyaStxVUIrYWlWaEppK1phcEJuZHo1SW1FU2FUQXoyYnQiLCJtYWMiOiIyNTJjZGYwN2UwMzdlZjZkYTZlYWM1YTlkYjc0OTU3ZjA3OTAwMDMwNTE3YzIwMTJkYmViNzhmMDk2Y2RhNmNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822651662\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1275917044 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275917044\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-763288441 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdoUTN4aVJCaDR3anBYbUo2UlN3VHc9PSIsInZhbHVlIjoiK0ZtaUxjNXNuS2dxcExUMWdhTEVvK3l1bmtLRnRkWVZNR1A1UHJzMlpyKzFYYThQbDk5WXdqb2VPQWsvaTlWdEZQb2MrWUhxVkRlZVFhL3NDZG1iVGdPL0xDUkZWM21CWjh1K0JPdGVYNG03RTZkNHJUMjlRYmVpQy9KdTZBTFZxQU1QTUNKbXhudXRwNFd0R3BvMTRCR1pHSG5UaDJCazZhMWNLSGIzcFk0RkdDNGZrMHVZSHliNjRxL1gxRGtVM1BZZlZLOTN0eEo1SHBtdjJaakJFMWo4c2piNm9nUVlvVjJJZFFFbXA1Q1V1MzZmNzNQQ2tPQ3l0YWUwSld5YU55RC9ZQnJKbW16T2pyL0J6WkM1U3krR084S2E3RG5PSEJSM3VacDhvbC9idGVzdnNOQXZLSXNHUUo2L2IrUStJazNIbTNxaWhMUGlvRGtuNnF0cVc1K1FHdkorYkh5T0c5SWE4dWlLMEVhWE9keitMS3VPRDROV3pDTGpnN1Q3VXlpYkFveXcrdUthY01nMlFrN0tjbkNKQnFzZE14SU05cG5TSjkrd1g0N3Zxcjg0UHFRMUNTVEVobUU0OEpjbitNY3BRZTNYTGJ2bUxNVSt0eGc1eDd2NGNHWW9BSXBtejMyc2xMRDhYWWpKMjR5TzIxK2RJNVlTZFdKRjNLMFIiLCJtYWMiOiJhMGRjMThlM2JmZDEyMDZiZWZhMTc3MmM1NDkzNjZhYjEwNTAxY2RkMGFhZTUzMDE1MzkxM2U2MjFmZDViMjVhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNUUlh5TWlncmJXaWJPNjkzQzB5VkE9PSIsInZhbHVlIjoicTUrKzkxWFdwT0NnMzUyOUdydHVDMDExRXd6eVJUZ1N6dkt4VGp0amR2U2NlOFZpbmg2Rm5BS202MTBpSXVHcEJLNUk2N0E4a1pSV3BOSHdsKzI5SU1oMDBobFhPbEFIS1piMDBjQVhoYXYxNEp2bytCVG1iTTUzM3MzMGJUN1VDN1hVNWZ2N0R3elF4RjcyNlNXamhGQ3ZLallBMU12a0RvaGt4MWlwR1h1Rm0vQ0VpV3pzSTlKYzlpQU5UUkdYYS9HOERnMGt3dDdhYUxSem1IWHVSSGRqV082UFJueTJvWXByWDI2dkJUMVB5UnJVU3lOT1N0cGduTjBLUGtjZkh3V0tqcThZaGMwNXJPT3V2Yk1RNW1tTjBVVHUxRDNvcmtoS0RDaXhjZ3IrbE9GNmhhSGR4dFJUUkNQTFUydyt6Uld3ZmxDOW1PU2FtMFBOREhCNzRPcFZ3VFZrZzFaTVdHWG9EWStQQ3NYZDN5dUg1Yk9DVWpPVnVkZGo0dk82SGx0NVFvWE5TeUR0OXlHeHdPc3d6NkhWbHh3b2hqeVVsekNmWXJEaURlMWdqVFVsU2o5RGhZTjB4K0N3RFY4QTZpdWtUQktNbU9HekVZWEx3UVJ3WTgvWkQvcm9WUWFrQVA3b3BuK29sV3ZZbWFEdTNFQkJQcXcwbXVSNWlnTzIiLCJtYWMiOiI1NzdmNzlhZTNkOGQ3NWNlYWQ3MDk5NDVmMTFlZjBkZjM4ODU4NDA5ZDFhMmFmOGVhYjk5YTA1NGFiMGYwMDJhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdoUTN4aVJCaDR3anBYbUo2UlN3VHc9PSIsInZhbHVlIjoiK0ZtaUxjNXNuS2dxcExUMWdhTEVvK3l1bmtLRnRkWVZNR1A1UHJzMlpyKzFYYThQbDk5WXdqb2VPQWsvaTlWdEZQb2MrWUhxVkRlZVFhL3NDZG1iVGdPL0xDUkZWM21CWjh1K0JPdGVYNG03RTZkNHJUMjlRYmVpQy9KdTZBTFZxQU1QTUNKbXhudXRwNFd0R3BvMTRCR1pHSG5UaDJCazZhMWNLSGIzcFk0RkdDNGZrMHVZSHliNjRxL1gxRGtVM1BZZlZLOTN0eEo1SHBtdjJaakJFMWo4c2piNm9nUVlvVjJJZFFFbXA1Q1V1MzZmNzNQQ2tPQ3l0YWUwSld5YU55RC9ZQnJKbW16T2pyL0J6WkM1U3krR084S2E3RG5PSEJSM3VacDhvbC9idGVzdnNOQXZLSXNHUUo2L2IrUStJazNIbTNxaWhMUGlvRGtuNnF0cVc1K1FHdkorYkh5T0c5SWE4dWlLMEVhWE9keitMS3VPRDROV3pDTGpnN1Q3VXlpYkFveXcrdUthY01nMlFrN0tjbkNKQnFzZE14SU05cG5TSjkrd1g0N3Zxcjg0UHFRMUNTVEVobUU0OEpjbitNY3BRZTNYTGJ2bUxNVSt0eGc1eDd2NGNHWW9BSXBtejMyc2xMRDhYWWpKMjR5TzIxK2RJNVlTZFdKRjNLMFIiLCJtYWMiOiJhMGRjMThlM2JmZDEyMDZiZWZhMTc3MmM1NDkzNjZhYjEwNTAxY2RkMGFhZTUzMDE1MzkxM2U2MjFmZDViMjVhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNUUlh5TWlncmJXaWJPNjkzQzB5VkE9PSIsInZhbHVlIjoicTUrKzkxWFdwT0NnMzUyOUdydHVDMDExRXd6eVJUZ1N6dkt4VGp0amR2U2NlOFZpbmg2Rm5BS202MTBpSXVHcEJLNUk2N0E4a1pSV3BOSHdsKzI5SU1oMDBobFhPbEFIS1piMDBjQVhoYXYxNEp2bytCVG1iTTUzM3MzMGJUN1VDN1hVNWZ2N0R3elF4RjcyNlNXamhGQ3ZLallBMU12a0RvaGt4MWlwR1h1Rm0vQ0VpV3pzSTlKYzlpQU5UUkdYYS9HOERnMGt3dDdhYUxSem1IWHVSSGRqV082UFJueTJvWXByWDI2dkJUMVB5UnJVU3lOT1N0cGduTjBLUGtjZkh3V0tqcThZaGMwNXJPT3V2Yk1RNW1tTjBVVHUxRDNvcmtoS0RDaXhjZ3IrbE9GNmhhSGR4dFJUUkNQTFUydyt6Uld3ZmxDOW1PU2FtMFBOREhCNzRPcFZ3VFZrZzFaTVdHWG9EWStQQ3NYZDN5dUg1Yk9DVWpPVnVkZGo0dk82SGx0NVFvWE5TeUR0OXlHeHdPc3d6NkhWbHh3b2hqeVVsekNmWXJEaURlMWdqVFVsU2o5RGhZTjB4K0N3RFY4QTZpdWtUQktNbU9HekVZWEx3UVJ3WTgvWkQvcm9WUWFrQVA3b3BuK29sV3ZZbWFEdTNFQkJQcXcwbXVSNWlnTzIiLCJtYWMiOiI1NzdmNzlhZTNkOGQ3NWNlYWQ3MDk5NDVmMTFlZjBkZjM4ODU4NDA5ZDFhMmFmOGVhYjk5YTA1NGFiMGYwMDJhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763288441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1866342664 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866342664\", {\"maxDepth\":0})</script>\n"}}