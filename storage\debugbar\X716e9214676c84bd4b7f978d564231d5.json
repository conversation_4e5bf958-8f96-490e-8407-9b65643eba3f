{"__meta": {"id": "X716e9214676c84bd4b7f978d564231d5", "datetime": "2025-06-30 18:42:16", "utime": **********.268617, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751308935.851271, "end": **********.26863, "duration": 0.4173591136932373, "duration_str": "417ms", "measures": [{"label": "Booting", "start": 1751308935.851271, "relative_start": 0, "end": **********.226417, "relative_end": **********.226417, "duration": 0.37514615058898926, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.226426, "relative_start": 0.375154972076416, "end": **********.268631, "relative_end": 9.5367431640625e-07, "duration": 0.042205095291137695, "duration_str": "42.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43887320, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0025, "accumulated_duration_str": "2.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.255909, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.262155, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 78, "width_percent": 22}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1287 => array:9 [\n    \"name\" => \"KDD حليب شكولاتة 180مل\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1287\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2010660749 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktrdUlKU2tXNnRvN0xSdmpRbk1JT1E9PSIsInZhbHVlIjoiY0o4ZkJVK1VRc0IwQ3RobmFYM1E0ZGlHRUwvK003VnV5TmxLVDBJQ05FNXptbC9weTN3RGY4RXpmVEoxZWZFMUtTdTlKUUVCbzV2cnZweVR0T2ZCR1VvQ1d2Y0FFZWJXd0xTNVJudUhTK2lyVVY1ajhQLzFwRnBhRXVBRXRscW5OOU5PVkowajhqL1FrcXc2cWxGN0ozNm5lWVIxQXFvWVREdHZaV21XaHJ5KzRLNDl0ZnNleWJxNzZySm95QVllOXJpTm10NUYxUWpNUHVMbldRVVFmTjFnSForRU4zcjZzVmZraFpXTFVBY1NSMUNTTGJFd2xHYWRwNHpQSGQ0MEhVOHRxVnZiYmdrVUVHQ1NiR1cyenJZRHU0OVlkK1hHMFlGeVl1L3RBNVRvckNIVFp6cUVhY2xkRG1mWFFRK2xKVkpKVXI4d0V3bjVRRkVqelE1dzF4cEVEMm1JWE9KMUk3czcraFZJR090VzlXL0pVdW92NWY5bnM3UG5GT2pHNTduQjQzMzY0QWgxVHNmbk84UlNOWGJSMU1oMnFHbWFjdDJaNGJSNXdJR2FZcUIxUFVNS0JZSFVJck44MEJadHlLMkN1TEQ0WmRrYmFtVGV2ZzhZTWtyWjFYbytkSXNMOElscm9DSkdPK2IzQ0NZcHFqc3dHRTVLeVcwcmhlWVQiLCJtYWMiOiJmYTNlODQwM2M1MWVmM2ZkODYwMDEyZDhlNzg4MDQwOGNiNWI3ZTc0MTQzZmUwMjljZThkNDgyOGUzNGE2ZmU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdnWXh4NHl2QkF4b2gySmExcFl6OVE9PSIsInZhbHVlIjoieUVYRk15d1AxdHYvN0pCbTJRbkdGQ2wrbUpMOE1kZDJpOXViNzE2Y3E1VEQ1aTJuTng4dUQ0Z3FRWTluVUxSNmU2NkR0RDVwdm9JVG81dU9KNGZSZkpaMnBpaXZiY0pRTE9Ldy9QdDkzYjEzaHoyVzEwWm85cWJzTEQ1cWwxa0cvNEgzbVlQbWFPejIzanVXSkFsMjNsOEVXdll1N2Zpa1kzZWdTaTNHOVZ3VEZCd2gwWGxnVVBGZ2d1ZEErc2E2elNEdExrV0ducGdLZXVlRzFFTFRYdDNxQ01qWjlUVUJJM0RqNlV3bFN3OE5SUXl6MDMwTUl1Y2dlajVuN2U5QlpnNVA2NTZsZDV4a1BVcm1UV0dZcXV1TVBEcGxWM0U3dWcvUTZObm5WVFFJS3hTYkpFUCtxY2F1UklFanpqOTM3OVh0Z1BJSlBUOTRmVlp6STRYeXB2ZkthNy9CdGFhOUFIVGgyY0FocXVKeTlPR3ZuMnc3TFNSZXRVYjNnVm9sVkExTm1nSzBjZU8xcWkwVTRMWVc2cnZ0NWtRc3lPSmp4RE9UR3MxSWNIcDFad2p5ZkJpcTdBNzU4WlZjY3Z1c2U0WUFCQ05xSFRPeWxTNWtwRDJUNHhxNjJ1R1R2S0ZvT1JkRllhNVNBYXUvTmUrY05wK21uTWVrZDRxUnp3cHMiLCJtYWMiOiI5NzZiMDViZGJkZjc0ODE5YmM1NTVkOTFhNjcxNDUxNGVjZDU0Yzc2MGU4OTliOWUyY2UyM2E1MTA0ZTIzN2Y5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010660749\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-507237365 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:42:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZWTWFUNy9IS1lFTExqVnVtZmg4VlE9PSIsInZhbHVlIjoiWm1zc2tGd0JMbWU0aFYxUVJTRWR2L1ZUamkrQVEvam5RbW84b0RSQUJhZmpCUU9tVHpXY1dicUxYd0YxbFBjNkVpZ2liZVNnaDcxdzBvbUhzZkQvamxnNmFNVmlhOE05UG1EVlVGZ2N0Y010dDhkRmpEcFg3TE5TdDMzK1lRNzVUK2FRa0pnTkM2T2llUEE4NFN0N2hDRHhKNnRWTG1jQ3hLNXRIbEo4UUJNWERsZFhHam4yT1BzT2xCQkprZ21pQVZScFVWRUVrNTZSMmZidy9vV3c0STBkSFQ3RnNiRVFRMDJ6ZWJYMTJUbHZRWWZzcXQ0eXM3TnhoS0ZvZDZUc3Bobm51UVRjVXI4ak9TcitVLyt0aUlaNE4vZ1FqQkRuS2llUEkzY2hwUjZTYy9sNHNrK05aZEtFam04N0tsVm5xa2tMQ093cVY0dFI4VHp2SGNLY3QxZFRmZFRUd0V5cW9oQ0YzSmtCSUNMT05PRmhJYnoyUlZjdHQrQnNOdU5OMTJ0ZWlraG5xekVKZlc4Y2NxY1VKU2dXSFdTODkzcXN4UjlFQWIzSnpLOVZvZGFYNUNkelRMbVNyb2l4eWJNWjZQckR3UWdvR3hvSUJNNFZDbWpjWVNyMFZyTGo1YWk2d2VYNmFubUM4YzdYbmpjL1oyc3pDMzBXcUZmNWhhMS8iLCJtYWMiOiJlNTYyYWNmNWNlNzg1MTk1MTQwZGJmOGU3ODNmMDEyYmUzNTY4NTE5NjRiNWEwOWUzMDBmOGYwNGQ3ODM3ZTY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InorejNmbFR6a0xHL3o2b2p2bjNwZ1E9PSIsInZhbHVlIjoiQVZEck9YSWtqMlJSS0FpN2NmakVjalJMUndlVWNTVUhqWTlXSllkNnljZmlDZmp1WlQyM2NNa2kxTVBZUWRueHV3K2F1QWg5aDIvNFd2WklGemI1YkxXcUk4TGpRT0lQWVpxVXZ5Rm9DbU9VTVp0R1duL3FxeCtyc2ZtQzFnQjd1UGZ3TnBwN3BjYjNuK3FPYjRpWElsMC9tSFRER05ORldwY3BiYitQaTZjd1dvOGZsbnhyVHpzNVBFZTZGN3JOMUtCdUpSeHNzY0JmOVNnZFJSajBvUmlIdU9YT2NQa3pxaS8zaWt1MzNMYTRjQWYrNHNzak1qK2tQTElOMTcvZ1F5dTVSQjRVMDVmRG5aaHhSOG4zWjh5RXJGbU5WMlA2SjJ6dmlBclpyTWQybGxmd01vblJOWXVBVHRJbUI0cHlIbmJDd2FRWTQ1TXdUanZYMVBsa2dNbnM5aExIRHpVVVpmUkxVSjMxaWZncjZqaVB1RDNvYW9mSVVWZG8yRHhOWnlQWHgzRkxPV1U5YTY4Mml5V2pBWkdnSmlHbVZLTnVJU1J2SFViN2dXRFBmU242SnRkRkdqVGpqNEF0Y2RTdDczZ2s3M1lvaG5uVjU2ZmNMbTJRaURBNk1pcytzVndGTnFLbjJTQlZxRHpqM2NKcmJYOUtJSEZnRkVhMW03Q3UiLCJtYWMiOiI4MzM1YmQyZDc3Mzk3NjNlNzI1ZGVjNDkxMjdhZGJhODk5Nzc1YjU1OTJiOGVkZjY0ODdmMDM3ZDk1ZTFiYTgzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZWTWFUNy9IS1lFTExqVnVtZmg4VlE9PSIsInZhbHVlIjoiWm1zc2tGd0JMbWU0aFYxUVJTRWR2L1ZUamkrQVEvam5RbW84b0RSQUJhZmpCUU9tVHpXY1dicUxYd0YxbFBjNkVpZ2liZVNnaDcxdzBvbUhzZkQvamxnNmFNVmlhOE05UG1EVlVGZ2N0Y010dDhkRmpEcFg3TE5TdDMzK1lRNzVUK2FRa0pnTkM2T2llUEE4NFN0N2hDRHhKNnRWTG1jQ3hLNXRIbEo4UUJNWERsZFhHam4yT1BzT2xCQkprZ21pQVZScFVWRUVrNTZSMmZidy9vV3c0STBkSFQ3RnNiRVFRMDJ6ZWJYMTJUbHZRWWZzcXQ0eXM3TnhoS0ZvZDZUc3Bobm51UVRjVXI4ak9TcitVLyt0aUlaNE4vZ1FqQkRuS2llUEkzY2hwUjZTYy9sNHNrK05aZEtFam04N0tsVm5xa2tMQ093cVY0dFI4VHp2SGNLY3QxZFRmZFRUd0V5cW9oQ0YzSmtCSUNMT05PRmhJYnoyUlZjdHQrQnNOdU5OMTJ0ZWlraG5xekVKZlc4Y2NxY1VKU2dXSFdTODkzcXN4UjlFQWIzSnpLOVZvZGFYNUNkelRMbVNyb2l4eWJNWjZQckR3UWdvR3hvSUJNNFZDbWpjWVNyMFZyTGo1YWk2d2VYNmFubUM4YzdYbmpjL1oyc3pDMzBXcUZmNWhhMS8iLCJtYWMiOiJlNTYyYWNmNWNlNzg1MTk1MTQwZGJmOGU3ODNmMDEyYmUzNTY4NTE5NjRiNWEwOWUzMDBmOGYwNGQ3ODM3ZTY2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InorejNmbFR6a0xHL3o2b2p2bjNwZ1E9PSIsInZhbHVlIjoiQVZEck9YSWtqMlJSS0FpN2NmakVjalJMUndlVWNTVUhqWTlXSllkNnljZmlDZmp1WlQyM2NNa2kxTVBZUWRueHV3K2F1QWg5aDIvNFd2WklGemI1YkxXcUk4TGpRT0lQWVpxVXZ5Rm9DbU9VTVp0R1duL3FxeCtyc2ZtQzFnQjd1UGZ3TnBwN3BjYjNuK3FPYjRpWElsMC9tSFRER05ORldwY3BiYitQaTZjd1dvOGZsbnhyVHpzNVBFZTZGN3JOMUtCdUpSeHNzY0JmOVNnZFJSajBvUmlIdU9YT2NQa3pxaS8zaWt1MzNMYTRjQWYrNHNzak1qK2tQTElOMTcvZ1F5dTVSQjRVMDVmRG5aaHhSOG4zWjh5RXJGbU5WMlA2SjJ6dmlBclpyTWQybGxmd01vblJOWXVBVHRJbUI0cHlIbmJDd2FRWTQ1TXdUanZYMVBsa2dNbnM5aExIRHpVVVpmUkxVSjMxaWZncjZqaVB1RDNvYW9mSVVWZG8yRHhOWnlQWHgzRkxPV1U5YTY4Mml5V2pBWkdnSmlHbVZLTnVJU1J2SFViN2dXRFBmU242SnRkRkdqVGpqNEF0Y2RTdDczZ2s3M1lvaG5uVjU2ZmNMbTJRaURBNk1pcytzVndGTnFLbjJTQlZxRHpqM2NKcmJYOUtJSEZnRkVhMW03Q3UiLCJtYWMiOiI4MzM1YmQyZDc3Mzk3NjNlNzI1ZGVjNDkxMjdhZGJhODk5Nzc1YjU1OTJiOGVkZjY0ODdmMDM3ZDk1ZTFiYTgzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507237365\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1660025184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1287</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">KDD &#1581;&#1604;&#1610;&#1576; &#1588;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; 180&#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660025184\", {\"maxDepth\":0})</script>\n"}}