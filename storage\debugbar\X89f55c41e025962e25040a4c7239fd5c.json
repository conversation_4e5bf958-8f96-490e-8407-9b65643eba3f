{"__meta": {"id": "X89f55c41e025962e25040a4c7239fd5c", "datetime": "2025-06-30 16:05:53", "utime": **********.59318, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.14754, "end": **********.593196, "duration": 0.44565582275390625, "duration_str": "446ms", "measures": [{"label": "Booting", "start": **********.14754, "relative_start": 0, "end": **********.53622, "relative_end": **********.53622, "duration": 0.38867998123168945, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.53623, "relative_start": 0.3886899948120117, "end": **********.593198, "relative_end": 2.1457672119140625e-06, "duration": 0.056967973709106445, "duration_str": "56.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43576992, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01716, "accumulated_duration_str": "17.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.569019, "duration": 0.01716, "duration_str": "17.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-404510371 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-404510371\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-109534680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-109534680\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-31563555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31563555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-88013205 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5YWllyb0Z4blovR2JNa2RNUWQxaUE9PSIsInZhbHVlIjoienBwQmdCZjZ3L0JTV2tQMllTVVRKT3Btcy9vRkxnVUpVZGdNQ09WV0lOL0pyaU9kUUZFakFqMUk5a0U3T2FDQkk5bHhJeW5oWjh0MFJBaStJOHR3Q3YybDBuSTlnYzRuLzk2OGdKTnppZWJVdGJjeW5tUVpVbjJJYVhoOFEyZGpDWkd5M1RPclc4Z0lkZFRKODhtS1NRRFFaZXlSWHVQL0l1eXF2R1VGTDlYcUVxV1BjR0daS0dDMkhOV1FldDNpU0pJeGMzRlU5U0FCWUlockVmZEYyZTlCZHVHaXg1ZUF5dUpOODVyK3lDSDBpck92dnE1RllTRWJhWlBnOGFkSHlUOWYxNjNIdk02YlRjL2s5YUhJZEdWK29JOEs5a1NNZjl4c0YwdHgwc1hTMGNUUUtKYWlEWDRjNUd6SmtqV1B2Qmg1bi95MTNGTDRNRjRjckl5Uzh0NEdBZURoUFZXd0ttWU53dGFjV2h0Tzl2WTliSC9IMThLdnB0dXZkN0RxM2tabmNiQ2N5OWtoS0J4QXloSDl2aVdlcDNqTVpBSGQ3TXB5VSt5VEI3VlAreEFrQms3QVRWQ0NXbm0xVG1mMFppWElFd3dYZ0p4ZE5DRy91V0NzMnNxdThCYTNBVmJEckQweStMRmVvMjlSbkFqSUxJcTloSTRzc1J5UkJWRGQiLCJtYWMiOiIwOWU4MmE2ZDg3NmIxZWI5M2QyNjMwNzIwYTY2ODI5YzE5NTI0MDkwYjRkZDYzYWZhOTIxOWRmY2ZjODE4ODkzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBNRFVxM1NYTURSbEl0Zlo4UzBUaGc9PSIsInZhbHVlIjoiWGh4c1JTVnZ2bVdIVG9iRVVqOU51enNRWEgrMDlWeUhUeS9FU3k4YUwxUTZ6aWRUVk9GaVc4ZGVCRS9PTk45UXFYaEVzaHN5RTFKZ3F0eGU5ZE1FR0xNcm5WZFJYWndSbmU3Z1labCtiSDFHUGJycXRKL0FJK2JCUEp6cnFkQnRIU1Fkb0dObEVMVW5Id3JqSnAweEY5QTRNckNjYzU2bDFEMzFyaFdpYUg4NFgrODR4akd2eEoyWnJ4VW1lL09KQ1ZkYmRaandMbDJDdEtoU0hEVjQyMjExTU83R1ZUS2FEUUlUQlpFK00wRm1XVHNOdjBNOU9XUHo1V2tnS1pZQm5aNXRvMWhyQ2J4SE5Edm1USWtnMVY0YmJoOVE1a0svNXRaQVhMUzQxQlhZSGNBb0drbzNyRFd5clZMbmRuVHN2ZkhwYXZqdm1MMGFnZE9HMC93QnNnWWFIYTk1UnVJOEFZeHFENUFTNUZhVzViZzRkTlV2WUF5M0ZDc3dTaDZCTktSRGdySG1nQURJV1Y5S20ycFIwd0VSdDUwbTN6dERmaCtIaHdUSTV5OVBZNERTdmhLbjdBaytJM0ZBN2VucEVRNzI1dFlkL2NoYzJMZHMvZ1QrbUlaMGJZNUVNdXl4MWVtbHZ4TW95YXZVNVYwNmd0bXBlK3gyRmZQUTI5NzciLCJtYWMiOiIxNDFlZWEyZWRhNGE4ZGM0MTNjNmNjMjc0ZTU1YWJlZGI4ODFmNDZiMmI5NDgxOGFmNDk4YTQ1ZmI1YmM2OGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88013205\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1670122342 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670122342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-733194498 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNJVW1xVmR3YTdaQ1JObzMxRVUrd1E9PSIsInZhbHVlIjoiY2FobDA2enlpUCt0K0lmTWViNUM2Q1RnREtZRjJsZCt5OHZ1VjBKTEcrWW45V1ZhM0RxOHZNR2JXb3R3NVZKY21hL093T2FqWGx2MlhqdDF3ZXE4d1Q3SHMxWkVhNHgvVFdxWnZDL1gxdnI4NzF5SE56STNLK1lRMnVjb3lGb3FPZzdFeXZSL21UdGhzQ2lNTlNmcWlmUElIaGhFd0xDV0ZxT0VaVDFrM1lWWnhORkIyL1ZyUjFTeDFmSVE3Y2RmMTM4L0tVKzdvRnRXV1NZS20wMHpEak9xdjRFSmJnM1V3VUlkYU9oSzA2V0RHOVJVU0J4UWs0UzRyY0NpRHVmckhIaUVBUHlxY0o1ZWl4bUU1VFVQRjhwYzU3UDFvM1pQcll3OFNZTGUyU21XQ3B5NFdhOFIwZjl3eVg2QTJnYlpQUkEwYmtVUDF3WU4za0QrNWVHOFpJT1FldGRMN01sNUxNNUNGRmdOZXk4M1lVby9sdXB6KzlRVVEvM0UrR1NqQTBNRDRSYkJwa0Myay9QZitwd1Y3NVhtaDdYU3JmYjdvV2VFK3JXNG1UVWpFSE03Y1dHTTY2eWxCVXdTYUtZcVc4TDdzNk5wd2g3SG5SMVNHeUJqWFg0bnZ4Z09YR1cyUEdZTzdnVG9XaSt4UkhVVldjNVZnajk4SHg2SUd5TDgiLCJtYWMiOiIwMGVmNTdjYmEwNDgwOWRjYjBlZTEwYTA1NmJmNWZiNTg2MzZkMjQ0OWNiODlkODE4YTlhOWNkOTk0ZTJiZTVjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFIVVB1VkxMVElmN2pyMW91MmRxb1E9PSIsInZhbHVlIjoidlRsOFdBVTV6ZEl2TlJrWUMzbGlvWG54RGNoWFBsL1dUaWN2YmVuSU5qbDFSVzZNKzNHNlNERysvNjBMWVgyWmVIS0xJQlN5WXpvU3hiVXZ6R2lSTXJ0NW9aMEd3cEVlZWExQWxsa1VyT3NOY3ZNY1Z0SUJvVGtlL0dhc2c2M1k3MC9GbnJjWXVEeGU2YXY2aTgzYTJTMGYxdTZVT2dBOVJyU3lrb29WMGFtYjNZWG9yclpMa2lCQTVtSW1UTUdiYTFKczBjTTJsL21pNGIxc3NRRzVRTzF3aWFnRGxLMWtTZXplRytac2RLSnZjUlhSbTdNaTFzQTUwK291RVZLUXRReHFTc01oKzdMOVp1WDZsazVQSUtMNlE0bm43YkYwUnRDcnBsN3hhanQvbERCd2JOV2V1Y2FPei9mS1dOOHZwYWQ0V3NuRXJEbnJHeHppZmt3d2VqckJCTHdoSzNwWmVzVHNscTFvT0MybjhVQWdJRGNxVXl0eFdkVEJMNEJrYjJWSE12T1A0WFJKRXl2ZyswNkhzRno1MmtmOXZReFJFU2VnOVEzQ0FaYm1ENFpaeURCcUJKbFlHM2lkSHNFdk9tS0tkNDlxZ3h2OHgwMnBqNWw1RmdVOHh2Yld4dUV6eFdUTUdvVlY3bVF0aW5kSXoreXNLY0IzdmxBUUppM1kiLCJtYWMiOiIxMjcxYzk0MjI0NjM0NWE2YmZjYjQyMzZhZWE1ZGZjZTQwM2RlNGQzNTA3OTBlMzI1YzM0M2NlYzZjNjFkM2UwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNJVW1xVmR3YTdaQ1JObzMxRVUrd1E9PSIsInZhbHVlIjoiY2FobDA2enlpUCt0K0lmTWViNUM2Q1RnREtZRjJsZCt5OHZ1VjBKTEcrWW45V1ZhM0RxOHZNR2JXb3R3NVZKY21hL093T2FqWGx2MlhqdDF3ZXE4d1Q3SHMxWkVhNHgvVFdxWnZDL1gxdnI4NzF5SE56STNLK1lRMnVjb3lGb3FPZzdFeXZSL21UdGhzQ2lNTlNmcWlmUElIaGhFd0xDV0ZxT0VaVDFrM1lWWnhORkIyL1ZyUjFTeDFmSVE3Y2RmMTM4L0tVKzdvRnRXV1NZS20wMHpEak9xdjRFSmJnM1V3VUlkYU9oSzA2V0RHOVJVU0J4UWs0UzRyY0NpRHVmckhIaUVBUHlxY0o1ZWl4bUU1VFVQRjhwYzU3UDFvM1pQcll3OFNZTGUyU21XQ3B5NFdhOFIwZjl3eVg2QTJnYlpQUkEwYmtVUDF3WU4za0QrNWVHOFpJT1FldGRMN01sNUxNNUNGRmdOZXk4M1lVby9sdXB6KzlRVVEvM0UrR1NqQTBNRDRSYkJwa0Myay9QZitwd1Y3NVhtaDdYU3JmYjdvV2VFK3JXNG1UVWpFSE03Y1dHTTY2eWxCVXdTYUtZcVc4TDdzNk5wd2g3SG5SMVNHeUJqWFg0bnZ4Z09YR1cyUEdZTzdnVG9XaSt4UkhVVldjNVZnajk4SHg2SUd5TDgiLCJtYWMiOiIwMGVmNTdjYmEwNDgwOWRjYjBlZTEwYTA1NmJmNWZiNTg2MzZkMjQ0OWNiODlkODE4YTlhOWNkOTk0ZTJiZTVjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFIVVB1VkxMVElmN2pyMW91MmRxb1E9PSIsInZhbHVlIjoidlRsOFdBVTV6ZEl2TlJrWUMzbGlvWG54RGNoWFBsL1dUaWN2YmVuSU5qbDFSVzZNKzNHNlNERysvNjBMWVgyWmVIS0xJQlN5WXpvU3hiVXZ6R2lSTXJ0NW9aMEd3cEVlZWExQWxsa1VyT3NOY3ZNY1Z0SUJvVGtlL0dhc2c2M1k3MC9GbnJjWXVEeGU2YXY2aTgzYTJTMGYxdTZVT2dBOVJyU3lrb29WMGFtYjNZWG9yclpMa2lCQTVtSW1UTUdiYTFKczBjTTJsL21pNGIxc3NRRzVRTzF3aWFnRGxLMWtTZXplRytac2RLSnZjUlhSbTdNaTFzQTUwK291RVZLUXRReHFTc01oKzdMOVp1WDZsazVQSUtMNlE0bm43YkYwUnRDcnBsN3hhanQvbERCd2JOV2V1Y2FPei9mS1dOOHZwYWQ0V3NuRXJEbnJHeHppZmt3d2VqckJCTHdoSzNwWmVzVHNscTFvT0MybjhVQWdJRGNxVXl0eFdkVEJMNEJrYjJWSE12T1A0WFJKRXl2ZyswNkhzRno1MmtmOXZReFJFU2VnOVEzQ0FaYm1ENFpaeURCcUJKbFlHM2lkSHNFdk9tS0tkNDlxZ3h2OHgwMnBqNWw1RmdVOHh2Yld4dUV6eFdUTUdvVlY3bVF0aW5kSXoreXNLY0IzdmxBUUppM1kiLCJtYWMiOiIxMjcxYzk0MjI0NjM0NWE2YmZjYjQyMzZhZWE1ZGZjZTQwM2RlNGQzNTA3OTBlMzI1YzM0M2NlYzZjNjFkM2UwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733194498\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1824807594 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824807594\", {\"maxDepth\":0})</script>\n"}}