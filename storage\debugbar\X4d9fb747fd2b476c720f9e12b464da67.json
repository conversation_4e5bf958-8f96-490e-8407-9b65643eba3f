{"__meta": {"id": "X4d9fb747fd2b476c720f9e12b464da67", "datetime": "2025-06-30 16:07:07", "utime": **********.450102, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299626.998265, "end": **********.450117, "duration": 0.45185208320617676, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1751299626.998265, "relative_start": 0, "end": **********.396103, "relative_end": **********.396103, "duration": 0.39783787727355957, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.396115, "relative_start": 0.39785003662109375, "end": **********.450119, "relative_end": 1.9073486328125e-06, "duration": 0.05400395393371582, "duration_str": "54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00345, "accumulated_duration_str": "3.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.425483, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.652}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.436635, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.652, "width_percent": 11.884}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4423008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.536, "width_percent": 12.464}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2090762050 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2090762050\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-145870815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-145870815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-542157016 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542157016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-309572483 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299624170%7C11%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVTWXQxUTI3NXN1aFVKOE5iQVVOOFE9PSIsInZhbHVlIjoiOS90WWRRcWxxSjhnRksybVBTU1FOejFGZG1sN3l3c2RLRWNCTmF3aTNaUG9CZHFuMnVLcTRVc3NQN3FQK3l3RmRNNVRQYktyVlJYK3VIeHZ3NjdnOCtWa0JTVEF3NVZCM2xvQS9LWXltQ05Ia3FQQUhZbHlHeHhoN2NPNzF4ZU5RQnpnNHVDVDlDZVA4WS9kSUg2OXJlY0I4ekFyN090UWxHZ2daTUZSTHBaOFozc2J0bW45TUQ1Y1pqVTQxSzlXR08ybXNHMk8wRTFMa2MrTEFMSG1qRzdDS3ZjdEJGUFJzS0pWWWJWN1pCTHNaVWk2SWc3VkVPRzdzVGNGV1hhMGwvR3FKV1RqOWcwVHBiVzk3N3lzRDNubXB5VXFOSmVjTXlEaTF5c0Q3alVFUEJjV0M5L1JSSmtOcmxDa1BveG1zbTNQZkVoS1pHSm1OOXZzeWFmUHVyaEloSVhwemVQVDg3M0hTZjBTMzBLOEFtQmJhUEJyZis0VTl2RVhXcFJkbW4wUHBzbDk2aTcxdzVWUXRSUUp5ckVnMm1XOXROOVdYUGdWelprMk1MSGRieGJZMG5VSCtxcnc1SDFTK3dERHhaT2tDeUVZNzJxcnJvdmdIcGREbWtoSDVuSUNtTmhZRElaTGYvdFI1R2Y3UUllMjhEOXdOSFBqMTBBM2owWmwiLCJtYWMiOiI1YjRkZjcyYjY2YTA5ODQ0YzgxNWNkNDE1YTkzYzVkNDMwOGY0NjE2NjE1ZTYxOTdiYjkxNWNlMjVlZGY2ZmZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJWZWowaUVVRVNSV0NvYzJ6THhlNFE9PSIsInZhbHVlIjoia1JhYVBUZW1qL1VtZE1vQW5uQndldlY2VU1vLzRCby9IZm9JbjAreGd2STdWRk9MZTBlc1h2VlJhOFNkUUVoQWh1RXMvYXBZMlB4Z3hIS1JrRHRQYkI2OFppdHAwN25vczhZRlR3SFp6OUxwZ3ZJbCswVHQ1QWVGMDRmeFpVcFlsNFIrSm1tUEp3SGd4UmE4TUxXcHRRc0xlMWNUeHhxQ1VNV0NlT2ZGZnphZGR6SkdhcjhxM3AzWkFBd0ttOUxSRE9rODgzSThtbDZFa2UxZ3VrZnZ3T2xXYVZtRGU0YlFjYzJVODU0R1FRU2EzQjRpdkhXR3BIcUI0VWhhUWVJc0llMXplWm9LVDJEdFdWUG9nRTdBRVVNZVllbEpIRzNEaVZBMXEybUViTjZ3ODFqV2x4OHFFeVMwZlJiSkM2cVFDcXo0WW5GSVgwWU9iTkZNQVIwcit6eFBucHdxYk9LaWowSXVrbDRBWk5HRk0vVVUyaU1TY2dkK2lDNDFhMnlOR0VFSTQwanpXK2RnUFZwK21PUUp1WkU0TzZ5VFhSMVFuRjU0NUNJcnFreWh1cFgzRi9PV3NGRmlMVjh0NmQzSzlnYkd3WW9LSGRkL1dqd1Mrc3BCQlR5RjFmYWtYQ0tNUkJyY2VwMGNXWDdrWmZXazF5TVVmenJOL1NZRGllUmciLCJtYWMiOiIzZGM3OGIxYTQ1ZjNmZGM4NjI2NTY2NDA5Njg5YTM5ZThhYmY1NTAwYmQxYzdhM2NlYTljYmNiZjNlMzkwMjk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309572483\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1187348664 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187348664\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-848447552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZwaWE5aThVdUV5RTJJb1pzMWs5Wnc9PSIsInZhbHVlIjoiT1lhaE90ZjYyd0ZIWkhlZlRLajc5OVNOOVhHRzcvR1UwcUJLMlZscTIwbGxhL3h3MkVoc1hoa2NDUGUyYmpjcU1JQWR1UzhHdnFIQ01ocVpQb2g4UWlrSDIrWHJ6N2tvaGJxTWxTOVdIMzgza1pTRHdDZFQxWTZSSkdSQ2NSUktOMjFRcDEvWjNFTGZ1bW9jcXZocHF1dUIvck1odFlib0lpTVVPWTBGT2h3MGc2N1VyQ3hKNjNPSVRpZ1JMNWJFaXVCY1pMTTU5dmtFTkhXMU8vQlhZT2hVNXorWWZ6N2ppM2UrNDRKQzRXZHFqcU5RVmdCSW90NUpEcHBpVFJ2QXRTdnorKzRwVEFaSjBtSHdnN0J6MCtIVytWT2VzV2krTks0TVZ4eVd2VU11MkJqZU91ckRmMFRBWnR1N3dZV2pMNlBsTlZGZ2NDNFdRc1RpT1h3eXMxZkdzVjB0cVRCNlNLdjd5Q0F4bnhDMXRjY1ZnSjBwbVhPZEZ0VU5XaTJRUERETWYyQUszNmRwWnpDbVVnVGlBQVRyUE5oQWRuSkp0WG0vblI1OHlwM3BmTUptbkhISGZhanNma2NtVmVXbHpnNlZXZzduTC8zeUxQT3FjZy9ydzlOdW4yZjR6U2t6QmZ2TFNESm1lalBWSy90TjFoNkVJNVhyTmlET1dLalMiLCJtYWMiOiJmMmM2MzY4ZDFjNDUxY2IwYjIyNWJiM2JjMzM0MWZhOWFjNjY5OWNkZGY5MTdkN2E0YTE2NmRmOTQ5YzgyZTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IllHSTNmRGFWZHBnV3pDdUxaMUJOOUE9PSIsInZhbHVlIjoiVkxWMGZ0WkVWbU0yay96S0VLNmZUeWtIMFRKUHIyeTY5YzdveEJ5YWRMN21TNURYUTRUNDUzL1pqZnRrNytkMm9PY2lkbEtldEV2dHlZaWVXbmRQOXpIcFpRNXNwZDZFWmlkd0Q1UG9tMjJhRHg5NWk5Z3A5ZFVPb1NKZDRPdkpQMGdCK0N3ME1tQUVkSXdKSnNpTDN3NXF4c00rRDVqRTB4NlR0V2RtRTE1c2N1NzA0WmNRanB0YXdqd0VDN3NXNVhSOTAwMW5nRUs3R3pLMzVNRjVHcDYvTWduRzBrc1ZQNXRZbFZiVTNKVTRIdjkwWHhGOExOVUEzZXRJeWpldDVmRzNheWpld0syL25NUUFncTRjeXZFaDIzVzd1SUtPMTUwbXNRdGFyTDVqamE3bmRVV0FscEV0OWdXUE8vL1lwUkZWV2VHRHpqSW44RFl5SjBBaUhyUTJGUnpWdTYrTkNrRWJFZHlFY0JVbVV0U1B6MDFJblJnL1lEL0FtMnhCR0tkZWI1eit4NDQ1dCs2Wm8vUUNYTGpLeUU2Rk9WY3ZyQUJKU3QvSFY5cVgrckJQcjM5MjR4VFBLWkMvUnZkeGh4ODNDMWsxTmZteDBMSHNUZXVRRzQ5c0hyeS84SERndGkzNVNjVFRHR1Q1TWxGOVpQY2gzVnUzZG52UWkxUkUiLCJtYWMiOiI4MjBiYzA1YmNhNjk3ZDY3MjAyOWZkNzRlNDAwNGVjMjM4YTgyNTFiMzhlZGE1YjIyODgyY2VkMDgxZTkzMmNlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZwaWE5aThVdUV5RTJJb1pzMWs5Wnc9PSIsInZhbHVlIjoiT1lhaE90ZjYyd0ZIWkhlZlRLajc5OVNOOVhHRzcvR1UwcUJLMlZscTIwbGxhL3h3MkVoc1hoa2NDUGUyYmpjcU1JQWR1UzhHdnFIQ01ocVpQb2g4UWlrSDIrWHJ6N2tvaGJxTWxTOVdIMzgza1pTRHdDZFQxWTZSSkdSQ2NSUktOMjFRcDEvWjNFTGZ1bW9jcXZocHF1dUIvck1odFlib0lpTVVPWTBGT2h3MGc2N1VyQ3hKNjNPSVRpZ1JMNWJFaXVCY1pMTTU5dmtFTkhXMU8vQlhZT2hVNXorWWZ6N2ppM2UrNDRKQzRXZHFqcU5RVmdCSW90NUpEcHBpVFJ2QXRTdnorKzRwVEFaSjBtSHdnN0J6MCtIVytWT2VzV2krTks0TVZ4eVd2VU11MkJqZU91ckRmMFRBWnR1N3dZV2pMNlBsTlZGZ2NDNFdRc1RpT1h3eXMxZkdzVjB0cVRCNlNLdjd5Q0F4bnhDMXRjY1ZnSjBwbVhPZEZ0VU5XaTJRUERETWYyQUszNmRwWnpDbVVnVGlBQVRyUE5oQWRuSkp0WG0vblI1OHlwM3BmTUptbkhISGZhanNma2NtVmVXbHpnNlZXZzduTC8zeUxQT3FjZy9ydzlOdW4yZjR6U2t6QmZ2TFNESm1lalBWSy90TjFoNkVJNVhyTmlET1dLalMiLCJtYWMiOiJmMmM2MzY4ZDFjNDUxY2IwYjIyNWJiM2JjMzM0MWZhOWFjNjY5OWNkZGY5MTdkN2E0YTE2NmRmOTQ5YzgyZTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IllHSTNmRGFWZHBnV3pDdUxaMUJOOUE9PSIsInZhbHVlIjoiVkxWMGZ0WkVWbU0yay96S0VLNmZUeWtIMFRKUHIyeTY5YzdveEJ5YWRMN21TNURYUTRUNDUzL1pqZnRrNytkMm9PY2lkbEtldEV2dHlZaWVXbmRQOXpIcFpRNXNwZDZFWmlkd0Q1UG9tMjJhRHg5NWk5Z3A5ZFVPb1NKZDRPdkpQMGdCK0N3ME1tQUVkSXdKSnNpTDN3NXF4c00rRDVqRTB4NlR0V2RtRTE1c2N1NzA0WmNRanB0YXdqd0VDN3NXNVhSOTAwMW5nRUs3R3pLMzVNRjVHcDYvTWduRzBrc1ZQNXRZbFZiVTNKVTRIdjkwWHhGOExOVUEzZXRJeWpldDVmRzNheWpld0syL25NUUFncTRjeXZFaDIzVzd1SUtPMTUwbXNRdGFyTDVqamE3bmRVV0FscEV0OWdXUE8vL1lwUkZWV2VHRHpqSW44RFl5SjBBaUhyUTJGUnpWdTYrTkNrRWJFZHlFY0JVbVV0U1B6MDFJblJnL1lEL0FtMnhCR0tkZWI1eit4NDQ1dCs2Wm8vUUNYTGpLeUU2Rk9WY3ZyQUJKU3QvSFY5cVgrckJQcjM5MjR4VFBLWkMvUnZkeGh4ODNDMWsxTmZteDBMSHNUZXVRRzQ5c0hyeS84SERndGkzNVNjVFRHR1Q1TWxGOVpQY2gzVnUzZG52UWkxUkUiLCJtYWMiOiI4MjBiYzA1YmNhNjk3ZDY3MjAyOWZkNzRlNDAwNGVjMjM4YTgyNTFiMzhlZGE1YjIyODgyY2VkMDgxZTkzMmNlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848447552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-22815847 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22815847\", {\"maxDepth\":0})</script>\n"}}