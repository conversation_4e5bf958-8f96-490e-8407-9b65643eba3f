{"__meta": {"id": "X73a020d1670fe72c219a48ef02dc9a64", "datetime": "2025-06-30 18:49:03", "utime": **********.835097, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.358891, "end": **********.835112, "duration": 0.47622108459472656, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.358891, "relative_start": 0, "end": **********.756268, "relative_end": **********.756268, "duration": 0.39737701416015625, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.756279, "relative_start": 0.3973879814147949, "end": **********.835113, "relative_end": 9.5367431640625e-07, "duration": 0.07883405685424805, "duration_str": "78.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45706848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022099999999999998, "accumulated_duration_str": "22.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7907631, "duration": 0.02122, "duration_str": "21.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.018}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8203492, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.018, "width_percent": 2.036}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8261888, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.054, "width_percent": 1.946}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1312689252 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1312689252\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1703908830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1703908830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-743862386 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743862386\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1573942348 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309341619%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikt6RWp4cFNpYjhtNTJtQXNpeG0wWXc9PSIsInZhbHVlIjoiYlZnQnN0V2RjbncvUWljODRlOEJrbDJkQUE0TVkzdjJWVTZzamZVQSsrR0dMSGlTUm9xY3FJMW9RN0VuS2lnZGU5NTJIUHZJTjRYVCtsdzVZR3A4STBSSE5ZSTRJU3BFcVhJOXRSc2syd3RoZjVkQUJ6YlRuaXBpbkVVQ1FoL2NsZ1FQOHJjZnNjMFo5UCsrYTRMMTNNdFN0YnRyNTBza2tJTFlSN0Y2Q1Q4WGQyclZSVGdVaVM2V3NNM0FEWE45UmVwa002QWJ5NHpzTDgwYUZOSHFKbUx5WXB4SjVKR2VzL1lhSlRBVTJ2eU82bXpEalY4U05KdnZRRHdMWGdkdTlpRXVrdkN5ZXBBUzdEWUh3UzlPNDhQQ05GUGR3d2F6VGRubGRjOHBpSXA5SGxGOENGdW1GL2ZpYzh1QWNaSjZpNG5IcHRPUXhBeXBBNDJjQU5jY0x1Tk5pU0MzT2lDVVZ6ZUllQWRsM1RWU3JZR3RVVWZtd2JoWndJdVlCTEVOK3hUcjV2Z1lZWWhUK1BlK2NQWm5VZFNxQWwyWkR2SHV3aXFjdnFIdDBUVHRjV0wrL2d1V2tlbllER1BJTzdpaHl4RHMyS2tybG50QlVNenRwaGVENXNhVDN3ZmE1a0swT0lFZU42N2JnbmlEUjYrWWY0VDQ2TkRVelJWclBZNTkiLCJtYWMiOiJiMGU3OWFlYTY4YWE5ODY2MjY5ZjZjOTkxYzk1MzU1MWIzNjJjOTZjOTBlZDY2OTczNzcxNmU1NjU5OTJjNzk3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRaU3lUS0xPaDhhNVVjSmp1cmw5L3c9PSIsInZhbHVlIjoiWHRHSnFsQVdVWDFTdlRDV1JsWkJnZXZMeEh2M1NhQ1JtSGlBR3hzcjYzRWRtSmgvQXZDanRKMmJ2bDNITmcvaiszeG5EYmJKeEFEYUljbEVCYlBlQWVzRzJmSHRTRDUzMU1aTTRTN1kwSW5WSDRtNFN2Qm51MmJnRXdEZXROSEgzMEhacGRNZDZnK2FQbnczMldCU3p4c3pCQXcwaFNlZ3ZGRm9rVEwwejZINUg2QkYvUjJCVEd2MWNXelp5WVFBZXp6M05OR1hrR3h5MkxxZVYwTkVvaEhtZWhOOUFHY1k1eEFrUDhBK0g1NWVNb29KaU41ZzRLOStYa0hhTnFJQ3BZNERkSDdOY3ZqYmpJa1EwZUhlMENiY0p2WEZJUnhUVnZLRFgyc1BoeklDSlRYWjJXZ3dZT3Y4UFkxTk1zZERwQ1B1ZXlFd3dqemdUVXprYWpSZXlaNGowN2FGMzBnRWNqbFllVTBtcFB0Z3orMDU5aG0xTFpLZll1NjVPcGFiYUt1V3Y2YXFTWEVCVGZZcTJGb0xJcGF1aFhJc0RVcHpqcFNZVTdDeFZLMWIwa0d3M0FuK1lKTmhURjN5akxrcE03SGtXeFdqSVFGYlFwNFllM2oveGxRUmpnQ09TNlVQeXFtY0ZGLzU4NnRUbCtxdVVSWWoxbDVhc1ZvdnBNdS8iLCJtYWMiOiJkMGI4NTFmNDJjYjQ5ZTFmNTdlYTEyNTc3ODJhZWUxY2NkMjRkZTQ4ODYyMzIzZmNmMDBhZWVkZDQ4MzcyNWUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573942348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1204385228 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204385228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409015131 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklOK3BkRVBBVTFsWWM4VGZWSGM1V3c9PSIsInZhbHVlIjoiK0dQaFlranNRQ0xhc0g2aG92blg0VGdRS091L1Qwa0xPYnA0NEhlY21KRXNDYXNCdzhvM2NJMDJsZEY5L0UzTWZLeGxmRjdwbHlyeWpteVlIY3lXUnJzYmxaTE1IZUt6V1VhazJWOU5JUjNZczRHR0hXcjE3dzV2dGxpa0U3ZXR5T2dpUVM2Vy9TdjJHT0pUZEhaOUhEWC94cEw3cytEUXdDR25LeVZTcG52VW5TYmh4NGMva1BqcFNNeWZXSUM0OUFPU1ZnU0hjTWpicTNUZjlHL0p5Vnk0TGR0N1Y3Y20rTk9FbSthcmhBUGxmOFdFaU5LbWtpUVh6a1hWME9rQXhMSHhPaW5ibVpkM0wrUnU1OUVTZnRkRWFCTDQ3U2VpMk51UWxHY3NxZEtaTzVrcHI2UXhuenhSVkFoLzVNTXBXRFBUbmRqSlYrUEUxQVZ0WnBlMGVIM0M1MlJXYmNHSjE0NWJsZXo5YmFtdUlWemQxdlRvalhicmhONTYyVzV4SjcxR2Y1MDU5cmpUR0xKTHIvMllDNDNnbVMxUUNqTG9vYWd5MFNyWUdnMDJFVXljN0k1UkdHMm03VEJNQ2ZXMlJFclJJamliRG4xekJSRSsvbGoyQUh2YmtaRzZHaGhHUnZFeFNRVndlVW0wbkdjSXZrRGNEZmdYdEpWTXZvNXoiLCJtYWMiOiI3ZGZkYzUyYjdmNmE5OGE4ODcwZDk3ZTQ2M2VmODRlOTJhYmRhZjhlN2IzMmFlZDA2NjIyOTUzMTNkNGFlNmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9QKzUzUG1IY05iTWF6UjczQUV4TVE9PSIsInZhbHVlIjoiQmJhRVc5d0xIb3hieXRXMlZ3Q3d0aHpaeDd2TCswL1hPcEJPVEpSWVlLNGhwZis1ZUhYaWJWakk4MEFwSlZNTGU3WDJqTzlnbFZ3UVU4emxMS3JkTXBkVTNBRmdMUDhMR3cvU0FrdTlXV2RnMGliSDFNSmZ2Wk9lNXYvYlBzUUFLUEpaNzdzK2MxSkZMdUVNRUtuejF4MHpGN0Nta1g1Tk9MMk1aWjlNSTZVclBWdTdnQW9jYnhEVGRSb2hlcDhRN01YYlA0UXZBU3YvMkhXYXJOaGIrSnV0VjZ3YWVSRFdMRzlJYXgydGNRVFh0L01LVldESUNMY3hOUTdnVjZHMVUvcjlSaDUyMzVLN1JZR0NsZk1HenJORXExSnhUb05lMTlBZEwzWjBvbFJ5L2hORnlBaXk4dW8rejU1Ujg4NVJkSzM2NE5IVldrR1plYll0T0lqZnE2cm5sOEMxdDJGY0ZZeE9DWDd4OHJHWlVzZ0tiMU8vSFVNMjFNazdubHpNNGhmMVF2VGNrT0pMT0kvV0k3TUhQNDc5S2dodG81K0dua0FsRURiN1g5dTlRMldtNUpPdmRNNnVqcklGMXVzZW8vU1gzaGk4Q2czK3FBQnY5N1BVeUlDTGN2RFpmUWpQY1l5MnJzazdiTm1Ca3g4YUtRc0p3VDNVVXRCZ29zSjciLCJtYWMiOiJjMmJlMWQ0YTlmOWM5YTVmYTkzM2FhN2MxMDI2NWQxMTNiYTRhYzZhMGIwNTg2NDFkMGY3YTQ5YWY1YWU5ZGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklOK3BkRVBBVTFsWWM4VGZWSGM1V3c9PSIsInZhbHVlIjoiK0dQaFlranNRQ0xhc0g2aG92blg0VGdRS091L1Qwa0xPYnA0NEhlY21KRXNDYXNCdzhvM2NJMDJsZEY5L0UzTWZLeGxmRjdwbHlyeWpteVlIY3lXUnJzYmxaTE1IZUt6V1VhazJWOU5JUjNZczRHR0hXcjE3dzV2dGxpa0U3ZXR5T2dpUVM2Vy9TdjJHT0pUZEhaOUhEWC94cEw3cytEUXdDR25LeVZTcG52VW5TYmh4NGMva1BqcFNNeWZXSUM0OUFPU1ZnU0hjTWpicTNUZjlHL0p5Vnk0TGR0N1Y3Y20rTk9FbSthcmhBUGxmOFdFaU5LbWtpUVh6a1hWME9rQXhMSHhPaW5ibVpkM0wrUnU1OUVTZnRkRWFCTDQ3U2VpMk51UWxHY3NxZEtaTzVrcHI2UXhuenhSVkFoLzVNTXBXRFBUbmRqSlYrUEUxQVZ0WnBlMGVIM0M1MlJXYmNHSjE0NWJsZXo5YmFtdUlWemQxdlRvalhicmhONTYyVzV4SjcxR2Y1MDU5cmpUR0xKTHIvMllDNDNnbVMxUUNqTG9vYWd5MFNyWUdnMDJFVXljN0k1UkdHMm03VEJNQ2ZXMlJFclJJamliRG4xekJSRSsvbGoyQUh2YmtaRzZHaGhHUnZFeFNRVndlVW0wbkdjSXZrRGNEZmdYdEpWTXZvNXoiLCJtYWMiOiI3ZGZkYzUyYjdmNmE5OGE4ODcwZDk3ZTQ2M2VmODRlOTJhYmRhZjhlN2IzMmFlZDA2NjIyOTUzMTNkNGFlNmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9QKzUzUG1IY05iTWF6UjczQUV4TVE9PSIsInZhbHVlIjoiQmJhRVc5d0xIb3hieXRXMlZ3Q3d0aHpaeDd2TCswL1hPcEJPVEpSWVlLNGhwZis1ZUhYaWJWakk4MEFwSlZNTGU3WDJqTzlnbFZ3UVU4emxMS3JkTXBkVTNBRmdMUDhMR3cvU0FrdTlXV2RnMGliSDFNSmZ2Wk9lNXYvYlBzUUFLUEpaNzdzK2MxSkZMdUVNRUtuejF4MHpGN0Nta1g1Tk9MMk1aWjlNSTZVclBWdTdnQW9jYnhEVGRSb2hlcDhRN01YYlA0UXZBU3YvMkhXYXJOaGIrSnV0VjZ3YWVSRFdMRzlJYXgydGNRVFh0L01LVldESUNMY3hOUTdnVjZHMVUvcjlSaDUyMzVLN1JZR0NsZk1HenJORXExSnhUb05lMTlBZEwzWjBvbFJ5L2hORnlBaXk4dW8rejU1Ujg4NVJkSzM2NE5IVldrR1plYll0T0lqZnE2cm5sOEMxdDJGY0ZZeE9DWDd4OHJHWlVzZ0tiMU8vSFVNMjFNazdubHpNNGhmMVF2VGNrT0pMT0kvV0k3TUhQNDc5S2dodG81K0dua0FsRURiN1g5dTlRMldtNUpPdmRNNnVqcklGMXVzZW8vU1gzaGk4Q2czK3FBQnY5N1BVeUlDTGN2RFpmUWpQY1l5MnJzazdiTm1Ca3g4YUtRc0p3VDNVVXRCZ29zSjciLCJtYWMiOiJjMmJlMWQ0YTlmOWM5YTVmYTkzM2FhN2MxMDI2NWQxMTNiYTRhYzZhMGIwNTg2NDFkMGY3YTQ5YWY1YWU5ZGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409015131\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1993496109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993496109\", {\"maxDepth\":0})</script>\n"}}