{"__meta": {"id": "Xc2eeeb92475214dd5f510bf8b800d03b", "datetime": "2025-06-30 18:07:35", "utime": **********.711991, "method": "GET", "uri": "/add-to-cart/2306/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.127497, "end": **********.712006, "duration": 0.5845091342926025, "duration_str": "585ms", "measures": [{"label": "Booting", "start": **********.127497, "relative_start": 0, "end": **********.585034, "relative_end": **********.585034, "duration": 0.4575369358062744, "duration_str": "458ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.585055, "relative_start": 0.45755815505981445, "end": **********.712008, "relative_end": 1.9073486328125e-06, "duration": 0.1269528865814209, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48659488, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.007390000000000001, "accumulated_duration_str": "7.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.639969, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.476}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6569948, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.476, "width_percent": 10.149}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.680754, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 42.625, "width_percent": 5.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.683325, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 48.444, "width_percent": 5.413}, {"sql": "select * from `product_services` where `product_services`.`id` = '2306' limit 1", "type": "query", "params": [], "bindings": ["2306"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.690691, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 53.857, "width_percent": 7.037}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2306 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2306", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.695514, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 60.893, "width_percent": 33.965}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.699742, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.858, "width_percent": 5.142}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079469081 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079469081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.688958, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2305 => array:9 [\n    \"name\" => \"بيبيرو أصابع بسكويت مغطاة بالشوكولاتة كرانشي - 39غ\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2305\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2306 => array:8 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"id\" => \"2306\"\n    \"originalquantity\" => 52\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2306/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1854870890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854870890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1963466880 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldUSmlSVHBVNFZqZTAxcFRPM0NmQXc9PSIsInZhbHVlIjoiaURsTHlHaHBLSlpWMDJFSW05T3BLdElrOXM5UVRIcDJtb2F4amJoQ1BTNFh2ck5WWmxvZjBZZkF2MEtNb1BCKzI1VHBreEtuNTZiUjVFOERnNER6dGx3QkpUWEFaZHVUQ1hRTGlEMURNN2c0bUx3UTZvbEFxTEFteXVUa0VMSVpYYk9Uclo4VDFmMmNXRjkvRUFaVzFkU2d0WW9NTllrcXVJWnVteVN4TnJDNWEwZXdLYnNOZmZheVVXZ21sTmRkekdkbkFRRWlXcUtRSnhKbWd0anNjY2dqSW5EcE5vNnMxUUNmTUkyQ3pRdGlwdWZpRFR3V3c4eHNSbSt5WFcxandCSnpVTU5Oa0hNTmNIT3NudkZhL2szQlROZG1aTElkV2VVUENTQ0dsbjJWekswcjUxcVpLejl6bWpKRWhTNVNzaGhxbmgzWTVWL1YrMXg5RkgreFlpUUtuR3FSZmwzVk5hSEFZK0RaeGlQWU1DNVBkZWoyaFdadkFuSThURFNwSTROb2g1Q0MveGkydUlDZnk1d2JQem5RTk9UZE9TSkp3Z2s4YXBZc3JMQlFRbUhXbzVFak9oMS9VdFlnNzdSakhyN1M1R1lxR1M1Qm1wVVJiV2cxdlhMUnZjT054dHBRS3BrRGMxaFZWZERIYXhwdVFjb2l6MTErRHI0bmtPSUoiLCJtYWMiOiIzNWYwMzFlZDQ5NmZjNDc5NTZiM2Y3ZjU5NTI3NzVmMjcwMTU3MDAzODNjYWJjNGM3MWQwYzZkY2EzMDA4YTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IituS3d2UllCY3hINklaV3dPb2Rjb2c9PSIsInZhbHVlIjoiUkJsbzZ2SmFRb3liRWhFdGVYTldlRkR2bWhMNXVYenIzd2tBQU9BWktCaWh4VmtBWEdSTk9YY3VwckJwV1RvOElubkUyYlpLWVZjYkNRckFLRGFsOEJ5bFhDVE1oR2hlSTRjTTdoWnBrZkNmc1JPZFpUV2dYNnM4MnR3WHFZUzBweWduVitLeWxzN3I2YTNZeXlZZ1dzbUNWS3B1YWxnQkdydkMzM2ZkOC9KeGlhU1prVE5icTBkZDZFSUFSUUNTWTE0SG5pN2lHOXV3M2w2eFRUUUFoVUVYVDZYaFZIVml6YTVMMUtvM0tEcFRqZ0M0QjBzalBBaWNnZ2xxb1MxUnJJRkQvUXgxbWRhcTdIampCaHMyOVBmd1Job2lxMG9CWXk0Z0pURlEvUzZOZC9LWWp3Z0lqNEpaY3NpazY2UkNXZXdlQ3lrYWhaTVRPMHlucWw1Rit6YkplcDlFZUFIRVhlbDVLTG5QRktBb1NqQ3g3M0U0RlM5YUxNem5HakQ4cGZKL2k1UldqaHhnSDhTTlY2L3dkZnpSdFpjYURWUFRlNEhNMTc5L05OSTVsM2ZTd3RiSnpCN2dTVDNqU09wZDVqa1Fmd1BkRG9uWXZ5ZWpKcFFON2piQjI0RkNVRmZ2bmZ0T05jZDZJZDg5UFFUZWJ1OGNiMXhpWXdmSWpJUVYiLCJtYWMiOiJkM2I5YTIzYWVjNGFjOWI1MmMzOWE0MDZmYTJmOGFhOTQ4ZGY2ZjdlNjA2MzA1Zjc1NjBmNjM1ZjM5ZDUyNjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963466880\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1531076408 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531076408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-441114613 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:07:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitsR2FaVEJOVVZ6ZDUvQlNQVXhMb1E9PSIsInZhbHVlIjoia1NlUktFRTU0ckg1b2dKTE5NTVNjNk8vMG1oVnpFZGlOWnlXUXlyQ2t6YVF6V0hPek1iOGVsL0hNTVUrc0tJZjdISldpbS9iTHQ1eGhQdFBBVVBYQmZOOHd4RkQyL1VNWlJVZTVzS0YvQTVQZm1oQ0g2OERMRzBNZVpjdHUwTjZ6cTlPdCtTOVQzN1lKLy8wNklua1k3OElleHN1ZURSUmV5R2wzekQ3cTZsSmZIS3dCRWZKTERaVWhiWXQxSFNsNGdOVm9XR1A0NWs1QlNLUllUZWhEY3U0SXhoYUdmbEZqQzVFcW94SUIzbDhnbDNhS0UvSCtYeks3d1NpT0NJWDdWczdhdDlPeFMrQ3d5OENJa2M2THgyTWE0NG1tWnc1VFV4MXRUeU55NmFGc25KbWVXQjhJek5sSno5S09TMnJZYlBxdG5BaUpPVnBlN3hwaVBqMmRQK3Y4TjFsMFVzMGVPWGljcEI1YWRKV1R6MCtBWXJaNkFKekt3a2kwMFFMVlBFcSttQS95R2hHQmd0M1hTbWlCYUdtb0Y4bnR3ZmxBVmdkdWZ4ekt6T0pCOVZQK2FjMWw3eU1QLy8wMUlTNWs3eUpBNVRSWDJtZmYrY1NZU1R3VmltMzJwK1ZLUWlXQnNVQys4YjRQOGtOTm9xdnBBaGxiVGorV0pHdEpneGkiLCJtYWMiOiI0NzQ5NzQ4MzQ2YjIzNjlhYzEyNTY4OGYxZjRlYzc4YjRiNzA5NGUzNzNhNzZkNDUyYjFlMjljMjRlOTM0ZTg3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhYU1V5bERzZ1NSc0U0ZE5DY3dualE9PSIsInZhbHVlIjoiSGVjR3pEZ24rcEdJZXh4TCtGNHJHUFZHNW9QWTFyam1nSEROM1VTRkIrZEsvdllPcjdDZlVQTEovbHNkcU5ueDVjY1RnSktjdVRWQ0hpeWJHR1JraGlmMWxXQTJsbW8ySWJ6WFU3aFlpbkRXbnArRk5nTmFENE1sUHlod3RoT0hOVy9PaHRXQkVWbGFMdHBRME1Pc1hzUTRyeHJLeUc4S1RLK1poZ285ZjlhNnMyV3Q2dnArRjZjOGNYcHRZaTV6LzNIRllSY1J4SlBFUWRLQ3VSTDZJalk5eHRDbW1sZ1hMakpSZ0o3WUF6L0djaWdwTDR0aUNGdTFoS1RHS1Y4QXNIZlgwZm5PVnNYMDhxcFh0bm9nYlhuMmRYZ29HbDV5S2tIMVl3cEdkaE9wSHErZDFLZk0xL2VYeVdOZTZCRHNNeWs0cERJdGczZG1MelY4d3Q3Wm9JN2t3NXF5dEdwT1NHN1IrTWpwd1VhUFFseXp0VUF6TGpiQXJ0MnVZcGlEOXI3SHF2REdncDc0VzhrUFBqRE5aLzl5Q1JvQ3ErVWY5MlFkSUxNay9SbzdWejRBdFYvSENxV3lsVXBPQkdOenJNby9XNlhQd0FyQVdJb2l3NnNMazlRZWw0am82a2FNRSt4RDZGWG5wV3UwMnI4MFpTdHo5RnQ5MGkydEZtLzgiLCJtYWMiOiJiMmUyYWIwODFlNjUzZDA2ZGJhN2NiMDQ3YTkyYjIzZDE2NjNjZDdjOWMzN2JkN2M5MGEzZmRlNWE2ODc0YzI4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitsR2FaVEJOVVZ6ZDUvQlNQVXhMb1E9PSIsInZhbHVlIjoia1NlUktFRTU0ckg1b2dKTE5NTVNjNk8vMG1oVnpFZGlOWnlXUXlyQ2t6YVF6V0hPek1iOGVsL0hNTVUrc0tJZjdISldpbS9iTHQ1eGhQdFBBVVBYQmZOOHd4RkQyL1VNWlJVZTVzS0YvQTVQZm1oQ0g2OERMRzBNZVpjdHUwTjZ6cTlPdCtTOVQzN1lKLy8wNklua1k3OElleHN1ZURSUmV5R2wzekQ3cTZsSmZIS3dCRWZKTERaVWhiWXQxSFNsNGdOVm9XR1A0NWs1QlNLUllUZWhEY3U0SXhoYUdmbEZqQzVFcW94SUIzbDhnbDNhS0UvSCtYeks3d1NpT0NJWDdWczdhdDlPeFMrQ3d5OENJa2M2THgyTWE0NG1tWnc1VFV4MXRUeU55NmFGc25KbWVXQjhJek5sSno5S09TMnJZYlBxdG5BaUpPVnBlN3hwaVBqMmRQK3Y4TjFsMFVzMGVPWGljcEI1YWRKV1R6MCtBWXJaNkFKekt3a2kwMFFMVlBFcSttQS95R2hHQmd0M1hTbWlCYUdtb0Y4bnR3ZmxBVmdkdWZ4ekt6T0pCOVZQK2FjMWw3eU1QLy8wMUlTNWs3eUpBNVRSWDJtZmYrY1NZU1R3VmltMzJwK1ZLUWlXQnNVQys4YjRQOGtOTm9xdnBBaGxiVGorV0pHdEpneGkiLCJtYWMiOiI0NzQ5NzQ4MzQ2YjIzNjlhYzEyNTY4OGYxZjRlYzc4YjRiNzA5NGUzNzNhNzZkNDUyYjFlMjljMjRlOTM0ZTg3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhYU1V5bERzZ1NSc0U0ZE5DY3dualE9PSIsInZhbHVlIjoiSGVjR3pEZ24rcEdJZXh4TCtGNHJHUFZHNW9QWTFyam1nSEROM1VTRkIrZEsvdllPcjdDZlVQTEovbHNkcU5ueDVjY1RnSktjdVRWQ0hpeWJHR1JraGlmMWxXQTJsbW8ySWJ6WFU3aFlpbkRXbnArRk5nTmFENE1sUHlod3RoT0hOVy9PaHRXQkVWbGFMdHBRME1Pc1hzUTRyeHJLeUc4S1RLK1poZ285ZjlhNnMyV3Q2dnArRjZjOGNYcHRZaTV6LzNIRllSY1J4SlBFUWRLQ3VSTDZJalk5eHRDbW1sZ1hMakpSZ0o3WUF6L0djaWdwTDR0aUNGdTFoS1RHS1Y4QXNIZlgwZm5PVnNYMDhxcFh0bm9nYlhuMmRYZ29HbDV5S2tIMVl3cEdkaE9wSHErZDFLZk0xL2VYeVdOZTZCRHNNeWs0cERJdGczZG1MelY4d3Q3Wm9JN2t3NXF5dEdwT1NHN1IrTWpwd1VhUFFseXp0VUF6TGpiQXJ0MnVZcGlEOXI3SHF2REdncDc0VzhrUFBqRE5aLzl5Q1JvQ3ErVWY5MlFkSUxNay9SbzdWejRBdFYvSENxV3lsVXBPQkdOenJNby9XNlhQd0FyQVdJb2l3NnNMazlRZWw0am82a2FNRSt4RDZGWG5wV3UwMnI4MFpTdHo5RnQ5MGkydEZtLzgiLCJtYWMiOiJiMmUyYWIwODFlNjUzZDA2ZGJhN2NiMDQ3YTkyYjIzZDE2NjNjZDdjOWMzN2JkN2M5MGEzZmRlNWE2ODc0YzI4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441114613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1921472649 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2305</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"50 characters\">&#1576;&#1610;&#1576;&#1610;&#1585;&#1608; &#1571;&#1589;&#1575;&#1576;&#1593; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1605;&#1594;&#1591;&#1575;&#1577; &#1576;&#1575;&#1604;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1603;&#1585;&#1575;&#1606;&#1588;&#1610; - 39&#1594;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2305</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>52</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921472649\", {\"maxDepth\":0})</script>\n"}}