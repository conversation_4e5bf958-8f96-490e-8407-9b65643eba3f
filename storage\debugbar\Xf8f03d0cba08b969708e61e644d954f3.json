{"__meta": {"id": "Xf8f03d0cba08b969708e61e644d954f3", "datetime": "2025-06-30 18:11:27", "utime": **********.179635, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307086.734787, "end": **********.179648, "duration": 0.44486093521118164, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1751307086.734787, "relative_start": 0, "end": **********.096359, "relative_end": **********.096359, "duration": 0.3615720272064209, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.096369, "relative_start": 0.36158204078674316, "end": **********.179649, "relative_end": 1.1920928955078125e-06, "duration": 0.08328008651733398, "duration_str": "83.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48652904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0054800000000000005, "accumulated_duration_str": "5.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.135519, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.38}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.145283, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.38, "width_percent": 7.299}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.158477, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 36.679, "width_percent": 6.934}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1601532, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.613, "width_percent": 5.109}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.164413, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 48.723, "width_percent": 6.569}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.167933, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.292, "width_percent": 39.051}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.171432, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.343, "width_percent": 5.657}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1218498651 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218498651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163606, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 10\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 575.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-23551509 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-23551509\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1726493673 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1726493673\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJmWGpYTlBwNWlMenBqNFJmcnB4d2c9PSIsInZhbHVlIjoiRjh6ZkZ5TXFET05FZCtQSkFDL3cwd092S0xJUTNPaUg1Y0lPbHIxNzYxcGFPVDNQUzZlZ0haT253ZkVmdXpBQ291MTNHUWFHZkt6anJ6V01IL1ZyQTNRZE1HUUx0VEkwUUxuOWtzQjJsUVMvTGtYZ0dFT2V6cnBoUXhiT0pCcHdpN09scWlUVFd3N0VlVFo3Q1dZcS9zZ21pS2RHL1dQQzFvUkk4S3RzNG8xVGhYYjhvQ1dGRGlsNkpKWW1NbW1pRnk3VGE0aXNwdmFKam1vSTEzV3Q5SktlQ0dTbHhhZkdWTEZqQ0QvT29jSEZwL2lkbG5qd2NTa3FFY2plaHdFODJ5MEw0NDdJVzdzVEpRMGtra0FpOURyQnpqek5wMWRMRlJvaEZETUJISFV0TTZMSXFWK1U0c0hodGpjVlhXenBYWVdxeGNrUmhPbnlORkxxRmU3Z21EU29ESUpWZW1mQ0FVRW5rMlNucjRzaFRUbXN3WVZvS1VveUhpRXIyYkpScVpCZnpVb3YvMnNvUTRRNC9URDcvcFUxQW5GT1NxZnRoZzFLWWpXci80NnBJOHh2TENPSGFYZnQ2VG9FOHVpUWlkeVpFVjEyRUJCWHY3bDZYWXdiT25kT1g1anUvbDEwZjRWdHhmZy8ydE5GcjMyZTZMMVp4RVY4Y0VFVlRSa0kiLCJtYWMiOiJiNTQ0MTFiYmFkNTE0OTYwY2NiN2ExMDdhZDQwOTFlMDFlZjBjODMwNmYzZDI4ZjgzNmI5MjQxZTlmMmM2NjczIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZRSVhPVTBLY2NUTzBwOExzZWRzdFE9PSIsInZhbHVlIjoielp6UHJtWDc5Vk9qUkltd21XbDdLSmFkd0Q5MlRPV2pYYXJDSGlhcnNpYU9xWC9NekR2cXhNQWRTaEkyaGg3WG9JMXdsYkxrZzJyVnJDY2VQYXBxZ2hvWXRsb2JUM0g4a0hXMDFZSWFldDhiR00xRWRCRWl5ZUhyRTdnTEsyMnJQdVgrU285aXl6MlhYQkU4MDBNTUNmTm91ZXR0RjVucllsQ2lJLzQxV1JDcnI3a1ZNaXB0NTZ1Q0U4WEJkNzhqbkJBdFZWdys5UjFrK3VhWU5Dazc1ckd4N3U2aU5rcHB6azd1S1FrTTREd3JEcXJ5c1lmT1lOK1hUWmNCS283MVhOMm5ZUlU4UlZCQ3p3TmtScFZWY0dudjVCRytMSE9vZForREU5S2ppdjIrWDY0cE5iSjQwdmQ2Z20wdEZOaFkwY1A5VnoxUU00dHlVQXBWRmN2c2c3aEdSZkxUdW5GU2d1Mk04eDM4YUNmTGFXcTMraVdRS1dtcHIyUjZWcG8zOUQvd3ZUM2d6aWpVWTlRYTFjcENLRHBRMCtiakFTL0NHSTNJU2hnVmsrdG1vWWFhbGNqcldJR2tubk1EM084N2Y2ZC9DYU92K050TzN4Sk1iVEQ4RDVqaW1hZjJJNzNaZURJc3Zua2h2NXZsZDVPKzZnUnFadUVaWHVKbmZibnQiLCJtYWMiOiIxYTMzMzdkNTQxODhmZGI1M2ZlMzBiNzM2YWY1ODVhMjEyOWNlNzcxY2QxZmRiMThiYmFlMWVmOGE3ZmM2ZDVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1447409834 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlAxeWdGbmV1SFZncFR3OW1IRHJaaEE9PSIsInZhbHVlIjoid2dsczhSOFZ1YXVIZVVYMk12UG1aN1dOUGYwWGVrTm1hK3N3RTcrem9UaGVIUE5QS2R6aE5semZOMUFwTEtEcUpuVllhbnk1VUFJTDRwSXU4WXQvczBMTS9ZV2ZraVVMZ1VtMUYvbFl0TFoydEljQjhQVEh5cHBOU0V5VW1DMkRWclp0MWdRQWZQcVlCeTdTbXQrdjc0NFVZNHJtRHpiWkdsMmJEcng4eWxGdXlKajd1L2NFRG41Q00yUlphekNKZ0pRYTU2b3NlSmlrb3dRMnQ2MVFUdG40V3JlZmQ4LzFJM0NiME1SVkkxUEZKSVd4K1lXR2xMYktGNkx6cjFqSUlQdU9HVThDc1RrWmlRdXpRWCtBbWJZS2lIdEZNNGkzM3QrL3FtTUIrVmkxdk5FcE5ZVUFkTG15bWZpYWxFT2poQlkzL2krOE1uUldVdW9sSklUTFp3alo5KzVSaysybk95QUp2cmozSjdPMFZ5Q1VDYjA2dS8rcFZXUysvaGt5SVhrWVczbGM0T0dUUFdmdTJmZ0xNcWNsNWd1TklBQis1OG4wRmFZZkRxQ2dIZjBsTS9ZOGkxMlNwV0tPY0xMRFkxejFXdFFuQks4Wk41eWdLQm5DR21qMWVkUy91Z3c2ZlU5dWM4Wkx6OGVmU1NhVzlpWE94OHhxQnBXbG1JN0ciLCJtYWMiOiJmODhkZWNlYjhiYWI4YWU3OWI1OTllMmI4MDIzZjAwNzg1MTU1YzNmNzFlNWMyODZlMjU5YTg0MjBhM2JiMzViIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlkxVmFqVUlIeC9XVnlQVU9JZ2Vhcmc9PSIsInZhbHVlIjoiM0NSUFdDSGVSZEZweTNlUXFZb2hjYU1XMXZtYkJCNkd5T0ZpNmd1M09rdGxFZjQxYlE5Z29JM1FaSmZYc2tnV3k1TEhoeEFOSENBQUliTGRlbkhscXhPTlNBcnViblJpR053eWpTOTJiS0hWM2Jvb05nQmFqblREOXFEVzZiVjdTd1E3bjVoaWd0TWVWK2tzNWwrdU9EZkVWbEU3akdGV1dIZnByK2s0YzN0Q1hnZWliVXM3cWxaVXB0Y0ZFK0ZhWW5PNUQ1MGpSOVZZNXIvMVpFeXdWdTBsMmllUFpmWmpFZnc5TlJRcjlIY1ZJMDQzbTdtWTJkUXRJQ2E4eUpkcFQxcUp6Wk1DYmNoZi9DRlkvb1hiSDM4L0s1N1lRYld0cU5pV21iVVlIUmhSTjV5SHlsSW91K0dWRVNjd2U3Wk1SZU00RkxBeGk0ZWVVMS8yVi9EdUZ0czlYVHQ3ekRieGNOZS9XdHFub3MrL2FzMTJTcElzK3VvZ1BJMXBqTHhad0VBemlIdys1YlltajM4WEV5dDlrYU45dmtCSFdCeThkSzI0YU16TzNpVXpFblFycG4zZ1FxRUNXeURNd0g4cWxMeGErMzd1SlVWeFZsQkJ6UGU4NXBNL1JadmVqRVVEYWtlT1BFQVRJRmNxdDY4OVVIUnEvTkErdFlZcU5ScE8iLCJtYWMiOiI0NjA3YzMzMmQ0NGFhYTY1ZmQzYTA1NWQwNjhhNGFhODE5MzM5ZDQ4MTMzZjBhNGRjYTI1YTY0NmYyYmU2ZDI3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlAxeWdGbmV1SFZncFR3OW1IRHJaaEE9PSIsInZhbHVlIjoid2dsczhSOFZ1YXVIZVVYMk12UG1aN1dOUGYwWGVrTm1hK3N3RTcrem9UaGVIUE5QS2R6aE5semZOMUFwTEtEcUpuVllhbnk1VUFJTDRwSXU4WXQvczBMTS9ZV2ZraVVMZ1VtMUYvbFl0TFoydEljQjhQVEh5cHBOU0V5VW1DMkRWclp0MWdRQWZQcVlCeTdTbXQrdjc0NFVZNHJtRHpiWkdsMmJEcng4eWxGdXlKajd1L2NFRG41Q00yUlphekNKZ0pRYTU2b3NlSmlrb3dRMnQ2MVFUdG40V3JlZmQ4LzFJM0NiME1SVkkxUEZKSVd4K1lXR2xMYktGNkx6cjFqSUlQdU9HVThDc1RrWmlRdXpRWCtBbWJZS2lIdEZNNGkzM3QrL3FtTUIrVmkxdk5FcE5ZVUFkTG15bWZpYWxFT2poQlkzL2krOE1uUldVdW9sSklUTFp3alo5KzVSaysybk95QUp2cmozSjdPMFZ5Q1VDYjA2dS8rcFZXUysvaGt5SVhrWVczbGM0T0dUUFdmdTJmZ0xNcWNsNWd1TklBQis1OG4wRmFZZkRxQ2dIZjBsTS9ZOGkxMlNwV0tPY0xMRFkxejFXdFFuQks4Wk41eWdLQm5DR21qMWVkUy91Z3c2ZlU5dWM4Wkx6OGVmU1NhVzlpWE94OHhxQnBXbG1JN0ciLCJtYWMiOiJmODhkZWNlYjhiYWI4YWU3OWI1OTllMmI4MDIzZjAwNzg1MTU1YzNmNzFlNWMyODZlMjU5YTg0MjBhM2JiMzViIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlkxVmFqVUlIeC9XVnlQVU9JZ2Vhcmc9PSIsInZhbHVlIjoiM0NSUFdDSGVSZEZweTNlUXFZb2hjYU1XMXZtYkJCNkd5T0ZpNmd1M09rdGxFZjQxYlE5Z29JM1FaSmZYc2tnV3k1TEhoeEFOSENBQUliTGRlbkhscXhPTlNBcnViblJpR053eWpTOTJiS0hWM2Jvb05nQmFqblREOXFEVzZiVjdTd1E3bjVoaWd0TWVWK2tzNWwrdU9EZkVWbEU3akdGV1dIZnByK2s0YzN0Q1hnZWliVXM3cWxaVXB0Y0ZFK0ZhWW5PNUQ1MGpSOVZZNXIvMVpFeXdWdTBsMmllUFpmWmpFZnc5TlJRcjlIY1ZJMDQzbTdtWTJkUXRJQ2E4eUpkcFQxcUp6Wk1DYmNoZi9DRlkvb1hiSDM4L0s1N1lRYld0cU5pV21iVVlIUmhSTjV5SHlsSW91K0dWRVNjd2U3Wk1SZU00RkxBeGk0ZWVVMS8yVi9EdUZ0czlYVHQ3ekRieGNOZS9XdHFub3MrL2FzMTJTcElzK3VvZ1BJMXBqTHhad0VBemlIdys1YlltajM4WEV5dDlrYU45dmtCSFdCeThkSzI0YU16TzNpVXpFblFycG4zZ1FxRUNXeURNd0g4cWxMeGErMzd1SlVWeFZsQkJ6UGU4NXBNL1JadmVqRVVEYWtlT1BFQVRJRmNxdDY4OVVIUnEvTkErdFlZcU5ScE8iLCJtYWMiOiI0NjA3YzMzMmQ0NGFhYTY1ZmQzYTA1NWQwNjhhNGFhODE5MzM5ZDQ4MTMzZjBhNGRjYTI1YTY0NmYyYmU2ZDI3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447409834\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1001853256 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>575.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001853256\", {\"maxDepth\":0})</script>\n"}}