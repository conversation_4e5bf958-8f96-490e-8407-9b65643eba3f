{"__meta": {"id": "X62f06d29ddcae2bdca8acd63f486a2e9", "datetime": "2025-06-30 16:07:22", "utime": **********.581159, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.087321, "end": **********.581178, "duration": 0.49385690689086914, "duration_str": "494ms", "measures": [{"label": "Booting", "start": **********.087321, "relative_start": 0, "end": **********.520731, "relative_end": **********.520731, "duration": 0.4334099292755127, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520742, "relative_start": 0.43342089653015137, "end": **********.58118, "relative_end": 2.1457672119140625e-06, "duration": 0.06043815612792969, "duration_str": "60.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348344, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00408, "accumulated_duration_str": "4.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.558347, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.529}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.570333, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.529, "width_percent": 13.48}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.573187, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 87.01, "width_percent": 12.99}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1875632443 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1875632443\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1224869351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1224869351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-980914822 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980914822\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1135113841 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299634011%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw1dmNVanYrVDZ2eU15ZVVwSkViOEE9PSIsInZhbHVlIjoiRjJpNmdCRDdpbjdhaTJnOVJWb29sOVFCSlNhd0RPQTdRQ0NsamhTSEd1SlBFMERXOHpMazVVaFZiclBvcFJIZ3pKa0JFUGNneEptK3BabG5PNkRPTENhM3lRdmNvOWhReXUzWm9oclMyRWh2cHlEZXg4bTlacXlrUXdDV0xRRWFQZCtPNWgzb2RBZ2FYeDhkQk80dlBvRzQ0ekhRTDlnYW1wcE5zRldiOGUwUjM3K1I0ZGx4ZnQ5NVZtU3ZpOHVSaXk3SzJpTHMyUVA0ZEtPeC9SdE5oY09OVm5NdVZ2c0sxd3FrMTZEbHovNkVNZGVYNmVMaTlPWDVnZy9hTFVMeis1cWlYVkRuOThwZWlUSDFjSGJRRTQ3R2RpZHAwMHE4MHVxa1hIaWZlekY2dUxqMU90S3FJbWJ0WDFTTmYxQ1F4d1Z2azJjdFNvaU9NUDk0dDk1eW1nMTNWY1l1Tk5uTVR4SzE2Y1hHY3pQN3d3T3ZmK0JaVGFNWGcwZXFBdE0yMDhlcUVYWEh6Nm5wZVNZdURucXowY3hGU0xFYmdJc0diK3M3blpWdkI2UXB2RUlDSy8zOGR1V1llRHBwYXdvTEdWVHM1R3RBZVpKeEg3L1FYSUxFalk3WXk2bmhvTnpaRjNhRWtKL2Y4TXJIYzJqOXRTOWt2N0d6ZkpEMEI3YVQiLCJtYWMiOiIyN2E2Njg5MWEwN2RmMzc3YTRlY2NiZmIwYTI4MzkxYWZlZTY2OGNmNzkxMGJkNGI0ZWMwMzcwYzBkMzAzOGMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRxa2xkZ1ZaUWIyZWk4OExzVHRpcXc9PSIsInZhbHVlIjoieXN1eUdZTU9xQjZ0d2o5UDYxUDZnYXlpekU4aUtWM1hUYmhUNFZ3anJsUEFydDBENGJSTHpQOHhlN1pnOHFkU2xKR1czLzhpMDlPTTZKNktpQXV0Qy9EOGhiQUFMeCsyanpHWWVORCtYUWJjc1ZEOHRJMXRXMmxna0NsQzV3dEFYaFZSVW01SkJBMG9hM0V1TVFoaHNwRmpacTJnTzI0Lzc0cEQyZGJzbTRaemFjRWUxeE0zZmgwRGxVWEtsN1FaQlJDeVdNd2p1djZkNlQxTEY1emJXa2xsaFNWT3FjVlBuOWV6U09WaXpabHVITUliVVZqVzNGOWZYYWtHWHdXRS9OOWZuTGFIUGNSYXN2eHQxL0ZiTDhpVzFDVWNHYmVjWTQ1Q0g0QmlRSzhzY3dwbHRUK0RuZ1dyT1BFK0NIY0NyUWx2Y3NRbTNsZnBablFhUWJPMUNaZDZpRDMvR2YrcGh2WEdYcFNBQ0NCWVdzNTFHQm8xUWxpUFhKV2crVThjMCtlNlZRcFlUZGREK1cwazB3dnhDQzJBREk0a2lwZDh5bm5tcUVHVENOTzVjVTdjWUhLVjYrNGlwdTQ5bkM5TnpWeVQ0MG5BMmcxVXAwcUh6bzRKZUY5MkQxOUpLUmtOQTRubkdBakcxQnR1b1NENUhZZWVqVnpsbFV3VmU2ZEQiLCJtYWMiOiI4NzFhODZiNzdmNDVlZDQzYTI2MjQzNTc4N2I1ZDcxOTZiNjg4ZTBlOWY3YTA0MDM1MWY0NWYxMTlhYjU0MGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135113841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-368752120 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxHTW9WWEtzTnpHbGVwYlRLcjA3UHc9PSIsInZhbHVlIjoibzAyaDN4ZnhHWDQrd0lkdjY5Tzc0VmJwTjZkcnVubTJEaUxER3g1TzcxQklYNVFwWHVaMUMxMFBWN24vTmF0aitUZk9LSzVTd3J2L1pCMlFuamRsbXNVSG9Jc3c4OEphNXp4bU15YkFsbnFTNFRXWDhFemQrQXZXRUNTa0svNzdFSFZqQ1VTTUt1dUplaUJ0U3BYc3lwNFMzNmg5bVhGK2N1L2pCNDB1Ti9QS2h3eW1oNmhzdnlEZjRWNm9qdVFWUHpmakdKK3JWcGEwWStCZEppNmUyYngxaDRPaXA1ZHFqbG50cFpQb0IwWEtSZ3RCdElWOTE3UWprZTF4RHZEeUpGQlFVNFJlTjRQd2xZQkdrK3EwOFlYUjd3OVV1NnMwWmo0aVdRWlZpZldGaEFCK25qVmJJMnRpTkZ2Q0ttMVRlTlhDN0ZzM0V6b080dTNSei9GcWJkUUcydWQxQm1QaW9acjVnbUlocGNSb2Z4Mm5LZG9LdW05RVNnOEN6Vkc0QjllZVI5M29rc2FqOUtuTE15dUVBWEpGMWNBblR2YmFUdU9hckF3RGF4VHJOZE9FVGo4anQrM1VQZGlxenlqVjVBQm13QktCR1QvMXR2M1VVaGwrWHhNSS9uZSs0LzBLcjdHOG9nT3FnS1IyckJHdGlGVTRMcHo5bHJiQ3JrYXAiLCJtYWMiOiIwNDZjN2Q2ODk5ZjAyZDM4MDRiNzFmZmVmZjA4YWVjMmZjNzUxZWE4NWE2MjRhYTQzYWYzNDA2OTMzNTA4Njg3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjEzcExydmo5Z2F2TUdtRm0vWkN0VFE9PSIsInZhbHVlIjoiNVhOQUJWSFRaQ0VpUDg3dnhqZ1VIai9MK2hFSG1qd043VFdQVXpxejlHMUlQejRCMUpxc1dPS2pWMDZaNVR6STFXS1RhbGlBWHRISENKcUltSS9VVUZCY0tkRytMS2FqT0NtNHg0ZGN0ajZxQnNjWmFUN1V1YWE1QytaeXhMdm9kNW92SXV1aktSK3JsN2E1Y2JuOTdEdXkxK1lha0dJSUhpYmNuZHJIVitmVGdiVWNBNndlemRpTWJVUjV4UHRWdjNhZVlIR21PbjBGZ3pmOGV6a0p6SVVjM1hTZmtLeFArUWxTQkgva1g0STUzUWttcGp4WnVwMWVxbDU2R1NETlZqZFpsdXhqREc5NlVDQ1U4aDZTcWJBZklhRkxjZHBhQVJPNG9XTUl5ZDZyditDNkZ0TnZKY29kMkVpajRDWUhiVGJVa2RIQnQxaW9wNDhTWncwaCtHZnRaUmxnNW5Vb252UlZJTFc1STBiVzNXTjVmcDJyOEsxV3k1dHpYdk84bFl0TjVyMjJqdEd6TnZXTXdlM0VUY0R3STNzdXRGWWNvcWt3bytyTVZjcDQ3QmlxY3RMRUZ0ZlNKZ29nd2s0RXg0N0R3TVYxUkF3QTRDc1ByUko0NTg2S05vQ1pRa09RTmdHVE9GZHZ0ZGZaaUoyN0puZFVHRUhnaVpWMURFbEkiLCJtYWMiOiJkZmQ5NmNhOWExNjQ5YTdkYjY4ZjBiYTBkZmYyYWZiZWE4ODdmMjU4ZDFkZGQwNDJhOWNhN2RjNWNlYzRjODdiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxHTW9WWEtzTnpHbGVwYlRLcjA3UHc9PSIsInZhbHVlIjoibzAyaDN4ZnhHWDQrd0lkdjY5Tzc0VmJwTjZkcnVubTJEaUxER3g1TzcxQklYNVFwWHVaMUMxMFBWN24vTmF0aitUZk9LSzVTd3J2L1pCMlFuamRsbXNVSG9Jc3c4OEphNXp4bU15YkFsbnFTNFRXWDhFemQrQXZXRUNTa0svNzdFSFZqQ1VTTUt1dUplaUJ0U3BYc3lwNFMzNmg5bVhGK2N1L2pCNDB1Ti9QS2h3eW1oNmhzdnlEZjRWNm9qdVFWUHpmakdKK3JWcGEwWStCZEppNmUyYngxaDRPaXA1ZHFqbG50cFpQb0IwWEtSZ3RCdElWOTE3UWprZTF4RHZEeUpGQlFVNFJlTjRQd2xZQkdrK3EwOFlYUjd3OVV1NnMwWmo0aVdRWlZpZldGaEFCK25qVmJJMnRpTkZ2Q0ttMVRlTlhDN0ZzM0V6b080dTNSei9GcWJkUUcydWQxQm1QaW9acjVnbUlocGNSb2Z4Mm5LZG9LdW05RVNnOEN6Vkc0QjllZVI5M29rc2FqOUtuTE15dUVBWEpGMWNBblR2YmFUdU9hckF3RGF4VHJOZE9FVGo4anQrM1VQZGlxenlqVjVBQm13QktCR1QvMXR2M1VVaGwrWHhNSS9uZSs0LzBLcjdHOG9nT3FnS1IyckJHdGlGVTRMcHo5bHJiQ3JrYXAiLCJtYWMiOiIwNDZjN2Q2ODk5ZjAyZDM4MDRiNzFmZmVmZjA4YWVjMmZjNzUxZWE4NWE2MjRhYTQzYWYzNDA2OTMzNTA4Njg3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjEzcExydmo5Z2F2TUdtRm0vWkN0VFE9PSIsInZhbHVlIjoiNVhOQUJWSFRaQ0VpUDg3dnhqZ1VIai9MK2hFSG1qd043VFdQVXpxejlHMUlQejRCMUpxc1dPS2pWMDZaNVR6STFXS1RhbGlBWHRISENKcUltSS9VVUZCY0tkRytMS2FqT0NtNHg0ZGN0ajZxQnNjWmFUN1V1YWE1QytaeXhMdm9kNW92SXV1aktSK3JsN2E1Y2JuOTdEdXkxK1lha0dJSUhpYmNuZHJIVitmVGdiVWNBNndlemRpTWJVUjV4UHRWdjNhZVlIR21PbjBGZ3pmOGV6a0p6SVVjM1hTZmtLeFArUWxTQkgva1g0STUzUWttcGp4WnVwMWVxbDU2R1NETlZqZFpsdXhqREc5NlVDQ1U4aDZTcWJBZklhRkxjZHBhQVJPNG9XTUl5ZDZyditDNkZ0TnZKY29kMkVpajRDWUhiVGJVa2RIQnQxaW9wNDhTWncwaCtHZnRaUmxnNW5Vb252UlZJTFc1STBiVzNXTjVmcDJyOEsxV3k1dHpYdk84bFl0TjVyMjJqdEd6TnZXTXdlM0VUY0R3STNzdXRGWWNvcWt3bytyTVZjcDQ3QmlxY3RMRUZ0ZlNKZ29nd2s0RXg0N0R3TVYxUkF3QTRDc1ByUko0NTg2S05vQ1pRa09RTmdHVE9GZHZ0ZGZaaUoyN0puZFVHRUhnaVpWMURFbEkiLCJtYWMiOiJkZmQ5NmNhOWExNjQ5YTdkYjY4ZjBiYTBkZmYyYWZiZWE4ODdmMjU4ZDFkZGQwNDJhOWNhN2RjNWNlYzRjODdiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368752120\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-837232260 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837232260\", {\"maxDepth\":0})</script>\n"}}