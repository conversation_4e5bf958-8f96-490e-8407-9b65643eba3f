{"__meta": {"id": "Xaf545d124b727578655723c76fdd5385", "datetime": "2025-06-30 18:36:54", "utime": 1751308614.006323, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.40963, "end": 1751308614.006342, "duration": 0.5967118740081787, "duration_str": "597ms", "measures": [{"label": "Booting", "start": **********.40963, "relative_start": 0, "end": **********.867859, "relative_end": **********.867859, "duration": 0.45822882652282715, "duration_str": "458ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867873, "relative_start": 0.45824289321899414, "end": 1751308614.006345, "relative_end": 3.0994415283203125e-06, "duration": 0.1384720802307129, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 62519176, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-508</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.05209, "accumulated_duration_str": "52.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9211912, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.571}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9317431, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.571, "width_percent": 0.941}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.934939, "duration": 0.04974, "duration_str": "49.74ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:487", "source": "app/Http/Controllers/ProductAnalyticsController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=487", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "487"}, "connection": "kdmkjkqknb", "start_percent": 4.511, "width_percent": 95.489}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-361508560 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-361508560\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1929886459 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929886459\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1199872850 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1199872850\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-214185680 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=1bujxd%7C1751308417876%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpMUlRtVzZZVS9qVytFT2FEUHk0M0E9PSIsInZhbHVlIjoiV2RUTmlRelJGMFZ0ckUrRlo5MXQ5ZnUvOTN3bkl0aDRjNldtTndUZlBBbXZxYzFiWkNXaTZyMWRmcm00ZE9PMmlKa1hhaEhNUFZXcHJ3b0d5cXV4aGowb3NLek9PazhrSEFTTHlEMzAxM0J0aVNaSys0bnl2WTAxWkNMYmxWVllUVjRXSy93ZnhYWXNZWDJJYklod3Y0MUNweEpvVDl3dUxDeWpZeGpheEZIek1oOHZPVWo2d2VDSW12NVNLMEJWVmo4UWNVTHAreDVxb0YzSld2eDFPUEJjbEZvTnN0Tm9WZ0VYVVMwendtQjZ6bXFKVWszQ1E3U3NHKzRMSE9BclVsaGVoTGQ5UUNLeXROUGRiUi9BUmxFQ0hybzZnZEp6dlJoUHdOZXgvSWZWMHptbEdlLzJxTTBqenhHT1NxUmVhYzRBK29SUUJtYWtpbi9uVEZETDh4SzI1R1EwRGJrbkZ4SXZsK3ZndmxRdHRpVFgySGZlOTFzaUhUY1BNRVZjM2N2eXI2OXozemZnZGF3Zi9tdDBBdkswUkJCby9ibit5ZmxaTktGWjlySTZTdzJOWlBxN2U1cC9sK0VHbWY1RFkyZFRHc3V2dEZ6TjdqWGxsQWRMZ2tBNHB6d2J0bjVmNy9aaTg3S1c3OS9JZ0NCTkd6ZnhhQlptSnlnSWRBL1kiLCJtYWMiOiIzNzJjOTg3ZTg0NjE5MTcxNmY3MTc5ZGYyYjFkOTZkMjUyOGRjNDU2MjBhN2Y3OGEzOTUxZGNjZmI3NmYzMzZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhkOXB2d0Mybysxc1JQTEYycXdTZGc9PSIsInZhbHVlIjoicEJQeEJ2YkxxY3pkZUs5TEp4ZUN0RXBEMFRONFFhdkVWZ2FsaGpYRE9waTQ5N0FkVEJpY1JSaVUvN09vUzhHVk95TXhmckg3WklaS3JHMEhjbkgzaG81Y3NqK3ltMnNmN1IyNGRlc01kYktsd3B2YkpGWkpZbVE1d1VPcmdmcjFUWnF1UGRFNCtwVmdjMDZhYUVWOXI3R1pNR2RwbWZsTHZNU1hqdXRTUnhpSTEzRDFncmtBTzNjd0gzaTNLa0RlMFJxNjY4N0RWRmZKcFl0SW5pR0IzRkczUjFCNk5VSDJCeGkyQzN5MElMbmxhN2ZQcWM3aXVCUmJ3M1hrK2Qyd1cwR0pUS0oya0lRVzEwWUc0WWNwcldpYjU5RmVUdWhRVS9yZkVvNERWVzVrSTFLdXk1WGlXcXFLRFRuVlp0eEI0OTRNTFV1dFhkbi9BUWdzTHg1d3lLcTJWNDJrNEVhNlZtQndBdjZqeDdsbWNOTWlDdkdFYktVeDk3ZFYvSkI1SlBjRjZPM3JOczZoNFpkdVFKVXg5T3VXOE5mRU1vRXRmdG9KZ2huTmg5cmh6SDRuVTJNd0o5VjhCZCs5M1FzM3dtb2Q5bUZpbUErQzh2TlJyV1h3WXBqVWFzdE9HOEZwL1RFQmNiYi9wb1RLckxtZmttaHlMN0o2YUJCTVFQVWgiLCJtYWMiOiI5ODVlM2M0OGI3M2Q2NjMxNmZiOGI4YzA2YTkwNDkwYWM2OGEwNTg5NzRmNzk3YzIxMGM1M2Y5M2NhMzE2YWI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214185680\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1028094594 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ItgBYbuMCIK2tVeknPfSFEKDbiqeLu0VRJnQpMxQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028094594\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-485243056 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:36:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZRbTUrZlgwUGtMNmNaZ05lVnR5dEE9PSIsInZhbHVlIjoiejc3bTJDbTJ4SFpsb0QzT2RyZ3ZKdlNXQ0NReHAxTzVGQWZQdjk0UDB6eWNFNDcxbjN2cHYrMVlpdldhZnduQXo0ZDQvNjFPMDRGeEd1bStDUXhTMVRoa0psNTZBbDRtVUNVZHhaMXpNekxsZnRla2c2bVdraGw3TkllaFBCbDBNL1JyQVl1UVc3WERmWGFmQXhyNGFyeS9JSDd3SFVzZ2tZZHVibnVNR1lNTVBQL1c1L0tXYzJKWFZwalBHS252eFhGZmVYMlBhYVdQRnB1eThLM3c3SXd1VTVvdTN6ODZoT0VuaGRQUm85VU11MnBtd3ltUkR0ejZGU0NjQmpLUmg5M2ZuNFBLVUhxK1gzUHF2Q0FmRExCdmdSWmtOSTl4MGNBWHlBR2pVOXpoYjJTQW1UZlB4V2VaQlMzRHdDNDBTTGtxY0YzM082bDljVXc5SHJPaDhWQmgvekFJT0hPaHkrSmoxcHRKWDU2Lzg5SjZGWDh0SWVMUFNmek5KNDdDUit5QjhuQUFEc1NkT1N5MldocUorNFVUbklqM3JpK0hndnJVcnBxcENYclVCT2NoY3g0VHBUMzBLWlorcmVSak1tdCtqY3QxdkwzY3Q2eGE1dEVPazI1ZzdNcUZvZHV6aUc3Qi9qSlh2N1BtMjRVT1lJTk14d3NoSzVMdTcrdlMiLCJtYWMiOiIzNTFlMWRhMTA2NGE1NWVmZmE1YWEzZTgzZWMwYWNmY2M4MmVmOTZiOGI2NDQ2ODkyMzlkNTg5ZGI4ZmYzYTFiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:36:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImI3YlNPMVphamhXUVZSOTMwRWpkMlE9PSIsInZhbHVlIjoiTmxCT094dGU2b2lZc25vajJPQTdqNnUvRXF5TXY4OThxeVBYOElqN3N3emdVeXFCd1BSbmIrUWdNcFFiZTBXK3pHZ2VObi8xVWF2TEdvMGxPZUlUT3Rub0lRc05hS3o1eFh3c0xhSnMwc0haTmZ2T0hXSUFUT1A0dFpRd05tYkJ5SU5hV2FEL0RrNGlTNmJ6MEszSEpZeU5CaGpjK3Jpc2NTb0c4WlpxekJ5NVZXUjFkT2U0a3lVN2I2RE9mL2w5d1d3SzNHcUtVM21BUmVKQ0dNUmtNdjJaNFZ3Yko1RXhjdkdTVW5QVEJZenpidk1IaDQwNFhsWU9pMXcyRnhmWDk3UTJQRkNmaU05dG9VM2UyaHE4bzEwL3lLa0tqclgzUHNEM1RrNysyZVFiZXRDVGlvV2xCc0ZTblVkU1ozcENkcTU3Y2JrM2lvYWhxSytpdTl2V2JtRXFvU25paDR4TkpiOWN4aTlUeXdGSnVYcXEzSnpDd2xyOGFtc0cyNGR6aXhKVHBzdUt4S1pQem5keEtzRXczbEM4UHhPVk9ibXZQbnlVNmRscnFsVFgrUWlKOE1KM3k4MEt2ZG9VeE9rc1FYVkJRMENLdHpPWkNKa3ZpWEhwNmt0c0VWeXZLYlRlU0dlWkJPMXNLWkVZTG1FZEtVNkM5bUljQ01IQjZWcS8iLCJtYWMiOiI0YjQ0M2MwNjIyZjYwZGIyYWYyYjYzNzJmMDg0Zjg1OTA2MTAzYTYwM2MxYmI0NmY3YjE5NzQ4OGFiOWMyMDkxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:36:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZRbTUrZlgwUGtMNmNaZ05lVnR5dEE9PSIsInZhbHVlIjoiejc3bTJDbTJ4SFpsb0QzT2RyZ3ZKdlNXQ0NReHAxTzVGQWZQdjk0UDB6eWNFNDcxbjN2cHYrMVlpdldhZnduQXo0ZDQvNjFPMDRGeEd1bStDUXhTMVRoa0psNTZBbDRtVUNVZHhaMXpNekxsZnRla2c2bVdraGw3TkllaFBCbDBNL1JyQVl1UVc3WERmWGFmQXhyNGFyeS9JSDd3SFVzZ2tZZHVibnVNR1lNTVBQL1c1L0tXYzJKWFZwalBHS252eFhGZmVYMlBhYVdQRnB1eThLM3c3SXd1VTVvdTN6ODZoT0VuaGRQUm85VU11MnBtd3ltUkR0ejZGU0NjQmpLUmg5M2ZuNFBLVUhxK1gzUHF2Q0FmRExCdmdSWmtOSTl4MGNBWHlBR2pVOXpoYjJTQW1UZlB4V2VaQlMzRHdDNDBTTGtxY0YzM082bDljVXc5SHJPaDhWQmgvekFJT0hPaHkrSmoxcHRKWDU2Lzg5SjZGWDh0SWVMUFNmek5KNDdDUit5QjhuQUFEc1NkT1N5MldocUorNFVUbklqM3JpK0hndnJVcnBxcENYclVCT2NoY3g0VHBUMzBLWlorcmVSak1tdCtqY3QxdkwzY3Q2eGE1dEVPazI1ZzdNcUZvZHV6aUc3Qi9qSlh2N1BtMjRVT1lJTk14d3NoSzVMdTcrdlMiLCJtYWMiOiIzNTFlMWRhMTA2NGE1NWVmZmE1YWEzZTgzZWMwYWNmY2M4MmVmOTZiOGI2NDQ2ODkyMzlkNTg5ZGI4ZmYzYTFiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:36:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImI3YlNPMVphamhXUVZSOTMwRWpkMlE9PSIsInZhbHVlIjoiTmxCT094dGU2b2lZc25vajJPQTdqNnUvRXF5TXY4OThxeVBYOElqN3N3emdVeXFCd1BSbmIrUWdNcFFiZTBXK3pHZ2VObi8xVWF2TEdvMGxPZUlUT3Rub0lRc05hS3o1eFh3c0xhSnMwc0haTmZ2T0hXSUFUT1A0dFpRd05tYkJ5SU5hV2FEL0RrNGlTNmJ6MEszSEpZeU5CaGpjK3Jpc2NTb0c4WlpxekJ5NVZXUjFkT2U0a3lVN2I2RE9mL2w5d1d3SzNHcUtVM21BUmVKQ0dNUmtNdjJaNFZ3Yko1RXhjdkdTVW5QVEJZenpidk1IaDQwNFhsWU9pMXcyRnhmWDk3UTJQRkNmaU05dG9VM2UyaHE4bzEwL3lLa0tqclgzUHNEM1RrNysyZVFiZXRDVGlvV2xCc0ZTblVkU1ozcENkcTU3Y2JrM2lvYWhxSytpdTl2V2JtRXFvU25paDR4TkpiOWN4aTlUeXdGSnVYcXEzSnpDd2xyOGFtc0cyNGR6aXhKVHBzdUt4S1pQem5keEtzRXczbEM4UHhPVk9ibXZQbnlVNmRscnFsVFgrUWlKOE1KM3k4MEt2ZG9VeE9rc1FYVkJRMENLdHpPWkNKa3ZpWEhwNmt0c0VWeXZLYlRlU0dlWkJPMXNLWkVZTG1FZEtVNkM5bUljQ01IQjZWcS8iLCJtYWMiOiI0YjQ0M2MwNjIyZjYwZGIyYWYyYjYzNzJmMDg0Zjg1OTA2MTAzYTYwM2MxYmI0NmY3YjE5NzQ4OGFiOWMyMDkxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:36:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485243056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FzV7IxHutMHO2bLABZqHFUL7MNtRBR7qIeUxzlkC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}