{"__meta": {"id": "X32420a9eb952900c146b37cb1d2e840c", "datetime": "2025-06-30 18:49:36", "utime": **********.192533, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309375.777268, "end": **********.192548, "duration": 0.4152801036834717, "duration_str": "415ms", "measures": [{"label": "Booting", "start": 1751309375.777268, "relative_start": 0, "end": **********.130848, "relative_end": **********.130848, "duration": 0.3535799980163574, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.130858, "relative_start": 0.3535900115966797, "end": **********.19255, "relative_end": 1.9073486328125e-06, "duration": 0.061691999435424805, "duration_str": "61.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43885848, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02121, "accumulated_duration_str": "21.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.162052, "duration": 0.02087, "duration_str": "20.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.397}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.186081, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.397, "width_percent": 1.603}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-602073278 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-602073278\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-381487077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381487077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-23686245 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-23686245\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1931272213 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309344290%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNsVmtXME1GL3phalFLTWN4WCtRelE9PSIsInZhbHVlIjoidjhDME9vZVdlZXlKV3ZFTFFtb3FnM2k4OU9TZ3NDNXluci9iY1NteUpmWXYzVUEzaHFjQ1hpM3IxcFJrdXpZUXgxNU9VWkVjQVc0dE81dzRZOW5Jb3BSVjRjRHdwOE5PbktLbXoxc2JqcTM5cW1ZdDNTU3I4NnZRdnh3d1RiMkZ3N0Q0U2ZGa0V3blZDdG55enVWVGJzUFFMbkltNHJLY1hBeFNKQXhEK1B4ZktHK0VxbmZCN1pZdDYyK0dkakpOblZIL2hiWTJqV3ltbjhwSExoRWdwUDlXc3I0TkpTdGhZaXZrSXVrdDJPTDVtdTNoelRadkQ3SS9pR3FZektFb09xM3FseXRFVHlwdHlCT0pMdUVCVncxRWpvQ0ZCYzBHaFYwUXcwNy9haXYwUTJObkY2dnpsVDVTelV5U2xkR3c3dEpMRVdnb2FsZHJTRi9JSjAyVEFSZjgxLzQ1NG5STWpOTE9XSDJxRWpCbFcwME0vZEFMS2pRVXhZUjRqQ2lLVXVzODZtTUMvZjUxVmVETUEzTjQ4SWxUVlhseFBuRlZHeWNqYXVOMzcwZXFheUJ5L2VjOC9zV3VyaGdBM3BRNjZSRDZXTUR6eE9PRSsvcXJRK0VvdWpKYmJiVTRDV1M0N0kxQnRpQ0V1MDBPK1JVZThTS1RyYVVPNlZOcDJFNU0iLCJtYWMiOiJmMTM5ZDA3NGZiMDlhY2Q0ZmY0OTQ3MjAwNzM0Nzk3ZDllMzYwMjBkNDZiNzRjYjU4ZGM2YWZjYTExZmJiYmQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlleFkvTFJZb1Ura2FEbTFSMUEyR1E9PSIsInZhbHVlIjoiZDU1a0NSZVN1cWk0YmtrdG1HUFQ0V2p4TC8xMVZBR1FSaTc3TnlsNjA3T3E3dHZuVlM5dG9TbTIwTGlwdkRFQlJoWVBTV3JCcFdYZGRzQ29GcnkraVZnNjZ3Y0M5MjlHbVFwNFNzSU91NllEbVY3dHAwUjAxckdNVDVTTGt4cEtzSktXYkZYSTlKRnJZa29MRHFvY0ErTkV2bGM1SGMrUERjR0UrMnRaM1hvR3N2TTBOcmdhTTBzZmlISHo3WVlNazRGb2F3dHR3SVVXU2JmMHNGeW9yQUZPaVlKd050YlNPU0lCMmtJMERnWmNOSDZDbEVxdktQdkpSQVpLcEJhQU41eE9XamRwRGJDSXplK0ZndXFYZ2tiNEVnUE1MTHk2c0hvZnE4ODY5d0duV2dld3Z1c0tLUFlnekJoc3A2eXRpS0RDZGJ1Z3c5T0VDWkJZdUlDYnNheVJhQ3JhcGJmcWFsVGR4WlA3QnJYa0VmY09JSjNHRTlMbmdNTG1IRHVVMW9DQ1hOU3Q5VlRHbkMwTnc1Qy9NRExMRUhQRHhVZDhwTnZlTGpndHJlMmhHVERvVEh6a2U5UTc0cUx0TUFRVm1GWGozcmUvTFU3VG43ZjRSWUgxUmI4THZzd2MzUWQ2TlYyZ1NWSENKdXR6Z0V5Y253d2s3RU5Id1ZLZXJpd3MiLCJtYWMiOiI2Y2MwZmI4MDgwODNiNTFiNDQwMjE5ZTBhZmE3NGNkODI0OGI5MDYzZTIxZTA5NWY4MDVjNWNiZjYwOTQxMzU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931272213\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1315610069 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315610069\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-946375033 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg3clEvdjRmR0ZLdm0yS3NMMVF1dWc9PSIsInZhbHVlIjoidHZFSCtFeE1DRGNlQkZxdG4wRnBjM0tGV0c2TXNaTUEwa2NRYTNQc2hab0J2QU95aHpyR1REeHg2NE9pOE9uQkhGdXNodnlUMjNGWUJoS3BjNkY4WGRTdllDeVk0d0VYT1dUZStSa255d29yUjBHQ1dJUzY4WjRpeFFEcHpFNjlRYk5vNC9CZkVwR3dsam1ZdDBUcmRrQmExYUNRODYrV3J3dHBRQnFuOThvcVRaS0Y0TW03Y0t2Q3ZDWXBXOGkzMFp4QTUzMmFLYjE4cTdpeUtFSnJobmZSbVIyRjU1TDlPaGdMUUp4aEo4ZWxNWVQ1bzBtL1pBc2pIL1VRa3ZxRUhQRmNEQ2FOTlZEdnZZemVIYXBzc1VrTEMxRWdVTVJRVzltbE9JeGhiTnFuNjFwM29yMWE3SDZ6RDQvYk9kTm8vSFZaS1E2eXFHVnhWNHFzREx1T2IwbkxaQzRWdHFnV3VkdXh6N1Zua0w3VFlaTDE5ZURpbzJ3amcwWVpQYU9KaW4zS0FFOC9qOHVhSGQ0Qmo0VmJTVjBMNnppVzNkVmFzQmRjTS8vMFFmdGRBcDNWY2hTeWpCbWs2YS9PaThabDFXdUIwaGpySlQ0ZUdGU3NRNDJybjhGeC83b1krci9PMEN2Y0lrSUI4d1VJMVk0WXhjWGk3Y2RYWmFITG5VZDEiLCJtYWMiOiI3YTQ3NDYzYTllZjZhNGIyMjllZWY1MzE0OGI2YzU4NTlkMGM3MTdhMmE3ZjU4NDY0NjIxMWIwMjM0Yjc1ZDk1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjQ0c2NDQ2lPMVBuRW9CL0pIMnlicEE9PSIsInZhbHVlIjoiY2hCczUvN1R3M0YvRlkwOEpHampiNnFLclFMeXRwbXNCeGk5VmNtTW5OY2tZWXNENXhhY1AxRzNheDlDeDQzU1pwRHBqU1FFMFRvaStsU2pQdFFHVlRrMEk4cktSODJKTWxiU29lMW9wVDBURU9sVjArZ0pHbVRkbVlqV2txOEZJaGlPK04vT3Y1KzlhbTl3ZXVrZjJaREJvQ2JGMk1rVHNEMTBqUFRmckNLWmpTYk14Z3RBNzYyTXRkQjBQY1hkQktEU0NDYnVPaWV6R0VzQThkUnV1U2VndzF3QWFHUk9seGI1emhVWFAxVjJtRXZpQ3lQYUpBTXQ1eWFmYTFqR1hxOGxFMGNaZExiTVJOUGNSUUpyUHdvLzkzQzNKYkRaOWFhSktTZWpITENrNDR3QjRQTUl3QUpGWFlQNTFxOUREQzRKR0Q1Z29NVDJoa1BNM2VPaUtiMVcvc3lCZk00OGpoMnJ0QXc2TlBXYk5DdTNvTGxaZFFYb0pDWVZsN3ovSlJJamN1WFVGTjM2ODJyQVl4bENCQ0JzWnRNWDZ3LzNsZ2x3M2F3S1hBYk82NTAvK1dGcTJhSEZIdlVNMlJnaGVrSWo2Q0l5eithdmN2ekJCKzExWlZzY1FrYlVHUmwxdzk2SjFDdDJCeEVCQUFNZ2xxc3hGSWY5R1NPNXR4cHgiLCJtYWMiOiIxZGEzYjI2NGE5YjdiODVhMGU3YzI5OTAwZTAwNWQ3YTg0OWY2ZjIzMGE0OWY0MjFkYmIwNzMzNzZhNDUxNWYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg3clEvdjRmR0ZLdm0yS3NMMVF1dWc9PSIsInZhbHVlIjoidHZFSCtFeE1DRGNlQkZxdG4wRnBjM0tGV0c2TXNaTUEwa2NRYTNQc2hab0J2QU95aHpyR1REeHg2NE9pOE9uQkhGdXNodnlUMjNGWUJoS3BjNkY4WGRTdllDeVk0d0VYT1dUZStSa255d29yUjBHQ1dJUzY4WjRpeFFEcHpFNjlRYk5vNC9CZkVwR3dsam1ZdDBUcmRrQmExYUNRODYrV3J3dHBRQnFuOThvcVRaS0Y0TW03Y0t2Q3ZDWXBXOGkzMFp4QTUzMmFLYjE4cTdpeUtFSnJobmZSbVIyRjU1TDlPaGdMUUp4aEo4ZWxNWVQ1bzBtL1pBc2pIL1VRa3ZxRUhQRmNEQ2FOTlZEdnZZemVIYXBzc1VrTEMxRWdVTVJRVzltbE9JeGhiTnFuNjFwM29yMWE3SDZ6RDQvYk9kTm8vSFZaS1E2eXFHVnhWNHFzREx1T2IwbkxaQzRWdHFnV3VkdXh6N1Zua0w3VFlaTDE5ZURpbzJ3amcwWVpQYU9KaW4zS0FFOC9qOHVhSGQ0Qmo0VmJTVjBMNnppVzNkVmFzQmRjTS8vMFFmdGRBcDNWY2hTeWpCbWs2YS9PaThabDFXdUIwaGpySlQ0ZUdGU3NRNDJybjhGeC83b1krci9PMEN2Y0lrSUI4d1VJMVk0WXhjWGk3Y2RYWmFITG5VZDEiLCJtYWMiOiI3YTQ3NDYzYTllZjZhNGIyMjllZWY1MzE0OGI2YzU4NTlkMGM3MTdhMmE3ZjU4NDY0NjIxMWIwMjM0Yjc1ZDk1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjQ0c2NDQ2lPMVBuRW9CL0pIMnlicEE9PSIsInZhbHVlIjoiY2hCczUvN1R3M0YvRlkwOEpHampiNnFLclFMeXRwbXNCeGk5VmNtTW5OY2tZWXNENXhhY1AxRzNheDlDeDQzU1pwRHBqU1FFMFRvaStsU2pQdFFHVlRrMEk4cktSODJKTWxiU29lMW9wVDBURU9sVjArZ0pHbVRkbVlqV2txOEZJaGlPK04vT3Y1KzlhbTl3ZXVrZjJaREJvQ2JGMk1rVHNEMTBqUFRmckNLWmpTYk14Z3RBNzYyTXRkQjBQY1hkQktEU0NDYnVPaWV6R0VzQThkUnV1U2VndzF3QWFHUk9seGI1emhVWFAxVjJtRXZpQ3lQYUpBTXQ1eWFmYTFqR1hxOGxFMGNaZExiTVJOUGNSUUpyUHdvLzkzQzNKYkRaOWFhSktTZWpITENrNDR3QjRQTUl3QUpGWFlQNTFxOUREQzRKR0Q1Z29NVDJoa1BNM2VPaUtiMVcvc3lCZk00OGpoMnJ0QXc2TlBXYk5DdTNvTGxaZFFYb0pDWVZsN3ovSlJJamN1WFVGTjM2ODJyQVl4bENCQ0JzWnRNWDZ3LzNsZ2x3M2F3S1hBYk82NTAvK1dGcTJhSEZIdlVNMlJnaGVrSWo2Q0l5eithdmN2ekJCKzExWlZzY1FrYlVHUmwxdzk2SjFDdDJCeEVCQUFNZ2xxc3hGSWY5R1NPNXR4cHgiLCJtYWMiOiIxZGEzYjI2NGE5YjdiODVhMGU3YzI5OTAwZTAwNWQ3YTg0OWY2ZjIzMGE0OWY0MjFkYmIwNzMzNzZhNDUxNWYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946375033\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-70624730 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70624730\", {\"maxDepth\":0})</script>\n"}}