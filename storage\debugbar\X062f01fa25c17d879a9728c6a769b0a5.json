{"__meta": {"id": "X062f01fa25c17d879a9728c6a769b0a5", "datetime": "2025-06-30 16:22:25", "utime": **********.783481, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.349156, "end": **********.7835, "duration": 0.4343440532684326, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.349156, "relative_start": 0, "end": **********.712502, "relative_end": **********.712502, "duration": 0.3633460998535156, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.712511, "relative_start": 0.3633551597595215, "end": **********.783502, "relative_end": 2.1457672119140625e-06, "duration": 0.07099103927612305, "duration_str": "70.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50913216, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00743, "accumulated_duration_str": "7.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7418592, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.611}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.752095, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.611, "width_percent": 6.46}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 16:22:25', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-30 16:22:25' where `id` = '25' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:22:25", "22", "2025-06-30 16:22:25", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7625601, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 29.071, "width_percent": 35.128}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 16:22:25' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:22:25", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.767621, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 64.199, "width_percent": 35.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-556349488 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-556349488\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-114983431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-114983431\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-7097373 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7097373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300543798%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxMOGJGNi9hcnpuNWFlMTYwL2RpN0E9PSIsInZhbHVlIjoiMlhlUFRHVmw3SUtRNzNHM3RycVZPditXUVZ0eVVhWml4bmxjNG1ROVJDRE9DYk5abVdPZ2pqdHVGTTdSekhEcUp1dDhDejBxclhrWXFRV2dVa2l3QzRJVjJ0NDRzSnFGNVNobXQ4YmsyYUNsaGxFTzdRck5FYVl3Rk10WEJWMnRiU0pHa3ZwTC9VM3Z6WlhDQ0Z4a04wT1BJSVdsTlB0aW5VbUhjQ05QUCtWT0lFTDVQUlB4alBtQjN5V0ZwVTZRcjBBYUUrSk5OK3NCTkZFaEMvM2ZlY0NWcXErbUU0WEg5NUpCVVY4UDdBWjV4NHF0R3ZiaFRjV1RwSTF4K25CNlg3ZXN0dHMydkRQMmhyK1BLWUNvbXdSdHFUNEZKS3pDTzQ0YUJvQnJMRTlTdFNNR2JoK0tCWnZJL1VHUW82c2ZHY2w1OFRVR0ZzQTM1NzdyQkhtMHZtMjh3MTZ2aXBtbGh6MFdkTXhCcjgrZUZ0MVhFUWlPam4xbGI4V0xpZEM0YkdDOFJ3Y1hOcjJYSU5NVVNobnhkdlhTS1E4bjA1ZHYvZUVCOWRybHpZWTJGV1gyL0pseHpQTThia2hnbmdlajJQcGR5L0xRZ2Fnb1lvaWZ0MVFaRndFSitpTWFsL24yaU5aUnZOakxBcWRDVFVCemVRUWM2MU1ZZjExQnY4cWMiLCJtYWMiOiIwY2I2MTg1NDc3ZDE5MTE1NjJkMjBlYWVlMDM0ZTEzYTQ0MDFiNDkxOGU4ZTU5MDFkODEyYTA1MGU5ODdkYWYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkE2TFNvWnZOM0NzSTVtaVdIREVxSmc9PSIsInZhbHVlIjoiWGpPSHFSOHJTR3c0dlVyU24rTzhGcUliQU9pOEpKcXlmMXdVQno5bTg2bUIwNzU5T3Q1RTVtQlRTTGdkZnV1SUs0bGdmbHBNeWp5K0s4WU9KNU5UN3kxblVoMkNodGJnMEtEQ0F0UmlsN3orM25WT0Z3djh4eE1XSzQ0RU5INW95Y2FWSWxIcHcvaTNoSHJPWFZEaFRaVFBySS9ORFhQVFlkRE5tMDFjZllSa0hxSjgxQnlmMVdzdU9tem5YRW5rMjlTajg0QUNhR3B5MHdaemVUVlF0d1RIcUtBRENiUENQdHNGWGpjZGhQUXl5eXI1Q1ltL2dFd1Jhd3BnaTQvT09YajRnZEpodTJIOGtNR2NLblBvR2UyQ3krQU4wRjVJODYrN3djaTBqWFcvWEZnQVpHUWRRWHJBb3FmZWxmVUlSTTdueTJuVjArZ3dOV0I0R1c0bmw0K0NpcHJVSW1HbzgrZHRvNGtxUFNUQ0RzS3d4aStFVmhtUWVHMEZNQWE1YjhSdGx5M1lzc20xRWNDNjRyVVhVSysyN2NWZDVVdDN1UDZKVE9FeFJuQUZ0dVdrNnpxeko1Mkp6NC82b0NjS0ZYdmVOTlkydW1pM3FaRnpQVHgwUWJhcFdoeWIyRk56eWFhWTNXbEwvUzArK2k2NnA4cUVzeTlQbi9tNEJwV2UiLCJtYWMiOiJjZGQwYTQ2MDNkOGM1MmZhNTZiMmUwYmRhMWJjMzBlNDk5ZWRiODBhYjJhYWEzY2NkYjg1ZjgwYzYyYjNlZjY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-953321705 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953321705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1703809621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InV3NVJpU0xSMjAvejUzRENGeTMrNWc9PSIsInZhbHVlIjoiYnFpem9PdjZHSTlZU0dhQ1RLa2pQUnorUHMxT1JjWmZRSnN2U1RLbU80bHQyMjVadjZxV3Y0Tm9mNWtlY2VuOFcvRE5hTzhxWVhYUEJsaEc0WUMyb0lmdkkrZ3BjYTZxT3VIdnR0YmdWVFhlOGZoSi9oT1BNR0tGRlNwK3FabllUNGI0MVdjSGk2YW9MSDd0TkpsUHYxYkNaRkljWktYbVR3ODdWUDNXSmlGMlVVMDZsYzhUdEhEZkUzT2xTR3hldzBNSVpHdzNlajNxS2w3UXhiQjJWbWZsS0MvK1d2K0FNaHBOMm5raWUrSlJCeU5vS0hyNmU0NVpoczhQQmVndCs3L1JXbTNReUZid0F0dk1uYVYwTU5YSkVmQ21MMlFlbVhSdVR5bEpjZld5VURwcXBhbEpNb0ZKZHhVWExaeXhrUWVtY3B2NkI1OFNyZUJNbkk1bGJGMGlFdmVjVlJkQ0F6YnBxeFlRN1pPYTFJY3ZlYVBTaDB1aTVuakJaWVBQd0p0YUhEUFhtNlBPK00rYjUzWWZsOXdlUll1ZVo0MXR1NkkvNXBXenpUZlc1aFRqL1FrQkJHYmlCVWo5UGxNbzBrUmlzdVhVdXZlN1Q5T0hRRHNnam5CV05CNFEyTkhYTjF1MDd3UERHcWFmTWFaVW9zcncwMmxwczYvRk1DQkciLCJtYWMiOiJiMGNlYjM1ZTU0ZTEzMGZkZTNiMGM5ZDUyMTJkNjNkMGMwODI5NjMyZjQ0MTA1NmZkNGQ1MzFmZjczNDRjZjE5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJTTjVSNHR4bnZDZTFPSFpRZDVhYnc9PSIsInZhbHVlIjoiSHhCdVNPV3MrRDkwUHNwZjc5T1F6WUpTTWRlbzZnWUtBK3dqaCtpakQ3QlVnMUg5eTAvSjhMcWtvb3padFM1NFQwRW4rQ3lxaHIxZnIwaW43ckdXL1ZXNGdmSEhpeVdjY1lSZGZNWnJLV0dzcFlEUi9mR01SaXJoU3lmY3dkYXQxamxZWWxyUFA5WmFmMVVSTWk5bXJMUUo0cDNxaktrOEVCMGtkU1A3OG4rblFCdDNQODJIbEJ3dTZGcEpZQUtHMHVSOEI2VSs0RkpuZUl2OUVJRlZXMjZlbGdyS1ZJQ1kvTjFZcmVDMGpSVzdqaVR6eDhYampsU0tBaTBSVmM4REN2MmhoTFhtNlBRRmZ6T1JlNUg4SmU1aUVXeUF0VTVINnRkRjVQYnpsUnM3QXEwVzRDYmpjMVVGa1ltbm1SRTl5aDl3N3IySk9pV1lWbFo1bERGSFhxNjhYTlhXekhoTzZVa0ZOOEVVZksvSDBmdEFnaE8xdkYwS2o5ZkRsM01WTVhIVjYrSE51VmxzOGsxdDRkSzBQQWNwSU83SlBPNWQrVWJKTjMzdnNDNG9Kd3VJYi82NllxUnBIU09oOGFYd1ZDZDAvWXVzRDM3ZVZ5aEs1anoxeUxKU0h3N29SNFJmVHI2RXQ0YmVRU01MdzNoY0lGMlZEcFlZWEZ1QUFPUGYiLCJtYWMiOiIzODYxZjFkZmMwNWJjNWU4Zjg0N2JiNTViZjA4MGNlODhmYTRjNzBmNTA3YWJlZGI2OTMzMjMyNTU2YzMyZmI0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InV3NVJpU0xSMjAvejUzRENGeTMrNWc9PSIsInZhbHVlIjoiYnFpem9PdjZHSTlZU0dhQ1RLa2pQUnorUHMxT1JjWmZRSnN2U1RLbU80bHQyMjVadjZxV3Y0Tm9mNWtlY2VuOFcvRE5hTzhxWVhYUEJsaEc0WUMyb0lmdkkrZ3BjYTZxT3VIdnR0YmdWVFhlOGZoSi9oT1BNR0tGRlNwK3FabllUNGI0MVdjSGk2YW9MSDd0TkpsUHYxYkNaRkljWktYbVR3ODdWUDNXSmlGMlVVMDZsYzhUdEhEZkUzT2xTR3hldzBNSVpHdzNlajNxS2w3UXhiQjJWbWZsS0MvK1d2K0FNaHBOMm5raWUrSlJCeU5vS0hyNmU0NVpoczhQQmVndCs3L1JXbTNReUZid0F0dk1uYVYwTU5YSkVmQ21MMlFlbVhSdVR5bEpjZld5VURwcXBhbEpNb0ZKZHhVWExaeXhrUWVtY3B2NkI1OFNyZUJNbkk1bGJGMGlFdmVjVlJkQ0F6YnBxeFlRN1pPYTFJY3ZlYVBTaDB1aTVuakJaWVBQd0p0YUhEUFhtNlBPK00rYjUzWWZsOXdlUll1ZVo0MXR1NkkvNXBXenpUZlc1aFRqL1FrQkJHYmlCVWo5UGxNbzBrUmlzdVhVdXZlN1Q5T0hRRHNnam5CV05CNFEyTkhYTjF1MDd3UERHcWFmTWFaVW9zcncwMmxwczYvRk1DQkciLCJtYWMiOiJiMGNlYjM1ZTU0ZTEzMGZkZTNiMGM5ZDUyMTJkNjNkMGMwODI5NjMyZjQ0MTA1NmZkNGQ1MzFmZjczNDRjZjE5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJTTjVSNHR4bnZDZTFPSFpRZDVhYnc9PSIsInZhbHVlIjoiSHhCdVNPV3MrRDkwUHNwZjc5T1F6WUpTTWRlbzZnWUtBK3dqaCtpakQ3QlVnMUg5eTAvSjhMcWtvb3padFM1NFQwRW4rQ3lxaHIxZnIwaW43ckdXL1ZXNGdmSEhpeVdjY1lSZGZNWnJLV0dzcFlEUi9mR01SaXJoU3lmY3dkYXQxamxZWWxyUFA5WmFmMVVSTWk5bXJMUUo0cDNxaktrOEVCMGtkU1A3OG4rblFCdDNQODJIbEJ3dTZGcEpZQUtHMHVSOEI2VSs0RkpuZUl2OUVJRlZXMjZlbGdyS1ZJQ1kvTjFZcmVDMGpSVzdqaVR6eDhYampsU0tBaTBSVmM4REN2MmhoTFhtNlBRRmZ6T1JlNUg4SmU1aUVXeUF0VTVINnRkRjVQYnpsUnM3QXEwVzRDYmpjMVVGa1ltbm1SRTl5aDl3N3IySk9pV1lWbFo1bERGSFhxNjhYTlhXekhoTzZVa0ZOOEVVZksvSDBmdEFnaE8xdkYwS2o5ZkRsM01WTVhIVjYrSE51VmxzOGsxdDRkSzBQQWNwSU83SlBPNWQrVWJKTjMzdnNDNG9Kd3VJYi82NllxUnBIU09oOGFYd1ZDZDAvWXVzRDM3ZVZ5aEs1anoxeUxKU0h3N29SNFJmVHI2RXQ0YmVRU01MdzNoY0lGMlZEcFlZWEZ1QUFPUGYiLCJtYWMiOiIzODYxZjFkZmMwNWJjNWU4Zjg0N2JiNTViZjA4MGNlODhmYTRjNzBmNTA3YWJlZGI2OTMzMjMyNTU2YzMyZmI0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703809621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1916883726 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916883726\", {\"maxDepth\":0})</script>\n"}}