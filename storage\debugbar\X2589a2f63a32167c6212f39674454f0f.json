{"__meta": {"id": "X2589a2f63a32167c6212f39674454f0f", "datetime": "2025-06-30 16:06:49", "utime": **********.967236, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.476778, "end": **********.96725, "duration": 0.49047207832336426, "duration_str": "490ms", "measures": [{"label": "Booting", "start": **********.476778, "relative_start": 0, "end": **********.891509, "relative_end": **********.891509, "duration": 0.4147310256958008, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.891517, "relative_start": 0.41473889350891113, "end": **********.967253, "relative_end": 2.86102294921875e-06, "duration": 0.07573604583740234, "duration_str": "75.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021480000000000003, "accumulated_duration_str": "21.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9204378, "duration": 0.02042, "duration_str": "20.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.065}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.953801, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.065, "width_percent": 2.607}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.959713, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.672, "width_percent": 2.328}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1949729558 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1949729558\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1790080616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1790080616\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1865802517 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865802517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2061701585 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299604028%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllveVBTNEdVNkFBUnFZL1g4akJqQ3c9PSIsInZhbHVlIjoiV3UwUk5QSVFVUEoyT25rYXhmNGpXNHJUSUgzaW1xRDhZT09KcW9TUkQ5SHhLOFpKRkE1Mm5CWXFlWkRjSFZsMUxjTHpsZSt5YjZMalBVTXhyMjhwTVZSdXRxc1ppZnpaN29WS2NxNTBNYVQrRjlqUEYrQXR1bXg5WjdvMmhvazlaeFV2V1FRTEhyU1N4ZUJ4K2NwTmxoZWRLYUN5L0ZtbGJGSEpLcmRYWXI4cFkxZFBQeE4vVTVRaDhORU1xWnAxOWwrQXlVVFdiUDByTWJhNENXT0d3Y3FmNit1TFFVSmxVb085SUVVYStLZldETDR1RmRPbTVjUTRoNlZFVURpS3BNVFNRSlc2ZUJFY2hVVlFpYzh6dGtESno1TGhuYXRwU1psRndSa2hYZDhjb0NhaHFWN0FKMnY3YlVkVHhCY1dGaHp5S1VCTDlSNEs0aS9FazZ3RXRERkE3d3FUS3BuTkFMMDZEUHI1bFUzVjZwMkpiSXc1czcxN2lPNU1YSjIxRk5EV0pSRGkwUVlaYWtCcER2LytvWWVtVk9jYkd5Mis4R2pHdlpkQmNFeUtUM1hRY29peTIyS2xLb05tODZUQXd0cmgrOVNjSzFCRFZtWjNVa1lrRVYxcFR1SjBtdXh5U29Gam1iQ2NYMWQ2NndxcDVxQytLY0k3d0cvK2MrWmoiLCJtYWMiOiI5YWUzODczYTUyZTc3MTU5YzBkMzA2MzQwYzJlOWNiYmJmYjUzZTE3YTBmN2RiYTA2Y2Q4YjM1MTAxNzFjNDIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkN3ckVIY0xnUDFHMHY3R2N0VzlOOGc9PSIsInZhbHVlIjoia3BoRUduSEhjMkFvYVdOdE1SdTlhMUxZdjhxSk54UlRmNXhoQ0VsM1RHK1loY3NQMkhPbzkyeDNKclphT3ZxM0trWEgzQnFiUk5INjFacEZITjlqay9zUmg3MlBUdjJMT0gwVDZzTXlOWld2UEhKR1JWRmxBSUZBc3pKbEp0elQ4ZFpHRVYxYXZwUkx5akdvRTMvQzdyTzBYOG9ONVFVVjJWcksydGl1Z3RxYWtKREJadFpmSDM0RGRLYVBLQTlrMEVnQmNpQ2ZqaWlWekZhTDRQdHZiNTJrbElhdmcwOE1ZOXRab3d1MzZkK1ZtYXNhZ2QwUEdtVWRheXNMa0hBM21DQTNWVXY4eDBNOFZCVDMzWUN0NU1JYlRHcGJmbkExMVlMdFN1aS9xTTZTZEt3TE1sWjVkRDd2SHQ3YzBZQjFqQjh1M2JSV1RHUEI2UDlWTmdNVElTZ0dURmpLaFBkVXJuRkJhRFByZzZJWm00S0Z3aTc4bHloc0xvMlUwTDBLam1TWkdWbWFrZTlYaUl5YkhkM3I2ajF1OW9oa1JwYXFlVXRjcjZzcElsSFcrYTQvSGxrclQzcWhvT3czbUFTU1ZyQUVtSm9pdVFwcnZIQm1OWXJXTWtnTkY3V3p3aXExNmdUYUNmUUhaUE9sU1NwQ24rTWtXTlNUbmY0T2JGbWkiLCJtYWMiOiJlNzdiNDIwNTkwOGQzZWZlN2U4NWEyOWUzMGM4YTNlYjI4MDE2ZDNlMWZjMzE4MzY3MzQ0ODVlNjg4NjcxYjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2061701585\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1842621735 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842621735\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-128507741 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndmY0MyamFHQXVwR0VBWWlVVWlnYlE9PSIsInZhbHVlIjoicWVKWWJ4UXFHamRYUEtEbUFoWERoWk0va1JrbnRiU05KdGhaTHBNU0d0WEJoclFiUnZTSTRjall5RzJmZlEyeXhleWo2U0xVRzd0azFBaWdNZ1pKNDdzcm44S0lBMUlraUFPcHczNVduTjIrak9LK09CcTRBZ0dMKzc4SDI0L0thZ1hsWEtxSi9aUmdYN05IUTBXSEIzcFhPcGRVcVE3QWRaUitXcDlFZldnTmxvRjMrQWh3SDhpSGR5eWhpcWhLcUozSHlna2xhdEpIVTVFZm9jUW5lNWl0MGt6bDFDTS9OWDE3eXRYa0ExYlZiVU5NanQzT2ZqMHJVcXY3MGlpSDlDSHZCcHVKQ014R0RCc0I5SEl2Z1J2REo1WnVyeSs1L0Z4eGZSdjRONkh2SDlYY2JxSHhjc3owNkdGT0FaSHFNSEh5ektLWmVWQUphOXhSKzBrM2NJRjVZMUhxNktGcW9ZV1Y3OGxFY2h4RDIxbkFXTTBYMzlYSlpuUHhFR2JUakliVmtNK0UvaHFBVDVpSnBJc0dwcmRDU2xIYnFmR3pHWE4zZy9RL1labTJob0ZZN3lrS1J5cWRsdWh1eHlmUTlBWVM4cWZnaDRVRk1rcjBJYkM0TXVndDBNY1I5WTAvTGFjSVFwU1R0MndLMER1WURHOWx0V21oKytsb2JVeloiLCJtYWMiOiJlNDFjNDNkNGFjMDNjZjEzN2NmOGI0ODQ2N2E3YTM3ZTc5MWUyM2M3YWFhZDUzZmNjMWEzNGI0ZWM4Mzc1YzFkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlR2STNGM3QyR0lnSDZIVVhNdmhvWmc9PSIsInZhbHVlIjoiYnR5K3BiM21qd3VONlR6UUJxbFFmazdYQ2xZQUoyVU5scXJRS1NscEl6NDgyMmk1UkpydVBVbWEveGd2MkFMTDRjQWNmVzR5Y2p6bm45VGJaaEJqa0oyZXdVa2tjV0FHVzl6Ujd6R2NxaVFWK0hZb2NrcUt1cEZOTWRtTVk0VGR5TlpndjFqd05EM3E1RjAxZFJBdUtrUlhuK1h2U1U3eWx1ekxhbHRxV1Jwbnkwb1hnSVhaOEtnTWtkbENZR3VqTEVGNklkMHB2NXEzRFY2dkNEelNLYkUvMk5ES0pPTHpnQS84VkdvajFiQkcrTC9ENlNyd1hDVzR5MU9JaHJBQjZ1UHU2K3JWbk1QWVlPc2c3RE9SN1NIMUg2cWtLc0dhUyswRHNIOE9nMjFwSklnNXVyL0owZHFnU1pZejMvblRBRXY0dlpZbDF1dFJGSjNNNFA3ZGRTR1pjSzZzK3RxVG9ycTNlVjdHVU1wR2JlaTJ0MWxuT0dmN09JUDJxcWRhbW1qdDU4NHVZdVBmcXJBZkJ5dzc0RytMNXhzRmdqbEZhVmVPRFVJS0w3cUk3VkRLZWRaZGNSTkdNeFRLTHlGVUZpd2xwSFJuMThiRFJtU1lXcVgxY3JmUDFKT3krelNibFArWDVqc2g1dmh2QjkwQlRjQzVtMDZzemlYbUl3YUEiLCJtYWMiOiIwOWQ1MDVlYzFhNWQ5NzZjMmU2YTRlMDE5ZDAxNTIyZDdmNjNmOTE3NmJkNWRkYTM4MjE2MGUzNzRiZWYzODM3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndmY0MyamFHQXVwR0VBWWlVVWlnYlE9PSIsInZhbHVlIjoicWVKWWJ4UXFHamRYUEtEbUFoWERoWk0va1JrbnRiU05KdGhaTHBNU0d0WEJoclFiUnZTSTRjall5RzJmZlEyeXhleWo2U0xVRzd0azFBaWdNZ1pKNDdzcm44S0lBMUlraUFPcHczNVduTjIrak9LK09CcTRBZ0dMKzc4SDI0L0thZ1hsWEtxSi9aUmdYN05IUTBXSEIzcFhPcGRVcVE3QWRaUitXcDlFZldnTmxvRjMrQWh3SDhpSGR5eWhpcWhLcUozSHlna2xhdEpIVTVFZm9jUW5lNWl0MGt6bDFDTS9OWDE3eXRYa0ExYlZiVU5NanQzT2ZqMHJVcXY3MGlpSDlDSHZCcHVKQ014R0RCc0I5SEl2Z1J2REo1WnVyeSs1L0Z4eGZSdjRONkh2SDlYY2JxSHhjc3owNkdGT0FaSHFNSEh5ektLWmVWQUphOXhSKzBrM2NJRjVZMUhxNktGcW9ZV1Y3OGxFY2h4RDIxbkFXTTBYMzlYSlpuUHhFR2JUakliVmtNK0UvaHFBVDVpSnBJc0dwcmRDU2xIYnFmR3pHWE4zZy9RL1labTJob0ZZN3lrS1J5cWRsdWh1eHlmUTlBWVM4cWZnaDRVRk1rcjBJYkM0TXVndDBNY1I5WTAvTGFjSVFwU1R0MndLMER1WURHOWx0V21oKytsb2JVeloiLCJtYWMiOiJlNDFjNDNkNGFjMDNjZjEzN2NmOGI0ODQ2N2E3YTM3ZTc5MWUyM2M3YWFhZDUzZmNjMWEzNGI0ZWM4Mzc1YzFkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlR2STNGM3QyR0lnSDZIVVhNdmhvWmc9PSIsInZhbHVlIjoiYnR5K3BiM21qd3VONlR6UUJxbFFmazdYQ2xZQUoyVU5scXJRS1NscEl6NDgyMmk1UkpydVBVbWEveGd2MkFMTDRjQWNmVzR5Y2p6bm45VGJaaEJqa0oyZXdVa2tjV0FHVzl6Ujd6R2NxaVFWK0hZb2NrcUt1cEZOTWRtTVk0VGR5TlpndjFqd05EM3E1RjAxZFJBdUtrUlhuK1h2U1U3eWx1ekxhbHRxV1Jwbnkwb1hnSVhaOEtnTWtkbENZR3VqTEVGNklkMHB2NXEzRFY2dkNEelNLYkUvMk5ES0pPTHpnQS84VkdvajFiQkcrTC9ENlNyd1hDVzR5MU9JaHJBQjZ1UHU2K3JWbk1QWVlPc2c3RE9SN1NIMUg2cWtLc0dhUyswRHNIOE9nMjFwSklnNXVyL0owZHFnU1pZejMvblRBRXY0dlpZbDF1dFJGSjNNNFA3ZGRTR1pjSzZzK3RxVG9ycTNlVjdHVU1wR2JlaTJ0MWxuT0dmN09JUDJxcWRhbW1qdDU4NHVZdVBmcXJBZkJ5dzc0RytMNXhzRmdqbEZhVmVPRFVJS0w3cUk3VkRLZWRaZGNSTkdNeFRLTHlGVUZpd2xwSFJuMThiRFJtU1lXcVgxY3JmUDFKT3krelNibFArWDVqc2g1dmh2QjkwQlRjQzVtMDZzemlYbUl3YUEiLCJtYWMiOiIwOWQ1MDVlYzFhNWQ5NzZjMmU2YTRlMDE5ZDAxNTIyZDdmNjNmOTE3NmJkNWRkYTM4MjE2MGUzNzRiZWYzODM3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128507741\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1315621843 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315621843\", {\"maxDepth\":0})</script>\n"}}