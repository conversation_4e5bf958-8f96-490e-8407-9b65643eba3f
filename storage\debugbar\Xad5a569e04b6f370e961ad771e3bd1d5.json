{"__meta": {"id": "Xad5a569e04b6f370e961ad771e3bd1d5", "datetime": "2025-06-30 16:07:13", "utime": **********.122584, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299632.589892, "end": **********.1226, "duration": 0.5327081680297852, "duration_str": "533ms", "measures": [{"label": "Booting", "start": 1751299632.589892, "relative_start": 0, "end": **********.022429, "relative_end": **********.022429, "duration": 0.4325370788574219, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.022438, "relative_start": 0.43254613876342773, "end": **********.122602, "relative_end": 1.9073486328125e-06, "duration": 0.10016393661499023, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45555880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02732, "accumulated_duration_str": "27.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.065048, "duration": 0.02595, "duration_str": "25.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.985}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.104452, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.985, "width_percent": 2.892}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1117818, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.877, "width_percent": 2.123}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-85857002 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-85857002\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-156456688 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156456688\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1179429135 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179429135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1090702233 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhJTVJ6R2FJdU94Yjk5c0k4ZzZDN3c9PSIsInZhbHVlIjoiQjdSb0JiUkx6QnpqMUwvQXdFbFZLZXZrL0tabkZJOVZEZVl0elJHSFdabUhHN3JEbVdieStMWE81K1JmQWtBcXRFSVRsSmZQM1lmN0tHdVhFZ2FOLzVKaFBLWkM0MW9jS2c4eHZ6bUFJRmZoRE9YcTNIcVV5R1ZRczBIL1dlVzk4czJabWFGQXVDNDI4eTdzTXJBQTB2QVZrb2Z4NjEvMGNuQS8xWGpRcXYrNC8rSjM0NUY3QlpuTTVGcFY2bjVvK01rVTNlZ29QUFVXQXh0OEN5KzAwcVg2MlkvcDFnQTFLZW0xclBDdDF2dnJCVDZwbWRGaFlPb01pQmZSckZaL2Nka2ZwaU1OcW5YU0x1VnE4V1JRNDU3N0RXM3lEWFFFeEZmemFhMlBGZlA4Zmhkei9KWVpSMWp1ZFBPa2V6WFVVZU5Qem9yRXBzM3UwbFBoU2hRZWRwMXM0cHNxd29WaHZBSlNrVzYraDVQbFdrQnVqQVNHL3hLMWhRUVJTRVZyb1lzTlNmYlNGdnlONkxmQjZBZUltd1pvaTBnMlFVZExQaFNhWXhNamVTZ1RIUUk4OXpPb2VpaUlIZ0RVcVRPWkVHMnVUNFYwZVhjS2tXMXlmdFpvMHBxdXN6R3RBajRPVVF4SVRhaUtLdGVBaVVRMGxHdlVDZjlWYUhhdGlDdVYiLCJtYWMiOiIxYTJjMjA5MmE2ZjQ3MzM5MjcxNmQ4ZDM0NzgzMGIwODM5NGMwZTZhNmFiZDc4OGQwYzhmZTViNzY1YWE1ZTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhBR0NvMDlpR3pSeW9qQVA2elpvR3c9PSIsInZhbHVlIjoiSkd5VlA0Q3JSN3dYa2Evd0hUTUJQSkZYekx4b2RHQThCMS82Z2pLa1pmQlNXTTkwYndGOXE2UVRpMktuUnZLaTQzczh1N3BRR2RrLzU1a3M3c3JwM1NUYThoNzVhbDJPV05LdlRNMjhJdURoV1hGS1pYMkpYb2lsVE01N0hqMldUazNVSVNhekl0aTVKaXV1MExTdTVsMS85Vy9jWlV0c2tLY2VIOXZRMi9pOHNzb3k0NmVsSWNuVjB1Z2dwbUNBekRFMldEM3FCNWVvQWlrbzNkZUY5T0Q1Zlg2NmZJLzBMTjlNUjVDalQ5WnJCMFppQVVuVUp5ejFHY0tDWGJUSlpKbGlKbEpyeXMwZzAyNmRaenNtczlOVDdhOFg1dG9PSXA2cG5aNGNFMzlqc3YvaUxDdTMrcWYrU0pDbkp0YzhMb1JwSTErWFVuMGVsb0lKS25UVkRDdE9UbDZnaXB0QTgwOUpVbFJPWERYMWVPN1RsdlJjd3FhaXIxTHBRald5T3p3OXR6ZGt0dHlMbXlpblZ2ZjZaaThObnRRdFE5dllyQjdDZWZSakU3ZnN0RjFZZlMzb3UvdnpZWTRNVDNyTThjenlxMHBVd3krNXhPcjhwT0pSSnc4N3orQXJIZjJGNHhqbk14cG9hbkgrTi9QQlJ6QitYYTNDcVltcWIxTloiLCJtYWMiOiI5MDY3ZTFlOGU4ZjI5MWRkOWU2ZGFmYmYxYzc0M2YzOWZlYjdiZGUwNWZjYzFmYTBmZTQyMDdkMTI0ZTk5NWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090702233\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-416435719 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416435719\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2028108713 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InN3ODVCeUhNTUZvUFZ1QUhpTWNsT0E9PSIsInZhbHVlIjoiZFlNT2FES1NsVUpSN3BnamZ3bGlZcHdPWnFVWWNacnhseUFDL3kzdW1qODVod3Znckk1SGhQQkFtMG5RU0FIYzREREo4bUFGWVUvRlZVK2FkK1BoUWF3bVphKzVDWSszV2VIeWx1NzUrOHNFVHY5OUZrMzZwQmpuUTY1cyt3T2FPdDNjWHl0NnhaSlNLY0hWMWt1Y2ZwTVVjTG9OVFNiQ3piMVkyc3NwSHg5aStOSHFSOWlUMUUza05DSEJpSWFiUGNCaFNURGRtKzNyL0pNR1hQaEp0cWt1eVFqcnZpL1Avdy9xbGNmZGVMZFBUeVA4Nm9DcTY1NHUvdVlBeEpXcm4rUmxzYU9lZXIzcTFzRHFocG9oOWdUSHNDWUpqV3lNMm9uL2dLZkNvQlltcU9jNGtrUlpuZk1KR2NPQjRuTFpoV0hTc0VnblkzcVA2Z1l3SkF5NUc5T2dzZWRtT0E4Z1JPZmFqVUxxdTBSa2NjRG9kVlpBdUFvWGxpNDBhZjZiSWhHYXV1VEFLNnpCRkVsSmo0b0c3VnZkdFpUQ3JOcDI5eVJ2dlVBWlJTeHNpUXUvVTF4VkdRUUhOb0p1MUtrR0Z0dTJrOXN6ampQRnlDclRCVEJvK3dPT2ZmSk5BUmR5Tjd2a2x2UUpQb3p0dmx4YVIvek1zcEZvRGNTQzJ1bDkiLCJtYWMiOiI1NWRjMTA3OWVmZTY3Yjg1YTI5ZDY2Y2IyNjMxZWIyNjRiMGVhNmU0ZDk0NmI5YmU4OWJmMzQzNTY0YWY0MTM3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZHNkZZT2laTEhHbkZtSDdycVhkakE9PSIsInZhbHVlIjoiT2JOc2E2WG1TSk5TcTE0N211Si9QSzNFblNIY2k4YUMyL1Q2LzU0RFRVQjZGRFZiRzVCaXVPVE55aUpqNEJoMEUzZ080OStocGQ0YVN3bGhxc0FyMEl3aVFaQTFDZTVGaURkaERzRDhoR0xSRW9BOGNocHRVK0JySEZScHZKWHFNQXVSc2VUKzZTZ2lURGluV3ROYXY0QTdnWWRZaldhR04wVjJ1UkFGUGRiNEF1YWp4azEyOUZpMTVwNnBRK3VncWYxeEdQMWs2V29hTUtaMnozRVRKMzNDRnU3dWExaE9QYjdQS0hDaWJRNHphUGVpeWZmdzBEMTVWWEVmZDF2YWVJTHkyRzRLN1p1ZkNXTDhXTmM0cHA4cUdhT2RDZW5zZlZxamtNUVhhU3hrYnQxTEJ4ZWdvMElVYko2alQySkIySmFpZENSRTkzSUxPaE9pSTNWMkNIaUhTRUxzcEpGc2NoMCt6VDB5a1FTZDllc1orSjdONWVBVkVianlUcEZTeDJYclpOY2x6WTBpajRjYUJ4UVlRck10R3VGdkxxU3UzekdXeGwrZ0RFR0EzRVpvaGVXcFJ5NXhIWVZyQUdxVU1jUDUvYWdpR3pXVFZnNXlka1IwalhhT2RmUXQ5UVVERHRtblppSERZV0grYjhOOWVXTnMxOFJiYzloMHEwM2oiLCJtYWMiOiJiYTdmMTI2YWNiMDgwMTJmZmZkMmU2MDE2NjZhN2IwNWQ2NjcwOGRiZjc2ZDhkOWMzZjhiZmQ3ZjkxZGJiMDEwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InN3ODVCeUhNTUZvUFZ1QUhpTWNsT0E9PSIsInZhbHVlIjoiZFlNT2FES1NsVUpSN3BnamZ3bGlZcHdPWnFVWWNacnhseUFDL3kzdW1qODVod3Znckk1SGhQQkFtMG5RU0FIYzREREo4bUFGWVUvRlZVK2FkK1BoUWF3bVphKzVDWSszV2VIeWx1NzUrOHNFVHY5OUZrMzZwQmpuUTY1cyt3T2FPdDNjWHl0NnhaSlNLY0hWMWt1Y2ZwTVVjTG9OVFNiQ3piMVkyc3NwSHg5aStOSHFSOWlUMUUza05DSEJpSWFiUGNCaFNURGRtKzNyL0pNR1hQaEp0cWt1eVFqcnZpL1Avdy9xbGNmZGVMZFBUeVA4Nm9DcTY1NHUvdVlBeEpXcm4rUmxzYU9lZXIzcTFzRHFocG9oOWdUSHNDWUpqV3lNMm9uL2dLZkNvQlltcU9jNGtrUlpuZk1KR2NPQjRuTFpoV0hTc0VnblkzcVA2Z1l3SkF5NUc5T2dzZWRtT0E4Z1JPZmFqVUxxdTBSa2NjRG9kVlpBdUFvWGxpNDBhZjZiSWhHYXV1VEFLNnpCRkVsSmo0b0c3VnZkdFpUQ3JOcDI5eVJ2dlVBWlJTeHNpUXUvVTF4VkdRUUhOb0p1MUtrR0Z0dTJrOXN6ampQRnlDclRCVEJvK3dPT2ZmSk5BUmR5Tjd2a2x2UUpQb3p0dmx4YVIvek1zcEZvRGNTQzJ1bDkiLCJtYWMiOiI1NWRjMTA3OWVmZTY3Yjg1YTI5ZDY2Y2IyNjMxZWIyNjRiMGVhNmU0ZDk0NmI5YmU4OWJmMzQzNTY0YWY0MTM3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZHNkZZT2laTEhHbkZtSDdycVhkakE9PSIsInZhbHVlIjoiT2JOc2E2WG1TSk5TcTE0N211Si9QSzNFblNIY2k4YUMyL1Q2LzU0RFRVQjZGRFZiRzVCaXVPVE55aUpqNEJoMEUzZ080OStocGQ0YVN3bGhxc0FyMEl3aVFaQTFDZTVGaURkaERzRDhoR0xSRW9BOGNocHRVK0JySEZScHZKWHFNQXVSc2VUKzZTZ2lURGluV3ROYXY0QTdnWWRZaldhR04wVjJ1UkFGUGRiNEF1YWp4azEyOUZpMTVwNnBRK3VncWYxeEdQMWs2V29hTUtaMnozRVRKMzNDRnU3dWExaE9QYjdQS0hDaWJRNHphUGVpeWZmdzBEMTVWWEVmZDF2YWVJTHkyRzRLN1p1ZkNXTDhXTmM0cHA4cUdhT2RDZW5zZlZxamtNUVhhU3hrYnQxTEJ4ZWdvMElVYko2alQySkIySmFpZENSRTkzSUxPaE9pSTNWMkNIaUhTRUxzcEpGc2NoMCt6VDB5a1FTZDllc1orSjdONWVBVkVianlUcEZTeDJYclpOY2x6WTBpajRjYUJ4UVlRck10R3VGdkxxU3UzekdXeGwrZ0RFR0EzRVpvaGVXcFJ5NXhIWVZyQUdxVU1jUDUvYWdpR3pXVFZnNXlka1IwalhhT2RmUXQ5UVVERHRtblppSERZV0grYjhOOWVXTnMxOFJiYzloMHEwM2oiLCJtYWMiOiJiYTdmMTI2YWNiMDgwMTJmZmZkMmU2MDE2NjZhN2IwNWQ2NjcwOGRiZjc2ZDhkOWMzZjhiZmQ3ZjkxZGJiMDEwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028108713\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-976491706 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976491706\", {\"maxDepth\":0})</script>\n"}}