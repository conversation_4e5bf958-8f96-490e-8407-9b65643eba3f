{"__meta": {"id": "X2fa3a152a5ea3d631255367e1e83a9ff", "datetime": "2025-06-30 16:22:23", "utime": **********.885308, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.418482, "end": **********.885336, "duration": 0.4668538570404053, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.418482, "relative_start": 0, "end": **********.790632, "relative_end": **********.790632, "duration": 0.3721499443054199, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.790641, "relative_start": 0.3721590042114258, "end": **********.885339, "relative_end": 3.0994415283203125e-06, "duration": 0.09469795227050781, "duration_str": "94.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724064, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02219, "accumulated_duration_str": "22.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.825737, "duration": 0.02134, "duration_str": "21.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.169}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.856166, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.169, "width_percent": 1.667}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.862436, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.837, "width_percent": 2.163}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-461282407 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-461282407\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1936055779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1936055779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-528959431 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528959431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-312136275 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300540314%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZZdXlSRkdDbmNERXVVL1FVZmRzbXc9PSIsInZhbHVlIjoiTWtDVldiQUpCVmEvNm9PQVdaODN6QWlocmFUdFVOZkgwbUNyMU0yT21yZWlkK0M3Y0FrcFl4TXgxR3ZZSWJpWUV1NW9UNG5hbE1xdDZDOS93aVp5em9Rd1k4b0RuS0lockRZQ21aMGt5SURGcStUMjVlS0xyZTNJVnhtempuM0pnWjN5NHE0NzdsbWI3YnpTSTZsZGk2bVpGWERDYXhuMFdoVDdRY1kyUThOTjZ0S0RZV1R5cnNOdWpCa2g1NjBoZ09tWE9XbFVhMWI2QjZKTGVQZ2xWNXdmSy9vMUdDVGI3TXpSd3dhNXkvSEtHL1A2bUdqZ09HM3pIQjlGQyt1VjF0dW1hUEN4eXlvMnV5NlNhc1pZMFYzRTlJeFY1Z2w3ZGFkU2JibWNJSHJ3bGZuRTBhQ2tUSFNuM0pEU3dmcWQrTDNHWFhtbHBGMkh2MEh2TXJsUFJURFJXeFpNUjJoYkVYUUpWcURJeEFHOCszTGNicHFBTlRFWFovUHcvM0pVZ1JiMnpCZzNRZkIxRUErUzJGMHZGSE5KS05QYzNkNTZZbnN5c0ZrZ1pwVGZWRStLcERwem83dEJzQVhadmNxYW50SzJTaFlURksyNGtVd2ZqV0NJSGRlTk1WSHo2Tkp0LzNwWHh4cmlnalVmQ0ttNVRZVGJtU21SeUxwYUJNa3ciLCJtYWMiOiI0YzYwYTU3ODFkNjFjYjVmMzY0NjgwNGY3MWE5NjhmZmJmZTY2YmQ0MTczZjJkYjZlN2ZhYWJmMjQxZTA3OGYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdobHE2WGhuY2ZnZEwrTXkvNDgwUFE9PSIsInZhbHVlIjoiZFBnelphRHJ5UXVwdE1xNWhoVU5tUUZUM0N1OVBSNitUME5JaHhqTUFRY2c4bmp0RE8rUWNYTGFxM3pPVExuUFU4Q3ltcExjdjVDVGp5MHArcEM3K0tXdmlJMnFlRkRQdGxFbDI4b1ZkYUZaQUc1TzlsaGVmTnoyd3BtcnJaYUNnd3NxNlZINE1HQnBQUXhVZWlQTG1COStTQU5BKzJlNWFOQS93QXF2NXJ2Q1Y2UWw4UVpFWEw0OUJSUVdZVDgvWUJUNHhhaGY2eWc3YnA1TUZCcGpyQ1Y2eGp3OHlUZUgxaFlGQzhwVVAxQUVZdWJHZWNodys5dERldHVXZlcrNkhsOGF6OW92MWh5Qmp4OVNuS1dCTTBTYk5XTTlXUXkwZkJ2V3pTbktCci9ibitsTzZ6ekhlZWZnOHZJR0N6Ty9zRW5BU3lvWE1SZTJheStFTkkvZGlPbXNnK293aThJcGZJVEVMRURLcFlYSndwVkFuQ1YyelFVN2pzdnVINGpMTjFXNCtDS2dvQ0JLRXVaclo4ZktPR0Jaa01DN0gwaStoVlpDQ2FoQ0dPQkltOG1obFM5K016d1N2Nzd6SjQ3bnVpTk85dlRCTTdFd3ptazhIN1NjS3pwVitDVE1PdGdVSU9abURGOEhVcmFybCtkYU9LekxkNHBjRXBFc1k0WkkiLCJtYWMiOiJlODRmY2FkYTFhMDA0NmI0OGMyN2IyMmM4NTg4ZjFiYzAwMDU1YWNhMTk2NTM0M2UzZjBiNmE4NGRkNDlkZjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312136275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1319968877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319968877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1837297078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJOK0x4K2I3KzhtbFBsT0RQZkc4Tnc9PSIsInZhbHVlIjoiVlJXdGhaYWZnL2hzOEd6V0Zrck0zaGxoREVKNEV4OUpWZVcwZWF4YUpqTERjRlQvandoYSticGtxRWp3MU1NQmplbXc3M0hpb0JOazBGY0FvUmxxb0pvcWw3YlM5TC9MV3dqMW82SGUzYWVwTTgvRXFMblprS3NscDMwU0syRTlleVQ1NThQOS8xVHprMHZtSHMzU2JRM0t6SWMwd3pIWlpUWGlkcytta09MbjFzdUM0aWhWU2l3TlhpRGxsV2RlWkduYUdVY3pUNzR0VExtQ0hQS2w2TXBIY2pnVzhYam85QXJITWY2bmx0eUNBVnpJTHptZFpIYlI3dDRILy9ZcStQY2I4WndNUWs1M2lQWkY2K0M2NWQ0Y0hXRjdIWWU4MWJHSnAxNTN0dmV0WStVSWNoUG1Ib2JRNVBlTkJLOGJ2TTh4Z0N5ZzB5c000VEdIdy9RMnpRWFFQeFI0WDQ0TGFIMEQyRUxqQ1B1Q3QrMzREYVBxSGM2VXNRTmxMM0lyR1U2Sk4zV3A2NnY4bDNRa3lkWTd1bExwTGkxeWJyWnlwREN5VjZhb1NYQy80eGpwS3lDS1MrdUNrTUlBcFgzQ00rUy8zTVFwVEJIalh2MGZnaEJwVm05NUFrWTVRbFRtQklKN2g1dmRmb3p5VHFwMzBVNy9IcjluTXF3ZUJ6bmUiLCJtYWMiOiI5NzU4NjdmZWQwNjM3MmMxMjg0ODIyNjE0MjgzODEwOGExZjMyNjczNzVjNGZiNDBlOTBkYTAxOWM3NTUyMzJlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9jcWNkQWJRc2haTGN1bUhIaGt6SGc9PSIsInZhbHVlIjoid3VTK1c3clJ0WnJPM2g1S1JEZHFKUDdjZXIyYUNjaGZEdmxic3l4YWJuMHJTTWJOT00vS1czU1NnUi9Db3NoMHZzdlAyZi9yMjRyS0R5VThZNUdLcnp4cmpzWTlzUTdNaUV1eVUxZ2hLUGkyRXdkQW9TbldQd2Q0V1diQldyWWFrRXlUMzlyNGZxeXgzaDBCL3dzS1hlcjJIcElkVlhEbVpXbUsyK1VTTGFRaXF3QnRjMXJJYmw4ODVVOEJMMmVlcW5sQjBLQ0IvZnFjOTh1Y3lpUVc4YzJISFhGMUJLUzEyeUM1L1krTW9oRnZTaXVxS1hkYllnZVFjeGQvdTNNQnBqT3VRRG9SMWJBWGZGemVOdEkxRStiOXdJSFlqV0N2Unl2c3NuRHF6VVFkS0dZRnE1SjllWVkyNTU1dmQ3dk4xZW5VWXlPVUxscXZ5b2EzVjFxWEVVck9GY1NSakxLcWgrSFIrQUJFZUk0Wjhjc2Q2VDZaYXJBczNJcTRBTmN2K2VWUkpiNm9lbklYZ280NTZESS94MGhpTnJDSTFPZ0ZBdXBMYmNvMmJoU0RyVFJXZVJWOG5HemIzV3NiR3h3aG5na2Rtd3FkY3U5ZnRWS0J3WFZVSjRYSlEzUXlGdTQxVjgxaHlvaXhzMEJRdFZDR3owdUhkZFBBQ2JETFZSTWUiLCJtYWMiOiI1OWYzN2MzMWVkMWFiYzBjY2VmMzRlNmM4NDgyNmQ2NDIyYmJiOGFmODdmY2Y5MDNkZDRkN2FjMWMyNjc4ZjhlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJOK0x4K2I3KzhtbFBsT0RQZkc4Tnc9PSIsInZhbHVlIjoiVlJXdGhaYWZnL2hzOEd6V0Zrck0zaGxoREVKNEV4OUpWZVcwZWF4YUpqTERjRlQvandoYSticGtxRWp3MU1NQmplbXc3M0hpb0JOazBGY0FvUmxxb0pvcWw3YlM5TC9MV3dqMW82SGUzYWVwTTgvRXFMblprS3NscDMwU0syRTlleVQ1NThQOS8xVHprMHZtSHMzU2JRM0t6SWMwd3pIWlpUWGlkcytta09MbjFzdUM0aWhWU2l3TlhpRGxsV2RlWkduYUdVY3pUNzR0VExtQ0hQS2w2TXBIY2pnVzhYam85QXJITWY2bmx0eUNBVnpJTHptZFpIYlI3dDRILy9ZcStQY2I4WndNUWs1M2lQWkY2K0M2NWQ0Y0hXRjdIWWU4MWJHSnAxNTN0dmV0WStVSWNoUG1Ib2JRNVBlTkJLOGJ2TTh4Z0N5ZzB5c000VEdIdy9RMnpRWFFQeFI0WDQ0TGFIMEQyRUxqQ1B1Q3QrMzREYVBxSGM2VXNRTmxMM0lyR1U2Sk4zV3A2NnY4bDNRa3lkWTd1bExwTGkxeWJyWnlwREN5VjZhb1NYQy80eGpwS3lDS1MrdUNrTUlBcFgzQ00rUy8zTVFwVEJIalh2MGZnaEJwVm05NUFrWTVRbFRtQklKN2g1dmRmb3p5VHFwMzBVNy9IcjluTXF3ZUJ6bmUiLCJtYWMiOiI5NzU4NjdmZWQwNjM3MmMxMjg0ODIyNjE0MjgzODEwOGExZjMyNjczNzVjNGZiNDBlOTBkYTAxOWM3NTUyMzJlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9jcWNkQWJRc2haTGN1bUhIaGt6SGc9PSIsInZhbHVlIjoid3VTK1c3clJ0WnJPM2g1S1JEZHFKUDdjZXIyYUNjaGZEdmxic3l4YWJuMHJTTWJOT00vS1czU1NnUi9Db3NoMHZzdlAyZi9yMjRyS0R5VThZNUdLcnp4cmpzWTlzUTdNaUV1eVUxZ2hLUGkyRXdkQW9TbldQd2Q0V1diQldyWWFrRXlUMzlyNGZxeXgzaDBCL3dzS1hlcjJIcElkVlhEbVpXbUsyK1VTTGFRaXF3QnRjMXJJYmw4ODVVOEJMMmVlcW5sQjBLQ0IvZnFjOTh1Y3lpUVc4YzJISFhGMUJLUzEyeUM1L1krTW9oRnZTaXVxS1hkYllnZVFjeGQvdTNNQnBqT3VRRG9SMWJBWGZGemVOdEkxRStiOXdJSFlqV0N2Unl2c3NuRHF6VVFkS0dZRnE1SjllWVkyNTU1dmQ3dk4xZW5VWXlPVUxscXZ5b2EzVjFxWEVVck9GY1NSakxLcWgrSFIrQUJFZUk0Wjhjc2Q2VDZaYXJBczNJcTRBTmN2K2VWUkpiNm9lbklYZ280NTZESS94MGhpTnJDSTFPZ0ZBdXBMYmNvMmJoU0RyVFJXZVJWOG5HemIzV3NiR3h3aG5na2Rtd3FkY3U5ZnRWS0J3WFZVSjRYSlEzUXlGdTQxVjgxaHlvaXhzMEJRdFZDR3owdUhkZFBBQ2JETFZSTWUiLCJtYWMiOiI1OWYzN2MzMWVkMWFiYzBjY2VmMzRlNmM4NDgyNmQ2NDIyYmJiOGFmODdmY2Y5MDNkZDRkN2FjMWMyNjc4ZjhlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837297078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}