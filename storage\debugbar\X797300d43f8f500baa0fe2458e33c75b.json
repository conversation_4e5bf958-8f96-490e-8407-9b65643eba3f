{"__meta": {"id": "X797300d43f8f500baa0fe2458e33c75b", "datetime": "2025-06-30 16:06:44", "utime": **********.115266, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299603.686746, "end": **********.115284, "duration": 0.42853808403015137, "duration_str": "429ms", "measures": [{"label": "Booting", "start": 1751299603.686746, "relative_start": 0, "end": **********.060387, "relative_end": **********.060387, "duration": 0.3736410140991211, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060401, "relative_start": 0.3736550807952881, "end": **********.115286, "relative_end": 2.1457672119140625e-06, "duration": 0.054885149002075195, "duration_str": "54.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029, "accumulated_duration_str": "2.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.089792, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.897}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.100198, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.897, "width_percent": 16.897}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.105854, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.793, "width_percent": 16.207}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/58\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1311370923 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1311370923\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1282343192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1282343192\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-387980861 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387980861\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1636966828 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299595611%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN1K1F0VWdEcnRDVUdBWGhmTHIxSmc9PSIsInZhbHVlIjoiMDY5amVFNlRLUjZvOHNXcHlhaWpiWGNUVGkxdTV3TkJXWngwYUl2cXdWYzE0WExZQk04MlNCbzZQbmJLVHExVjRxTnZKQlVJaWNSeEM0enNBSVdjak9LVEhEVThtK1VoUmRLQ2w0aVhrcXpJL3dwYzZZL1l3aldValRxTWd3NGFsR1VIMEhCc0lsVVZuWEhYZTJxZXJCa3JaMVJBdmNhZmpSM2s2V2dDZVMvMW5wajcyNEFQd0xwOC8yY0plb2pQWDN4NitvTm5pRVRueEZWWW9NN1hQM3haaXduNHk0TVZkUkcxei9JVFZ2NllsSjJITi80SGEyc2tOZGRrZkJUWUltdzkrQ3g1WjBCRmlHemJYYnJKVStKcWNBZDUwRGZvM3J2clkvWFJmVHNvdGxCaG9HbDJXWGJtOWxlK1pqeXEwTXREQlJUb0E1azY1QWNmOEE5L08xaXZUWUIyK1FKdEVYelQ2SEdmV3k3QUpwbDZrMlBTWG1lTDJuRURQS1ByeXl4MFU3bno5V2JFMDdIalMvc3RYVUtBN1ZiY01XVnFtWGZ1SW1seDk2bzJXbldzUVBpRTI5YXgxSFVGRlEzYlQ3UWNCdVB4SnY4WmE3eVk1azNMZVhEUFVaZmNhamVNMS9EVVA0RzFxT3ZXQ25NSUhMbmVlOSt0dDZ4OHBIdWkiLCJtYWMiOiI5YmQ1OTg4NTAwMTlmYThmM2M3Yjk4NjcyMzAwMDdjMTNmOWY2YjY0MjNjYjYwNGFlYjgyMmQ2MjljYmQ2YzEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im90MG9tT2hFMmNocTFDa2ZOWlgzU3c9PSIsInZhbHVlIjoiMExTcUkyWWJoRW53NExlT3pQOEpUWXdha1RabS95MHJiZUg4WUZvQSsxbjN1RXVQYnM1bFQxVEtXSkhqZGhMTmdybFhPRmllN09sMTJReCtXN3kxRUdjYzZDcXNiWFdVS3MvUldsM1dTaGJ3cHE3bnJaN2NzbjQ2czEwZnVZYmdQSzVNVmlzYnZmUDZ2dTFqSy8vREpJVFlTQzhzYlg5SzBSZkNQZUlUcnAwZXA4dEJsQlYwaEhLQ2czZ0M1Yzc4Q0RLVkpNTitvMkN5YytxS0tWdWtlNUV3a3hFNnRhaVo5ZzRFQUNRRXV2THVMWjVqdVB5VGVaSnI1ZzgvdDljb2FjYlBha1Z2TUNnWFp4TUlHQWdCZ285c2RLMnRsSmFqU0tEK21wVWNIQVFYVjR2Tjg5TitkVHByL3orRUFvZEh2YThZVmovcEFsd3NuamVnWTlYRGcxVzVUb09XeTJTdXJNaDhFc053RHAwTXA4UmNidWY3Nk1SbUtsQlQzZ1VWTFoxb1A0SnVGTENZbDdrWkRnYnkxUWpad0s1U29qdmt6aHR5c016dzVSWUJGL1lURVRycHBJcWErR0NmaS9zaEkwSGZqaWlBYW95aFB1ZWdqWGJXYVVHa0YzK2tqVk4xV1YxaWgvOHVQUHZIM0hKUy9icVNLdDAvTi8vdTVnaDAiLCJtYWMiOiIyMGY1M2Y5MWNmMzBlZDY1MDI1MmRhMjc0ZjVmNzJiNWFlMjViYWI3NjkwNWE5MjJkNWU5YWZmMjM4YmMxM2MxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636966828\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1156230936 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156230936\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjgzK2diaEtwYUZ0L0lCWlA1ZzMyYWc9PSIsInZhbHVlIjoiTi82NmV0ZlVyWnBwdnhWZmtERjZKaStkNk1IWkxLeTBVa2VhZUlvLzZNMnhTcG9pWTh4L0doNnQ1dHY4aFhaOUpOK1JQajJIb2VIeUVhcWl4ZTRILzAzUFRwODkwQWJXaGk3N2hDTFcyRnpRUE5sOVJlRVd6SnROZEg1bFVSRU45czhNSTVPQ1pJck93b3JheDE1b2ZxUzVyd1lKUGQ5bHZUUzJNQkNJeE5FY2hRRW82amNvMmpYbWV4UjBrNEdodWdHU0lHdzZ6Wmo2WkEzMVZrbG9EQXh1MUlmYTljQWtHbkJSM3BYZ0t5WUE1cjRuYVlJbk9CZUw2WHhEQ1BodFJRaUZBUTVqR3pSOXIxdEdBRmVtcDY1ZlJrSlV5VE0zOG53TGswMlBXdHBlUzBiRjhoV3ZsWDNvVnp5VjExSWczYldTT1Awb0w4dEFpMFlZNGhFVTM5bm5hamNvcGk2NjZJdkY1WGVWOXhidkNaT0pWUEpRRXBmMG56MEY0UkR4WGZZRFdyOXY2MVN1Q3ZrcnVDVUt4NjFsQkZrMTVVeEpHTmdFWC8vNjA2bytBbG1KTkVkakY4elg2eDdHNWNhaW9QRDZ3OHMrYjBnWHI3Wk5vbGJZY2FwS1ROWm1NZFZUcWh2MkFOMnRock1nMEIxbGM5cEFNdUxHcElOUnIrSjkiLCJtYWMiOiI2ZjY5NWU1ZTM0NjQyMzg1YzcxNDBkYjY0ZTNhOTYwYWE1Y2MwMjQzMWI4MDM0Y2YxMTQwODM5MDhlZTc4N2UwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9VckU1TTAydkp4T2NzbkpDU3BiR2c9PSIsInZhbHVlIjoibXB3VzhzNXRVUnlyWWlKVFZ6MGhYMVdZaWdHRkFoOTY2akc1TUZKZ0RYQVZYRkNPbXo3dC9CRFh1SlhGc3ZObjB2VENudWdYems3eUx5RUZaRE0wSlhYZTVjNHhrcXROa1o4YUtZeXVWS2pNeVZhZEpXT1FMYUplWGtNV2NDak40M0tTMUFySi84VmszbHc0QVNFV3pLckc1dGxoRlNDUzRHZzc1TzdVOHZiM2p5WUMwYjNDd0VFVkc3SGJWSUNlWUJrWUhwejZvdTJ0bDQrUWNFeklEZTFLUFZkR0JwTEVGOWFvOU14QWtFb2V4YzZsMFM3ZnFwbllNOWRtVnJ4LzJCczdYVjdUbWpWamlIcHp6RTNWT0ZWRUIyWkszbngyVkFSeG9MZjhqUlBxeG96eGozaE8rNWs0dStCSit6T1dGR3pYTElrTGh0eVJZOXhuREpqeDUyZG1VU2M3Y2dNcy9HV1FtT08zK3V0ejlSQjIzTWxQV0E3ak5SRGhTV0V4QUVEaXl4cSsrMGw2c1BZZE1DY0UzbXJTSm9GZ3VkeEkrSUtZYjFPWjBDZW5QUFg4UHdkd2xkemxIUkhWUjFia1NjNm54OFA0dnpleTlad1N2M0pDanF5b0xvc3NYNDhmb0t5eHM5cjNJMU1ybkdVdERxTjlxSkJOZkVUVFJpdWUiLCJtYWMiOiI1OGFhNzU5NDYyYTAxNTZkZGY2NDNkM2U5YWIyMDYyMTE4Mzc5ZmUzODRlNDMwMzNkMjk2MTFjNGMxY2NmYjdkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjgzK2diaEtwYUZ0L0lCWlA1ZzMyYWc9PSIsInZhbHVlIjoiTi82NmV0ZlVyWnBwdnhWZmtERjZKaStkNk1IWkxLeTBVa2VhZUlvLzZNMnhTcG9pWTh4L0doNnQ1dHY4aFhaOUpOK1JQajJIb2VIeUVhcWl4ZTRILzAzUFRwODkwQWJXaGk3N2hDTFcyRnpRUE5sOVJlRVd6SnROZEg1bFVSRU45czhNSTVPQ1pJck93b3JheDE1b2ZxUzVyd1lKUGQ5bHZUUzJNQkNJeE5FY2hRRW82amNvMmpYbWV4UjBrNEdodWdHU0lHdzZ6Wmo2WkEzMVZrbG9EQXh1MUlmYTljQWtHbkJSM3BYZ0t5WUE1cjRuYVlJbk9CZUw2WHhEQ1BodFJRaUZBUTVqR3pSOXIxdEdBRmVtcDY1ZlJrSlV5VE0zOG53TGswMlBXdHBlUzBiRjhoV3ZsWDNvVnp5VjExSWczYldTT1Awb0w4dEFpMFlZNGhFVTM5bm5hamNvcGk2NjZJdkY1WGVWOXhidkNaT0pWUEpRRXBmMG56MEY0UkR4WGZZRFdyOXY2MVN1Q3ZrcnVDVUt4NjFsQkZrMTVVeEpHTmdFWC8vNjA2bytBbG1KTkVkakY4elg2eDdHNWNhaW9QRDZ3OHMrYjBnWHI3Wk5vbGJZY2FwS1ROWm1NZFZUcWh2MkFOMnRock1nMEIxbGM5cEFNdUxHcElOUnIrSjkiLCJtYWMiOiI2ZjY5NWU1ZTM0NjQyMzg1YzcxNDBkYjY0ZTNhOTYwYWE1Y2MwMjQzMWI4MDM0Y2YxMTQwODM5MDhlZTc4N2UwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9VckU1TTAydkp4T2NzbkpDU3BiR2c9PSIsInZhbHVlIjoibXB3VzhzNXRVUnlyWWlKVFZ6MGhYMVdZaWdHRkFoOTY2akc1TUZKZ0RYQVZYRkNPbXo3dC9CRFh1SlhGc3ZObjB2VENudWdYems3eUx5RUZaRE0wSlhYZTVjNHhrcXROa1o4YUtZeXVWS2pNeVZhZEpXT1FMYUplWGtNV2NDak40M0tTMUFySi84VmszbHc0QVNFV3pLckc1dGxoRlNDUzRHZzc1TzdVOHZiM2p5WUMwYjNDd0VFVkc3SGJWSUNlWUJrWUhwejZvdTJ0bDQrUWNFeklEZTFLUFZkR0JwTEVGOWFvOU14QWtFb2V4YzZsMFM3ZnFwbllNOWRtVnJ4LzJCczdYVjdUbWpWamlIcHp6RTNWT0ZWRUIyWkszbngyVkFSeG9MZjhqUlBxeG96eGozaE8rNWs0dStCSit6T1dGR3pYTElrTGh0eVJZOXhuREpqeDUyZG1VU2M3Y2dNcy9HV1FtT08zK3V0ejlSQjIzTWxQV0E3ak5SRGhTV0V4QUVEaXl4cSsrMGw2c1BZZE1DY0UzbXJTSm9GZ3VkeEkrSUtZYjFPWjBDZW5QUFg4UHdkd2xkemxIUkhWUjFia1NjNm54OFA0dnpleTlad1N2M0pDanF5b0xvc3NYNDhmb0t5eHM5cjNJMU1ybkdVdERxTjlxSkJOZkVUVFJpdWUiLCJtYWMiOiI1OGFhNzU5NDYyYTAxNTZkZGY2NDNkM2U5YWIyMDYyMTE4Mzc5ZmUzODRlNDMwMzNkMjk2MTFjNGMxY2NmYjdkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}