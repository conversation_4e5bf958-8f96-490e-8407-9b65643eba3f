{"__meta": {"id": "X0f3c654537ef1248b99ef7c8add247d1", "datetime": "2025-06-30 18:11:30", "utime": **********.863526, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.386561, "end": **********.86354, "duration": 0.47697901725769043, "duration_str": "477ms", "measures": [{"label": "Booting", "start": **********.386561, "relative_start": 0, "end": **********.77621, "relative_end": **********.77621, "duration": 0.3896491527557373, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.776219, "relative_start": 0.38965797424316406, "end": **********.863541, "relative_end": 9.5367431640625e-07, "duration": 0.08732199668884277, "duration_str": "87.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48652904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00685, "accumulated_duration_str": "6.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.812439, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.822994, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 28.175, "width_percent": 6.569}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.837113, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.745, "width_percent": 8.613}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8391678, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.358, "width_percent": 5.693}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.843577, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 49.051, "width_percent": 4.964}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.848434, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 54.015, "width_percent": 39.708}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.853005, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.723, "width_percent": 6.277}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1063385684 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063385684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.842708, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 18\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 1035.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxjcUVQeWpWMlN1MENvWTJxRjNUYWc9PSIsInZhbHVlIjoiVFNtOWNZMzhyenJwZVNlSCtGYUJTMk1rMittblBYWjM3RlVab0ptcHgxQXRwTWw3T01vZW5xOWJjcHJjS3Y2N3JyNWZTNUdIcTBiY250cXJQZzBObWJRblVCM3pXbVdTdDVZMkpWTzV5ZGdKNFlvRXU0TUc1S0grUFlSUjRNY0dtcjlGKzJWZkdKd3F4VVRXallNZkh0akZhMkVTN0hLTWZmeW91ZHdlbnlSS1RhYTBTei9qb2h0TmJQYThTZXFNdTBjNVR2UWs5b09aK0toVlpBSEE2dTRUTTd5RkIvNVVtSUszWHJKZWVnNHA4TnE3Qy9PQWJLVjhlOGl5TWZKUERxdzZhbFNBYW5aZTl2ejl1a0VIMVVrTnQ3dTdZZ3ZqUEE3NjRNTk5WZDB4djEyeFdoZUJQTlJUM2tJMy9oZkpVVVlrTW9Ia3MwMVNKOVZ2ZVIzSmNIVDNlaFRUUU9oem1ReGdqKzR6b1d1ZW81YjV0emxtTGt1ZWttUDJ3c2xod3RiUnNNRy9hR2NQTDJxbU5BNy9PZko4RDNId2VldVBIZWFQd0g1STRBTndqR2pubGlTQXprUld4UXZQVXBqN1ZSaHBxcFozUVpIdTY4by95K09RRnU0NnF4Z2E2WW5qZ0FjNlpFYWNTcmlOdjdmUDRYV0orMFVOMVAzVzFOV3IiLCJtYWMiOiI5Mjk2ZmY5YjFmODM3NTU0N2YyMjQ3NTQyYzk1ZmVlOWFlMDI4NDdhYzEzODcyNjQ5ZjExMzg5MTFjMmUwZDI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJmclVtejZ2TmxoM2hZY2c0ZXlXY0E9PSIsInZhbHVlIjoiODNtd1duSFgyZmJBZi8rRFZDWmJQVGIwQy9SSlJBQVU2eFYvK2JjRjVYc3laNkdybUVjMmlVSnJEbzIwSFlhWXBGaGhBZkhwSnkrQnF4dUo5TXpzNnpHa01uLzBVc2o3RnIxTG1vQ0xaU0d6dGdXeG5FSFQxaEJqaFBPV3FoRzlTZzZFenQwTXZ4K1VubHZVZ0tCeURCV3RXdG1uWFIrYk1ObGVNSWpnZW9VcjBFc25pY3NnQUd2U3psZEZ2ZXlyWUlMNXp2YmczUTdMVmZNbnVzR0NsL0FjYTg2eFNwUk8wK1RqN1cxM3M0ZWNwRlgwcnJhZ0UwOGltVEo4RWRNY1ZrWXhjVkhxSHJucHRleVZUeGd3dWlvcmd0R3ZVcjMrMjdMb0NCQlRWVGlwbmpSeFFsMkVXRTRMSDJRcmgrK1FyR1dXNU1qYmNCd1RGY095TnVCMmVWZ1ZGU3ZZZC8rRnpJUUJNSWFsTDdmRUhOQ3VBc29PdnFvaW13OFIrWE1RVlJKNGZnQkZIVjN1bWNKYWVPd2NTaXBaeHhpMGdZM3RRblRLZzQwbmZmbHBQaHJJaU9ZZytTNHBIZmlGQnpiN3BYbTEwWVRSOFRmOVNNZ1IzcTBDdGJydCtybW96RndUVDdGMDFpL1BVZ0w2VUNudlFSRHhpWnlHaDhsRnlnd08iLCJtYWMiOiIwNWQ0YjMxMGU3NTlhNjhmNjBjODM5NzZlYmRiYzExZDRkZjA4NGMzMDc1MmQ0M2YyZjM1MmMxMWEyZjA1Yjk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1365823934 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkgwUHpnZlRCSk5SOWhlc2xjNUtrUkE9PSIsInZhbHVlIjoiUWE2WEZVeENnbFZMMEFxZGd4TThxTmxLZTlmajhwT1FET1FjcllmVEJtSUVKTzdxdU9DcDVHVldCc3FLeUtNRGU4RU5HRklkcDdPdEQyS1E5MWRlR0Q4Y2VPL204a0VRRjQwcEZsR1ROOFNPd2RqZ3htaHRhZEQxem5qdTczK2tBSGVNMC96Qk5LdGloSHNKMnlMSndBUFM2T3NBK2s5Zy9NQk42N1JsU0hoekdEeU81bHhUbkcxOEpib3hpOHBFR1lzM3UzMzNEZmozWW1ZNW5zM0pRR0NpcDRNSzlVSlFTb3pUQytCVlVZU1JaZFQ2NEZ3QjVYNC9kcEkzVVVJYktZWFp3OHF3RXZpd1IzZ1BLT1pKVllpTlM3cU42TEJ1UktNR1VnZHlYdDd2dzVrZE1LODJkNk84d2l1cUFITXlldXhUQkNucnFGMTdyK25mNzlxWjJOTUJwVTlYLzB6SGZBK0VaazRMakRUb0lPd3NrSTJkbC9rYVhiY2JRVm9BcC9Dc0RqVUdleTNKeFFYcy9ENSsxZms5Ym9MRk90aDZFUHRnYU85OU00SGUxdXpUUnd1QlF0bnB0UFMwNFdIY1ZEVUxaR0x4T29nbW9MU09rK2JydDVMVmlOSmRNYmFpdk5HVkUxRXUvaThPeHpHRWRlYWtudk52ejJWZ1BFU0wiLCJtYWMiOiJiNWMzZjk0ZDM5OTFhN2U3YzU2OTM3N2U5MzdiZWY5NDVlZDdjM2JlYzM1YTUxMzE2YTBlNGVhZDQwODI2NDU1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpDd0loN1lDK1RVS1lMclVLRDcySlE9PSIsInZhbHVlIjoiVDRQUGowS0ZIKzhPMEtjSzhZVFhPc201ZkJzNzJzUmZoWnhwd29oNTY1bzVqa1JRRit3ck5GMnRRWm5ZQmNncXYrTGdaWEpPSTFrd2s5R21MUXRMVnNiVGp1MWM2TW0ySkpPRUc3MDVabk1sejhaazJRTWFTWER2WThNcWpZbHdqcHlCU2hyU1FsaVZVUjFxREZIZlU0WlNFL003dXN1cmVMSFhtVXErQWxoRjBpRDVBRCtOS09hM3NxQUtsYXBtcjZIMWJZVU9WSHM1OFZOTDVNNEU5NVhNNXdSalFCeURkSE0wbFV2K1o3V01EUWtyVDRRcjh0TUJUdVpTVTJCb0lOYUlIOHZ5NXNLZlM5TklrbWU1K1ZyNlIrTHZENWhrL1NLanY0c2UrZUlWL2Z3S2JVWFpyNGFCSlNkTUhkaU9vaCt6NnFmU2lqL0lzUk9pZXFKVWIvUjVxeGc4Z0d1ZUNURC9KM2kzM1NPRU1xdUpXcERkZkNtRGhzSXJMWlNRVzZFL3RqbW9aNXhxSjVKaFlXZ2U1a0FPV1F5VWZucXFiTmFPUThWdTRlSk0xM1Vmd05Ja3ZlYngwci9JWGNzQm5WSzFtQjBDTWlCaWdPTVQxMjc0T05yT1d1QjdRSW1GTHZWZlgzbDMzYWcyUzd4WDRTY0RCckJNMS9vaFRNc1QiLCJtYWMiOiIyMzhkZWQ2OGJiYzU3MjRmNTA1NTg0NDM5NjlmNGQ1N2Q2MDM4ZTQ4OTc5ZmNlYTAyMzFlZmM4ZDc0NjE2NmZlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkgwUHpnZlRCSk5SOWhlc2xjNUtrUkE9PSIsInZhbHVlIjoiUWE2WEZVeENnbFZMMEFxZGd4TThxTmxLZTlmajhwT1FET1FjcllmVEJtSUVKTzdxdU9DcDVHVldCc3FLeUtNRGU4RU5HRklkcDdPdEQyS1E5MWRlR0Q4Y2VPL204a0VRRjQwcEZsR1ROOFNPd2RqZ3htaHRhZEQxem5qdTczK2tBSGVNMC96Qk5LdGloSHNKMnlMSndBUFM2T3NBK2s5Zy9NQk42N1JsU0hoekdEeU81bHhUbkcxOEpib3hpOHBFR1lzM3UzMzNEZmozWW1ZNW5zM0pRR0NpcDRNSzlVSlFTb3pUQytCVlVZU1JaZFQ2NEZ3QjVYNC9kcEkzVVVJYktZWFp3OHF3RXZpd1IzZ1BLT1pKVllpTlM3cU42TEJ1UktNR1VnZHlYdDd2dzVrZE1LODJkNk84d2l1cUFITXlldXhUQkNucnFGMTdyK25mNzlxWjJOTUJwVTlYLzB6SGZBK0VaazRMakRUb0lPd3NrSTJkbC9rYVhiY2JRVm9BcC9Dc0RqVUdleTNKeFFYcy9ENSsxZms5Ym9MRk90aDZFUHRnYU85OU00SGUxdXpUUnd1QlF0bnB0UFMwNFdIY1ZEVUxaR0x4T29nbW9MU09rK2JydDVMVmlOSmRNYmFpdk5HVkUxRXUvaThPeHpHRWRlYWtudk52ejJWZ1BFU0wiLCJtYWMiOiJiNWMzZjk0ZDM5OTFhN2U3YzU2OTM3N2U5MzdiZWY5NDVlZDdjM2JlYzM1YTUxMzE2YTBlNGVhZDQwODI2NDU1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpDd0loN1lDK1RVS1lMclVLRDcySlE9PSIsInZhbHVlIjoiVDRQUGowS0ZIKzhPMEtjSzhZVFhPc201ZkJzNzJzUmZoWnhwd29oNTY1bzVqa1JRRit3ck5GMnRRWm5ZQmNncXYrTGdaWEpPSTFrd2s5R21MUXRMVnNiVGp1MWM2TW0ySkpPRUc3MDVabk1sejhaazJRTWFTWER2WThNcWpZbHdqcHlCU2hyU1FsaVZVUjFxREZIZlU0WlNFL003dXN1cmVMSFhtVXErQWxoRjBpRDVBRCtOS09hM3NxQUtsYXBtcjZIMWJZVU9WSHM1OFZOTDVNNEU5NVhNNXdSalFCeURkSE0wbFV2K1o3V01EUWtyVDRRcjh0TUJUdVpTVTJCb0lOYUlIOHZ5NXNLZlM5TklrbWU1K1ZyNlIrTHZENWhrL1NLanY0c2UrZUlWL2Z3S2JVWFpyNGFCSlNkTUhkaU9vaCt6NnFmU2lqL0lzUk9pZXFKVWIvUjVxeGc4Z0d1ZUNURC9KM2kzM1NPRU1xdUpXcERkZkNtRGhzSXJMWlNRVzZFL3RqbW9aNXhxSjVKaFlXZ2U1a0FPV1F5VWZucXFiTmFPUThWdTRlSk0xM1Vmd05Ja3ZlYngwci9JWGNzQm5WSzFtQjBDTWlCaWdPTVQxMjc0T05yT1d1QjdRSW1GTHZWZlgzbDMzYWcyUzd4WDRTY0RCckJNMS9vaFRNc1QiLCJtYWMiOiIyMzhkZWQ2OGJiYzU3MjRmNTA1NTg0NDM5NjlmNGQ1N2Q2MDM4ZTQ4OTc5ZmNlYTAyMzFlZmM4ZDc0NjE2NmZlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365823934\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1035.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}