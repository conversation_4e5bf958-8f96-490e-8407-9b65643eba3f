{"__meta": {"id": "Xf8620af19ac27f7a995eb98ee9edb689", "datetime": "2025-06-30 16:22:20", "utime": **********.495384, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.02066, "end": **********.495402, "duration": 0.47474217414855957, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.02066, "relative_start": 0, "end": **********.413281, "relative_end": **********.413281, "duration": 0.3926210403442383, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.41329, "relative_start": 0.39263010025024414, "end": **********.495403, "relative_end": 9.5367431640625e-07, "duration": 0.08211302757263184, "duration_str": "82.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00249, "accumulated_duration_str": "2.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.441978, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.462}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.451995, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.462, "width_percent": 22.088}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4577072, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.55, "width_percent": 12.45}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-655802507 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-655802507\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1633864509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1633864509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-137436049 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137436049\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300536063%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZNWEdzTmIyNFpJM3cwZ2krTytPQXc9PSIsInZhbHVlIjoiT2V6cGhtSjkzZU9yUUpNN3dYOEFuTUFEbCtnZ0RsMnFzaXQxK1JoVVc4WmlQQnpHNlRTcUtoLzloek9pQWU1U3AyYlArQ1BmSStKWVZLdHpVaXdlRzFrS1J1SENOd0VhR3VDUE9HdkcvK3RzbW9ZNGVFb0FPSGR3V1ZzckhHaGF2a3cxMVNYTTZuWStlbkhWdFZUeVZQWW9BYkhZdzQvdFN5TDZYd1hia1UxejNWMWpWdXd4UjhONWkvMkdBdzNhcWVnemtPODh1ZFRXd056eitaUS9RY0JMZWMxeUFJUXlEcTQ3cnNOVjVkZCtlMkVNV0VPaU83Rkhmb0V3Qkt0ZnBydWphbkJTdUF5SnBBaHRrV0R3amRweVBvVzFNNzVRYlQ4OTdjczMrMnE4UlJiUDhQZVZNYzgzblFnbFpyME1MUHhmUmtyamc3ZVdEZGpBeSszQlcrT0RDUTNwWG9PL0wvaFJJT1pRdUNkL3RBcWJvZTZlSXZmeWR6UVcvOEZhR2RnTXkxZFhSQ0VucUg1ZWFhVWV5Z0NJNmRUTDVsbllVN3ZJRW5NL1RFWS9CeFY0WDFLdnc3bDN0VUZaSEpPMk90ODlLY0E4V1JxUnliVDUvY3kwZjJ2YkRhYlpEdWlzQldWdjNuRm9iYVV2d1Z1dUorcDI4QWNSRHU2dXI3Sm4iLCJtYWMiOiI0MDYzNmZlY2VjYjhjZDBjZGY0ZmUxODc3MDAxMzNkNWIwOTA1ZTRjODA5ZWZmZDk1ZDhjZWMxZGQ1ODhlZDdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZyWGJUZURINExmclhGM3Eydk5laUE9PSIsInZhbHVlIjoiczRvKzdQOW9nR3M3VzYvK1huUWpSK3RYa2ljYm0zclV5TjJtZE95Y0ppRXFrUFdzYmtlZjRwcXo0a3RYemI0L2VBNlVycktYUE5XUjdQL2JOVHFiQUNDUGZybHVXcmFNa1NHbEppVW56RjFuSkNCS2hYN1RtVExXbHdlemZ0c2Jub29KRjZiTlZIdEpGRnRvSTQrQmdDbWQ3NG1rVWxaN3FwaU50Vm5rZEtlZkpNR2MwV3ZPcHpncUJzSEdNNFZnSTFnbTJzWXB4Tm5BMUhOeDZxZHl4M29DaHJFUVN4ZjdpUHZMeDdTVUo0VzN6OE45Kzgxc0xDR01maVp1QjZUVEFMMVJGUkFMalI0MkM2UDRZYVhVMlp6Vkg2ZGs5WEVtUTJzVFdqSUg3Nk9xZnNzUGZKdExibzc1clNEU3Q1VVZDNjN6Z1o4OWVhUHlWdm5PNmwwcjBjMmxPVStnWU9OemlpYUoybkNuYXpPUG5WMCtTRmZQYWUrZUxnd1lQRnhyTEZhNEVaNzdlSWRPUmkxRk5yL0NDNUNQWDgwcFpvU3V1TUZFNVVVelAzMkVjUDZQTGx1bDNuU3FTUTI2aWd1RVBvYXJUZ2hyOHNBZU0yVFFZSXBSZkJROHltUkpQMlUxckkvMVo2QjBLV1F0OWZEYWh3TGNYcjlzWmwxOSsydWkiLCJtYWMiOiIwOTZkYmRhYjZmZWUyOWQzMjAyYWI2MGRkNjZjMTQ5ZWJiNWFkOGQ5NjM4YzE3YTEzYjBmMGNjZTBjZWI0MTBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1421433167 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421433167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVzbU9FKzdmcC9peG1aRzJSanZCOFE9PSIsInZhbHVlIjoiNHZ4UklSUWJyMng3YWtiMVFZWXNEQ0dXeTZUOHpLUFVQbUFsTzc1WlZLVjgzdE9LSS9vNDh4dHFQMTB6QXpvMDBsRmU2QmNWUjlPdzFCbzAyQ2NNaXhXcEdrY3hJakszMzY0UHE1N00zRlE0c1VSeGNOZE9rcFJkbk9IWnlRdFE3bncrOW94ZFpyZS9McUFkZWRSRHlRZjVjeVg4RDRMaGN0UFM3MDNWSW9vaStTN0tNb01ndXJhUDR1aG5lOFJUYVZ0SlVIazc0ZVU4T2J3UVNlYW5VK3V5Ymx4QVZrblp0aDZQU1A1eklRN1BIeXUwK0FHbDFRTWR4WC9BcmRKZ2Z5UFJLdDRIU2Q4eExOTnZWYkdEQzAxeE11WVduTE53R2N4MXVwOTVqWmtaNGM3dDhJQ3dlL0ZOR2N5NlM4OXNTOWMrTlVNeHlSZDFxWTFBVXFDUHhVR1c5SDBzUy82NkFUdFZBWEpZbTF4WUtIbzJubU10L3k1MGF4QXg1TXMzNlJ6ZnNuNjVEcjV2KzVOUDVYZzJkdmFqN0xPdlI5Z0h6UG54RWI5bUJhR1UvNHJ0T1VUTHQwRkpMdTZJaTR4YUdiZ3VubHdKRVFTZjd3ZFpOMU1mTCswNUdrVk1Hc0Z2TWl5dFJPaDE5eFlvYlR3WHdtQWFzdU1DeHZTdHFIbFkiLCJtYWMiOiIwMDU0N2U1Njk2ZWRhYTQ4N2UwYjVkNmY3NDkwZmZlYTJmMzA3N2RmOTkwZGZkOWRhOTQ2MTdhMGNjMjAwNmYzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJDQ0pMaXFldXE2NVZSb1g0Q1BHa1E9PSIsInZhbHVlIjoiL3pKN3lqZFRiMjhvYTVpTVJvQnV2Tm52REVwNDFLTmp3aE5Md1djZnhJTmIxQ3o1M1pLcDBkOXpCTWVGUTQyODBEM3o2NXRJVXcrUk1vTnF6STd5L0dUNm8yeW0xazRYbTdoRlh0bTJHUlFobUJybHhQRFdJZjk1WFlvY01RNWhmdGhkVHRYSElkN0JUWU5RV045VTNTbmFDTk1weklpZ1FUdUJoa1ZMRzdDYS9TK2E3THMvWnI2Y2FGbzdUNXlnOHM5RTBrL3NrcVgzREE5Vy9oanVaV3dtY0lnRUZyblhVcERDVkNMcVhoR1Bpb3U3NFBjZXNHc3FRSUM3c0pMZDAxSFlxRGV1N2RoTkVIQjdGdzNYT0RXTERsYUt3eTdRMEs3TThkMThxL3R5YzNadGlobjlDY3Q3aitIeEUxa3FnajVRYWE5aUF6bW1kVERvNVZlUGdQcy9vTXZpamNwZldUUXMzc0N5ZjBXbGhnQjhOSlg1VmJXeDdsRjcvSlUxUm1SR3lubGVBT1MvTlZQY2FyNE1oVjNibnhza1BteGJWRi85K2p6MjRJaUs2UU1Ca2hCRFo3cVorazVtbWE3eHZUOTRHNmtCa2tucEtmVVpCVzNQcm5EN2VtRkpNYm5yWTAyT0NKL2VsS0VrWjJMVVZBdEpUVU1aaEFIOFYyNFoiLCJtYWMiOiJiNWNhODdlZjkyODc4NjQ2MzFiNzA2YWY1NDE5MmM2NWEwOTQ0ZDliZDMwZDYyYWI1MWY5NWI3NzdmMGU0MjMzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVzbU9FKzdmcC9peG1aRzJSanZCOFE9PSIsInZhbHVlIjoiNHZ4UklSUWJyMng3YWtiMVFZWXNEQ0dXeTZUOHpLUFVQbUFsTzc1WlZLVjgzdE9LSS9vNDh4dHFQMTB6QXpvMDBsRmU2QmNWUjlPdzFCbzAyQ2NNaXhXcEdrY3hJakszMzY0UHE1N00zRlE0c1VSeGNOZE9rcFJkbk9IWnlRdFE3bncrOW94ZFpyZS9McUFkZWRSRHlRZjVjeVg4RDRMaGN0UFM3MDNWSW9vaStTN0tNb01ndXJhUDR1aG5lOFJUYVZ0SlVIazc0ZVU4T2J3UVNlYW5VK3V5Ymx4QVZrblp0aDZQU1A1eklRN1BIeXUwK0FHbDFRTWR4WC9BcmRKZ2Z5UFJLdDRIU2Q4eExOTnZWYkdEQzAxeE11WVduTE53R2N4MXVwOTVqWmtaNGM3dDhJQ3dlL0ZOR2N5NlM4OXNTOWMrTlVNeHlSZDFxWTFBVXFDUHhVR1c5SDBzUy82NkFUdFZBWEpZbTF4WUtIbzJubU10L3k1MGF4QXg1TXMzNlJ6ZnNuNjVEcjV2KzVOUDVYZzJkdmFqN0xPdlI5Z0h6UG54RWI5bUJhR1UvNHJ0T1VUTHQwRkpMdTZJaTR4YUdiZ3VubHdKRVFTZjd3ZFpOMU1mTCswNUdrVk1Hc0Z2TWl5dFJPaDE5eFlvYlR3WHdtQWFzdU1DeHZTdHFIbFkiLCJtYWMiOiIwMDU0N2U1Njk2ZWRhYTQ4N2UwYjVkNmY3NDkwZmZlYTJmMzA3N2RmOTkwZGZkOWRhOTQ2MTdhMGNjMjAwNmYzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJDQ0pMaXFldXE2NVZSb1g0Q1BHa1E9PSIsInZhbHVlIjoiL3pKN3lqZFRiMjhvYTVpTVJvQnV2Tm52REVwNDFLTmp3aE5Md1djZnhJTmIxQ3o1M1pLcDBkOXpCTWVGUTQyODBEM3o2NXRJVXcrUk1vTnF6STd5L0dUNm8yeW0xazRYbTdoRlh0bTJHUlFobUJybHhQRFdJZjk1WFlvY01RNWhmdGhkVHRYSElkN0JUWU5RV045VTNTbmFDTk1weklpZ1FUdUJoa1ZMRzdDYS9TK2E3THMvWnI2Y2FGbzdUNXlnOHM5RTBrL3NrcVgzREE5Vy9oanVaV3dtY0lnRUZyblhVcERDVkNMcVhoR1Bpb3U3NFBjZXNHc3FRSUM3c0pMZDAxSFlxRGV1N2RoTkVIQjdGdzNYT0RXTERsYUt3eTdRMEs3TThkMThxL3R5YzNadGlobjlDY3Q3aitIeEUxa3FnajVRYWE5aUF6bW1kVERvNVZlUGdQcy9vTXZpamNwZldUUXMzc0N5ZjBXbGhnQjhOSlg1VmJXeDdsRjcvSlUxUm1SR3lubGVBT1MvTlZQY2FyNE1oVjNibnhza1BteGJWRi85K2p6MjRJaUs2UU1Ca2hCRFo3cVorazVtbWE3eHZUOTRHNmtCa2tucEtmVVpCVzNQcm5EN2VtRkpNYm5yWTAyT0NKL2VsS0VrWjJMVVZBdEpUVU1aaEFIOFYyNFoiLCJtYWMiOiJiNWNhODdlZjkyODc4NjQ2MzFiNzA2YWY1NDE5MmM2NWEwOTQ0ZDliZDMwZDYyYWI1MWY5NWI3NzdmMGU0MjMzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}