{"__meta": {"id": "X6db3b65e125b64e3e52fb65d95745c24", "datetime": "2025-06-30 18:09:54", "utime": **********.438542, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306993.991238, "end": **********.438555, "duration": 0.44731688499450684, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751306993.991238, "relative_start": 0, "end": **********.355606, "relative_end": **********.355606, "duration": 0.3643679618835449, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.355614, "relative_start": 0.3643758296966553, "end": **********.438557, "relative_end": 1.9073486328125e-06, "duration": 0.08294296264648438, "duration_str": "82.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00616, "accumulated_duration_str": "6.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.393575, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.162}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.405638, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.162, "width_percent": 6.981}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.419493, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.143, "width_percent": 7.955}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.421334, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 40.097, "width_percent": 4.545}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4255438, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 44.643, "width_percent": 35.552}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.429809, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.195, "width_percent": 19.805}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-113086822 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113086822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424617, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1959323785 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1959323785\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-264482152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-264482152\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-275351036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-275351036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFCVDFzN2dFQ3NXRVFyTUY3K1hxc2c9PSIsInZhbHVlIjoiQ3hXNnI1VkhqMXlxai9LZDZjeEN5S01oQk5qU295VmdBVTRBc1loaWtSY2pIVU5BTzlwOHQzUFNMdVJ2eHpNbk5uZVp3Yi80U0MyNC9aVldlcVExeEtEUmJUaUpkbk00T0hGc3NVeWRZaE8rS2ZoTEZSc0FFWW1idUlDK0NjSnJ1VkhEQVdQbFRhZTZlM2IrSWs3Umc0RmJvQ01oUkt0cytjMW9xeWpIRjJVMldrd3EyTmI4dFpBV0VnKy9DblhsOENtbWhWeU8vR21ET0kzY0pnMXR4S3Z2cVovSTVvTFhzNlhxQ1NWL3o4b2g2RG1YSncxc1BRZWJsTk1vRTZLb25qMHliVERZMFgvQVkwdmpjWjhYTUVMUjdxM1F0RlJtcnNaUC9CUTFMSHVjYkhSaFZPZ1dZL2R2YTlVc2hORG1SYzY2eXJ4SG9yNWNtZCsvb3NMUHlvSFNGdXZSR05mUTZCMWxmZnBYWlgrakY3QU4yWVlZZ1dWU09PTnkwakVHelpVMHcwa0IwNmdvZm8wWVVsajVMcWVyRmRBL2ZGV0c4QTBFWjBqbnpSdEc5bmhOdWJwaGp0ZDJYMTZJcU1INllKYXk4VmlWU2FMaWQwUjJFRVBEY0pXZjJaclB3amJ1ZnNpSVpXU0lac0cycGlZZmZCRS80bVJwRmRNejRydjIiLCJtYWMiOiI5YzZkZDY1MDllZThhMGJjNWQxOWNiM2VjZmQ0YTA1ZDRmZDEzNzhmOTAxMmM1MDA2Zjk5NmNkNDZiYzNlMjAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InEwYkQraWcyWXptcGIzdFB5QzJJSHc9PSIsInZhbHVlIjoiR1MySTkyVXAwMVp0THllNDZOTm1KTlRzbW9pUjZ0V2VWRmVsNjBlcC9Ha3pUOVF3OTNaVzRyMXV0NllVUW96TDNRcTVPMlNnb2Y5YUNTWXlDSzBtN3kvMXNTME1hdlRIck8xa29pQlBpQlV1SjNXbmtlbm9PdThCZlNPV3FrK2VZMmFubWJMMjFRV3k1SlVhYTRMV1VWNWx5V0cwUklDTHI0YXM2WXdLeGtZcjB6aGlscThPeW9TdWgzN2t3Z1JPd3dMQVgxR2xXcGkvbGQ4aWFBYXRxRmhrWU5BWE55ZzhxWFZYRm83SFN0YzRMMkhOZ2tMaENkVDNoVDhzRE1zZ0VtTnNuenp2RmRzZFJaN1BqSmFqbVYrSFg3aUhwZ3dmNEhNRVVoNExZMnc3b2JRVWZDaUM5Wmw0aE5GN3BUeXNrYnFvYXRNRWUwQi80LytYaUk2RFI1ZGVyMzZPYXVNVEpTbUUvaTJQRUFUQkQzYTEwLzNPU25WMkRzdU5PblplRVlpUEtQWXVVOXV3T2NCMXE2eVVoQVBkd2wrYkdaajkxRmVabEFSWlU4czdFOHR3VWMwcjJyVjZSNDdIWkhmQ0RpVWU2OTB2UlY3QVE4anhrQXhKbGd0YnBXdEN0K1N5SlJTbjRMK2tjVlNwc3FDeVRodEdSWndJTG5Ld3l3WG0iLCJtYWMiOiJiNjU3ZGFkNTMwNDg4Yjc1ZTU1MjU5MjYwMTNiMzIxNTUzMTM5MjE5YzhjYzBjY2MwYjg4ZWI0OGY0YWRkMzdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1635226538 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635226538\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1617762347 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJCbkxleUxPUHR6MlhsTFpKaUpwS1E9PSIsInZhbHVlIjoiS2VtbUxGT1NlZlZPeEJjTE1ZV1daVE9TM3NBNXdHMlhURjY0dHdrZUt6WGdac3BGMm9vOTRoUU5MeDV3RGN4Z1hVenJVY1RLcERsSFZyL0JSS21ZZHZRTXQ3ekkrQ0VvSkxNWmJ6NVFGeXN3TWhDTUtOTWRiK1JZSGIvUkZVYThReWVkRjZPWFV1a3RDVmI4dHREQUNVYjVkbFFMbGEzQlROZ2FqQmx0SHNsb0dLVGw4N01pd3l4WUlUZ0c2RVBtdFpMb3gxWmh6dG9ndzE3K1kzbHNldTl3eW95ZHJOak5NbjhnQm43M04rQWRkQ3hXTmpCeUM4Zjc2K3c1THMrRlBsSWNUeUpLdHljSnptbk9KNVJxMkxUWXQ1QThiQnFNamtkOWM3cXRuaHAxVkx1TGJuZ0lVWmhyeGtBWVBIU2JjT2JPSi9jTVRuZmkycDNrVzE5SkZGWUwyVVZQaGlmWjFxUXFYV0Evek83RkpEMVFhYzJhZTgxMDNhSWh6QmlSdWNqZnBGTkp3Q2tNcGxXMnhpVFJGN3dzNjFXZXlJaXI0YXRRN0dyNkFzY2NvSXNNdGhPMmhOeHRtYmdsem1taXV0Z3hocWM2YTg0aWQvZTBDbnlQTjBFeUZNbVNTelNodmM3b3JwMW5KOG1FZ2NBbzRmYmgvdDVKZitFaWVPWFQiLCJtYWMiOiJkZWEzZTEyMTI5ZmYyOTIxNDgxYmQ4MGI1ODgxNTk5NmU3YmZkYzVhMzE0ZWFmOGRhYWI0NmY4ZmJiYmFjYTljIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFVMjUwMlZJL004dEdwemlheURkL3c9PSIsInZhbHVlIjoiSHpwNDliMWRJT2FYa3dJaW85Ylo1RXhuN1h0N3RwbjN1RGx1cWxKMzVuSTBmT05RNzRwOWlPcUZjaHlaUTI2aEdEdDdsaGJyMHdsejdsemQ2czJsUXhhVmdSNXBtdlJNdStmbjMyYTI1Z085WGVIWUF5WS84RGlZa09PRGlSUkJFUHZObG52TFdRUGxtSy90b3NqQUVORzhBK2NSTk0zUWZ2SVZsNFBuaFZDYndTZWdFb2FRQUplM1ZxbHVqSFNJWG93Mzk4QmJkekJTb251dzRTZWJkQzhlY05vNGVQU0dWT1BqRk9XNzlyM0NtbmlaSW1Hbi9qV3RVNFdlSnFoUmVZVXFGdGlKUTU1cjMvQ0liaHJPRHhROTBZbEJCTkloZ1UzS0M2Z3d5S3pybGhaekhDK1MvcHluMmRNWHc5NVhNUUFYVEkrWDJrY3JZYkZGQUdJMjU0MXBqYUYzMDRXOEFJZW9PdHl0c3RaVjFmc2k3QVBVWmY3WVlvSnVTK21RdzJ6YUpMOEh6Z2YxK05Tdzh5bGg4OGdLWTJmN0xYT05zaWNKVEdDdHFVOVFIQXJsRHI3WGFNQ1c3ZFFXb3JnT3JDdXcvWkUvZlBQdHltZHI5ZVRPNVUzRGhyeGEwMGkzUU9OU1BPdnNRYTVqR3ZaTEVkekpyc0ZSWVVZc0JtZVQiLCJtYWMiOiI1MWIzMjEwYjFmY2YzNWMyZWRlMDlkNzYwMWExZTg4NThhYTg0ZGYzZWY1MmFjM2NmODk0YWUxYTdlMWM0NmY3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJCbkxleUxPUHR6MlhsTFpKaUpwS1E9PSIsInZhbHVlIjoiS2VtbUxGT1NlZlZPeEJjTE1ZV1daVE9TM3NBNXdHMlhURjY0dHdrZUt6WGdac3BGMm9vOTRoUU5MeDV3RGN4Z1hVenJVY1RLcERsSFZyL0JSS21ZZHZRTXQ3ekkrQ0VvSkxNWmJ6NVFGeXN3TWhDTUtOTWRiK1JZSGIvUkZVYThReWVkRjZPWFV1a3RDVmI4dHREQUNVYjVkbFFMbGEzQlROZ2FqQmx0SHNsb0dLVGw4N01pd3l4WUlUZ0c2RVBtdFpMb3gxWmh6dG9ndzE3K1kzbHNldTl3eW95ZHJOak5NbjhnQm43M04rQWRkQ3hXTmpCeUM4Zjc2K3c1THMrRlBsSWNUeUpLdHljSnptbk9KNVJxMkxUWXQ1QThiQnFNamtkOWM3cXRuaHAxVkx1TGJuZ0lVWmhyeGtBWVBIU2JjT2JPSi9jTVRuZmkycDNrVzE5SkZGWUwyVVZQaGlmWjFxUXFYV0Evek83RkpEMVFhYzJhZTgxMDNhSWh6QmlSdWNqZnBGTkp3Q2tNcGxXMnhpVFJGN3dzNjFXZXlJaXI0YXRRN0dyNkFzY2NvSXNNdGhPMmhOeHRtYmdsem1taXV0Z3hocWM2YTg0aWQvZTBDbnlQTjBFeUZNbVNTelNodmM3b3JwMW5KOG1FZ2NBbzRmYmgvdDVKZitFaWVPWFQiLCJtYWMiOiJkZWEzZTEyMTI5ZmYyOTIxNDgxYmQ4MGI1ODgxNTk5NmU3YmZkYzVhMzE0ZWFmOGRhYWI0NmY4ZmJiYmFjYTljIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFVMjUwMlZJL004dEdwemlheURkL3c9PSIsInZhbHVlIjoiSHpwNDliMWRJT2FYa3dJaW85Ylo1RXhuN1h0N3RwbjN1RGx1cWxKMzVuSTBmT05RNzRwOWlPcUZjaHlaUTI2aEdEdDdsaGJyMHdsejdsemQ2czJsUXhhVmdSNXBtdlJNdStmbjMyYTI1Z085WGVIWUF5WS84RGlZa09PRGlSUkJFUHZObG52TFdRUGxtSy90b3NqQUVORzhBK2NSTk0zUWZ2SVZsNFBuaFZDYndTZWdFb2FRQUplM1ZxbHVqSFNJWG93Mzk4QmJkekJTb251dzRTZWJkQzhlY05vNGVQU0dWT1BqRk9XNzlyM0NtbmlaSW1Hbi9qV3RVNFdlSnFoUmVZVXFGdGlKUTU1cjMvQ0liaHJPRHhROTBZbEJCTkloZ1UzS0M2Z3d5S3pybGhaekhDK1MvcHluMmRNWHc5NVhNUUFYVEkrWDJrY3JZYkZGQUdJMjU0MXBqYUYzMDRXOEFJZW9PdHl0c3RaVjFmc2k3QVBVWmY3WVlvSnVTK21RdzJ6YUpMOEh6Z2YxK05Tdzh5bGg4OGdLWTJmN0xYT05zaWNKVEdDdHFVOVFIQXJsRHI3WGFNQ1c3ZFFXb3JnT3JDdXcvWkUvZlBQdHltZHI5ZVRPNVUzRGhyeGEwMGkzUU9OU1BPdnNRYTVqR3ZaTEVkekpyc0ZSWVVZc0JtZVQiLCJtYWMiOiI1MWIzMjEwYjFmY2YzNWMyZWRlMDlkNzYwMWExZTg4NThhYTg0ZGYzZWY1MmFjM2NmODk0YWUxYTdlMWM0NmY3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617762347\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-897859830 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897859830\", {\"maxDepth\":0})</script>\n"}}