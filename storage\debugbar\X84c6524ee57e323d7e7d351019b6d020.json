{"__meta": {"id": "X84c6524ee57e323d7e7d351019b6d020", "datetime": "2025-06-30 16:07:30", "utime": **********.073761, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299649.605931, "end": **********.073776, "duration": 0.46784496307373047, "duration_str": "468ms", "measures": [{"label": "Booting", "start": 1751299649.605931, "relative_start": 0, "end": **********.020491, "relative_end": **********.020491, "duration": 0.41455984115600586, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.020502, "relative_start": 0.41457104682922363, "end": **********.073777, "relative_end": 9.5367431640625e-07, "duration": 0.05327486991882324, "duration_str": "53.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00291, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0494099, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.856}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.059885, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.856, "width_percent": 21.649}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.066992, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.505, "width_percent": 16.495}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-456258872 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-456258872\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1896449515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1896449515\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2074260332 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074260332\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1932555327 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299627314%7C12%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxPK3liMWFjZ2tLNWJlZlFLb0QyeHc9PSIsInZhbHVlIjoicFBlRVBFVUFneGhsRFdPeWptK2VoVFhaMXpDUFErSWJESlE2YVREdStOMHcyZUtXZnRZaFN4eUR4a0JOTTBFdE81L2tLTWk2djdITDVXeHpwYXZURUJ1d245Tzlqa05QTW5CUTRZY3JGVlMwZVZUOUJCTjF5TlU5VGlIZmppREl0YUF0MlZmVklWVks0YzlLUUZuNkZTR21JQmw5bmpiNFdCUGx6TjFNWURaMlhwSWdYa2M1UW5JYUZwM05ab3M5NGZaSGxLRW41dDZiUVFCV1NkK21tMHRucUNHMlZpanZHNGtMNzNsV0R3bUJTWWJPa2pESDRNK09McmZtSEJJcTd1cjZsSWJaazdYZEtkTW96eUh1aVBBOWlzRUFWcTdBZThKR2xEZXlYOUtiYkthOXBISDYzYWJKK2p2NEpNaVR5cU1hSWUvNDhqVi9kV01pWHJ5TjZnVGhEeW9JTDkxWm9TeXRlNWJ4VW5Ua1k4TjIvZVd0dWdvUytxZHc0TVFqbE5wdDVZQ29sWGVPV3ozNWVRQ0Nnb3lZQWNqSXVodDh0eDRUNDVScXJVNmRKWUVXT2pTZkRBdDlhMVRKTVAzd2xvc1o4TzVEdGorR2dhWEx3NDlzNXdIZ0s4Wi9QRDVPY3QvWVg2ZG4wcXJnVUh0YkRXakdaWDgzOFhhUHpYZFMiLCJtYWMiOiI4NjRmZDI0N2Q1ZDlmMzkyZGJhZjYwM2NiZDAzOGMzZWZlMzExNmJhY2YxNWNmY2U2MDE1MTNiYzBjOWEyMDA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9KcHB5OTRrMFJwNVZJQWpCbG1LbFE9PSIsInZhbHVlIjoiK2s0cS9JeHVScFNrWmU3a3JERnJLYVl6MHN3NkhPNHExS3FVdytuc0poYzc1OWhMNzZLNVkvL3crRDA4L0NDUFdmZkFvTlFWdjJ5UnFSeE1PTEpOYjdoYjVDM1JZY2lsN0drZnJ1bDNPQmlBWFNOVzlrL0NoeUIvZno3MlY5eVRsM1BmRjlTZFdBV1RlcndtVGFpYm56K3NyTld5NDZ0WEVNTElpcGFiV0lhMlF4aG1xQWRhRnZGNVE3YTg5SHpTNmNXb3VMZ0d6UllnNnNMYWdqS0NSMEtiYzdzVy9QQTJDMjJOUzVzejVxc2xJN2tnbitSdTFXa3Q4dDh0WSsxQ0tlQytHWGRMakV3RktlUG11Z21uQ2orRnRzdk5iOFJjdmJUc2pCQ213OWQveGl1RkNxdnJDemRhbWlTUjEyR3pJMnE4UzQxWkZIbWhtcGpJSnhPUDBkVGt2cHZMZkxpdnYzcnN0WjFnalptRXhjWXBXQkxOZElJa0hpSnJvRUJEN0p2bnRwcDdIcGRaUFBTcFdOUzRWYzI2M3pHNzNsdWxmUDVjSDE0OXJaTnNLTzJEeWM4OExCS1BZRTJDN01rbXVkaEQvWmVrdWo1ZVcwbWV5RWVTemh1dVNjQTI2NWVaVUJWc0IzZUY3WENuMUJEQXgvNUw3VWI2YjFHRThjNksiLCJtYWMiOiJlOGExZDRjYzMyODFkZmI0NDM1ZmJmNWI4MGI0YjQzMzRiYzkxNWY5NTBkN2M2YmQwNWQyZjc2YWQzYTk1NzhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932555327\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-981536764 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981536764\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-278272624 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inp3Y2J1RytuRVlWbDFZWjhjN3lQWGc9PSIsInZhbHVlIjoiMnE0U1ZOZnhWNjlPdXY2V2FDd1JHRm9XZDl6bzI0YWw3VDgvNnBNNURWZ2g4bUdTMi9pYXhxWFcrR1hkdGhzeTZVdnZrTGp3a1QrRG5LbmdiQkcrQ2p3djNXMGdFbDEvd0dnNHVJVjIvTERXQkh5K1k4RWJyZUhNaXlETVYvdmFlOVNLazY4WUdQdFNVZTdKbVdkS01vcjVuS0crV082eFJVaXBwenZhaDJGNWxxZHJnVTFwbGdVVmZWNmIrOEZpbmNrR0Z3TC91SmMzOTZjcmU4ZzZVSXVRck90UFFqSEpsbDVGbWhjQnVvYmJlOVM0RWxybDJIZWF4VFZucUFhTXVXeWNYSVAwTlNVYXNSWlk1ZFdjNmRlOHdlcTU4OFBIR0JKZS9BT3pxUG1leXNuVFR2aGNWVnl2MXBVWjhLcmFEVGJneW53WWl1RnBvbm8rMFJHQ2JrNGZXWHBNYjhrVEZweFR0SzNGSnNVVlVaRVNhM0JvYnc5alZkQVJ5Q2hoYmxwVXl3RGhSUWNjZi9WR0JhbHZIVHc5MU9TWUJtQ2FPanorK1UwbWo0dnN6ZTJFbTVnL3ZFMmZHeDIwZkFvcnRxK1kvWXU3WDcwdHhZTStpclBXTU8yYTFnY001eGt3ajIxRVlZUENCQ292cVpDN0dXaDIybWpQb3hpQS95dmkiLCJtYWMiOiJjNjM2MzUwNDM1MWMxMWQ3OGM2Zjk3MmI5ODIzMDc3ZGZkYmNjODk4YzAyNDM2MjcwYjA0ZTkyYWNjNTQ2ZGFkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9tRUNxaTFDY2U3L2dsTHBBZGdXYUE9PSIsInZhbHVlIjoiZFZtaWN0eHNFU3J5K1RUUWlVQ3JFaGdNSDFMbkE4TnRFSjZCbkorN3lEYk1VaGZiRkxYRkNRYktVNTlkZFpoTWRBanN6czFZdjk1YzRIRDgrL0xtQ2pVQWNIMGlqNzRpeVNOdXkxcG1xRXU0U05uazA0STdIV2FmT2JGSkRZNmxad1NDakZaYXlDQ1VpamJaZSt1cWtMTEJCN3hUaVIydEc5aHJOaEUyVWF1bGtxajN3YTc3enlKZTJjMHFDbjh6b1pwOEVXQ0dvcWpXN1R0QXFmbFh1cEwxMGh1UkNUMXBSSzlmL3QzL1lIWlBtTG5rT1gxUnpDWFphc1FSNFk4T2hZVFUvbHJneGpKRlVxMUEwcXVpdVpuWlpubUtIdnBsNmxkdVZhRlR3N2NjbTBMYnYxSEJIRHo1TWx5SUROQ01kOXR5MEdZdW02SHpiYkttVzBzZ0R5NlQwcG4zclBCYjNLOGNsQTRvTjJCTVhPQlVaWHZLUlFHOTgwSW8zZVhYdlhRc3BoTC83OFE5eG5POTNaUTBsYVRqaTFzRXhQNXBheWY3aVJobWRJSENsT0g0eHBDdEJCa1NaOWtCeHlGcWRodEU0SnVvdXowV2M0NzIvY296QTFDYlFVN0lubzR3RE1ocTJLa1RSVmxXT2Uvb1k2OTNlMHE1K3VNR1F0d0wiLCJtYWMiOiI4NzMxZDg4MzMzNmEwYzg2YjJmMDQ0NjBlYjBkNTA1YmFjY2Y1MjFjZDhhMzllNDMzMzY0ZTgxM2I4ODU1MGQwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inp3Y2J1RytuRVlWbDFZWjhjN3lQWGc9PSIsInZhbHVlIjoiMnE0U1ZOZnhWNjlPdXY2V2FDd1JHRm9XZDl6bzI0YWw3VDgvNnBNNURWZ2g4bUdTMi9pYXhxWFcrR1hkdGhzeTZVdnZrTGp3a1QrRG5LbmdiQkcrQ2p3djNXMGdFbDEvd0dnNHVJVjIvTERXQkh5K1k4RWJyZUhNaXlETVYvdmFlOVNLazY4WUdQdFNVZTdKbVdkS01vcjVuS0crV082eFJVaXBwenZhaDJGNWxxZHJnVTFwbGdVVmZWNmIrOEZpbmNrR0Z3TC91SmMzOTZjcmU4ZzZVSXVRck90UFFqSEpsbDVGbWhjQnVvYmJlOVM0RWxybDJIZWF4VFZucUFhTXVXeWNYSVAwTlNVYXNSWlk1ZFdjNmRlOHdlcTU4OFBIR0JKZS9BT3pxUG1leXNuVFR2aGNWVnl2MXBVWjhLcmFEVGJneW53WWl1RnBvbm8rMFJHQ2JrNGZXWHBNYjhrVEZweFR0SzNGSnNVVlVaRVNhM0JvYnc5alZkQVJ5Q2hoYmxwVXl3RGhSUWNjZi9WR0JhbHZIVHc5MU9TWUJtQ2FPanorK1UwbWo0dnN6ZTJFbTVnL3ZFMmZHeDIwZkFvcnRxK1kvWXU3WDcwdHhZTStpclBXTU8yYTFnY001eGt3ajIxRVlZUENCQ292cVpDN0dXaDIybWpQb3hpQS95dmkiLCJtYWMiOiJjNjM2MzUwNDM1MWMxMWQ3OGM2Zjk3MmI5ODIzMDc3ZGZkYmNjODk4YzAyNDM2MjcwYjA0ZTkyYWNjNTQ2ZGFkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9tRUNxaTFDY2U3L2dsTHBBZGdXYUE9PSIsInZhbHVlIjoiZFZtaWN0eHNFU3J5K1RUUWlVQ3JFaGdNSDFMbkE4TnRFSjZCbkorN3lEYk1VaGZiRkxYRkNRYktVNTlkZFpoTWRBanN6czFZdjk1YzRIRDgrL0xtQ2pVQWNIMGlqNzRpeVNOdXkxcG1xRXU0U05uazA0STdIV2FmT2JGSkRZNmxad1NDakZaYXlDQ1VpamJaZSt1cWtMTEJCN3hUaVIydEc5aHJOaEUyVWF1bGtxajN3YTc3enlKZTJjMHFDbjh6b1pwOEVXQ0dvcWpXN1R0QXFmbFh1cEwxMGh1UkNUMXBSSzlmL3QzL1lIWlBtTG5rT1gxUnpDWFphc1FSNFk4T2hZVFUvbHJneGpKRlVxMUEwcXVpdVpuWlpubUtIdnBsNmxkdVZhRlR3N2NjbTBMYnYxSEJIRHo1TWx5SUROQ01kOXR5MEdZdW02SHpiYkttVzBzZ0R5NlQwcG4zclBCYjNLOGNsQTRvTjJCTVhPQlVaWHZLUlFHOTgwSW8zZVhYdlhRc3BoTC83OFE5eG5POTNaUTBsYVRqaTFzRXhQNXBheWY3aVJobWRJSENsT0g0eHBDdEJCa1NaOWtCeHlGcWRodEU0SnVvdXowV2M0NzIvY296QTFDYlFVN0lubzR3RE1ocTJLa1RSVmxXT2Uvb1k2OTNlMHE1K3VNR1F0d0wiLCJtYWMiOiI4NzMxZDg4MzMzNmEwYzg2YjJmMDQ0NjBlYjBkNTA1YmFjY2Y1MjFjZDhhMzllNDMzMzY0ZTgxM2I4ODU1MGQwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278272624\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1313035032 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313035032\", {\"maxDepth\":0})</script>\n"}}