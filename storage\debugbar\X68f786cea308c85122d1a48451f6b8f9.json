{"__meta": {"id": "X68f786cea308c85122d1a48451f6b8f9", "datetime": "2025-06-30 18:50:36", "utime": **********.069641, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309435.616721, "end": **********.06966, "duration": 0.4529390335083008, "duration_str": "453ms", "measures": [{"label": "Booting", "start": 1751309435.616721, "relative_start": 0, "end": **********.000202, "relative_end": **********.000202, "duration": 0.3834810256958008, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.000213, "relative_start": 0.38349199295043945, "end": **********.069662, "relative_end": 2.1457672119140625e-06, "duration": 0.06944918632507324, "duration_str": "69.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45568720, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.003299999999999999, "accumulated_duration_str": "3.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0402238, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.909}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.050993, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.909, "width_percent": 17.576}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.05879, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.485, "width_percent": 21.515}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1845773190 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1845773190\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-407452021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-407452021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1103761657 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103761657\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-778099385 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309393964%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndrdlpxNTE1bElSTkduamNHaUNRK3c9PSIsInZhbHVlIjoiQ0tFdVNvYmZnY3lDVEh0SmtEU2d1TmJLcjlRdHZiNTVqRTN5QkkrNjRjS1ptVEg1NDBranBHMDlpWDJDL1pNOHV4cVpDaG9qL1ZpT1l5cG9OOEpqWExjS1Njck9VNXNoaUo5a2RPRUpUQ1pTd2hwWVJYQ3oraTUzVXY5ZHM1cFNFdU52eWRpYnIweWtaZFZSeUludWZ1QTJRWk9GbnZpNG5rK291VWVrcU80bHhxeVRRaEEyOCtCTzN0MkJ0aXFjUVY0bTdpZ1dPQXJVVDZoNUJHK2huR1pmRGpWVnNOL2hmUTlDRWtVL2JhelJaYzVzV0pBbzdBVmlIL1VwZVA0cmhVaDJZOFA3Q2hLb0FUQVk4eCsyeHpteDJWSGVOeFNvbzZVaHRJb1k1c1RaUmVOTTIyNngyUVJaVEhwRGx0L0pNZE5LcnViVDZza2VCVWVjSzdoa25BZU9SbWlYTW02MjA3RmVJaDRMRXU0QTBjekVleUVhYzBMR2JHdXU3VjRRaFN1b3hZL1d0TDFXY0VScklWMFN6c0IyNnZvRmlEN0RiYk5UTmhYcXl2bWc3V1NZWWVUZXdMY2FRQzhjTzVVWSs4bVJNYjlCd2I0S1F1UXhZdWVTTG9Fcm1WSjUvanh6T0pxVmNvc0N4UnV0ZEFYR3R4RDcwZDNBWjdQY0p5cGsiLCJtYWMiOiJmYmU4N2Q3NDdkNzk5ZTQwNDdhMmE0ZWY3NTI2OWZlNTUzMzIxZTNkYzk4NjM2ZDc0YTIxODk5MDIyYWI2YWFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFLcUswOHp1THF6eXBSSTlCRkh6ZUE9PSIsInZhbHVlIjoiand0eXBRM3paK3VYUjd0ZlcrdmU0QWc1anhXK2sycTgvdFpNWVVWcFdwdWx2aXBXTi9ramdLdEZXTHUxbGZSWUJqL2w1NWJOSUVUSGdRMkZVSjBsYlk4cTlYM0hxZGx1dHNpeDZHRlJobGg2K2NrT2hVNTBOWmRmZGdiSERQeG9QZDNHWFlHM1diRkp5MHNJODJMYk8vbEFObEo1bG9NZmw3NlB5c2gxRkRiUTl1T3RHSmdsREVIOTdPb2JvazM3anNYRE02Q2NmcUlRd0Z0emFsY0lBQlpXaWVxaUFJQkhzVUk4R2tMNVFRNUxDWkJJUyt6aDlQT2tOUHY5a1dsWnZzTVNWT29tb00vVlRUTEFydUhkZDBxYXhQOEtwTzBLZzZiYyt2Z0o0QzIzMThsSHdhUGtONVY5QlpPUlI2U1R5am1WTjk0RTJUUGwxdGUwaVNZb3hwVVJDaXpScHczWlFpSHVYVGQ0M3JJWEZ4YThzRW9McU1wVUVPekR3b0R6dlJMYjN5djdRUVRqTC9GaWJMYWZHNmErYlgyNHQ4dXhsVy9Ic25OdFU2Yk9NNFF1QTAyVHJTM3lHV0NCVmp1TGhTN2FPNkxpSEFTRGVuUU5rUEt4VU00RkRpNGxvNXgzVXYyMUprYzI0U1RiTkpMaGlyT0o3MWRwcWdZaFp4bnAiLCJtYWMiOiI2NWM2Y2NjNWY5OTQ3MzFjMGY5YmE5ZGExZWY2ZDg2ZmEyYzE2NDg1NDllMjRiMjhhYzNhODk2MzE4OWRhNWViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778099385\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-985608089 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985608089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:50:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ5UllkVWF5YVJHcmRHV1lMNjFQSWc9PSIsInZhbHVlIjoiR0VzZXdZRlVBVlFYd1E0Ylc2aTNKSXJZMjMvTVhwQkdESGFHeFNyYU03Q3JxQkhVVVhKcXVXU2Q2SjRxMGd2VDZvRCsrUURyRlZFNWtCSTdGZ21Sa3VrRExNWTNXK2dKNDBXK0RVR09EK2dTVFdkN2pXYzYzcVlGUXlhdE1ZOWFvN2x4RUVvd1pnUzJ4eVE4N0I0dHZpUUsvcC9OMkQ3WkkzM1dkR3pwMWZxaXdMYU1IQ25jYkU5V2tyWjhFQmN0c0tRT0RRWXRVV3BtMDFVM1JKMW5WNXp0VkI4V3RkNnRPcXBpNmpRdTBpQnoxdnhIbXF4eXQ1aFBCZG1aUkRNWVlQRkVxbFlSVmV1ZEdNenhMN2FJb1RhUTd4VHRNdEZTaFZmcFJKMWN0OFRKby9KY05MY0RydGIrc21YWnJuL0MrbXRpZ2F5OUFWaFlUMWJCYitUWjMvRmh1ODhNRC9ydXpTRUM3emtucVRXZlp0eEpaL25mWkFIaDFHU3hNMUdwMkFpMUtXbGI2V1h4NXh1N21iNitFK0VqWDlKa3d2Mmc3MSs3Rmw4QmpFaElCSW1rRFgwNUJsUG9HWEtUaTRIRlpvUmorWTM4aWRkc3NySXlvY21kV0RVVnArYnhSQlJONnA0cEJ5STJBTDljVVFiU3VpUFNkUmdjWDdNcGpvNDIiLCJtYWMiOiJlOGZiYjEyNGZiOWEzMjQ3MzM4MGZmMDcwZjM4MGQ4MzQ1MTg0YTY5YzU0ZTk1NTI3MWY4OWRmMTcwZjgwZWUzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjMvWUVPQ0QyYmdpTTNYZXRHQWkxVHc9PSIsInZhbHVlIjoiL3djN2tZOThSMVRmaUIwUjBPSkRqWHgxQlNSRU8wanFXQ1hhamxhVlpYYzlLMk1Sdi9URjFLdXc3RHp1T0RPVFRWRVA5RnM4djVaU3ZKa09GcnUxWUIyeDBrbGFiQ2JSSXRmcmpNYnZlOWJkR1dGVUg0bWFlTHJMOHFiL1dlSHBJd0UycDlDbXJEbVBjTWtmaTZuWm03WVI3RUJNQlUvc1NTNUZtb2NyZjdUK0Ezck8xdE8xTUQ1UVpaY1c0eWJlN3hiZkFwTkR3ZVNNMzdpVEhKcld0WTRYOFRsMHlxT3MzQWFJSDhaNHVDeml4TjRTNklXWE15QVVRR0RHRHhBRlJ6Z0VoRG54UGZ6aUY5SmRrSjUycXJwaklkNUo0cEduNXB6Q25uUElBS2s4dFN4eWU1MDFHcTI3YVB0cWNLTjJPbHFqUWVYT0MyR1FUQk16RWpEMDhDdm56ZSt0Lzl0bTB5dEVtQmxpVFY3aFpPUXk4NU9uRGV3Q0ZDbWtpbWliZ25WN1Z1UDJtL0JyZm91S3Aya3Z6V1owTmZjNUZhS3NYSDNyUzNPZmNURE5mVFBaZEVXZG9RaElHTnE0WTBIWVRIQ0dUZUJNek93ZlRieWp1dVQwQlM3cGlSVllQNHpZM2s1YXJsZ2pVRUMvdnIrTXRST2JwSGdwSTU4OFozUkEiLCJtYWMiOiJkYjE0OGQwZTlkYmQzYTc0YmJjNTQ1YmM0YTUwMTk5ZTkwZWE5MGUxN2Y3NTM2MDE0NDM3YTkzNDM5MjMxNmI1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ5UllkVWF5YVJHcmRHV1lMNjFQSWc9PSIsInZhbHVlIjoiR0VzZXdZRlVBVlFYd1E0Ylc2aTNKSXJZMjMvTVhwQkdESGFHeFNyYU03Q3JxQkhVVVhKcXVXU2Q2SjRxMGd2VDZvRCsrUURyRlZFNWtCSTdGZ21Sa3VrRExNWTNXK2dKNDBXK0RVR09EK2dTVFdkN2pXYzYzcVlGUXlhdE1ZOWFvN2x4RUVvd1pnUzJ4eVE4N0I0dHZpUUsvcC9OMkQ3WkkzM1dkR3pwMWZxaXdMYU1IQ25jYkU5V2tyWjhFQmN0c0tRT0RRWXRVV3BtMDFVM1JKMW5WNXp0VkI4V3RkNnRPcXBpNmpRdTBpQnoxdnhIbXF4eXQ1aFBCZG1aUkRNWVlQRkVxbFlSVmV1ZEdNenhMN2FJb1RhUTd4VHRNdEZTaFZmcFJKMWN0OFRKby9KY05MY0RydGIrc21YWnJuL0MrbXRpZ2F5OUFWaFlUMWJCYitUWjMvRmh1ODhNRC9ydXpTRUM3emtucVRXZlp0eEpaL25mWkFIaDFHU3hNMUdwMkFpMUtXbGI2V1h4NXh1N21iNitFK0VqWDlKa3d2Mmc3MSs3Rmw4QmpFaElCSW1rRFgwNUJsUG9HWEtUaTRIRlpvUmorWTM4aWRkc3NySXlvY21kV0RVVnArYnhSQlJONnA0cEJ5STJBTDljVVFiU3VpUFNkUmdjWDdNcGpvNDIiLCJtYWMiOiJlOGZiYjEyNGZiOWEzMjQ3MzM4MGZmMDcwZjM4MGQ4MzQ1MTg0YTY5YzU0ZTk1NTI3MWY4OWRmMTcwZjgwZWUzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjMvWUVPQ0QyYmdpTTNYZXRHQWkxVHc9PSIsInZhbHVlIjoiL3djN2tZOThSMVRmaUIwUjBPSkRqWHgxQlNSRU8wanFXQ1hhamxhVlpYYzlLMk1Sdi9URjFLdXc3RHp1T0RPVFRWRVA5RnM4djVaU3ZKa09GcnUxWUIyeDBrbGFiQ2JSSXRmcmpNYnZlOWJkR1dGVUg0bWFlTHJMOHFiL1dlSHBJd0UycDlDbXJEbVBjTWtmaTZuWm03WVI3RUJNQlUvc1NTNUZtb2NyZjdUK0Ezck8xdE8xTUQ1UVpaY1c0eWJlN3hiZkFwTkR3ZVNNMzdpVEhKcld0WTRYOFRsMHlxT3MzQWFJSDhaNHVDeml4TjRTNklXWE15QVVRR0RHRHhBRlJ6Z0VoRG54UGZ6aUY5SmRrSjUycXJwaklkNUo0cEduNXB6Q25uUElBS2s4dFN4eWU1MDFHcTI3YVB0cWNLTjJPbHFqUWVYT0MyR1FUQk16RWpEMDhDdm56ZSt0Lzl0bTB5dEVtQmxpVFY3aFpPUXk4NU9uRGV3Q0ZDbWtpbWliZ25WN1Z1UDJtL0JyZm91S3Aya3Z6V1owTmZjNUZhS3NYSDNyUzNPZmNURE5mVFBaZEVXZG9RaElHTnE0WTBIWVRIQ0dUZUJNek93ZlRieWp1dVQwQlM3cGlSVllQNHpZM2s1YXJsZ2pVRUMvdnIrTXRST2JwSGdwSTU4OFozUkEiLCJtYWMiOiJkYjE0OGQwZTlkYmQzYTc0YmJjNTQ1YmM0YTUwMTk5ZTkwZWE5MGUxN2Y3NTM2MDE0NDM3YTkzNDM5MjMxNmI1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}