{"__meta": {"id": "X21dc1ed10cdcb5984da57a31d7eeb8c7", "datetime": "2025-06-30 18:00:30", "utime": **********.188713, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306429.763727, "end": **********.188733, "duration": 0.4250061511993408, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1751306429.763727, "relative_start": 0, "end": **********.121612, "relative_end": **********.121612, "duration": 0.35788512229919434, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.121621, "relative_start": 0.3578939437866211, "end": **********.188735, "relative_end": 1.9073486328125e-06, "duration": 0.06711411476135254, "duration_str": "67.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47454088, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1611\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1611-1625</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00287, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.149687, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.052}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1592011, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.052, "width_percent": 17.422}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.172126, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 72.474, "width_percent": 17.073}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1740131, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.547, "width_percent": 10.453}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1457433953 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457433953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177473, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "error": "العربة فارغة!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1779944511 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1779944511\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-805688768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-805688768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2104951283 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104951283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1549468190 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlV1aVByZVpIcUVQZXJCeFZUUVhyclE9PSIsInZhbHVlIjoiR1oyV2lmbE5ZWktPQUhOQXVCSjZWYlVXNzg1eUZkTE9ZbVRhL0VoVzQreWhPbXgrNk9TL2pmMUJPS1hvcjhMRFJEMXg4bHVQOEpEcFUycEprL0o1WHdLNTV3dGRYOEdSRWdFYlM3N0xPTHJ0T05qNmdmZ0xoTkkzV3N2eFcyVFpicXFtanFRV21KcmRpUFVXRjFhWUcvNitBcXZFVlNWOG51akFEQlVSSWprQkIwOFczZ1FZKzV1VEhZTXd1QVZlN01vTWRLMjZFemxtOFJNVUFFWkl4bEdQL01LOS94Tm9LUlUzWW8zUzFrckxkZ0YxNmgrUmNySHVzaWtIdWtseFN6U3NxcUVOU3A4dU9RSUxPQ2RhajV4dmliQnJWTHNCdUhVeklvNW5pWTNIRVZBeld2aVJWYjN4bTJXVUV3V3o0ZWxTa2poaEtrbTdVOUhUOEpBVGY0YUVZNEVFT0lWTWFYSVd0Y2lsdjVBZFA5Q0Z1WDQ3ZlRQdUdDK090MFlvZkh6ZXB0c2h0b3ZTeE5XWHFwbG9pTXAyRFQ4czBidC9TaW5zUEh4NS9JL3Ezd1dUc2I5Q2N2RGtlc1g2bEhqM1JLY1BVM0ZzMGt6T0w5OHdhdDdJUFhGekNWb21hbzlYZmxBTGwwYWpocmJDVHppN0kwekF0N3dQOVROK3JkaVIiLCJtYWMiOiJlMzY2NDI5NzVhMzU5NDU4ZjkwNTJhY2VhZmUxMDAwY2E0YmM3NTVmOGVjZjI4ZDRhYWU3NDViZDI0ZDU4M2Q1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFQWkx0d0t4OWdwYXhvSmxmcEJwbFE9PSIsInZhbHVlIjoiZjZiMWVzeFdZTURuNDdpTjI1TFZ5bG1DcGdGQmdlVlhvR3MvRkV6TjkvYVJvZm5WQmJSd2wrRWVLZXlwZ2dQekRPWkJreDFNeStnS0tNdHJLMmtFVVRsQTJOd0R0c1Z5Ny84MDNiWDJ4aDd3V2dpNytDTGV5MTFpb3NaVTZ5d0dEWXJiTHg4R2Y5Rm8yTGhPVGF3ZjZqckQvVG11aFdrT01jRkg1SDViQjVJUnRFaDNmbUZGNzZPM3gyRTdBTDF1QitPeHhxRzRaMzVTT2puYVdEVldQamNpVWZSRkpZcDlzWHpnajJ2UVRZRnBTNzdYelFieVlUK2FwdThoQjdMMEcrN2Q4bm5kUkdGRjBGcmJiNlhiUVBzamNiQTB3YWwzd3l3Y1pNWCtJa1J4Nk1kMXAyakpYeVB4T0p2VlZUeUQ2UGtLMkdMVE1QTkU2SWtMVmhIU1Q5cEY0T080RXhlS0o1UDJKZGJDVjNEUC82Ykdic094aTlQNGZJSkhYSC9YNGZaQkgrYUZqbFkyR01ra3hNWnRLQUFGemZxSkgvTnlCYXBOZVBMcVBIa1FVdFZSNWhRc284V1B5c3hhcnhPM0wvMmJNTWdCYXJmclJxdGxDaFFJT1lRdUtVTnYvaTMrSUFoOWRqdDRPbnZpd1RRNVB5TThuejZkeUY3K1ZFdWsiLCJtYWMiOiJkMTdmZWQ5NTE1Mjc1NmY4OTFmYzlkMDQ4Nzg2M2M5ZjVkNjEzMzc5Yzk0NDBjMTA3N2M4ZDA5NGY2YmU3YmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549468190\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2043012566 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043012566\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1040472312 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:00:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJqazgvSnhNSXdjYzdIMFhveVkzK0E9PSIsInZhbHVlIjoiN1BEQU5JRUFsWTBXUGwzeTFucTFoOVQ5Y0tPbTJESlh2dEIvVllUUXAvQmtwcCtjTHA2anY0dkZXM3ljMHF3cmhpSDkxRWJYUmdpSEFGKzkrU2pxMHJQa0tSVWhRbi85eGVmZXdZM1VZL2dDcEdVR0ozanEyMThvZ1Rtd1g3dkJYRC82SEJQWTl0N2FIK080bmcvWldiR3BEVG5Pc1FHc3JDSERBVXpFTm9UdGdCRzlJOW9FbGpXL052Y3gvMGlnc0JoejFVaDZrZmRZaklhNUxwVllUM1VlWmJrWDBCaHVkR2I4THNFcFI5dHZYc3RseHRUNUk3WENnN0NTdC9SMFpnTDFjbTJtU0VEb1ZJeTN2NGhQK3UwRjNWV2ZQWnhOT0VENFphZTdFK05FOWNmbmhDSXVLSG42Z2UrSXRUZ0pUTmYrRFlhU1ovWDBVdDdXSGVyY1dhUlNxUUk0VzFuN1I1YVVKdmh0KzFHRW5VQ2ZwdkdWL005OW5QL2xGNTVtdEhDaTlYZmJoSDRmQ2VXY1lWUTNHS1JDQlRydHF0SkdPa01HOEFIVUw4ZnhrS3J1Q3ZkZkVCUlNxVFZ5THBkUk1KbmRMY080UG9ycXRPRlk4T09DbGF2c3lOODhCZXVrSzlxaVEreVBvU25GSGRHcHJnaUM5RUQraENFbFVIQWgiLCJtYWMiOiI3ZTdkOTQ2YzNkZDVhYTQ0NWZhNmI0NTFiOWI0NWVhNmFiMjhiMGZjN2VmNDcyYmEwZWRlYTE0ODU1MDZjOTFiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:00:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkEwODk4cVlRQ0kzT0ZZWDVqamlhYUE9PSIsInZhbHVlIjoiNW9wd1J6MDMzMFZJa3lHdDU0dEFJZk9PN05QRkgyOFpmL3BqMWtjdzRrZDZrVlhPdGlmcmtDTVR3NU10VnBKT1lGcWdiSXZ2QlhDQzRlNWVtcUFwa0UxVTQ0aDd1bUNzWGhSTXBqMFIrbW5VMXh4YTdEWWVZUHR4RjYvWDZXNUNFQ3gvelh0NXo0VGpNZHJ1MmlYWHR0L01ZSURuTTFiNlZ6MVhxRVR5WDM1YUxHbVVIb21Ja2dZd0FicDlRWnNEeDZDWDlXU09PRWszNjVPbW95cldDRGhYYkZ3alI4OE4yT3YyQmZwM0ZhOTV6Sk5YRjI1R1QyTkdoTXVQSXU4Y2lidThrSVRGWUthamxJOWpmUnRYcmNlOU9SZFNIUFdpT3ZDZEhJdGExcTRBOGxWSTU1dmFiR0F4by9GWlQwcThFSE45Mjd4WEFVenZjMmdKVzMxTzAvSDlUMUFCeSsrT21rbk5EUU92K3FWNm55bHVvcE8yYzBVWWI3d1hhRDBZWE1odmNkb2NsWWp5VEVVbHBKdFQyR3dNSmZOdVppSU5VV0JLSEthU0RDTER0TmFQNkpleUhYazVSVWVpdzZ3MzErTGZCTjJseEdxOUJ6WHUrVCtNY29WenJHY3ZHM3IreGFyVHNLdUNvM0l0WHBPQWV4dHV4TlhOdGtkNngxYjMiLCJtYWMiOiIwNzVhNTY4OGY2YWUwMjk3ZDdmNGJiOWQwNjI2NjliYjBjNjY3M2JiNTM2YzA5NzhmZjAxM2U2YTI4ZjE4NWFlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:00:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJqazgvSnhNSXdjYzdIMFhveVkzK0E9PSIsInZhbHVlIjoiN1BEQU5JRUFsWTBXUGwzeTFucTFoOVQ5Y0tPbTJESlh2dEIvVllUUXAvQmtwcCtjTHA2anY0dkZXM3ljMHF3cmhpSDkxRWJYUmdpSEFGKzkrU2pxMHJQa0tSVWhRbi85eGVmZXdZM1VZL2dDcEdVR0ozanEyMThvZ1Rtd1g3dkJYRC82SEJQWTl0N2FIK080bmcvWldiR3BEVG5Pc1FHc3JDSERBVXpFTm9UdGdCRzlJOW9FbGpXL052Y3gvMGlnc0JoejFVaDZrZmRZaklhNUxwVllUM1VlWmJrWDBCaHVkR2I4THNFcFI5dHZYc3RseHRUNUk3WENnN0NTdC9SMFpnTDFjbTJtU0VEb1ZJeTN2NGhQK3UwRjNWV2ZQWnhOT0VENFphZTdFK05FOWNmbmhDSXVLSG42Z2UrSXRUZ0pUTmYrRFlhU1ovWDBVdDdXSGVyY1dhUlNxUUk0VzFuN1I1YVVKdmh0KzFHRW5VQ2ZwdkdWL005OW5QL2xGNTVtdEhDaTlYZmJoSDRmQ2VXY1lWUTNHS1JDQlRydHF0SkdPa01HOEFIVUw4ZnhrS3J1Q3ZkZkVCUlNxVFZ5THBkUk1KbmRMY080UG9ycXRPRlk4T09DbGF2c3lOODhCZXVrSzlxaVEreVBvU25GSGRHcHJnaUM5RUQraENFbFVIQWgiLCJtYWMiOiI3ZTdkOTQ2YzNkZDVhYTQ0NWZhNmI0NTFiOWI0NWVhNmFiMjhiMGZjN2VmNDcyYmEwZWRlYTE0ODU1MDZjOTFiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:00:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkEwODk4cVlRQ0kzT0ZZWDVqamlhYUE9PSIsInZhbHVlIjoiNW9wd1J6MDMzMFZJa3lHdDU0dEFJZk9PN05QRkgyOFpmL3BqMWtjdzRrZDZrVlhPdGlmcmtDTVR3NU10VnBKT1lGcWdiSXZ2QlhDQzRlNWVtcUFwa0UxVTQ0aDd1bUNzWGhSTXBqMFIrbW5VMXh4YTdEWWVZUHR4RjYvWDZXNUNFQ3gvelh0NXo0VGpNZHJ1MmlYWHR0L01ZSURuTTFiNlZ6MVhxRVR5WDM1YUxHbVVIb21Ja2dZd0FicDlRWnNEeDZDWDlXU09PRWszNjVPbW95cldDRGhYYkZ3alI4OE4yT3YyQmZwM0ZhOTV6Sk5YRjI1R1QyTkdoTXVQSXU4Y2lidThrSVRGWUthamxJOWpmUnRYcmNlOU9SZFNIUFdpT3ZDZEhJdGExcTRBOGxWSTU1dmFiR0F4by9GWlQwcThFSE45Mjd4WEFVenZjMmdKVzMxTzAvSDlUMUFCeSsrT21rbk5EUU92K3FWNm55bHVvcE8yYzBVWWI3d1hhRDBZWE1odmNkb2NsWWp5VEVVbHBKdFQyR3dNSmZOdVppSU5VV0JLSEthU0RDTER0TmFQNkpleUhYazVSVWVpdzZ3MzErTGZCTjJseEdxOUJ6WHUrVCtNY29WenJHY3ZHM3IreGFyVHNLdUNvM0l0WHBPQWV4dHV4TlhOdGtkNngxYjMiLCJtYWMiOiIwNzVhNTY4OGY2YWUwMjk3ZDdmNGJiOWQwNjI2NjliYjBjNjY3M2JiNTM2YzA5NzhmZjAxM2U2YTI4ZjE4NWFlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:00:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040472312\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-875435426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1585;&#1576;&#1577; &#1601;&#1575;&#1585;&#1594;&#1577;!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875435426\", {\"maxDepth\":0})</script>\n"}}