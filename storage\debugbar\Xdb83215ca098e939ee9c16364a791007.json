{"__meta": {"id": "Xdb83215ca098e939ee9c16364a791007", "datetime": "2025-06-30 16:10:00", "utime": 1751299800.085827, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299798.997308, "end": 1751299800.085849, "duration": 1.088541030883789, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1751299798.997308, "relative_start": 0, "end": **********.347005, "relative_end": **********.347005, "duration": 0.3496968746185303, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.347016, "relative_start": 0.34970808029174805, "end": 1751299800.085851, "relative_end": 1.9073486328125e-06, "duration": 0.7388348579406738, "duration_str": "739ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50819120, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.59953, "accumulated_duration_str": "600ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.377697, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 0.319}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.387586, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0.319, "width_percent": 0.075}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 22, 9, '2025-06-30 16:09:59', '2025-06-30 16:09:59')", "type": "query", "params": [], "bindings": ["100", "0", "22", "9", "2025-06-30 16:09:59", "2025-06-30 16:09:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.470534, "duration": 0.11777, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 0.394, "width_percent": 19.644}, {"sql": "select * from `financial_records` where (`shift_id` = 61) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["61"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.590629, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 20.037, "width_percent": 0.118}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (61, '100', 22, '2025-06-30 16:09:59', '2025-06-30 16:09:59')", "type": "query", "params": [], "bindings": ["61", "100", "22", "2025-06-30 16:09:59", "2025-06-30 16:09:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.592632, "duration": 0.36763, "duration_str": "368ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 20.156, "width_percent": 61.32}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-30 16:09:59' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-30 16:09:59", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.961508, "duration": 0.11106, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 81.475, "width_percent": 18.525}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-966766018 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-966766018\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-341211900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-341211900\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-547587454 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547587454\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6Ik9XNXdQbmVqNGx1TnVMOERCUnlnNkE9PSIsInZhbHVlIjoib0pNMnI2M3EvWkN2UDlKZkVDYnBTQVRjSnZWbTIzeEhsUHhlUkx6RGl5UlFNUmpFd0M3bGVIOXMrWWkzcXp6czJHVmxSaGVNejJmMmNNcFcycWR5QlU5RU4yUk1IQUhmbjlxdWIvM2p6b2drKzJFa0s1SXNMYzBuNS9xclhBUkdISHJTKy9RbnRUM2o2SHVMNVp2SWVqZ0RjclY5Qm1PTDBQVll3TksvZHF0Q1plSWFkeTQrcDJXQ2lFaHVib3YxR2NiOUNrcG9hQVFGaWNkY2xLZVVNMFhjYWFJbjJwZ2xsY3lmK3RDQk10WDYrc29oU2lsYUFHbzFpNFd2amxQck9jWXA3Q29IMi9LOTVzQSsyclFrdU9WNWZPdlVHNzVNUzBWajB1MkxDL21XMGh1SUhMZCtjNnJ6dmFmem5FMXY2RXl2NDdSdTRLdi9LUVdrN01iUTNLb2tsMUpWTElpYW1ZczdQa1RoaTViMW45WitScWJJeUp4Q0VqeUpVUEFDL200ZWdLRjhCejBrL3d0cXFZeFZxd3BwNVROcWk3aEhvTUNwTXNQWkV5bitHSTVDVFVNbXpkNmRDNFZPb2JnamI2cTFQRXZGb3J6L0locFdmdEkwcEsrMjRBc1dCRHRMeWZ1dmM0UXlsY0pKMWl5SFF2Zjc0MmE1eTVnemZibHYiLCJtYWMiOiI5M2ExN2MwM2Q0OThiNjMzNjdlYTgxNGNiMzkxZTRlODFjZmU3YTY0MDUzZDVjZWNlOTVhMTNhODA4OGNiMGVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9STmVBbmdKNklUYXVBN0VHeTE0MFE9PSIsInZhbHVlIjoiV2RIaTJHcXFBMlhqYmhWc0hoTGM4WGNhektzMFpZbFNxdGR4MVlMaHhaUnVhWnBUeW94OW1QNjh5S3lWQXF5L2JaRjNtQjV2T2p1Ykx4bEhKU2dNOEE5MkFKRWF5TTIrd210TWtFOHFVVkZvZmQxTW56ckgvSDlUTUZKWGF3U3VUd1BWNTBsaGdIYzFSaDEvbVBsblFjT0JYZGZVWW9kUGJsMmhDZHA4cGhYcmRkYzY2WWlMTm5WejRhSlpmWm9ERGI1QUVMZk93UHd1MG9xWllMZHhsVlM5cVY2Q1VXTE5wa2lPdG5EcmprNGRtV2VyQW85VG4yQUdyY2o0R3JpeWd2UDZUczBkeWU5Y1NFa0JMR1haWVZvUm9FM1dkSUs4aWhlcjQ0TlRoVTJ2bnRoL2xWS1JvSUNEK3ZBSnhSL2tiY3ZxcUNSa21BYjZHMEdZekZId3JaMS9FcHQ2VnVka3p3SzNnNDdidjRlNkVMdDE2bGJCeFVtYUN1RWJPekdpUEZHeHp0aTZrWmRLdFI4WVFabEtuYkVDdFp1c2ZIOHByQ0VBc2tpemhTbWJLRDBjVWFDdkJiRVgxejZGME5LQW45WVlCQTBCVmZIek8zejVYTHJzb0tIN2JOdVNGNkFxTzk2V20zOE9GMW9wL240QlNiUUNJam03UTFHamlDWWEiLCJtYWMiOiI3MmY1OGE5ZTgzY2NjNGZmNzU4NjIxMjZiOTdlNmE0YjViNzQ5YTEyMWM0MDExZWZjZTgzNzJhNmExMjA3ZmNjIiwidGFnIjoiIn0%3D; _clsk=16h7wx3%7C1751299796773%7C6%7C1%7Co.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-521095374 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521095374\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-124594617 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRlSUplcXk0dUo0aXVVZXdFNmI0ZUE9PSIsInZhbHVlIjoiYXpTOWQ0bFJSUDluWXo4UTYwOWRUeFJ3ZitpWW53STFLcUNyT3d1K2hFZUg2bHJXSTR0MWx6RU5hZkI3TnhWQjVkcTJsL2I3Y1h6b3N5WXQ3b1JWN2h6cUl1UmJQZXFZUGRpUW9nQ0tGZTE1V3JiZUdEV3pCL1J4VG1VdmFRQks2MTJWc3FZVkVzbnNHbG5yRW9GN0hWZ3UwZ0pDWHdZRjNyMVN3c3N3aVk3UDFlNDZRY2RYcWphZVVhWmZYYmZYS2xEMUh5eXpmNE9zSFlCeCs1QjVXdEh6cm5iYjd0aThCaUlxOXlockZZdjFQajFKbVhPODQzRUlhVFpSNXhjNVZWeTVJTHhWaFNvSjJZYlV1bUZZWG9nYVhtMDk0cGdtOXc5NVJDZk91SXlRTDFlWlNtQ28zOEUwckpaNE94c0g1Tmo3cE4zOXVFdFZsRWZPMDRqcXBxcFF5SzBxMVFoTkphUEVHbjRnclhtbXJ1UXFUVHY2blNPZ0RVeUdVVnNNYk84cGNJU3NnaHJrTWV2LytKY2tvT3Q1cmtVQ3NNZFlDekRXeWNqWnpZc3llRTBCb20vMzB5SG80eVpxazNPajVJYXRPejRaTmdrK1NPbFpSUGF3REE4N3ZnL0QvQWowWEs2d3o1Y3ovT2hkcFpFUUtzMGgySzg2ZGVoLzl0ZHMiLCJtYWMiOiJmYjkxYzBiMjAxODA5ZWQwYzY3OGI4OTBkNGQwMjQ0ZDk2YzI2NDIyNWJjY2E5NTJkYTJhOGVkYWQ5NWEwZmJmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpVcklEYWczRmswYnJEdjJHK0svcVE9PSIsInZhbHVlIjoidW9ZVWNEajIxQ0YzYThkbEhVQnhqTjNycVYrRUJhNW1kb0szQ0VObnZSZVIzVUxxVmZwUmVUUW1Qb0FrTmVqK0lPMzVPZWMrcjdKSXNQUlZYWERtWmVQMjRNS1pWcTVsczYwaHVZbjVCODRJYzk2SnZSSHc5TUh4K1BvTXZmUjhTUitEaCsxbUdMTXlVODhzanA5cWlQWUNMWEFva2FkSHdBNlQwVDl0VmhmQktNdG15VmZKNWV6WW9kd1lIODEvNkJVdllGNXozV3RJTjFyaDN3K3FnS3EwRGpKWkZiRVFPcURjNGR0aURSOWJIQ2V1MG5yRzZmNUhlWFQvUFRMZXIyMDhlT3dqZ1A2bmJCMzBRVVgrMjlOTlA0ZElUQkxOM3o0NXpuMnFmNnBTQlVGUVNPTktXY0x4UjRuZ01sczJLemJpalJSSi9aTHlGTTg4SmJqWkVvMkdzVEh4ZjI2WXBzK0JuZndqMGwyeXc0amhGZVpBZjNveUUwempHbmxqaGQwLzFQNHdGSkZVUjEwdHA0cUI5WlU0K1J1M1c0V3pqa2c3d3dJV1pxdExaYk04ZWNiaHRBaXJEVW9lZnRGMzNlVE1qS3pxSVlzYTgxMXErdUN3LzdjSERWSVNGbmgrditMWEFpTXNtNFV2bnIzQytOMlkwWTlRTnQwdmVYMjMiLCJtYWMiOiI3NmM5N2EwODQzZWM0ODhjOTUzMmVkYjhkYWE4ZjBlY2NlYWYwNTFhNzRlNDI0ZDI4NDdlZDgxYjI1MjkwMDRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRlSUplcXk0dUo0aXVVZXdFNmI0ZUE9PSIsInZhbHVlIjoiYXpTOWQ0bFJSUDluWXo4UTYwOWRUeFJ3ZitpWW53STFLcUNyT3d1K2hFZUg2bHJXSTR0MWx6RU5hZkI3TnhWQjVkcTJsL2I3Y1h6b3N5WXQ3b1JWN2h6cUl1UmJQZXFZUGRpUW9nQ0tGZTE1V3JiZUdEV3pCL1J4VG1VdmFRQks2MTJWc3FZVkVzbnNHbG5yRW9GN0hWZ3UwZ0pDWHdZRjNyMVN3c3N3aVk3UDFlNDZRY2RYcWphZVVhWmZYYmZYS2xEMUh5eXpmNE9zSFlCeCs1QjVXdEh6cm5iYjd0aThCaUlxOXlockZZdjFQajFKbVhPODQzRUlhVFpSNXhjNVZWeTVJTHhWaFNvSjJZYlV1bUZZWG9nYVhtMDk0cGdtOXc5NVJDZk91SXlRTDFlWlNtQ28zOEUwckpaNE94c0g1Tmo3cE4zOXVFdFZsRWZPMDRqcXBxcFF5SzBxMVFoTkphUEVHbjRnclhtbXJ1UXFUVHY2blNPZ0RVeUdVVnNNYk84cGNJU3NnaHJrTWV2LytKY2tvT3Q1cmtVQ3NNZFlDekRXeWNqWnpZc3llRTBCb20vMzB5SG80eVpxazNPajVJYXRPejRaTmdrK1NPbFpSUGF3REE4N3ZnL0QvQWowWEs2d3o1Y3ovT2hkcFpFUUtzMGgySzg2ZGVoLzl0ZHMiLCJtYWMiOiJmYjkxYzBiMjAxODA5ZWQwYzY3OGI4OTBkNGQwMjQ0ZDk2YzI2NDIyNWJjY2E5NTJkYTJhOGVkYWQ5NWEwZmJmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpVcklEYWczRmswYnJEdjJHK0svcVE9PSIsInZhbHVlIjoidW9ZVWNEajIxQ0YzYThkbEhVQnhqTjNycVYrRUJhNW1kb0szQ0VObnZSZVIzVUxxVmZwUmVUUW1Qb0FrTmVqK0lPMzVPZWMrcjdKSXNQUlZYWERtWmVQMjRNS1pWcTVsczYwaHVZbjVCODRJYzk2SnZSSHc5TUh4K1BvTXZmUjhTUitEaCsxbUdMTXlVODhzanA5cWlQWUNMWEFva2FkSHdBNlQwVDl0VmhmQktNdG15VmZKNWV6WW9kd1lIODEvNkJVdllGNXozV3RJTjFyaDN3K3FnS3EwRGpKWkZiRVFPcURjNGR0aURSOWJIQ2V1MG5yRzZmNUhlWFQvUFRMZXIyMDhlT3dqZ1A2bmJCMzBRVVgrMjlOTlA0ZElUQkxOM3o0NXpuMnFmNnBTQlVGUVNPTktXY0x4UjRuZ01sczJLemJpalJSSi9aTHlGTTg4SmJqWkVvMkdzVEh4ZjI2WXBzK0JuZndqMGwyeXc0amhGZVpBZjNveUUwempHbmxqaGQwLzFQNHdGSkZVUjEwdHA0cUI5WlU0K1J1M1c0V3pqa2c3d3dJV1pxdExaYk04ZWNiaHRBaXJEVW9lZnRGMzNlVE1qS3pxSVlzYTgxMXErdUN3LzdjSERWSVNGbmgrditMWEFpTXNtNFV2bnIzQytOMlkwWTlRTnQwdmVYMjMiLCJtYWMiOiI3NmM5N2EwODQzZWM0ODhjOTUzMmVkYjhkYWE4ZjBlY2NlYWYwNTFhNzRlNDI0ZDI4NDdlZDgxYjI1MjkwMDRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124594617\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-26009519 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26009519\", {\"maxDepth\":0})</script>\n"}}