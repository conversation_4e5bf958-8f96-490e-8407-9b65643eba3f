{"__meta": {"id": "X8ca5aadb836a8de3d5f7de4c0b891759", "datetime": "2025-06-30 16:04:58", "utime": **********.247048, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299497.734573, "end": **********.247063, "duration": 0.5124900341033936, "duration_str": "512ms", "measures": [{"label": "Booting", "start": 1751299497.734573, "relative_start": 0, "end": **********.184475, "relative_end": **********.184475, "duration": 0.4499020576477051, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.184485, "relative_start": 0.44991207122802734, "end": **********.247065, "relative_end": 2.1457672119140625e-06, "duration": 0.06258010864257812, "duration_str": "62.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45226496, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00235, "accumulated_duration_str": "2.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2295702, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.851}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2400732, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.851, "width_percent": 19.149}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1512694946 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1512694946\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2128975542 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2128975542\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1645666864 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645666864\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZEbmZtcjNMRnNLVTRqWEN5RENZOVE9PSIsInZhbHVlIjoibUpaTWVKZ1lqWkNIdTRSZTBVSUxwSm5meWFXbFhiUXIrTk5RTTNRU2lVamcvVEtIM0YzQkdaOXJWT3RPU1NrY0prbTl6cE9DSU1ubDRaUFo4MkxjcjNYVWJnMUlwc09kNk44WklvZkRtYnhDU0grSDk1dWFTOS8vclhWRHRGd3psWXlFZWVDS3d1eEtBbWpObi9waGhPTFVGWEJzb0cwUHBvRFMxcmEvT2Q1TC9WUkpVdzd2VDdJQ2dzamtrTkgxdEhQc0Y2ZmIrTTVjN1VwK0JldHJHak1kdnRzWGlUWC9JcStyUjhMcm5PeXpBS1g2SUJWazIxV3dhOW1Wazh0eHM2SUZqUWtuZnJRZTRHRDJWRVNQVlNjdmt1RGtQdTFaMVhWK24veGdXTHFhdzJzVDdBZDhtRnE4bUk5WTNrZ2RnazE3Q3A0L1dQNmxkWFRLQnUwd1BHNWk3a0Vwd3hBTUR4d0pFZksvQ3NUS0UweGRWdlFzeFpEcHNZdDZib1dnTG9raG15RkptZUxReStibmV4TEsyTGgydE4vOUYzU09Sc1NNTExHd2JCQ0x6SnBxUkdVaTQzbENjRVlmcTY1SkFLRXdWOTZWV2RJRDRtMm1DTDJEQnFsOVR0NnQzRmlUcGJMbUYxMzBBWEltTTVESnV2WDNWdDFKZE1GSlVBd0UiLCJtYWMiOiI5MWE3OTRhZTFlNDdmODIxNGUwZmQyYzEwNzhlMzE2ODk0MDgzYzdhYTFkNDUyNjI3M2I3OTgwNWM5YzI4NTU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAzcW9yUGd0RndHNmdPZVptdnhsNGc9PSIsInZhbHVlIjoiRVlTUlBXYVhxWlJ5a0cxRVp2QUZPeEV4ZlZjS2pHWlZkUC91ZjkrcXRlcHR1YlY5ZFpsdSsxZ1N5MzI3MlMzckVNeXFCZjU0MHJ3cEYwcU1XKytkcUYxRU1rUFoxRkE5MDRvWndoeE1FbG1ONVdEY245RXJmVmVSZncwRlowTlhJcm1walo0L3kwblZHYU9Sdkx0MDdsU3FGSG83TmJsU0QrRHVObCtFT1d3aXY3aGF5dXZaK3dVODJHRVlsVDJ2QXJ1WDJKUXpDQzRTU3ZMei85SlZQK2E3ek9BV3gvc1FKN2R2eVJyazc1T3ZsSldKSVlHYUFGdHVOZSt3bzZ6bTFEWG9GanVGcDd5T1liM2VGMW1OeFhpWk5WMkh2M3dEajdwRytFbHJZWVlac2R2TGd0bTBNRlJ2QVBCWnpSRUpoRkx3TGlOWnIvbzZjaGJHZ091ay9zQ0RGcmprb0ZXem56VHFEeUZNRk50SnNtOGNqcnVaUVc3QUtUY3QxMGVnbWdmdmVBZ1hOTXJSRHdPRzRvMEhrRlZnOFR6LzhIV2ZjNHkwZVVFWlA3ZlNXSk9ocEtsT21zZ1VRRHB6VTc1U2NIUTA5N01DNXZZbFRLOEVQY1VuUmd4VGRnc0l6VHEwTjZTT1pmSXMvR0VEQUl5VkI0ek9nU1k2d0NZUmZpZ0UiLCJtYWMiOiI1MmI4MDJiZjFlODcwOWFkMjRlN2ZlMDA4N2VlMzBlODFlODkxMmY5ODIxMTk4NjM3ZmViODExMmIwYTdjYzcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-277510580 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277510580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1914943153 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:04:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktEVEU0SXBFWU9sTkhRWklZS2tQZWc9PSIsInZhbHVlIjoiaTY4c2JvWEIwOFhMei9FRi9PS1ZUaEhob1ZYa25yd21ldXo0ek54eVlZS3dyQnhZa3R6QU96ZkNFVDZMVzN3V3NVbmlMd1RNS0x4VzZ0QThiMkxOdnhZcHhUVkpla2hVZC83QWZsaFg5L3hMeHVBWGI0Z2pXUDZJYWgwNloyWVIwditzb1hTaERjMjFuQURRRjJzSFFjeDhmeXZwZUkwUjNlM05seEU5UUtkQlNPdVNGUGQvQ1J3OEVxWG96V0RwUHFWZEdMT0MvQ1JDMzFxR01YSVJPTGJpdlR4SnBiM2FYQzByM3JUNjFOR0ZnK3hmWE5YVHVqcGF2SEliWEtrRm5pbTVjMnI5MWE3R2JDUGhpdXNCVkI4ZkxSY296TVljWng3UVVWRlJCK3JBZ2E0M3JTdFozR1VDblBYVnB3Mi9XYU1ITk4xcGQ3cFBhUCt0dEJHS0pNNittNTdUbUJYZ1JlbER4eU1YOTdncUp4ekpoNXc0MTk3V1BxMWdwRXFUWXJua0w3dGd6Zm1jdktVcXFDbTRGTCt1cDhKZnpUb3BSVTRMejVMc3hMMnFRMDhGOVpGMWxNVzhUNkMranZOUVp5aWJOV2dKWEhNejlaY3BGV2w2SkZyd0V0RFNsT2lmeExRdUhZeU9FdkM4RlhKcVNadGExSnp4RSsvU2Y5eEQiLCJtYWMiOiI1MTExYWFjZGE0NzI3Yjk3NGYwODRmMGQ4YTMxODY5ZTM3NTRkNGVlMmZlOWNjYmY3MzI5OTQ0MjNjNWE2MGUzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:04:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im41RzVoaGpSdEtpdWlaOVZLMVNUSlE9PSIsInZhbHVlIjoiMUR3STducytUVmNQbk1mVGtRcHdQazVhNmo4ZTNEbWovdUpQSzFUUStWWWRKSTJzLzQxUWF4dVpMT1Z5NzRXc25HZldqS0VibTd4TDBsaExXMitibnlSWGdVak41WHF4SmwyR1pDTGgxVUtMU3lodm14cDhoZVVCQXg0UzR3NytpMnJwbjFoU3Z1MHpScWdMTmF6YzR2Y29GK1QzNjdKUDdkOVp1UjBjL0x4bU05OERJelV5ZXRrWUVkc0J1SjN0SmRsRFJQaWw2b0RkTGRSMy9QWWdDczhpLzZYS1NRQzdxZkVKTHlXTi9wMi9zK3V5MnFyWXFZSENDaERhd25oOHZsUHg2em5zNVEyM2Y2MGRhMXlLT3BGM284Ri92QnNFeTR4MWtxUXBUUkFvTGhnQ2FoaDdnajNVaG9EMHAwWk5hQ2xlc2dlY0xicVVpQzlKOFdMcXc1UmQ2bXM0Nk5iWXlvSWs3T0dFNTlOY0JOeFpPRSt2bis3R0ZIekdGWEVlQUw5d1d6aGo0ZDRUeGd3cGNXWEhlR29tWWdraVZiZ3ROQVBncTk5c3AwQkx3amJMaXUrTldaT3IzdjNrV21sU1JZcjNTWWQvL0NtOFBWc21LNlpYNDhWVGxsUTBvK1BVRVM3N0FrLzZXNktTdUR3VmJWY2M5aVlReUM5b2dNZGMiLCJtYWMiOiJlNDhhMTU5ZmIyMGM3M2NjODJmNmY3ZmUwOGRmYjQwYzE3OWI5Mzc1MTBhMDUxMThjODkzMzE4OWY3ZDM3MGM4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:04:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktEVEU0SXBFWU9sTkhRWklZS2tQZWc9PSIsInZhbHVlIjoiaTY4c2JvWEIwOFhMei9FRi9PS1ZUaEhob1ZYa25yd21ldXo0ek54eVlZS3dyQnhZa3R6QU96ZkNFVDZMVzN3V3NVbmlMd1RNS0x4VzZ0QThiMkxOdnhZcHhUVkpla2hVZC83QWZsaFg5L3hMeHVBWGI0Z2pXUDZJYWgwNloyWVIwditzb1hTaERjMjFuQURRRjJzSFFjeDhmeXZwZUkwUjNlM05seEU5UUtkQlNPdVNGUGQvQ1J3OEVxWG96V0RwUHFWZEdMT0MvQ1JDMzFxR01YSVJPTGJpdlR4SnBiM2FYQzByM3JUNjFOR0ZnK3hmWE5YVHVqcGF2SEliWEtrRm5pbTVjMnI5MWE3R2JDUGhpdXNCVkI4ZkxSY296TVljWng3UVVWRlJCK3JBZ2E0M3JTdFozR1VDblBYVnB3Mi9XYU1ITk4xcGQ3cFBhUCt0dEJHS0pNNittNTdUbUJYZ1JlbER4eU1YOTdncUp4ekpoNXc0MTk3V1BxMWdwRXFUWXJua0w3dGd6Zm1jdktVcXFDbTRGTCt1cDhKZnpUb3BSVTRMejVMc3hMMnFRMDhGOVpGMWxNVzhUNkMranZOUVp5aWJOV2dKWEhNejlaY3BGV2w2SkZyd0V0RFNsT2lmeExRdUhZeU9FdkM4RlhKcVNadGExSnp4RSsvU2Y5eEQiLCJtYWMiOiI1MTExYWFjZGE0NzI3Yjk3NGYwODRmMGQ4YTMxODY5ZTM3NTRkNGVlMmZlOWNjYmY3MzI5OTQ0MjNjNWE2MGUzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:04:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im41RzVoaGpSdEtpdWlaOVZLMVNUSlE9PSIsInZhbHVlIjoiMUR3STducytUVmNQbk1mVGtRcHdQazVhNmo4ZTNEbWovdUpQSzFUUStWWWRKSTJzLzQxUWF4dVpMT1Z5NzRXc25HZldqS0VibTd4TDBsaExXMitibnlSWGdVak41WHF4SmwyR1pDTGgxVUtMU3lodm14cDhoZVVCQXg0UzR3NytpMnJwbjFoU3Z1MHpScWdMTmF6YzR2Y29GK1QzNjdKUDdkOVp1UjBjL0x4bU05OERJelV5ZXRrWUVkc0J1SjN0SmRsRFJQaWw2b0RkTGRSMy9QWWdDczhpLzZYS1NRQzdxZkVKTHlXTi9wMi9zK3V5MnFyWXFZSENDaERhd25oOHZsUHg2em5zNVEyM2Y2MGRhMXlLT3BGM284Ri92QnNFeTR4MWtxUXBUUkFvTGhnQ2FoaDdnajNVaG9EMHAwWk5hQ2xlc2dlY0xicVVpQzlKOFdMcXc1UmQ2bXM0Nk5iWXlvSWs3T0dFNTlOY0JOeFpPRSt2bis3R0ZIekdGWEVlQUw5d1d6aGo0ZDRUeGd3cGNXWEhlR29tWWdraVZiZ3ROQVBncTk5c3AwQkx3amJMaXUrTldaT3IzdjNrV21sU1JZcjNTWWQvL0NtOFBWc21LNlpYNDhWVGxsUTBvK1BVRVM3N0FrLzZXNktTdUR3VmJWY2M5aVlReUM5b2dNZGMiLCJtYWMiOiJlNDhhMTU5ZmIyMGM3M2NjODJmNmY3ZmUwOGRmYjQwYzE3OWI5Mzc1MTBhMDUxMThjODkzMzE4OWY3ZDM3MGM4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:04:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914943153\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-918133328 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918133328\", {\"maxDepth\":0})</script>\n"}}