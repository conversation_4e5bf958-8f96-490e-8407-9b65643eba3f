{"__meta": {"id": "X4a74270c2318ca8f3f37c7fc1a1f3260", "datetime": "2025-06-30 18:06:55", "utime": **********.672457, "method": "GET", "uri": "/add-to-cart/2305/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.235589, "end": **********.67247, "duration": 0.43688106536865234, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.235589, "relative_start": 0, "end": **********.579146, "relative_end": **********.579146, "duration": 0.34355688095092773, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.579174, "relative_start": 0.3435850143432617, "end": **********.672471, "relative_end": 9.5367431640625e-07, "duration": 0.09329700469970703, "duration_str": "93.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673776, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02084, "accumulated_duration_str": "20.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.611757, "duration": 0.0166, "duration_str": "16.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.655}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.637033, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.655, "width_percent": 1.631}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.650385, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.286, "width_percent": 2.495}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6522439, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.781, "width_percent": 1.871}, {"sql": "select * from `product_services` where `product_services`.`id` = '2305' limit 1", "type": "query", "params": [], "bindings": ["2305"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.656418, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 85.653, "width_percent": 1.631}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2305 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2305", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.660748, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 87.284, "width_percent": 10.797}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.664249, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.081, "width_percent": 1.919}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-72196599 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72196599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65566, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2305 => array:9 [\n    \"name\" => \"بيبيرو أصابع بسكويت مغطاة بالشوكولاتة كرانشي - 39غ\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2305\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2305/pos", "status_code": "<pre class=sf-dump id=sf-dump-1842733084 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1842733084\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1470681098 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1470681098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1205043502 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJYUVBnMHlvRUF5ZzVlclBsdXNGMHc9PSIsInZhbHVlIjoiV3ZIMmpxNUQ4UjMzL2NRR0RtTWdiNWRRbWQxcFdMTlZ0TkhGb1lCN0FORmZWem9oQ3R1SGlGRGFKUjQzUEwrbkhCeTRrMThwdFNvTGplREg1Z1orSXh1OGVXdWhLUnpGbGgrNlhYUFJib1hxV3U1bkRaNkdCZWRJQ0JqSXNzVVJHMWZXRHlJTkhHZzJ2YytXM2h1RFp1dEZrYURUOHdwV0pmSU90QWsreitJZTgvMHExTFQxdnN3UHVaNUlvNTFuUlRYc3RxdXQrdlY1WXJRWG9NemxSMS9aZnlLSmdMbTQ0RFY3Y2VZTDFXak1SUFRRREVLbEREVjhTcHM3Y09vS0dwRzV1d29JbmZGMjBoMEUyb2pad1ZwaFpDN3Q2K3JNVXZOV0M4NzB0Q09HYXM2bmFxeGI1UWVjNENhbXRsNXRDNjJyMjhRMHZWR3pVYm5LZ21Ob2dLRWhKTWV4bk1DVWFoOGZYMXc3WjErSWs0QzgwZEt2VTVJQkxFckhwRVlYSE41NGVPWEcxYW9Hbm5veitKYjNyeDVIeTJpbjJQS2V4NmU5SmJlUHBYWVN6K2txUHdYVGlLUUZzQWRYZ0thKzB4WHhNOGs2aUZXZ0pwQ2UwTE1tUlFxWjNxNkpKb2JMVlZNMnpMR0l0NGFXbVJscHR5U1BxTEhZZWY3TUMweG4iLCJtYWMiOiIzZGE3OWY0YWFkMTEzNzc3N2U0OTUwOTZmMmQ5NGU2NWIzOTE4NjE1ZDU1NTA2NzhlMTJkZDNjMmFiMWI0ODY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5oTkFXeVdFbkNxUGxyZ28zSTFZblE9PSIsInZhbHVlIjoiSkdGRExqSmw5eGk4MGVMdFZ6L2VQNzN3cVF5Ukp1dGc1MS9aeFA1ZGVFZks4TVZRa2Z0OGgvTGNBTFdJK0JUMitqWUFEZ3laMjdOQnpOaG5MMFBxU1JJYzVRYjAra0l0NVNjNWJOVkFSVmp4TVR6b1BQMmtlWXppUVVSSHdRcEs2djdMWlQ0T2pjaXZidks5LzlnSnBGV0pQWitnUFJvcDgyYkN6ODFQQjlBTmVRdEI4MU93MU1TdTBiZUxrUysrVTdPOGhpTXJPMUxnMzVXM0drQ2MvNFVZSVlSRmJlbkhGNFRLYi9oek9NM3RkaHdvTjVmQkx2ZWwvc1N5T0hxNlh5NTE5TnhvNlpuMTRNMURQMEx4RVVmLzVkWFFGU0lLdFU1WHlDZTZmNlRla0xTT0RnbU1VUTJQS0dhRHdrN0RiOUUwc1FPdi9VZy9sNHRjNCtSeWVIVDU0UWg1M2ZnRWo3ZkhiRXV0QmxuT1JSSVBQT3RsNVkwV3JrZjEvcmlsc0JpYitpbGt0T3c4Q2J0bTFQcUlSdW1GSEVGUFlLR1p2alVQT2hJWmFVODZkdDRvNkYrMXdlRzhTVVlqamlhaHJrZ3R0NkNGc09lUmpzVmdXWlFQZk9GeVVTSzhkQ2dYVEYxb0lNU1YrZjYyVDlNQ2lYRDNLSHdvR3V0S0JJcEwiLCJtYWMiOiIxOWZhNDBiMjk0OTRmNDYzYjI3ZTU2NTkwY2Y5YTQ2NDBmMTMzMDg4ZWIwY2RmYzUyMzI0NTM1OWY2ZjJjNDAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205043502\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-276770372 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276770372\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-542814983 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:06:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUyWjRrRHh4WUxHb0t1azBxbGZ0Mmc9PSIsInZhbHVlIjoiRCtUcElUQTRmZGN2KzlqTDA2RzJlRjlCQ1kzY2wwMFM0YWF4bld2Q2IzNkJFeW4yUnUxdnRiYVBTZ0ZsRk0zLytJT2ZjR3EyaGFIcFFEdHBLSnZLSUtvZzNSNDR2WVZZSk4zZm5DQWJzUldpWnVwMTdlTzQxVlNMektpeVlxTkxaVXBucXhmdTdRTXVjUzhHcE5yTWJyVXdMK2pYRFR3Vll1OHVYYVFGVmx0ZXR4OEo1aUt5RkcxYktrRFEyanp5ZU1hNjNibDNLSzZIeVprUkdkSUhPZGR3UzhtOVFuUG9GSVU5MG5BZDlpME9QcGtxSExxaW9kM2F6cEluTDhVSWQ5NDF4Q2tUdVZQeG5kMVRIVElkSEI4QzlVVEtTamVYRDU0VUlxU0x5RTNlY1hGVUlEUGJsaDRjWHRJbVVJcXB5VmJ2cXdDS01VUVlWY0o4MVVyRWVlVFpjUW9CNFBZK0l5dUo4TUY0MTBTOWZJbG5FdEJPNmpVVVNkQUlhTXk3RVAzdm1kbzloeUxnbzFmdkdKa04zUWlUaXp3Y0tZL2Y5U0xPeXVBSzRvT2N4RkRWMXpsNXJCakVjaHFQTjdVdVV3K3BZNEtPZDhUblhtNE1kRXpHQWx1dHl2MUdLUi9jbmlWZG1rTFBPMjRGN3NicStSazg5QStyWVp0T0kvR0YiLCJtYWMiOiJiYmUzNjhhZWMzMWIzMTdlOTgyZjQ0NzNjOTVmNjM4YmIwODcxMDRkYTAwNzQ4ODVjMzZiYmFjOTExZjllMjMwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:06:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZOZEVyRzNyQjVVUStDVGtzcXAybkE9PSIsInZhbHVlIjoiU3FJdGNnUWtkSTlMVWZqNmtLSmtqaWVaUWc3akZ5Vnd6eEVxNWwrT3J5eXdpOEc1RnY2bnBGdEdWZm0vcXhQTUxrUWVjaWtxdFF4dDJPTXBpRGRacVZVU2N3YnFISlp1YVRqR1BDbEp0OW5nT1E3UkFkSDM2bTFJanNMR2p2MGdtKzhjWllLMUt3MndCSnUvNkF4ZjZnblg3NHIwRFhFbCs1R3Fsem1PQXJYVkZ3VWluZTF4RGNVdWtEN1RrTEpmazFRdUVWTXZCT2ZCNi9XMkUrd0E3WDI1UXRZcWdzcWlxK2o1c0VFc0IvUHk0dHR4bVMvMm9TMWZNeEkzRVpnUlVrRTROUnd0Uzhobnh0U1JEbmE4dVJoZjF5S01jbjBMcGdqb08xSm9SWnJnWnB0bWNTRjJ4bUxHOVlqMXJYWTVudFc4SmVzU3FIOWt2SmlBMXlVZTBUZGN4MWMrMjg0QjBSZks3SnFpVGM5Vk91Wk80cVg1VFBXMjlQY3R1NnlLZXNkOERpSERGbGJqVTArOXBmTWhWcFBaQkUrVFB6UW95WFNkQ3l5RVNXYi91bmlkMS9MNlpXNFlLdUdsczJYc2JCaXE3dU9mSytibVp0QUJML1UrS3duNzA5LzUzTlhIS0QvbVZGOHFnRDhrVkpNc1hsVWt1ZWJ6amlHemJKYVAiLCJtYWMiOiI2ZTM4ZDJjZDhkZWE0MmNkYzMzZThmMzVmZTA3ZDcxM2QyYTA3MDMzZDYxYWVjNjNhYzQzNzc4N2EzNWE0MGIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:06:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUyWjRrRHh4WUxHb0t1azBxbGZ0Mmc9PSIsInZhbHVlIjoiRCtUcElUQTRmZGN2KzlqTDA2RzJlRjlCQ1kzY2wwMFM0YWF4bld2Q2IzNkJFeW4yUnUxdnRiYVBTZ0ZsRk0zLytJT2ZjR3EyaGFIcFFEdHBLSnZLSUtvZzNSNDR2WVZZSk4zZm5DQWJzUldpWnVwMTdlTzQxVlNMektpeVlxTkxaVXBucXhmdTdRTXVjUzhHcE5yTWJyVXdMK2pYRFR3Vll1OHVYYVFGVmx0ZXR4OEo1aUt5RkcxYktrRFEyanp5ZU1hNjNibDNLSzZIeVprUkdkSUhPZGR3UzhtOVFuUG9GSVU5MG5BZDlpME9QcGtxSExxaW9kM2F6cEluTDhVSWQ5NDF4Q2tUdVZQeG5kMVRIVElkSEI4QzlVVEtTamVYRDU0VUlxU0x5RTNlY1hGVUlEUGJsaDRjWHRJbVVJcXB5VmJ2cXdDS01VUVlWY0o4MVVyRWVlVFpjUW9CNFBZK0l5dUo4TUY0MTBTOWZJbG5FdEJPNmpVVVNkQUlhTXk3RVAzdm1kbzloeUxnbzFmdkdKa04zUWlUaXp3Y0tZL2Y5U0xPeXVBSzRvT2N4RkRWMXpsNXJCakVjaHFQTjdVdVV3K3BZNEtPZDhUblhtNE1kRXpHQWx1dHl2MUdLUi9jbmlWZG1rTFBPMjRGN3NicStSazg5QStyWVp0T0kvR0YiLCJtYWMiOiJiYmUzNjhhZWMzMWIzMTdlOTgyZjQ0NzNjOTVmNjM4YmIwODcxMDRkYTAwNzQ4ODVjMzZiYmFjOTExZjllMjMwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:06:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZOZEVyRzNyQjVVUStDVGtzcXAybkE9PSIsInZhbHVlIjoiU3FJdGNnUWtkSTlMVWZqNmtLSmtqaWVaUWc3akZ5Vnd6eEVxNWwrT3J5eXdpOEc1RnY2bnBGdEdWZm0vcXhQTUxrUWVjaWtxdFF4dDJPTXBpRGRacVZVU2N3YnFISlp1YVRqR1BDbEp0OW5nT1E3UkFkSDM2bTFJanNMR2p2MGdtKzhjWllLMUt3MndCSnUvNkF4ZjZnblg3NHIwRFhFbCs1R3Fsem1PQXJYVkZ3VWluZTF4RGNVdWtEN1RrTEpmazFRdUVWTXZCT2ZCNi9XMkUrd0E3WDI1UXRZcWdzcWlxK2o1c0VFc0IvUHk0dHR4bVMvMm9TMWZNeEkzRVpnUlVrRTROUnd0Uzhobnh0U1JEbmE4dVJoZjF5S01jbjBMcGdqb08xSm9SWnJnWnB0bWNTRjJ4bUxHOVlqMXJYWTVudFc4SmVzU3FIOWt2SmlBMXlVZTBUZGN4MWMrMjg0QjBSZks3SnFpVGM5Vk91Wk80cVg1VFBXMjlQY3R1NnlLZXNkOERpSERGbGJqVTArOXBmTWhWcFBaQkUrVFB6UW95WFNkQ3l5RVNXYi91bmlkMS9MNlpXNFlLdUdsczJYc2JCaXE3dU9mSytibVp0QUJML1UrS3duNzA5LzUzTlhIS0QvbVZGOHFnRDhrVkpNc1hsVWt1ZWJ6amlHemJKYVAiLCJtYWMiOiI2ZTM4ZDJjZDhkZWE0MmNkYzMzZThmMzVmZTA3ZDcxM2QyYTA3MDMzZDYxYWVjNjNhYzQzNzc4N2EzNWE0MGIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:06:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542814983\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2305</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"50 characters\">&#1576;&#1610;&#1576;&#1610;&#1585;&#1608; &#1571;&#1589;&#1575;&#1576;&#1593; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1605;&#1594;&#1591;&#1575;&#1577; &#1576;&#1575;&#1604;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1603;&#1585;&#1575;&#1606;&#1588;&#1610; - 39&#1594;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2305</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}