{"__meta": {"id": "X6d671a084364fc69f273c94b1c26d658", "datetime": "2025-06-30 18:38:32", "utime": **********.792767, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.254228, "end": **********.792783, "duration": 0.5385549068450928, "duration_str": "539ms", "measures": [{"label": "Booting", "start": **********.254228, "relative_start": 0, "end": **********.719823, "relative_end": **********.719823, "duration": 0.4655947685241699, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.719832, "relative_start": 0.4656038284301758, "end": **********.792785, "relative_end": 1.9073486328125e-06, "duration": 0.0729529857635498, "duration_str": "72.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45706848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036599999999999996, "accumulated_duration_str": "3.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.757357, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.76}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.774003, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.76, "width_percent": 15.574}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.782341, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.333, "width_percent": 16.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976784472 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308318634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNPL2NpekVIcGdBaGo1YkhHMXpXM3c9PSIsInZhbHVlIjoiRkV6Y0pVdTF5VlhSRVFJbm1kMGx2RTRmSjc0OTAzNHh0b25JTnpwNTRkQXhpdTNtQVk4L0hyRnFCRndYdngyTEhNNDR2SW1oZFNCVmdvaWV2WTJ0YlcvTmdMWGx1bWJNOTlKbkhvaWlPNURwVTVVVDJHRVNOaG9pc0FoUWsyTGduVXIwRDYyYmp2Y285YVJMZmVmcHBzdE03cklvLzlBbFlZSml1T1R0R3prdUhGSmorMitBdUFPZW5udGFoS2krbDBCWFdkcFBtaFpZWDFFa3owWm10UVhkUUhhVkhQVG9ya1BWSXY4NjZ0VEdlenNURGFsVkl3WnJZcWxBcHd0aEEyRWdEZEJlR2wvMG45dllvd2xqSHhiTDQxN2I0clZFUmN4T3U0bnZidzFCb1NWZktTVjRiVEJpVTc4RlVvSUEvQWt5cjVOSkxhM2tUZ0ZkbzI1ci95TlNDb3owb3lqNnd2ZjJCVE4yZWJOWDJ1SmlMR2xKbDd4eXpDdGxOZUVDQ1NOTkhXb052Si9XUjUybElkSE52WGlTWWpxeGZLR2E0NUxySXhubHZaWkpiKzVVdnFYSjlJdmp4SXAxcDd1d2tWeWF1RkdFbTR3Z1VreFgrY0xNcjhKSWRnUE11VytjVzFRRWw1VmJoR2ZXY0pSMHdPMDZrYWwyRGVLejRGM2MiLCJtYWMiOiJiODhkOWExZTZkMTQxMzI2MzFkZWE5MDM3MGMzMWM2ZGFlNzg3ZmQwMmY2MWI4OGJjMWQzNTg2ZGE4NmI1MGUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii82WW9Od3NlYUQxeG5aWTlhM09pc1E9PSIsInZhbHVlIjoibXU0WGZwc0JQRFVmRFQ0bnZlUlZWSjd6UHBOUmNHU3p3N0Z5aWh5aEhCZXJhemFVcjR3eUJtOGc0STRqVmtVSGxRcHBzeHg1M3dHRnlFZHYzSVExcVlUOFNrWDFGTjVaS25wZjJnMGtuRFJDN05pbllZbW1yZFJoZTRHSjBicnhaVXFKUlRKaWZJYk52dmE4dEhKOVFCRVY0eFNXdEJmVkJqYmdmdm9pYjhuU29lR01Od09QZm4xWENVTkhQOUxtTCs1Yk1nRTZaTUFpdEg4TlpJVWNnU29oUi9UNlRyZlp1OFJjM1lVVjAyWjkrbkxmNFZzOHB4eUptSlpGUlVWMWVsMXdJMW41YktXYzQ4Ujg4ZExhRlZDY3N4b3V6TXhlaTRWNUhIbWxNWktkNm9lcFFwNUlOOUpxNlN2SHpFMkRja1RRekFNSlpUYVY1VnFTWFhHOVR1ZlVmQ2RKb2RpZk1lVzBQRlFkL2M1L080MjI3OFQzTHpRLzl5NG52T2ZJSHM0Y2pMSUw2L0NnZXg4Y1gwRk1TYlBjMHpmSlJCb1AxUVBGdGRUNTRRMUIxWVBDSVlvYk0wQWx2a0REdEs2c1FwTFY2eWhDSjR4dGNneVR2Rm9kcWVmVmdUMXFGV0YxNzFFNWJ5TFNrT0JGampqcStXcGhiTThCWHIrM2JqL2siLCJtYWMiOiIxZjdlNWE5MDRkYWZhYTc5MjcwZmQ4ODZlZjgyZTI3NDUxZWU5YjNlYzExN2ZiZmU2YWFiNDNhMTJmMTlkMmRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976784472\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1459827362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:38:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBTbzZlRzJXTUpWTy93WWVscXB0VVE9PSIsInZhbHVlIjoibGZmUlhJZk5CU1ZqMWRDNlhRVXBUazRmQWxSdGJUMXhvbVZ1QllmMVc3QU9nNWpjaE9UNm95eE96YUg2am8xdzdnWnpNZ0Q5YWtFQ1FPQlNIbU9QdDJHR3VDMFI4ZC9yVW4yRkthbFBzQ1d1UHF3YjJRS24xbXZMYXhYeXh4aTFwWGdhbno3d25hT1V6SEdpYW1BT0lESzVWZENIckliNk1UZjVsWTdmNDgxUEdMekl1cHJOUHpZWlNBclFON3l0Z2FvWW1CdENhaisraXQrS2M4RVNySjQzWkxQL210RzMxdlQrYm9PVTRHcW5reWJuNkRRUlJaMGpaQWZFQ1VnWG10MkhJSzNzaGcrbkNQcXR1ek00R1VNdm8rTEl4VDNjdGc4eUxFa0Jjd3hrTnBOYTRMbmdkWjVqRGdrM3prb3gweWlJZG9QcHQwL0FWa0VHSlJEakpGQi9kZmVHNnJQT0lNYnZIKzJaSXFiRmtVY29MMFh1RE9HblRkOUdmWWsrWVRCSm1sK3BaUGxnV095dDNCVWxjQnNDNkRsOGQ0TlZzbGJJQWlzT3ZWdnVsdElJK0ZZanN0WERya25mYWt5Rjk5Ynd6NVhZOVJWWDUvcmpmR21FVittaFZpV0dQNndROTd2T2YzcTd5NmJ5WGl2OWFBeWdlbks5Y1BMZ1hnTnQiLCJtYWMiOiJjYzMxOTY3ODcwY2NiZGI4MzA0NzUxM2Q3OTk5YmNkOWE3N2M5ODJmMDdiN2JjM2NkYjUxMTNkMjA0MjliYWEzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxBYlI5WWgrR08rL2FpVjIwYmtGcnc9PSIsInZhbHVlIjoiM1VOdm0xZGN4aWdPclhjNDI1elFMMndUSzJZdk5PQU5DRnRoNDZPbUhMcWYzanJQNFRvY0ZNeG1LMGhjRHNWTXI1YzF5Nk5pSWdhenVZRVlNOEZGTVdRR0xtelh3WkpEODZ5dWg3RjA3aFdsNHVjenB5czRuL2tzSmtKY1ZwUEhuaGphajVQUmNLUkJvb2lGelUvd3FNYStxaVJ2aW4vZHFGemw5VjhObGVicklpcGxhYWNyRVRHWmE2RlVHa04zekl2UGQzRFhvbDV2djUvSy9Rakozek1CWEFnNHlsQXNCYnIrYmRwNjNJWHBuV2xKckl3MERHWWFNMVRidTBrQ1JqTFhQWDVMZUZXWWpsK1dPa3l2RWY0VmZaNm9GUXlRK1hENElwbmtNN0JTYU50V3ROdGtZdmNPYWVJaEx1SVA0SUNITno0OXZybCtzN1ZYVSt3Y1FCNHhwaVN4aUFySUJCSE9LWXVvRXRIc0hzSDJwZnptNnlTYWJTWHlVNVlJMHl3Y0tzMlZPeXNubFE5a1FOaTRVY05PV0dKY1RReG1BQytkb1luYjFxTC9wVzg0dnBxa2NDMXh4VEtROHVwejNEYkkzWkV5eEp0dUdkbGR0QTlmbmVnaHZObzlvajdQNUpSRmVRT3V2K2o1V2JPeTJYd3o2ZEg2ejd5Zy9oYzEiLCJtYWMiOiIzYTYxNWQ3Mzg3ZTAwOTdjODI2MDAxZDZjZDk0Y2YxMjY4MDBlZWZmZjdiMDg5MDExNjUxYWUxYTcwNzI3MWJjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBTbzZlRzJXTUpWTy93WWVscXB0VVE9PSIsInZhbHVlIjoibGZmUlhJZk5CU1ZqMWRDNlhRVXBUazRmQWxSdGJUMXhvbVZ1QllmMVc3QU9nNWpjaE9UNm95eE96YUg2am8xdzdnWnpNZ0Q5YWtFQ1FPQlNIbU9QdDJHR3VDMFI4ZC9yVW4yRkthbFBzQ1d1UHF3YjJRS24xbXZMYXhYeXh4aTFwWGdhbno3d25hT1V6SEdpYW1BT0lESzVWZENIckliNk1UZjVsWTdmNDgxUEdMekl1cHJOUHpZWlNBclFON3l0Z2FvWW1CdENhaisraXQrS2M4RVNySjQzWkxQL210RzMxdlQrYm9PVTRHcW5reWJuNkRRUlJaMGpaQWZFQ1VnWG10MkhJSzNzaGcrbkNQcXR1ek00R1VNdm8rTEl4VDNjdGc4eUxFa0Jjd3hrTnBOYTRMbmdkWjVqRGdrM3prb3gweWlJZG9QcHQwL0FWa0VHSlJEakpGQi9kZmVHNnJQT0lNYnZIKzJaSXFiRmtVY29MMFh1RE9HblRkOUdmWWsrWVRCSm1sK3BaUGxnV095dDNCVWxjQnNDNkRsOGQ0TlZzbGJJQWlzT3ZWdnVsdElJK0ZZanN0WERya25mYWt5Rjk5Ynd6NVhZOVJWWDUvcmpmR21FVittaFZpV0dQNndROTd2T2YzcTd5NmJ5WGl2OWFBeWdlbks5Y1BMZ1hnTnQiLCJtYWMiOiJjYzMxOTY3ODcwY2NiZGI4MzA0NzUxM2Q3OTk5YmNkOWE3N2M5ODJmMDdiN2JjM2NkYjUxMTNkMjA0MjliYWEzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxBYlI5WWgrR08rL2FpVjIwYmtGcnc9PSIsInZhbHVlIjoiM1VOdm0xZGN4aWdPclhjNDI1elFMMndUSzJZdk5PQU5DRnRoNDZPbUhMcWYzanJQNFRvY0ZNeG1LMGhjRHNWTXI1YzF5Nk5pSWdhenVZRVlNOEZGTVdRR0xtelh3WkpEODZ5dWg3RjA3aFdsNHVjenB5czRuL2tzSmtKY1ZwUEhuaGphajVQUmNLUkJvb2lGelUvd3FNYStxaVJ2aW4vZHFGemw5VjhObGVicklpcGxhYWNyRVRHWmE2RlVHa04zekl2UGQzRFhvbDV2djUvSy9Rakozek1CWEFnNHlsQXNCYnIrYmRwNjNJWHBuV2xKckl3MERHWWFNMVRidTBrQ1JqTFhQWDVMZUZXWWpsK1dPa3l2RWY0VmZaNm9GUXlRK1hENElwbmtNN0JTYU50V3ROdGtZdmNPYWVJaEx1SVA0SUNITno0OXZybCtzN1ZYVSt3Y1FCNHhwaVN4aUFySUJCSE9LWXVvRXRIc0hzSDJwZnptNnlTYWJTWHlVNVlJMHl3Y0tzMlZPeXNubFE5a1FOaTRVY05PV0dKY1RReG1BQytkb1luYjFxTC9wVzg0dnBxa2NDMXh4VEtROHVwejNEYkkzWkV5eEp0dUdkbGR0QTlmbmVnaHZObzlvajdQNUpSRmVRT3V2K2o1V2JPeTJYd3o2ZEg2ejd5Zy9oYzEiLCJtYWMiOiIzYTYxNWQ3Mzg3ZTAwOTdjODI2MDAxZDZjZDk0Y2YxMjY4MDBlZWZmZjdiMDg5MDExNjUxYWUxYTcwNzI3MWJjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459827362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}