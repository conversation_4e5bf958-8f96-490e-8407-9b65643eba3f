{"__meta": {"id": "Xdef6803670e64864b1ecf4238fea1a74", "datetime": "2025-06-30 18:50:25", "utime": **********.3719, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309424.938095, "end": **********.371915, "duration": 0.4338200092315674, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751309424.938095, "relative_start": 0, "end": **********.289425, "relative_end": **********.289425, "duration": 0.3513298034667969, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.289433, "relative_start": 0.35133790969848633, "end": **********.371916, "relative_end": 9.5367431640625e-07, "duration": 0.08248305320739746, "duration_str": "82.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0079, "accumulated_duration_str": "7.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.325914, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.336225, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20, "width_percent": 7.975}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.350736, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 27.975, "width_percent": 7.468}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.352707, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 35.443, "width_percent": 4.684}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.357292, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 40.127, "width_percent": 35.696}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.362751, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 75.823, "width_percent": 24.177}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-498824607 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498824607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356346, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-547543964 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-547543964\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1880911198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880911198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1791291728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1791291728\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1564419698 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309393964%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZDc1pqWGY1Nm5pVmw3WmZ5ZXhaWGc9PSIsInZhbHVlIjoiZU40OU1pcmt2dHhaVmVjeFpXTmVQdjV0NkFaeENEQXk5UlFML09zelZzNkx4Zng0S0FuaUMwbVJmR25NUHpKSllnM2hmWWdrMUFsVjBRSGVnVkN6dnFVaXhKSHJVSFp3dWUrZURVVE1Vdlk1N3BpaXptNnJsclovSzlNanRURjc0RC9PZWl6amkzT2Vxb3ZTeHhVVVAyT0drb2ZkN01NNCtNaEpYeE0wR0Y5T3JNbzQxTEorQlhWeTJXNXJ3ZEE5N1o0RFVjMStBN0cyYm82TWlTVFMrcS9HVG5JSE82U2x6cTJzYWhnazVRTVkzSkpSazdNN0t2cU43RmVMT0FvYzdlNFEwMHM2WHdndHVvaXRleEdGOGJrbkE5d0x3cEo3UXIrYjlwUE10RXlTWFdPQndTaDVleEN6aUVBaEhLYlF0TlBZMEUvMVFnSThHVW5oNzdoR3ZOQkx2QnFJSmtCUmZ6bXUxb08rQ2FSL1FuUjAxeUNaVFF1WEFCbG5odDB2YysrNFpIUFRiWE15MldhbDVNSitUYzlVUzdHWWJ6WFR2ZDVTL296ZnBiVURRZGNZNFNtYUlkUFB3MkwyZmNKZWE4azNXSk5TenVMUWlDZGtQZk1PZzRIcnl3L2R4b3BNalpSc3Jnb0F5MUF5NDV0d2g3MXEvWmtXbE05czlJVnkiLCJtYWMiOiJmM2Q2ZTcyNDFmMTRkNzAwYWM4MjQ3NjVjNjNmMzNmMTdkM2EwYzMzOWNiZjM2NzlmYzRhNGYyMTc2OWQ4MjM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVrRlM5dXhlRUh2MGZsc0RpKzB6Snc9PSIsInZhbHVlIjoiWlRWQjgxK3lxY2hPMnAwREJlb0VaVWlqU1ROV05RRmV5bTc5eSsrZERUL3h1QWJvVFhjOTBieW5aeXJiNWcyYVVGQTdDa3RaM0Y4TXJVanp6SkU3VzJtUXo4RVJvRHgwanBDRE5rOFcwTzVmU29pK1ZKTjY2OHJpU25BeEJ4OG9yZ1dTR0dCVTZBb2NGWEZDMW1rMy9mbWZRU2R0b3RpeHNYY1REOTFyUHpSeGJuM1BCM3liLzRYenlnRmNsR3g4UHVuRmtCUStSUlJZcGxFVlkyVGNyQlVmVlJ5TUhhR1Y5Wk1MUFZwM3E4SjlmbEtNNXJCK3RYU1MwZkp1RDhnazBQVEl6eUFMVzZTSENvRnRyQmwxNy9mNTRsdkh5TThEalVpam56ekwvbTk4OUxsM3FwZU4rYUhUZXhRK0F4Nk1WaVp1RVpZVTcxM0VrNHhXK05LLzA5OTRnQWpTRENpcERZcFlGQysrd0lZSDdlMkJ4WkxmeXduRFFkKzRIK05NRFB3U0pobktrWXJMMWhVNkJDRDVwUHZmSjNOUXJjem9rMExVNmVLYm16TWxNYkF3OHN3aFpXRDN6a1hlVTVKRStiYTRxUWV6K2pTdGJVOVRFZUVZTUFjZUZkaDFkVFp6Y0NuU2JqcGNMb2t6aVgrN2pyd0xVM3cwSFpqVG5lSkgiLCJtYWMiOiJmMzU4ZTUwYWFhNTdjNmNmNGIzNjQ4ZWM5OTUyMzc4ZTk5OTQzMmEzZGE3NmNkZmU3MTYzNThmZDk5NDY0Y2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564419698\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1532759553 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532759553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-407233781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:50:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpDVk0rQ2FaSS9QZ1FVMjhXTDlsU0E9PSIsInZhbHVlIjoiSGZWY0F5b1RMVVcwdVBudncxTUdWeEFIU1YxNDcydTVuS2xub2ppQy9XTWJxWXJUN2pMamtGNHV0NktRclRBSVRRS1FUUGdNUkE4WVBPTlNJTklpNE04MkNtejV0bWs2Ykg3V2pHaUdWQVlCbHB5czdUaWJpWnRZN2JzVTNvdEFNb3FOaDRJWitUeGRCVWk1TjlldU5RKzZCaWV5L2UyUmIrazhoVnRFSktMS05UWkVaSUZjRVE5dWxnTS80bnpHNmh6VUo0dHJDODVWNmw1WTY1V0JQRGNYMHViclRWUEdmbjRCRGxSMVk0ZnFZRjloYWtvWW1WNW44OE5tN1hLR0hBSklFd2R6YTMzOGM3TW0vcGxNUEJ4cCsxTStja3hQZEJlc2hLdlRQcHIyTUs0T0dkYjRCeUdQRm1EMUFwUWhuSVJBai9RUzNoVTNlZnlCNEdMeGJGdk1QZUFDdFNiSlhwTExMN2tTMEFiU2d5cnEvSW5xSTFHaEpHOTNLM0R1SGdDZjVxT3FIWnIxM05XM0hJZFYxQzUvM0YxQ25YLzRXMlNqNVBwaVFISHdjVjM2aU5nYzkrTjh5a3BZTUhIdjRQVUdHVE5ZLytoMkFMQ2RCYXdMbDV2ZlJCRFVYT0R1VHF2VVJveDQ4aFhYWldVK2lXalRPN2s4clYwYlRmQmEiLCJtYWMiOiIyZDdhZDU1YzZmMWNjYzE1MjU0MDc2MGQzZmU4YWIwOGEzM2U3YWY3MzlmZTQ4NjM5MTMyMzQ0N2I3MTc1MjBiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImIzMW0zd2lHSTJYYndJZFk3SUE5M0E9PSIsInZhbHVlIjoiN1lTRXEzVkxDM0d1STRncEV6Q1lHY09EZUdyQmJDYVpQTTRSK0pVQldqQUpzUXFSNmVzM0JmelZLZ3dJcHIvRithc1pQTWRiRXg5OHQ0Y0VlbDFIUzZkM2hIS09Hb3paRE15OXMraWVQYUxueE1JaE0xQms0MS9Cc3hoL0JTOWx1YkhVMjRmakh4TTV4K0M2OWc0c0krT3VLMFJmWWZFVGN6WkprRGNGNE8zeXhqeG9KTWFiU211cWppUU1aVVhhQ2hoSW9lSFRsWmRWRDhjNkRZWnoyQnZ6NE11TEQ4VkcrUXBjQUs5T1VyODloTERoblZBSjZYcEZrMktPRHY4YW5YbHpvbEY5SGJrQ2ZkVXFveTZFaFBidzZNa1VrcnNsVXJzQmhPOVJZYW5HcDNPbHBnZUtWeU56UUpTbU9vNmRWTWJvRkY2cXBydEdsaW8xZjNzeFFrNWtvRHlJbXVVM2ZVTm1ReTNTNFdjSWJveGFjbGNQU3ZWYUFUenV0Rm9YVnNpL0xxK0hXVFBYVlo2NnpyQVc4RU9lNkIrTjFncmdZcnBTRXV5clBKVkhvekxGRHdweENJMEt1NUFneG45Q0ViZ0RyMjNrWGhxdEg4eUZ2S3o1VXdJN2E4OXhBZEMzMEZlenF1YklRMTloOGlZSnkySXZCZHVKdFZRR2lqQWYiLCJtYWMiOiIzNGFmZDViNzMzYTE3NTU5ODk2M2YyMGNlNWQxMDBkNWViMTFkMjFhN2NmMzQzMWIzOTkxY2U3NTM0MjM2NGFlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpDVk0rQ2FaSS9QZ1FVMjhXTDlsU0E9PSIsInZhbHVlIjoiSGZWY0F5b1RMVVcwdVBudncxTUdWeEFIU1YxNDcydTVuS2xub2ppQy9XTWJxWXJUN2pMamtGNHV0NktRclRBSVRRS1FUUGdNUkE4WVBPTlNJTklpNE04MkNtejV0bWs2Ykg3V2pHaUdWQVlCbHB5czdUaWJpWnRZN2JzVTNvdEFNb3FOaDRJWitUeGRCVWk1TjlldU5RKzZCaWV5L2UyUmIrazhoVnRFSktMS05UWkVaSUZjRVE5dWxnTS80bnpHNmh6VUo0dHJDODVWNmw1WTY1V0JQRGNYMHViclRWUEdmbjRCRGxSMVk0ZnFZRjloYWtvWW1WNW44OE5tN1hLR0hBSklFd2R6YTMzOGM3TW0vcGxNUEJ4cCsxTStja3hQZEJlc2hLdlRQcHIyTUs0T0dkYjRCeUdQRm1EMUFwUWhuSVJBai9RUzNoVTNlZnlCNEdMeGJGdk1QZUFDdFNiSlhwTExMN2tTMEFiU2d5cnEvSW5xSTFHaEpHOTNLM0R1SGdDZjVxT3FIWnIxM05XM0hJZFYxQzUvM0YxQ25YLzRXMlNqNVBwaVFISHdjVjM2aU5nYzkrTjh5a3BZTUhIdjRQVUdHVE5ZLytoMkFMQ2RCYXdMbDV2ZlJCRFVYT0R1VHF2VVJveDQ4aFhYWldVK2lXalRPN2s4clYwYlRmQmEiLCJtYWMiOiIyZDdhZDU1YzZmMWNjYzE1MjU0MDc2MGQzZmU4YWIwOGEzM2U3YWY3MzlmZTQ4NjM5MTMyMzQ0N2I3MTc1MjBiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImIzMW0zd2lHSTJYYndJZFk3SUE5M0E9PSIsInZhbHVlIjoiN1lTRXEzVkxDM0d1STRncEV6Q1lHY09EZUdyQmJDYVpQTTRSK0pVQldqQUpzUXFSNmVzM0JmelZLZ3dJcHIvRithc1pQTWRiRXg5OHQ0Y0VlbDFIUzZkM2hIS09Hb3paRE15OXMraWVQYUxueE1JaE0xQms0MS9Cc3hoL0JTOWx1YkhVMjRmakh4TTV4K0M2OWc0c0krT3VLMFJmWWZFVGN6WkprRGNGNE8zeXhqeG9KTWFiU211cWppUU1aVVhhQ2hoSW9lSFRsWmRWRDhjNkRZWnoyQnZ6NE11TEQ4VkcrUXBjQUs5T1VyODloTERoblZBSjZYcEZrMktPRHY4YW5YbHpvbEY5SGJrQ2ZkVXFveTZFaFBidzZNa1VrcnNsVXJzQmhPOVJZYW5HcDNPbHBnZUtWeU56UUpTbU9vNmRWTWJvRkY2cXBydEdsaW8xZjNzeFFrNWtvRHlJbXVVM2ZVTm1ReTNTNFdjSWJveGFjbGNQU3ZWYUFUenV0Rm9YVnNpL0xxK0hXVFBYVlo2NnpyQVc4RU9lNkIrTjFncmdZcnBTRXV5clBKVkhvekxGRHdweENJMEt1NUFneG45Q0ViZ0RyMjNrWGhxdEg4eUZ2S3o1VXdJN2E4OXhBZEMzMEZlenF1YklRMTloOGlZSnkySXZCZHVKdFZRR2lqQWYiLCJtYWMiOiIzNGFmZDViNzMzYTE3NTU5ODk2M2YyMGNlNWQxMDBkNWViMTFkMjFhN2NmMzQzMWIzOTkxY2U3NTM0MjM2NGFlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407233781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-317707056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317707056\", {\"maxDepth\":0})</script>\n"}}