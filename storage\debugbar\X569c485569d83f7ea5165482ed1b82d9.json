{"__meta": {"id": "X569c485569d83f7ea5165482ed1b82d9", "datetime": "2025-06-30 16:05:05", "utime": **********.749697, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.30806, "end": **********.749716, "duration": 0.44165611267089844, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.30806, "relative_start": 0, "end": **********.7013, "relative_end": **********.7013, "duration": 0.39323997497558594, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.701309, "relative_start": 0.3932490348815918, "end": **********.749718, "relative_end": 1.9073486328125e-06, "duration": 0.04840898513793945, "duration_str": "48.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872360, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00227, "accumulated_duration_str": "2.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7368882, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.26}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.742096, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 83.26, "width_percent": 16.74}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2119283216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2119283216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1134720632 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRrU3U3bUZrdHVuU3ZYVzlHbTRsaFE9PSIsInZhbHVlIjoiVjBITTJRWmhPaDZ2aWdFT21xd3lrait1RmVyN2pxY29MMzdMbHFMWEF4d200UWdFVUhTOGs0a1BaSzRSUlJlVENheHV3dEZvUWhjNUxPMTErQTRmZU9NZHdybG1RQk1pU09qRTV0eFpyNHk2N2hvRWdlSTY4bk1ueHJJdERvWHEyUVlDOVJ4dFh2REduVitpcUpIRzdtRUpuMU9mTnRnT3lJdHJmZFlYWlplSVBocFBRVElZSFp3aE5tc0tQUFFWa0o5WEpRR2JOYjNNMWlGcW81dGFqM3kwVUFBQi9VcW9udUszb1JoeFZBQy9kUUc4QlRIdER4MnZ6VnRnK21kMWRwQnhjbEFETU5KZml4Vm9PNGdNWEMzdnRhM0dDb2VqMmUvcXZiM2pQK3NYY1FQc1F6M1djU3lIREI1WWxUQXpIME9tZ2c3cjl3ZnNLazRNcXdYYVI5TTRjS3o2S3cyakozWlZHaEVJZnFnTWpLclNvaWt5Ykd3ZS9aNVlhK0ZDZ0ZWR1ZxdEJsd1R5N1NjZE5JMVZWR2lnd1lJdGpaYVBibWVQSGxPN1BiVUxCSFdYTllWZ0M2T01KRHd6Q1B6R3BrT1FhSG9sR2xoaFM5RDY0MUdIaW4vakxFZ0pCUEVHT293RHY2eFJNbk1vclBWMjREc1pQd1NZcjVlTmlCdlgiLCJtYWMiOiI4M2JmYjM4YWUxM2MwZWI2ZjM5ZTBjMzkwNWVlYzhiNzkyMzJkMjVmZjllNmJiNzk3OTE2MTM4OGMxYzM2NzQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJGR2pxUEFHS3BubXBBbThpUG1SWWc9PSIsInZhbHVlIjoibEVtc2dkb2ZXQm1UamFicWpOWEowZis3R3lqbEtLQkhEckJ1bkQxZzB1TmI0VVBRSWltQ2dXMG4rYm9NWmdFYjNudGxkR0xFVnltWklESzZuR1M5STBFSVIyOFkzNDEzMHFEVCt0UWZBdkZpdnIxQkpSMmZycExWRWFDWFpuVlEwT3p1ZVBPZmRzVU8zWnczVTZURFFRYUhJejdCU1FRT2x0SHJJRms3N3l3bEtNQzBIR2V5UTVHd25aaUJkQ0x0aGtieFRidE9qelM1QjZzNlA0WElWY1UzWWRZZkFpVG01dmdmTHIrVGx5YTlUWUZiY3pnL3M2SWVMTUdoU0k0UjFTZVU5eGtmQWtidzVFTGE2elVLOWdnVEtHajllb0czY3JqNUczL0tuNnRLbDB2d1ZYdUxZY1RuZXljdWMxeFljamFxalMzRW1JVll3ZDB6cmN5SUxwU0dFQ3ZxWHNpZUVQWEoycE5lWmhDNFcwRDNNTTB2TENuU203b3BPSWZ2NjVWWFdkMFREUXFYclhTRXVYSDdsdm9DdHdtRmhSNzh1V09yZysyTFdPT2t4ekJ0TmhkV2tITVZxSGo2WXdwdUtkNnppMUdoMm01Q3U2UkRIcmVFVm0xak0xV1lpVWZEemFCaE51N3dBa3liWjRheXNEVjcyQjBLU1NjOU9tSk0iLCJtYWMiOiI1OWUwNTQ3N2VjNzRhMWM0YTIwN2ZiOTJiZmRmMWQzNjMxZmEzNjhkZWZiZTc1MzQxOTFhNjY4ZGJlZDFiNWIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134720632\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1022374886 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022374886\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpkMWwzTXYzLzNVVThUYXI4TzFiRkE9PSIsInZhbHVlIjoiVHEvL29tUXFrRkpFZG1scUF6d2hWWXdSR0RZTFV3eC80TkxkODgxTHlxd1JHdk1UVEFLdjJkeWN6SHV0N0RyQmNFTTQwdkRwYS9ENUNQRHhKZGNvRUU2WDFFb0Ivb2NnbmZCTGdmWHZ2VXdzUFVMQXR4YVZrSDlwbkNGeWxsczI2RG44SEVXTG5YNFJLUEpKZUsxdGlsakxUeDBRZWVsdmFhcEJuY3VIU25oWFdiNTM5c1RtLzgxNS9MOW1QL2EvUjFERmhjd2k0bkU0aGJDVTQxZ1lFS3lZS0t6WkZxZFd3cVRTK1F1Y1I0M1lOVXpGZ3lMYythR3lIVWdLVkpsNE1LcVlyVFE0YUV1OTFMZ0VpSFRUb3p2K0RuQmFwczVrT3hNVm1MZTgrTkNWb3c4R0dOdDdKTWJOSmFOaGtENXFEWkdXdW41S0FWTzF6dlZnOXo1T004bUs1Qy9QRzQ4Y0RWQjgwL3p4Mk9pdis5WnprWGdwRUlvSWh6OGxZWEZGNjJpUGNLZXBuRVFOTWVzd1NIVFhGS05jZjRvd0tla2crR1NmNmxJMks5L0tiZlROR1pPb0lqZ3pCbEUvSnQwTExqUm5yZ3VxUUF4ZW1SQ3FoSy8rRnl1bm1XWUd2T2Q0Y0lOT3ZvMlBUeGFKQWRNRHJUcGIxZkdtRm1QaEtUVHIiLCJtYWMiOiJhYmJiZjJkYmQwMjRlN2NhZmUyMDc1OWFlYzBkOTBmMTYxZmJkMGFjNTVlNzEyYTgxMGEyNDM0YjRmOWJjOGFjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNpT25wSitLcXhOQjdSeWFhMjNnenc9PSIsInZhbHVlIjoiZDNnaDRDVm9ySEIvQ3ZmdGZYOUo2ajYwQVUvTkEwY3MvallKMWRqR0IwUDlHQmY3NjdSb1BRVitTRG00OXo4QjdZY01BNE42enVFMlpaRUVuaWJKWWphRFFCVWFSa3FXcTh6bHRZTDR5Zm5TaXNNUURqMmExWWFneDZOTDcwbThLMk11SDZNaHk2MEhkV3RjcnBlVURvdHJKTXFlSFhhQVUydjVFNkk4SWZoNGRGMHljVnVnTUtaUVlMc1NqTnp6NUFISVRtWTdkenI4K1c3eEcraTJybCs2RUIwUW9HWUhlbVFNZzVUT0Z4L0libTB3UlJVRmRRNkdJTVV4QThrRElJWU81elhYdEVzYjB4dlNsWWRlY2lFWnRzeUovQlVOWUJDTHlDbS93SlJRU3RnSGNMblNmQUFXc0U2SzBLa2RSL3M4aWcvcC9Nb2czWG5JenBFZFF6cHY5NitYT3E3S1Q0ZjZqQWtFV1FrTFo1TFZFZkN2WTA1cThQYlZjUExHUUkrVEJiamFXOTZ2akhVN2Ftb0dmdkxLT3hTUTlSWHJDQm1IVHZmakNaeVZnWTQ3WU9UNVpUK3VQcC91Wnk2UlVBOWxvNlFFN1VCYXlwODNGUld5SWpkbk5GK1NHTnVsc0dPWTF3ZkdCL1l0SUd2NG5RSGZIL2Zld2k5R0F6T1ciLCJtYWMiOiIxMDI5YzdkN2Y4ZTE2MTQ0NzFhMjU3MDY4OGM3OGFhMzc4M2VkYmQ0NGE2ZjUxY2JlMmU2ZjQ3NDE5OWJmNTJmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpkMWwzTXYzLzNVVThUYXI4TzFiRkE9PSIsInZhbHVlIjoiVHEvL29tUXFrRkpFZG1scUF6d2hWWXdSR0RZTFV3eC80TkxkODgxTHlxd1JHdk1UVEFLdjJkeWN6SHV0N0RyQmNFTTQwdkRwYS9ENUNQRHhKZGNvRUU2WDFFb0Ivb2NnbmZCTGdmWHZ2VXdzUFVMQXR4YVZrSDlwbkNGeWxsczI2RG44SEVXTG5YNFJLUEpKZUsxdGlsakxUeDBRZWVsdmFhcEJuY3VIU25oWFdiNTM5c1RtLzgxNS9MOW1QL2EvUjFERmhjd2k0bkU0aGJDVTQxZ1lFS3lZS0t6WkZxZFd3cVRTK1F1Y1I0M1lOVXpGZ3lMYythR3lIVWdLVkpsNE1LcVlyVFE0YUV1OTFMZ0VpSFRUb3p2K0RuQmFwczVrT3hNVm1MZTgrTkNWb3c4R0dOdDdKTWJOSmFOaGtENXFEWkdXdW41S0FWTzF6dlZnOXo1T004bUs1Qy9QRzQ4Y0RWQjgwL3p4Mk9pdis5WnprWGdwRUlvSWh6OGxZWEZGNjJpUGNLZXBuRVFOTWVzd1NIVFhGS05jZjRvd0tla2crR1NmNmxJMks5L0tiZlROR1pPb0lqZ3pCbEUvSnQwTExqUm5yZ3VxUUF4ZW1SQ3FoSy8rRnl1bm1XWUd2T2Q0Y0lOT3ZvMlBUeGFKQWRNRHJUcGIxZkdtRm1QaEtUVHIiLCJtYWMiOiJhYmJiZjJkYmQwMjRlN2NhZmUyMDc1OWFlYzBkOTBmMTYxZmJkMGFjNTVlNzEyYTgxMGEyNDM0YjRmOWJjOGFjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNpT25wSitLcXhOQjdSeWFhMjNnenc9PSIsInZhbHVlIjoiZDNnaDRDVm9ySEIvQ3ZmdGZYOUo2ajYwQVUvTkEwY3MvallKMWRqR0IwUDlHQmY3NjdSb1BRVitTRG00OXo4QjdZY01BNE42enVFMlpaRUVuaWJKWWphRFFCVWFSa3FXcTh6bHRZTDR5Zm5TaXNNUURqMmExWWFneDZOTDcwbThLMk11SDZNaHk2MEhkV3RjcnBlVURvdHJKTXFlSFhhQVUydjVFNkk4SWZoNGRGMHljVnVnTUtaUVlMc1NqTnp6NUFISVRtWTdkenI4K1c3eEcraTJybCs2RUIwUW9HWUhlbVFNZzVUT0Z4L0libTB3UlJVRmRRNkdJTVV4QThrRElJWU81elhYdEVzYjB4dlNsWWRlY2lFWnRzeUovQlVOWUJDTHlDbS93SlJRU3RnSGNMblNmQUFXc0U2SzBLa2RSL3M4aWcvcC9Nb2czWG5JenBFZFF6cHY5NitYT3E3S1Q0ZjZqQWtFV1FrTFo1TFZFZkN2WTA1cThQYlZjUExHUUkrVEJiamFXOTZ2akhVN2Ftb0dmdkxLT3hTUTlSWHJDQm1IVHZmakNaeVZnWTQ3WU9UNVpUK3VQcC91Wnk2UlVBOWxvNlFFN1VCYXlwODNGUld5SWpkbk5GK1NHTnVsc0dPWTF3ZkdCL1l0SUd2NG5RSGZIL2Zld2k5R0F6T1ciLCJtYWMiOiIxMDI5YzdkN2Y4ZTE2MTQ0NzFhMjU3MDY4OGM3OGFhMzc4M2VkYmQ0NGE2ZjUxY2JlMmU2ZjQ3NDE5OWJmNTJmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}