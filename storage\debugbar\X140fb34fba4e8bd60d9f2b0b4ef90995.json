{"__meta": {"id": "X140fb34fba4e8bd60d9f2b0b4ef90995", "datetime": "2025-06-30 18:09:04", "utime": **********.78949, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.434113, "end": **********.789502, "duration": 0.355388879776001, "duration_str": "355ms", "measures": [{"label": "Booting", "start": **********.434113, "relative_start": 0, "end": **********.745088, "relative_end": **********.745088, "duration": 0.3109750747680664, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745097, "relative_start": 0.31098389625549316, "end": **********.789503, "relative_end": 1.1920928955078125e-06, "duration": 0.04440617561340332, "duration_str": "44.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024600000000000004, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.770872, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.732}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.780515, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.732, "width_percent": 13.821}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.783125, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 84.553, "width_percent": 15.447}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => \"2\"\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1566921712 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1566921712\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1614036181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1614036181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1728364660 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdyalV3S2pPTDNqYW9qYis1aE1SRlE9PSIsInZhbHVlIjoiOEpqNVQrckhBL1hVSjJLbkNjZTAzODFrQnljU1k3dzRMblBmeldvMy9ySUE5NitUa3Y4bkd4U0VxRlAvam90RHhtazM4d3hQcXlIWm15bzBKdTc5RjBacGFMcjEwcDVyQ2VwTnhxdEpwKzI4ZWhNNmRLeGRoTFhXdVlPSHNtM3ZtSFQxbmhTcWkrQ1FtRWplaDZ5aVpjTVNSTFNpbDd4ZGcwMDB4eGpHTHNMc29pM2w2di9VNm40ZDJWYkhtMStJTjlKWjhsYi91b2I5ZE1YYXU0ZVJDNU5JRzIvRURWbHgrR0hHd29YSUQ1dWVxV2dCeU9za1VTcDhqakxJdU1rMGpjaWtUdnl1UjJMbFUxRGQyZ0Y3c0d5TXN1ZTFTNjZDa2t4MWh2ZUpyMTdxOHlyQm1HQzNUQ1pzZ0QzNDk0MExUV0NSbjdIc2hpbzRSWXVxNW5BaUJDOGlFOHFLVzFVRUtraGVrUTBjc1lzZmNpeWZHaUNIb2l3ektzZDhXYW1CY3JhNlV0MXZONnFheDB3TGI1YjJ3RGUxRDNTM0taYlhDRmNhYWw0TlVSNkd3eDREZ01zZFNNclJ3WmVBY3FjUnRrU1VLN0VFbVpETmYzQWNHL2JWL2tLRUxyNjB6NVBzWEt2UWoyM3l6VzNWVUQ1S3dZKzZXeGlTbE1WSzlJdS8iLCJtYWMiOiI2MGU5M2Q4Yzc1MjI2ZTU5NDFmM2I0MWI5OWNlZjIxMGJlNDU2NzcxN2QyNDU1ZTc4NTEzMmNkZDJhMTMyNzdjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik14M3RQcHFyeDJsMWJ5d1RpMWtHTFE9PSIsInZhbHVlIjoiNVd0VDcybnZyQndtOUlwQ091K3lIWmJ6MGlhMERERFRjWElOWFF3enRISzYrUjRWZER4NmpPaDJMNnlFVXh0alVNa3VjUTQxenJyd3JiV254ekZOaWlQM3htb1NqaHZiNkJBVVpRV0VMei8vSFF2YXhNemNFcnM1eXFkd2RJbkp3U2hIVTBSZDhhOWFEb3FSTzREVVI3L2RLWUxycU81V3Fjcm0wS0tUdVFLRlI5dElIeVZYMkhsTDBrZHJHZkp4SmMvaFVmSlU5dDBDZ1VSc2tKc3dyTmE3UkpQWkc3NmFsek1KeWZmUFZxb1BRYnBGQ3l0WTc4QTVrWXVoR3F2SnFDN204cnN1Z1lXRnpTUzloWTVMV0YxNVIvMEY1TjlJc1N1RjV3UzJTeHdmY3lMV3ZoMDA4VEJodHdDQUxCVFRpdXR5VVAyT2lheXUyUUZIaWNpblg3eHZrQkNZS0dSUStZb2ovN2todVdhbnFVWkwzYjZsVzFTanBJZWNCY0lTQVpaaDBEMTdUcC9RRThta2JXRWpQUm41SGpBQ0xBbnBFUENTYU1RVk5kOVhiT1I3NGZDbVdwWFdyQkI1dkIvM2tPS1daVGl0bFFNc0ViTmFCbWZyaTVGQzJsNTFFVGJJWHE1S3l0d0cxY1NQc3hMdkxqdDllZ0ZZbU01bnBnMHMiLCJtYWMiOiI2YTVhZGUxMTZhOGY3MTExYzMzNWY4NzE3YWQ0N2ExZmMwYmFiM2M4Mzk5NzYxOWE4MjYyZDc2Y2U5ZjJmMzU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728364660\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1227086309 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227086309\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1834296174 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwrNGFHY3JJR0ltdGdIUzErZnNtRmc9PSIsInZhbHVlIjoiVXNHSFdTM0NEdXFMMWpOcEkzUmxBNlRZWEIvc2ZMWlBua0o0WW96cUg0RWZvSTV5NGpEa3dlNU52bVVJQjdFOHI1UEk4THhaU3pNTTNyWDZFdExZN0pZZDdZYWhmekVZUXoxRUd0NTlpT3ZRWUdsU3NLU1AxMjFna0d6Y2N4QjNqTHJoWS9iTWdieGsraWJJbEVVcjdRbG9vZzBHQWFHSFNmN2NVMnJ4a0J0bGd3R0FWdUlOUTNJUXVKSlZ2bVpSbk8xa1FVbmRLbm5Ca29KbVZUOENiUXAwQlZNVjN6b1h3c1Q5amdyMzY5SVl4VTUrd0tNMFV5Y2ZIYXoyeUQ3dWdqdXdaQVhvYW81Y3VJc0doS2NzTnFYVTJrVXgwVjZ5MVZKSXFEcjllSm4zVUc0WWd5cEhOZ2Zoc01aa29FMkswRXNQRGMwUTZENjFGQ21vL0U3aDZQZWI3Qk1PUjZ2ZHY1QWVHWC9wN1hTYWg4UXllQ3BiQTljUCtNMnR3eFdzRHN6YUlBcm4zb2M3MXcxVWdaYmhCUjNQc0E5cWR2Yld2YTArVjRZWTFHQVRadVV0VGp1MGFmYk5JU0lpdjRyNG94Vm4wQ3lDSlFUUWMvRFBJZ3gzZ0ZKM2Q4RC9JQysrSkJaNFFRcDlUWjkvU3B2SUJkaEpEOVVOdDg2bGd5VGgiLCJtYWMiOiI3MzQ2MTU4Y2QzNDdlMDVhOTRmMDZjNTBjZGI3OGI3YWYyN2I2OWRiNDhmZDFmODk4NWQ4NmVmZTliMjgzNmVmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ims5TWVObHY4WWJNSkVFQy83K0lzYWc9PSIsInZhbHVlIjoiMVU2dngwRENISmJJc01iVnVXaDAxRVlSQTdQQ0l2QUVKQ0pMNVdkaXNLZWozSkI3VUxuek0wZDNmT1JuQjVoK2dia3NVVGFtSUk0WjJPemptRkJFWjljUm05VFMwVVhoL2JwbU5nTXBObWQ2dVlmSjNQclVZaDBFMDJsQ0FscitsVlg3TXJyVXlZbmRORVJhRTRKNmhESExrMTBuU05VamlHUi9iN0IrVGxDYUxBa3hwazYwK2RxMzZuS2VLYzIwN0VHSy8xdjZQbTNYZFMyd0piYUt5Mm9kOG1iYkxYeU9pUi9FWmtvSXVYZHZraUgzYkpJb25QUU9VRXlsU282SWx0ZWJzci9CVXhXcnhrUU9FNjRLL3RHQkNxZ1pGaFpaMVJyKzY3OGVTbVFpK042WUs5NG1sNVhGYmpnNVg4OVY0YkluaHIzWktNdFBWSk1JcFpNNTRKT1dsaW05emxkMXlZWGpHQTh6ZHVyREV6OERLZkxQdTVPbmNoUmRtcUhOYUJiNWlUdXVsOHRic05Ockp4SE1ldXI1RFNZVkpvelRiK3BBSm9sWUdvb0ZsUHMzYkhzZTNqdzkrZmZrQ2FQYkhmYVdnYU0xK3VkQ0VCZWVSaEVIMVRxS2t5ekNNQ3dyNGQzUmJHWmpHSzFKdnlVMlBaQzd2aGZjYS9nSkxTQlAiLCJtYWMiOiJmYmVkOGM5YWYyOGY2ZGYwYjAwMjQzZjNlOGY5MDE1ZDRkMjNiODY1ZDhkYTYwNTUxNDZhMzZmMmEyYmE3Y2QyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwrNGFHY3JJR0ltdGdIUzErZnNtRmc9PSIsInZhbHVlIjoiVXNHSFdTM0NEdXFMMWpOcEkzUmxBNlRZWEIvc2ZMWlBua0o0WW96cUg0RWZvSTV5NGpEa3dlNU52bVVJQjdFOHI1UEk4THhaU3pNTTNyWDZFdExZN0pZZDdZYWhmekVZUXoxRUd0NTlpT3ZRWUdsU3NLU1AxMjFna0d6Y2N4QjNqTHJoWS9iTWdieGsraWJJbEVVcjdRbG9vZzBHQWFHSFNmN2NVMnJ4a0J0bGd3R0FWdUlOUTNJUXVKSlZ2bVpSbk8xa1FVbmRLbm5Ca29KbVZUOENiUXAwQlZNVjN6b1h3c1Q5amdyMzY5SVl4VTUrd0tNMFV5Y2ZIYXoyeUQ3dWdqdXdaQVhvYW81Y3VJc0doS2NzTnFYVTJrVXgwVjZ5MVZKSXFEcjllSm4zVUc0WWd5cEhOZ2Zoc01aa29FMkswRXNQRGMwUTZENjFGQ21vL0U3aDZQZWI3Qk1PUjZ2ZHY1QWVHWC9wN1hTYWg4UXllQ3BiQTljUCtNMnR3eFdzRHN6YUlBcm4zb2M3MXcxVWdaYmhCUjNQc0E5cWR2Yld2YTArVjRZWTFHQVRadVV0VGp1MGFmYk5JU0lpdjRyNG94Vm4wQ3lDSlFUUWMvRFBJZ3gzZ0ZKM2Q4RC9JQysrSkJaNFFRcDlUWjkvU3B2SUJkaEpEOVVOdDg2bGd5VGgiLCJtYWMiOiI3MzQ2MTU4Y2QzNDdlMDVhOTRmMDZjNTBjZGI3OGI3YWYyN2I2OWRiNDhmZDFmODk4NWQ4NmVmZTliMjgzNmVmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ims5TWVObHY4WWJNSkVFQy83K0lzYWc9PSIsInZhbHVlIjoiMVU2dngwRENISmJJc01iVnVXaDAxRVlSQTdQQ0l2QUVKQ0pMNVdkaXNLZWozSkI3VUxuek0wZDNmT1JuQjVoK2dia3NVVGFtSUk0WjJPemptRkJFWjljUm05VFMwVVhoL2JwbU5nTXBObWQ2dVlmSjNQclVZaDBFMDJsQ0FscitsVlg3TXJyVXlZbmRORVJhRTRKNmhESExrMTBuU05VamlHUi9iN0IrVGxDYUxBa3hwazYwK2RxMzZuS2VLYzIwN0VHSy8xdjZQbTNYZFMyd0piYUt5Mm9kOG1iYkxYeU9pUi9FWmtvSXVYZHZraUgzYkpJb25QUU9VRXlsU282SWx0ZWJzci9CVXhXcnhrUU9FNjRLL3RHQkNxZ1pGaFpaMVJyKzY3OGVTbVFpK042WUs5NG1sNVhGYmpnNVg4OVY0YkluaHIzWktNdFBWSk1JcFpNNTRKT1dsaW05emxkMXlZWGpHQTh6ZHVyREV6OERLZkxQdTVPbmNoUmRtcUhOYUJiNWlUdXVsOHRic05Ockp4SE1ldXI1RFNZVkpvelRiK3BBSm9sWUdvb0ZsUHMzYkhzZTNqdzkrZmZrQ2FQYkhmYVdnYU0xK3VkQ0VCZWVSaEVIMVRxS2t5ekNNQ3dyNGQzUmJHWmpHSzFKdnlVMlBaQzd2aGZjYS9nSkxTQlAiLCJtYWMiOiJmYmVkOGM5YWYyOGY2ZGYwYjAwMjQzZjNlOGY5MDE1ZDRkMjNiODY1ZDhkYTYwNTUxNDZhMzZmMmEyYmE3Y2QyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834296174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}