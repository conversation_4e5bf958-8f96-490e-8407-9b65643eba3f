{"__meta": {"id": "X10cc5101bed9bc962c89216cdac068dd", "datetime": "2025-06-30 16:07:32", "utime": **********.411247, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299651.95224, "end": **********.411265, "duration": 0.45902490615844727, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1751299651.95224, "relative_start": 0, "end": **********.356451, "relative_end": **********.356451, "duration": 0.40421104431152344, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.356461, "relative_start": 0.4042210578918457, "end": **********.411268, "relative_end": 3.0994415283203125e-06, "duration": 0.05480694770812988, "duration_str": "54.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3874428, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.259}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.398025, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.259, "width_percent": 17.252}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.403616, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.511, "width_percent": 19.489}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-453477112 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-453477112\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1147841839 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1147841839\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-19497147 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19497147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299649939%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhaYk9scElGMlh2L3R4SCtoMnk0ZGc9PSIsInZhbHVlIjoic2ZzckU1RFgzaGMvc2NzRnZTUDdjR05TWjF4MkMrUkEyTG44ZGFUMGVkcmlhMUNCL05rRkNDK0llWUxod0VJYjQvS0tJa0Q1SDNkQVBGVGdOa1JhVTlYUTJQY3RFM2l5UVFGTkdsSjE1T1gzdUF2eGlmUWRxOHFoYWdEcTlSc01hRWpTWXhnUWFwSCsxU3YydEpNQkdMYUxtUkFZUlZuaE1YTC9CNFZGemVLYmZCQk96N1c4T0Mwa3RzL3p2YWVib0hSeWtPT21jTEZTeDBrWUV0RlRGN2V6RmNQaWtVajhQS3NqOXUvUkNSNVBZeGI0OGpyN1ZOM0lOeithU1JEMW85TXpwQ0l6ZUJUNHZGQ2lSRFVySDAxY0QyQXJxQ1J0MG54bFNOV0FnZ0wrS1RiNThLM0J6dFV5aXkwN09LL3ZCd3lmYWdFL0dEeGVNckJoTkExWFBSWFN6aWIwNE5aQTJXcXdQei9UWDR1Y0JCVDVGbG91YlFUMS84Vk1FeGRRZDNQUHdFUGRsQnlYL3ZyL2VhZjk0NHRBZ0k2dmRHZ3N0ZDJDRW1NZG9PZUdVL1EzUkFxNjU4MkkwMDVJVGRDQVl1NWlNc2NZUFhId0JUaU5jRGZ1UmdaWGNMQ3BYUGVKMHk1VXR4anI2RHBnREZrRWd6NVIrMFZSTUpneHEySzAiLCJtYWMiOiI2ODE0N2EzMmY5ODkxYzdkN2Y1MDZjOWQ2ZmNiNDQ2M2NkN2I1OTJmOTcyN2U0NzQwMWVhYjQ2ZWRhNWYyN2UzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJ1Q1cxL092YjlYaWtJUnI1Y09Xdnc9PSIsInZhbHVlIjoibWFhcjZjdm5vejlQWTRKSGtNcjlESXE0ZVQ1RmNham1NWit5VEZMam5hUFFOOG94bVIyN21mR2ZBMGR5Y3g2ai9scFMrdkUyMkVyUmZ3OWpTbDdRcDJYUzhoK1VkT1Y0cmFKbXBkeC9PenhSMDYrU1RMdXQ5c3NSUUNEdUc3UkJBdlFVNjZGMnV6Mk9hcnl1dzh5eU9ORFZKOTE2UUJIZFdyZTZqR095MVhtbjFuR2J0d0hybGUzcjV4ckFYdThwd3VPNTBKK0MvRGNpL0JPSkZYQkdPbnQ1cnBaMVRnOFVXWU14S3JubGpoSEIyN29LZmxqd3VYcDEySVh0NWhkMHVPOHk1TGp0Z2p0cWNRS3AzU3pVWmVJdmEvQk02TFZBNElWSlFBcVJpVmdlMi9tbDlhMmlkb2dqRTdwVkJpd3BIZmgxSTdpREQrMjhlb0ozQUwrV0ZHVW5ONytEQVJCVFVIZHJNTnJEVmpGZjNhNXNRN3k5MkFubnBNM2Q1Qlk2U29DRkJIc2pVSGRVTFJRMGx2UC9SSWhvUWcyUzJveXczakxobWxuSHZLejZnSm0xOWJ5Tkp5UXhnY2RmN1JPSzJTQU9SMDRDRk1TMU1IQkJpNE92TlpQOUNwR3dIQTRNTkFzY04rZGhYM1IraHZKSk9BY2tqMWNwSFk3LytCRG0iLCJtYWMiOiI4YzdjMGYxOWI1YzZmZTFlNDQzN2FkYzI1NTdlZTUwYTNlN2JlMzA5M2RkMzg3MzU2ODg3MmQ4OTg0YTI3NWQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-632428985 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632428985\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-359073722 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZuMW04cmt6Mkk3azQ3dWNNWjZkN1E9PSIsInZhbHVlIjoiSWNtUUlzd3hBU1FxMnFwVkprOXdWTUYrT3pJb3lGNHRiM0hSaGdsTzlxMzFqN05RdWNyYkRXRExOYkt5cjJSS3RxS2ZrNUpaUmpHSG1Yc3RFQU9mNXAzcnlkRisyd2JMZCt3cXhSMlN1R2hBYWZOMXFJQkZyTWNWR296Y2NDeURhbVpwY1lXaFBBaS9vV3JhQk9lSUsydGo4Y0hUTGZ0QjBVbDFNY1FrcmhHYlAwS2VxM0dDTURTMXpvYys2a2h0WmhPL1J3YXgxbHVlQmFwc0N5Y1FJaDVsaVZoRlVOSlVDVk5aRVA4cG5aUWFUeCtkd3lCVjRaUlJMQ2VmYWFseEh3TDN0a1cvUE1wdTk2Um1CeXgxUEJCaVlzcUoySi9mOUFBQ3FxeDZNa2JqOW05L3lwT3IvN1N2bEJUUWN4TXl2WmJUa014MzZBczVGT3d0WDdVRGxnUEhLRUdoL2prODVPMnlRRkRrcVVhaWM1OWFuaDZGMzRKZWtQdTdSOE9YWC96eS9kZkNBVklmMkRMVmZlNVl3ZmpCVXJuQkhZemFKV09sSllzUmdSVHFlS2FYSllCRWlkT2FwbVF1Ly96WnhId2d4OTFJUjQ1VU00bHRlZ0szY3Y0SytDTW1LNXZZU2syNnlOQjlUbXB4ZmltRzRWWlhUalJCTVRhYU16MlMiLCJtYWMiOiIzNDljZWY4NGRhYmZhYTAyMjg1MmZlNjQzMmY4NDNmYjc5MGIyZjBiMDI0YWUyZTBjMzI0MTE3ODQ5ZDVlM2UzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IndVSU5OZU1aekFLWlFBeXRkNCtVTVE9PSIsInZhbHVlIjoiUHZpMnFWd2YwQkZ5MVpUNWdEWEc2OWNrRmtQRWRZN3ordGdtT2NpTGtCWGxNa1ZoVkpzaGdlM2Y3SUl0VVhBVGhOMExOYlptUmlzUFpkKzR3MGs2dDdHeWtLQTZsd2lKaStHRStmWmRpVGNJbHlKSzRWRklnamNWS1NhTU45YUZlc0VFVkdlaTM1L0JlT3ZmMHpYamdKMUJaMTJXRy9IdXRXRXFNS1VmYWN2VTZQOVQ1SWkzVVNoQkRCUUVmL2F1MEU3RTVSVzFpTkMrNjdmYjliM3I1RUZpdURFc0dhZG9DNDl1SDluQXNRRzdFZnYxenlnL1B3RlY4elhta2wvdlUzczZzWmNha2pGMlFZd0tnbGVMZERWblpJNmZGOHYxV3EyR3JnRUhaeERLRlBqVU9WOUpOcFRLQ0RISkhNK25XWEIyNFF2Qy96a0Q2YWVvb1pqNVNTRXpvKzVaRWEwUUltVjI2azRRVjhnSnZ2cytZWlZtK0c0MThUM1BlMXRINWFTaHRYMWQ1ZEJiWVNSYTMrdmtDRWwweTNzOWMxTVpsY2VTVzhDQmdWZExUcWdORW5mSEFrdVU5ZVJQN3NFaDZxc1V4U25iRFRVS2UrbGhkUGgySWFJSU9CbWNobCt1L3BrWnRPay8yYjFlQXpGa0lNb2l2SktrSitEOWNHNGkiLCJtYWMiOiI1M2JhYWNjY2Y3ZjlkODAxZTg5NGIxMjdlNDFkMzYyYzI5NTlmOTU3NDc4ZjRiNzBlZjRkOTc5NWIwMDBlZTc5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZuMW04cmt6Mkk3azQ3dWNNWjZkN1E9PSIsInZhbHVlIjoiSWNtUUlzd3hBU1FxMnFwVkprOXdWTUYrT3pJb3lGNHRiM0hSaGdsTzlxMzFqN05RdWNyYkRXRExOYkt5cjJSS3RxS2ZrNUpaUmpHSG1Yc3RFQU9mNXAzcnlkRisyd2JMZCt3cXhSMlN1R2hBYWZOMXFJQkZyTWNWR296Y2NDeURhbVpwY1lXaFBBaS9vV3JhQk9lSUsydGo4Y0hUTGZ0QjBVbDFNY1FrcmhHYlAwS2VxM0dDTURTMXpvYys2a2h0WmhPL1J3YXgxbHVlQmFwc0N5Y1FJaDVsaVZoRlVOSlVDVk5aRVA4cG5aUWFUeCtkd3lCVjRaUlJMQ2VmYWFseEh3TDN0a1cvUE1wdTk2Um1CeXgxUEJCaVlzcUoySi9mOUFBQ3FxeDZNa2JqOW05L3lwT3IvN1N2bEJUUWN4TXl2WmJUa014MzZBczVGT3d0WDdVRGxnUEhLRUdoL2prODVPMnlRRkRrcVVhaWM1OWFuaDZGMzRKZWtQdTdSOE9YWC96eS9kZkNBVklmMkRMVmZlNVl3ZmpCVXJuQkhZemFKV09sSllzUmdSVHFlS2FYSllCRWlkT2FwbVF1Ly96WnhId2d4OTFJUjQ1VU00bHRlZ0szY3Y0SytDTW1LNXZZU2syNnlOQjlUbXB4ZmltRzRWWlhUalJCTVRhYU16MlMiLCJtYWMiOiIzNDljZWY4NGRhYmZhYTAyMjg1MmZlNjQzMmY4NDNmYjc5MGIyZjBiMDI0YWUyZTBjMzI0MTE3ODQ5ZDVlM2UzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IndVSU5OZU1aekFLWlFBeXRkNCtVTVE9PSIsInZhbHVlIjoiUHZpMnFWd2YwQkZ5MVpUNWdEWEc2OWNrRmtQRWRZN3ordGdtT2NpTGtCWGxNa1ZoVkpzaGdlM2Y3SUl0VVhBVGhOMExOYlptUmlzUFpkKzR3MGs2dDdHeWtLQTZsd2lKaStHRStmWmRpVGNJbHlKSzRWRklnamNWS1NhTU45YUZlc0VFVkdlaTM1L0JlT3ZmMHpYamdKMUJaMTJXRy9IdXRXRXFNS1VmYWN2VTZQOVQ1SWkzVVNoQkRCUUVmL2F1MEU3RTVSVzFpTkMrNjdmYjliM3I1RUZpdURFc0dhZG9DNDl1SDluQXNRRzdFZnYxenlnL1B3RlY4elhta2wvdlUzczZzWmNha2pGMlFZd0tnbGVMZERWblpJNmZGOHYxV3EyR3JnRUhaeERLRlBqVU9WOUpOcFRLQ0RISkhNK25XWEIyNFF2Qy96a0Q2YWVvb1pqNVNTRXpvKzVaRWEwUUltVjI2azRRVjhnSnZ2cytZWlZtK0c0MThUM1BlMXRINWFTaHRYMWQ1ZEJiWVNSYTMrdmtDRWwweTNzOWMxTVpsY2VTVzhDQmdWZExUcWdORW5mSEFrdVU5ZVJQN3NFaDZxc1V4U25iRFRVS2UrbGhkUGgySWFJSU9CbWNobCt1L3BrWnRPay8yYjFlQXpGa0lNb2l2SktrSitEOWNHNGkiLCJtYWMiOiI1M2JhYWNjY2Y3ZjlkODAxZTg5NGIxMjdlNDFkMzYyYzI5NTlmOTU3NDc4ZjRiNzBlZjRkOTc5NWIwMDBlZTc5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359073722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-948153760 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948153760\", {\"maxDepth\":0})</script>\n"}}