{"__meta": {"id": "X2945d3fa583c647b669012d5eb52a993", "datetime": "2025-06-30 18:49:00", "utime": **********.540757, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.027717, "end": **********.540775, "duration": 0.5130579471588135, "duration_str": "513ms", "measures": [{"label": "Booting", "start": **********.027717, "relative_start": 0, "end": **********.472662, "relative_end": **********.472662, "duration": 0.4449448585510254, "duration_str": "445ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.47267, "relative_start": 0.44495296478271484, "end": **********.540777, "relative_end": 1.9073486328125e-06, "duration": 0.06810688972473145, "duration_str": "68.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45706848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00264, "accumulated_duration_str": "2.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.506574, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.318}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.519298, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.318, "width_percent": 13.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.527796, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.576, "width_percent": 17.424}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2022825546 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inp3M2xxYngyT3J0V3JyZ1l2ZjRkRkE9PSIsInZhbHVlIjoiMlhyODRCa3VqbHJIUTBDV2FZdmNuWWJ2NnNCSEpqMlF3OWVWc0o5L05lY2NRNjZUMGZ4eVRQa2w5dWJ4UHZCMncvdWxxL2JXSGEwVWQzcEJ3S1kxZkZBaUtZNFVqTWxUS25jTzZTM00yWm5tWFV5RUZLMU9rQlYzUytySXoyeCsyKzQ4blU2cU1DdVlKQysrRWdQNW9kbTlKS2EwdnMvTFdjdFhmaEVFaXl4Tzc0U0Y3aGZTdkk5YkRkbkNjditBSjF0T2NZSGhxMVNZcFZkbng3bG4rNUtHbDBKdC96U2MycmMycnRaR0l4WDFpczN4N3BnOS9rbUFtam5XYjEvTTFaMmlCald5TjdOc3N4cGp5REd0d0FNYWVQa0JqMkFJcllpbkhhanhQUkw3YkVXYUVIem4yYjNTc2g4LzQzUXJOVHNNNGRVUE9KUk1VT281VlBsdngyMEFndFptVkJJdzNidFllUk96YnlIQUlnU3V3WlF0OUlRdGxEUmQxRWorNEZtakw4V012SzUxbWR4RHYzSWNxK2I4QnFtaVpiNFJQejVacmNIamExcWpmakpKS2ljdlhYd1NWa2xtNjRWeVA2bXRlWjJWUWRWaGc2MFRyQTRlNWwwUWNBdFowSkRVRkUwNFhyQ3gvaHpPQVRnQWJYVTFxOUdWenpkQXFSMGciLCJtYWMiOiI4ZjEyNWRkODhlNTU5NzUxMjRhNjQwYWQ0N2Q4Yzc0YWI1MWQxN2JhOTE3ZjcwMjhjODZkNTM0OTQzYzZjNWE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1zdmVkWmowMnJ4Q3lBR3p6aWpxNmc9PSIsInZhbHVlIjoiclFKZmkwRVZQZDhONkRabWZuT3REYlU2S1RLOXg5eVVTVjBXSTdDOENGbWVqTGtRVTJQRUp6cWs2aGliV1FTQ0FHcm1SSDZ6TWdIZ29RZEFtbDMzU2toN055cXR3dUlCWXJ1NTZkZDZCVXJtR0FzSkxLYWNrRlJVM1AwWGg3UlNQY0g2bWFsMW16My92cVQwQ0lHdGJrWWJHUCttRUFGSW1jaHNUdnB4ZW82ZmRSa3hxSHFyTnRWN3FPM1hyNUV5dG9jb3ByOGhKTlBXOVdtUUVBTjhlR0c1RVFhSzE1R1ZVUzl0QkljSUxZaDVSUWRvVlp1ayswVTkrckR2K2xrdnlreUVpVldTaHZpejUxZXQzUWhyUXhhU2lNajRhUjVta1c4ZllrcmVMM3pyMTd4MlZlTGo1bWpZTlBWaW44U3R0RTY1d3lBbXVneVVRUlZha2RGaGRQNEl2bDB1aEtkMURVTXlMQnhBOFl6YTN5aXhVMXN1YVc0WDFTY2ZUc2ZGMURscXQ2L1ljYUQ3azU0UndMQmJ4djV1Slk3U2xIQWJoWjNVUXFRN2pNQnlGWmd0cHpDUG4rUjlMT2EwWmlnMmRFYUgxb3ZvdTlmVXBvYzF1L1hJZDdSVHBDZEhnNUZxc1VWMWlhRUY0NmFKN2lsYUJtV3Y2bXRZcXFMejhkSGQiLCJtYWMiOiI2OTFiZmFlNjM4ZDdjYTM4NzgwM2YzZDVkNTkzM2Y2YmVmYjEzZWM2ZmNhMWE2MTJiNDZkZWUwNThjNmQ2MDk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022825546\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-955191885 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955191885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1783414386 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhKQjJjanE3ZTdQWW52S2Q1YzJSRGc9PSIsInZhbHVlIjoiUnhha2cwbis2NFNKZ2VsUjNkeWVlUjJuVHMrbSsybTZmS091OU02YnA4M1UyWXpiaWk5eDJyNjdJYkE5N2lmWUk2amV1SGZEZkpyb2g2cDIyeERyeDBKQ05ieGlCNEE1MUI5azdzbWVNWUppZHVJN0lOekw5Q2Z5U0pjYUhvYVppVHQwSnJENDUzYTh2emJsQVJGNktoSVREU3hhdDJheDh4WDBFUU5icksyNmpBZWhiOVBFV3BqRjhibXNIcFpkSXpaSmV2bGZkQTBoUEx1ZFM5OEVYM2M4YkJXQTBHYzVBQ2dxcW4rNXgyeWpQZ2xUbGNHR3JNNmhpWHh0cWNqWVVlU2ppQ01od3g1Zkcrall3ZGVhTGJ6SVovNngyQWxMMys1WTk4L0lUL3N1S1E4U0JDMGhGdUg3Qjlrd29KSWpkKytncnA5NDVwK0Q5ZUMxU3BIWEIvQXFtaGVxMUprMnZXZDZoZjQxaWtpcks2VG5HSFpjQzFQck1ybnhaTFcwQmtISm1HOW9lQjhnMWJMUGdtSmtrZnVRME9GNElXSTZldmZFdE0rWlRGNDhzRTN5WENLNEUwWjQvRjE0U3JWeGJJNmN0ajlud3lCMHB1enJKU0pxN2lCakxUMGRuSlYxcXovYjlWazJYU29Qc2lSbHptUXdqOGplc2hsdlc4VGUiLCJtYWMiOiJiMzI4ZmRhNzhmYTM0NzQyNDRkMWU0Y2Q5MzE4MGEwYTQzZTdjOTRhYzY3NDQzMTY3YTZlZWY3ODMwNDk2OTk5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpLblpmM1dWZ29yeVR1WVNCRFN6cEE9PSIsInZhbHVlIjoiWGZCL09PUjNhVWVsRmQ5UEd1Sy9LQ0JHVHFRK0hZcG9hRmVTMUlwSElHbXFDWXRtYkRDclVhOFFwQUVESHU3OWFJQkZDWk15TnNqTEVjaDZxUmNWaTNOaW8zZ1BzeDlLNjN3K01haXR2N1VMbXg5SXhBRXcvNWJHYTBvb2V6b3pESE9wZFFTZHdCYW9kdW01NURJUFVPVGliYVNnaUlNalJDYk5weklQbWdhSkxnb2g1WDB5M2w3blpvM1o2enhWM2gzOVE2cm9mbzhQT3FnM211dmI5S0R3WjNyRXovRVVUajhubExoMjJaQ2paT2ZrTWNFaU84dnN2WUsxby9PZHRYWldCdEYzcTBPWDR5MjNJWUVsRUoxd1dQeGkzQkNRZGUzVjdHOC9YMDBMYmtuM1U2cFlwWEdnVHI0c0pOcTUvbHQ2Z1M2U2lJVnA2SzdCVGE2VnJyaFRoODh6Yi9MOE9Ob09DQ0RZNnphWWtYYzlGZjJNY0tVaDNRS0NxaWxYOTNRYTJTV1Vmd3FNRHEyM1JOVm9QN3R1eFJvbUJrVEdxeElIT1NvTFg4THp2eVBvWndsaVBTUmdtUi8xTUZxUVgvRXJFcWduRDlNUElnQTRXN3hVNnFxaTV3dGEzRFFmOWJ3a0xhV3lDYlJoSlhDWFdlY2gySW11Y3d6MW9HWGQiLCJtYWMiOiJhZjNkNzgwMzExOWEzYjQwNjIzN2Q1MjljNzMxOGUyNTYxNGU0ODIwZTJjMDY0YjhjOTc4NWVlMTM2Y2JkNTBmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhKQjJjanE3ZTdQWW52S2Q1YzJSRGc9PSIsInZhbHVlIjoiUnhha2cwbis2NFNKZ2VsUjNkeWVlUjJuVHMrbSsybTZmS091OU02YnA4M1UyWXpiaWk5eDJyNjdJYkE5N2lmWUk2amV1SGZEZkpyb2g2cDIyeERyeDBKQ05ieGlCNEE1MUI5azdzbWVNWUppZHVJN0lOekw5Q2Z5U0pjYUhvYVppVHQwSnJENDUzYTh2emJsQVJGNktoSVREU3hhdDJheDh4WDBFUU5icksyNmpBZWhiOVBFV3BqRjhibXNIcFpkSXpaSmV2bGZkQTBoUEx1ZFM5OEVYM2M4YkJXQTBHYzVBQ2dxcW4rNXgyeWpQZ2xUbGNHR3JNNmhpWHh0cWNqWVVlU2ppQ01od3g1Zkcrall3ZGVhTGJ6SVovNngyQWxMMys1WTk4L0lUL3N1S1E4U0JDMGhGdUg3Qjlrd29KSWpkKytncnA5NDVwK0Q5ZUMxU3BIWEIvQXFtaGVxMUprMnZXZDZoZjQxaWtpcks2VG5HSFpjQzFQck1ybnhaTFcwQmtISm1HOW9lQjhnMWJMUGdtSmtrZnVRME9GNElXSTZldmZFdE0rWlRGNDhzRTN5WENLNEUwWjQvRjE0U3JWeGJJNmN0ajlud3lCMHB1enJKU0pxN2lCakxUMGRuSlYxcXovYjlWazJYU29Qc2lSbHptUXdqOGplc2hsdlc4VGUiLCJtYWMiOiJiMzI4ZmRhNzhmYTM0NzQyNDRkMWU0Y2Q5MzE4MGEwYTQzZTdjOTRhYzY3NDQzMTY3YTZlZWY3ODMwNDk2OTk5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpLblpmM1dWZ29yeVR1WVNCRFN6cEE9PSIsInZhbHVlIjoiWGZCL09PUjNhVWVsRmQ5UEd1Sy9LQ0JHVHFRK0hZcG9hRmVTMUlwSElHbXFDWXRtYkRDclVhOFFwQUVESHU3OWFJQkZDWk15TnNqTEVjaDZxUmNWaTNOaW8zZ1BzeDlLNjN3K01haXR2N1VMbXg5SXhBRXcvNWJHYTBvb2V6b3pESE9wZFFTZHdCYW9kdW01NURJUFVPVGliYVNnaUlNalJDYk5weklQbWdhSkxnb2g1WDB5M2w3blpvM1o2enhWM2gzOVE2cm9mbzhQT3FnM211dmI5S0R3WjNyRXovRVVUajhubExoMjJaQ2paT2ZrTWNFaU84dnN2WUsxby9PZHRYWldCdEYzcTBPWDR5MjNJWUVsRUoxd1dQeGkzQkNRZGUzVjdHOC9YMDBMYmtuM1U2cFlwWEdnVHI0c0pOcTUvbHQ2Z1M2U2lJVnA2SzdCVGE2VnJyaFRoODh6Yi9MOE9Ob09DQ0RZNnphWWtYYzlGZjJNY0tVaDNRS0NxaWxYOTNRYTJTV1Vmd3FNRHEyM1JOVm9QN3R1eFJvbUJrVEdxeElIT1NvTFg4THp2eVBvWndsaVBTUmdtUi8xTUZxUVgvRXJFcWduRDlNUElnQTRXN3hVNnFxaTV3dGEzRFFmOWJ3a0xhV3lDYlJoSlhDWFdlY2gySW11Y3d6MW9HWGQiLCJtYWMiOiJhZjNkNzgwMzExOWEzYjQwNjIzN2Q1MjljNzMxOGUyNTYxNGU0ODIwZTJjMDY0YjhjOTc4NWVlMTM2Y2JkNTBmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783414386\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}