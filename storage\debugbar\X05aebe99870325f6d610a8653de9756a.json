{"__meta": {"id": "X05aebe99870325f6d610a8653de9756a", "datetime": "2025-06-30 18:49:35", "utime": **********.769635, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.398832, "end": **********.76965, "duration": 0.3708178997039795, "duration_str": "371ms", "measures": [{"label": "Booting", "start": **********.398832, "relative_start": 0, "end": **********.722373, "relative_end": **********.722373, "duration": 0.32354092597961426, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.722381, "relative_start": 0.3235490322113037, "end": **********.769653, "relative_end": 3.0994415283203125e-06, "duration": 0.0472719669342041, "duration_str": "47.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00299, "accumulated_duration_str": "2.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.749443, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.582}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.759378, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.582, "width_percent": 13.043}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.762352, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 87.625, "width_percent": 12.375}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1865727509 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1865727509\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-801068689 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801068689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-641419820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-641419820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1122147789 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309344290%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdKVEVDdTY5bGp5ZUNla2Z6T1JYeHc9PSIsInZhbHVlIjoiY0h5OUlua1YrQmVzTlpxaDRsWWo3OGtpRHBJMmlaS1dEcFdTSXlTWDNHbFNzdFhwd3owOVViZlZTaW5UTE01WlkyS1NWZkhSUkxJQ0Ruam9xTElqK1pXUGxWOHVBQVlTSjJFT1JhYXNjMkdwYlhYK0NjR2VxSk1HRTBxdmJqSG5RY2xDZXdNWVVUL3N6OENNZmZmaXpYY081N3U5WkYvWUpZNjdQZTVWM2xOVEdiN2x2aVhlWk4yQ0JIOG1hQWVYMHBTWng0TURvZktHRDNmbFFvSVRtRlgydXhCOS91UysxYUZYTUVqZ2pnaDhBQkszSkRmblc5Vk9CQzRNNzI3OWVQLzgzSWZEd29CTFo4RER4NFJoc21UVHFURjAzbWRhZ3ROZzdFVmh2YTMrdFpJajZBQnZUeGhGUWw1KzQxTzhCZ2NRT05vOTNZK3dxWjdDTnBUNmpRVVpIVTF2TE1xY2VsOE5La0dyMnNYbnlPUnNmd1ltaWx4RkNRWjJsc3pVVFRidTdqQklBRDBialhvY3ZqbGhJZk9ZTDNaQ1dPRWFoREpMY3I0alh0S0ZLTVVFcUhYSFpYaTFZejM3RVJlY3VVQWtOUHllMC9rNHdCWk1oa09GYk9kSm1qTHhZT2dFSURtM3NxSFJwdm1WUHQ0VTZaQ05BNXVKU2JHRDhSQ2QiLCJtYWMiOiI4MjM0MjM4NjM5OTEyNTUyYTYzMGM5ZDJiZWZiMGE0ZWJlMmQ2NTFiODZiNDcxMmVhMDdiM2MwNmJjYTQ3MmFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBSbVpDNkZTdFJyd2ZsRytSdEZDK1E9PSIsInZhbHVlIjoid3pGUzQ3TG9MYnZZV1piSU1ES1FjcE9FYkViMUFyRnhMempjZHhwZERwS25VZDhhVm9TY2VTY1lpK2hpc0FnNWlqd0V5eHRMNnc2UkNwVDZlWnJ2WTYrQkhYdzNXR2llTW5yeERHUjNDbXlIWUUrNHMvNk56dmNsbWovUWcwTm9MZEZvNEZiNTFaNmZhM0tXQ1E4cDY0eVJES2lkS1hRWVMrK1NGQmpyWWxqS2N1dml5Q3BiaEJwVGRreDZQTElyMHh3MjJqQjFKb2dreWpmRW1VQTJrVldWWHFsUThJQ1MrV0pUWVI0S1RGOFVNSVV4d2loQTZQU0k2R1l0VEtodTJDWXhWazFGQWgwemdKZ2lza3d3dEd1Myt6cDdqU09JRDExenY4b0hrVUgvVldrVUlhalRYUGF0Q1NiSEFpZ3AwNUxQTnBkNXNPaHJvTlI1OGYwWG9ManhRa09JdVF3VnEvMlpCU1hWVm00eHIzbDBDU0JQYW5zSUxxYm9FL0tSaGpEdXQvZlp3ejZiK25CNCtjdWJtM0JnbjgxVTRXRWNOdWtXM1ZZZDQrN3hrT2t2NldnY2RDdXVHeDcxOU9NaEdpOUNZdTlubXlCdnhDam5paFA4WHZjeDBqZkZBeTJiQkxQMXFkKzJ1ZzhLUnBLSnczVHBIVnk1SlZJZ3hRY0kiLCJtYWMiOiIzNDUwZGZmMmI4OWMxOWEzZGE4YTA1YTEyZjY1MGYwYzJmZGFmMjFmOWI4YmQzMTgzODNmMDAwMjcxOTg3ZjNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122147789\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-26649019 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26649019\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-480738981 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNsVmtXME1GL3phalFLTWN4WCtRelE9PSIsInZhbHVlIjoidjhDME9vZVdlZXlKV3ZFTFFtb3FnM2k4OU9TZ3NDNXluci9iY1NteUpmWXYzVUEzaHFjQ1hpM3IxcFJrdXpZUXgxNU9VWkVjQVc0dE81dzRZOW5Jb3BSVjRjRHdwOE5PbktLbXoxc2JqcTM5cW1ZdDNTU3I4NnZRdnh3d1RiMkZ3N0Q0U2ZGa0V3blZDdG55enVWVGJzUFFMbkltNHJLY1hBeFNKQXhEK1B4ZktHK0VxbmZCN1pZdDYyK0dkakpOblZIL2hiWTJqV3ltbjhwSExoRWdwUDlXc3I0TkpTdGhZaXZrSXVrdDJPTDVtdTNoelRadkQ3SS9pR3FZektFb09xM3FseXRFVHlwdHlCT0pMdUVCVncxRWpvQ0ZCYzBHaFYwUXcwNy9haXYwUTJObkY2dnpsVDVTelV5U2xkR3c3dEpMRVdnb2FsZHJTRi9JSjAyVEFSZjgxLzQ1NG5STWpOTE9XSDJxRWpCbFcwME0vZEFMS2pRVXhZUjRqQ2lLVXVzODZtTUMvZjUxVmVETUEzTjQ4SWxUVlhseFBuRlZHeWNqYXVOMzcwZXFheUJ5L2VjOC9zV3VyaGdBM3BRNjZSRDZXTUR6eE9PRSsvcXJRK0VvdWpKYmJiVTRDV1M0N0kxQnRpQ0V1MDBPK1JVZThTS1RyYVVPNlZOcDJFNU0iLCJtYWMiOiJmMTM5ZDA3NGZiMDlhY2Q0ZmY0OTQ3MjAwNzM0Nzk3ZDllMzYwMjBkNDZiNzRjYjU4ZGM2YWZjYTExZmJiYmQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlleFkvTFJZb1Ura2FEbTFSMUEyR1E9PSIsInZhbHVlIjoiZDU1a0NSZVN1cWk0YmtrdG1HUFQ0V2p4TC8xMVZBR1FSaTc3TnlsNjA3T3E3dHZuVlM5dG9TbTIwTGlwdkRFQlJoWVBTV3JCcFdYZGRzQ29GcnkraVZnNjZ3Y0M5MjlHbVFwNFNzSU91NllEbVY3dHAwUjAxckdNVDVTTGt4cEtzSktXYkZYSTlKRnJZa29MRHFvY0ErTkV2bGM1SGMrUERjR0UrMnRaM1hvR3N2TTBOcmdhTTBzZmlISHo3WVlNazRGb2F3dHR3SVVXU2JmMHNGeW9yQUZPaVlKd050YlNPU0lCMmtJMERnWmNOSDZDbEVxdktQdkpSQVpLcEJhQU41eE9XamRwRGJDSXplK0ZndXFYZ2tiNEVnUE1MTHk2c0hvZnE4ODY5d0duV2dld3Z1c0tLUFlnekJoc3A2eXRpS0RDZGJ1Z3c5T0VDWkJZdUlDYnNheVJhQ3JhcGJmcWFsVGR4WlA3QnJYa0VmY09JSjNHRTlMbmdNTG1IRHVVMW9DQ1hOU3Q5VlRHbkMwTnc1Qy9NRExMRUhQRHhVZDhwTnZlTGpndHJlMmhHVERvVEh6a2U5UTc0cUx0TUFRVm1GWGozcmUvTFU3VG43ZjRSWUgxUmI4THZzd2MzUWQ2TlYyZ1NWSENKdXR6Z0V5Y253d2s3RU5Id1ZLZXJpd3MiLCJtYWMiOiI2Y2MwZmI4MDgwODNiNTFiNDQwMjE5ZTBhZmE3NGNkODI0OGI5MDYzZTIxZTA5NWY4MDVjNWNiZjYwOTQxMzU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNsVmtXME1GL3phalFLTWN4WCtRelE9PSIsInZhbHVlIjoidjhDME9vZVdlZXlKV3ZFTFFtb3FnM2k4OU9TZ3NDNXluci9iY1NteUpmWXYzVUEzaHFjQ1hpM3IxcFJrdXpZUXgxNU9VWkVjQVc0dE81dzRZOW5Jb3BSVjRjRHdwOE5PbktLbXoxc2JqcTM5cW1ZdDNTU3I4NnZRdnh3d1RiMkZ3N0Q0U2ZGa0V3blZDdG55enVWVGJzUFFMbkltNHJLY1hBeFNKQXhEK1B4ZktHK0VxbmZCN1pZdDYyK0dkakpOblZIL2hiWTJqV3ltbjhwSExoRWdwUDlXc3I0TkpTdGhZaXZrSXVrdDJPTDVtdTNoelRadkQ3SS9pR3FZektFb09xM3FseXRFVHlwdHlCT0pMdUVCVncxRWpvQ0ZCYzBHaFYwUXcwNy9haXYwUTJObkY2dnpsVDVTelV5U2xkR3c3dEpMRVdnb2FsZHJTRi9JSjAyVEFSZjgxLzQ1NG5STWpOTE9XSDJxRWpCbFcwME0vZEFMS2pRVXhZUjRqQ2lLVXVzODZtTUMvZjUxVmVETUEzTjQ4SWxUVlhseFBuRlZHeWNqYXVOMzcwZXFheUJ5L2VjOC9zV3VyaGdBM3BRNjZSRDZXTUR6eE9PRSsvcXJRK0VvdWpKYmJiVTRDV1M0N0kxQnRpQ0V1MDBPK1JVZThTS1RyYVVPNlZOcDJFNU0iLCJtYWMiOiJmMTM5ZDA3NGZiMDlhY2Q0ZmY0OTQ3MjAwNzM0Nzk3ZDllMzYwMjBkNDZiNzRjYjU4ZGM2YWZjYTExZmJiYmQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlleFkvTFJZb1Ura2FEbTFSMUEyR1E9PSIsInZhbHVlIjoiZDU1a0NSZVN1cWk0YmtrdG1HUFQ0V2p4TC8xMVZBR1FSaTc3TnlsNjA3T3E3dHZuVlM5dG9TbTIwTGlwdkRFQlJoWVBTV3JCcFdYZGRzQ29GcnkraVZnNjZ3Y0M5MjlHbVFwNFNzSU91NllEbVY3dHAwUjAxckdNVDVTTGt4cEtzSktXYkZYSTlKRnJZa29MRHFvY0ErTkV2bGM1SGMrUERjR0UrMnRaM1hvR3N2TTBOcmdhTTBzZmlISHo3WVlNazRGb2F3dHR3SVVXU2JmMHNGeW9yQUZPaVlKd050YlNPU0lCMmtJMERnWmNOSDZDbEVxdktQdkpSQVpLcEJhQU41eE9XamRwRGJDSXplK0ZndXFYZ2tiNEVnUE1MTHk2c0hvZnE4ODY5d0duV2dld3Z1c0tLUFlnekJoc3A2eXRpS0RDZGJ1Z3c5T0VDWkJZdUlDYnNheVJhQ3JhcGJmcWFsVGR4WlA3QnJYa0VmY09JSjNHRTlMbmdNTG1IRHVVMW9DQ1hOU3Q5VlRHbkMwTnc1Qy9NRExMRUhQRHhVZDhwTnZlTGpndHJlMmhHVERvVEh6a2U5UTc0cUx0TUFRVm1GWGozcmUvTFU3VG43ZjRSWUgxUmI4THZzd2MzUWQ2TlYyZ1NWSENKdXR6Z0V5Y253d2s3RU5Id1ZLZXJpd3MiLCJtYWMiOiI2Y2MwZmI4MDgwODNiNTFiNDQwMjE5ZTBhZmE3NGNkODI0OGI5MDYzZTIxZTA5NWY4MDVjNWNiZjYwOTQxMzU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480738981\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-490758385 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490758385\", {\"maxDepth\":0})</script>\n"}}