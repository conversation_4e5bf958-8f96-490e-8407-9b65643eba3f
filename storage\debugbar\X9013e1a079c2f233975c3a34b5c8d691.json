{"__meta": {"id": "X9013e1a079c2f233975c3a34b5c8d691", "datetime": "2025-06-30 16:22:15", "utime": **********.248258, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751300534.783756, "end": **********.248278, "duration": 0.4645218849182129, "duration_str": "465ms", "measures": [{"label": "Booting", "start": 1751300534.783756, "relative_start": 0, "end": **********.179879, "relative_end": **********.179879, "duration": 0.39612293243408203, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.179887, "relative_start": 0.3961310386657715, "end": **********.24828, "relative_end": 2.1457672119140625e-06, "duration": 0.06839299201965332, "duration_str": "68.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029400000000000003, "accumulated_duration_str": "2.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.213065, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.524}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.224264, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.524, "width_percent": 17.687}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.23108, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.211, "width_percent": 22.789}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-9217691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-9217691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-276937625 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA2RklLUG91WVgyY3AyWFNyRlFmRGc9PSIsInZhbHVlIjoiSTltR1lRc3hnZFRLOTZGVG5JeDRnQlRsaDNTS1pDTWFLU3loNVJIdjF2T0s0MVpZYkgzZUNOcGt3YlFZZXR3cFVLck1MUHM1NEludjlPUEp5cEFqZ0ZPQXFZdmdDazBwMi9venIvYzJGckJkU2U3b05XaitKZzkrVXlqd3piMGlocXVlU0xDUDJpNUUrWUxOTTFoTVNwUnJoOXlyM3VIeTF1bnV0TVhaemIwdlpJUnp4T1BobXhTNzZ3WEVRMGlsR0xORGgrUndCaFFqK1NibVFOUUNDSlU5MkE5MEwweTBaQzNoTEVVZVlTcWFlQXZzak9yMHFVdnoxMERoUWZ0eFNHL3NkVEJBVFRKZWtRT1NzY0VXMVdTVzFZUWVBQk5BeENvaEp6ZUtZM3dmQTU3MERDZmpIZXFabmlQMnN5WUIremZ2bWR3d1lwRkV3WGJUYnpDdWNNL1BiUUl3OVlaUzlyOFlYWUNtZXpDem03MUh1cmhOVXY5ZHI2a2NScU9sU0dyazFiZXNad3B2ZFdIeHB4dWpOeW9scUIzbW1EOHpWUi9iWWQ5alJ2YitabXBWSUZMN0JXMlkzR3RsblAwcHdvekYxMFpETk1MMUY0NHV0Uk5BejdzaDh5MU5rRFVCeDdOY2pTemk5NUh2UkU2d3MzTmpSckJCaUJ1ZzA5M3YiLCJtYWMiOiIxMzMxYWZlZDU5ZmY3ZWE3MjcxMjM1NGM0ODdiMWQ1ZmRkY2RiNTIyNjIxOWY2ZTQyNDg2OWViMWMzMWY2ZjAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImV5T2dvcnFKYXNKcnEwbUlYSUxDMHc9PSIsInZhbHVlIjoiREIrY1gzM3hFTEFwNTNXcC8vYTJKdHMrL2ZqZk9Ga3ZKd3ZnZXlOWGtRZ1Z3LzBkMFlxVWxtTHFwMW1lY0toSk4vMDFUeDg0OXRXVFpOa0VpQVhRT013M3N3djc5a3BGY25YMHNicEV1ZTVsdHhuYUxjYkpsSEhlbmViUzNML2NHR3AxT3psdlRoNzZZWXdNUGk2OEEveXRNTEJKMmJhb3U3VjZ1Sm5SSVpBTGE4S0R0Z0NhTXdwUzZyZHFqVDltQmtnK0VOOGQzN3BNT1QvTGVtUFppNUpEaXdsZGNqRzFrRDFZSTNCL2VlR0Q3V25FR0FEZjRZNmRzSGZVZnJhcjU1bGV3bFQ0eTV2SkJIUGFVK0JiRW91VUJ4aGU1REh3NXhtMjVoZnNqK1ljVXhrUzc4b3IzVFVzSyswTVppUHgyNEF4cWNVMnpMclZMODJPelJNKzNqdnFZQi9sR0QxYXBtWGR4TWpkc0g2MlBVR2hSL2tzWlpTMnppZ1VUWHFQb0Izd2ZvMTRiOEdUQ0Q0aTYwZjE1aWZMME14bHRWd3JaWEM3YkNaU0k3QVhYenlhaUFuQml1clFSU1ZuTkxEZ2tCejJvbmpXcExodTg5c3gxQnRVU0FSTjArZTY4VklEdGRpMmpSM2czVENDV0l5SmU4aWl2WFNMbXZDMnRIS1ciLCJtYWMiOiI4NDdmYjNkYzg1OWQyNmE2OTJiNjZjOTAwYTBhOWU1YmJhZTYzYjk4ODkyYTllMTY1YjA0MjU4NDlkYTk1MDU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276937625\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-515905847 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515905847\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-201609610 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkvaHFSSHhhOFNuWUd6cEJhZHU0R2c9PSIsInZhbHVlIjoiR1k2dWszdG4vazQrYkRXbzdsM1Y0RHcwcWdBckVGamhhN1dabm4yMVY5cE8rYjR6UXRxQUc5WExpY0g3YVNDUTFPb0VBbGdOUkpJSm9XVGVOUVAza1RLdi9EK1ljQmdJTTIrdjdlRkdvN0VobTBHdHhkeklCK3ZwV0M1YkZ2MW9BOFRNWlBGUXFVWHN3MUd6S2MzVWhaYUd0N1FxcFZXTmptWFNGZHdCbjc4WmRXRjNJMWoyem5Qdlo5YkovZnBZM1p1azA0WGhCbDA0d3ZVWWJRaGpyb2Nlc2pDd2NrU1ZxNkJ1ak5ueHhRQnorbitqMmxSSjlKSTJSN3VldXU5YURuWDBFUi9TRWdwYWFaNjZpQ2ZodnYwY0JBUmcrYXZlKzNlcU5EcVBuenRlZk5LekZld3JUUXFrNm1CSWV6Y2dXME1XSVhuZk1laUZMSlBQTHpIajNjUEZVYWtWUFNoMkpTZ0ZmZll5NlNQcnNTZ2g3bHJsNC9yTVZZRjVsejduMmhoNmVCWFdqSGhtakZoeXh6dUZsTFpzMUNVWFhkR1Jza3laTlA0anpFbmdQcFdNQ1crTjhGaEs5cFdQUy9rTE1vMzd3ZEFjTVZZRnpyaGh6cmdkTWJsb3lSUUVoQ0JZcWhxQVZhVGV3WVBDYW1uaDhTRitDKytnMW02T2ZLSm8iLCJtYWMiOiI5YzY1MmI3MzEwNmEwMjU0ZmM1MjA2ZGQyMWFjMmI1MDY2NzEyMTViMjc2NzQwNWJjOTA4MTQ0ZTRkNDZjYzk1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlV5Z2ZYdjE1TytIcG1PaXNNVWFGQmc9PSIsInZhbHVlIjoiUWJRZW53eDBQM3drRXB3NkJQMStKSnFqdklyZVRZTUpTT1VHZndGZ1ZCU3IyQmhla3E2c2ZnTVEyTDZtU3RHTnhnS1paa3pubkhhTU5YNTEvc0hWS3dwc0ZqQ3ZPM2tCcUNmYm1oTWRQcWg2QmtzUE45clYyMXp4UkJHeUUreXl4cDRyNHdiUmwraFk0ZGJxdkhRSGlGMGtnTWxDMG1RNmJQQXFSL1IrWUorSksyOVFYZWt1RjMwQVYxWGlzY2lZSzdxbFBQOTJtdmtuM0cyNDAvZS9NYWUrbytBU1cvZElLVVpLenF2RlFXTG9DaHd1Um5UVWxUSXpIeWVkLzhjMHdRcE1hVE5xYUNxY2lFWWppZ0RYSTdYSzhaYkZjMEMrVXgyUVhicEU2UEJYaXgzblhlZ01mTnpXYjRCV1daTTF0YjRWQ0orUFFYeTltZHBsT2h3QXpHTE51cEZkNmZqRjc5NERkOEJmNkpFNmttSUFzNjZmUTBHR2YrcmFUWmVzRG9tT3JtandzRk9kb1VzbWFaNXN6R3pjUWVQcFd3d2xoWWIrQVduU3doWEhhRnVLbG5CZDBVamFlbStWOEZHbjNSWEM5c1MvUnExQkl3OWMvRUdBQWl4Y0kzeEo1WC9SMXQrcG9pZGtDZWVYS240NmtUK0h3YUxPb2RCS2VkN20iLCJtYWMiOiI0OWVmM2YwOGJjNWE2ZTU0NzhmMDM4Y2NjZDNmNzE4ZmIyODk4YmU3NTQzZWIzMDM2YjA5OGM3MzUzZWM4ZGEyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkvaHFSSHhhOFNuWUd6cEJhZHU0R2c9PSIsInZhbHVlIjoiR1k2dWszdG4vazQrYkRXbzdsM1Y0RHcwcWdBckVGamhhN1dabm4yMVY5cE8rYjR6UXRxQUc5WExpY0g3YVNDUTFPb0VBbGdOUkpJSm9XVGVOUVAza1RLdi9EK1ljQmdJTTIrdjdlRkdvN0VobTBHdHhkeklCK3ZwV0M1YkZ2MW9BOFRNWlBGUXFVWHN3MUd6S2MzVWhaYUd0N1FxcFZXTmptWFNGZHdCbjc4WmRXRjNJMWoyem5Qdlo5YkovZnBZM1p1azA0WGhCbDA0d3ZVWWJRaGpyb2Nlc2pDd2NrU1ZxNkJ1ak5ueHhRQnorbitqMmxSSjlKSTJSN3VldXU5YURuWDBFUi9TRWdwYWFaNjZpQ2ZodnYwY0JBUmcrYXZlKzNlcU5EcVBuenRlZk5LekZld3JUUXFrNm1CSWV6Y2dXME1XSVhuZk1laUZMSlBQTHpIajNjUEZVYWtWUFNoMkpTZ0ZmZll5NlNQcnNTZ2g3bHJsNC9yTVZZRjVsejduMmhoNmVCWFdqSGhtakZoeXh6dUZsTFpzMUNVWFhkR1Jza3laTlA0anpFbmdQcFdNQ1crTjhGaEs5cFdQUy9rTE1vMzd3ZEFjTVZZRnpyaGh6cmdkTWJsb3lSUUVoQ0JZcWhxQVZhVGV3WVBDYW1uaDhTRitDKytnMW02T2ZLSm8iLCJtYWMiOiI5YzY1MmI3MzEwNmEwMjU0ZmM1MjA2ZGQyMWFjMmI1MDY2NzEyMTViMjc2NzQwNWJjOTA4MTQ0ZTRkNDZjYzk1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlV5Z2ZYdjE1TytIcG1PaXNNVWFGQmc9PSIsInZhbHVlIjoiUWJRZW53eDBQM3drRXB3NkJQMStKSnFqdklyZVRZTUpTT1VHZndGZ1ZCU3IyQmhla3E2c2ZnTVEyTDZtU3RHTnhnS1paa3pubkhhTU5YNTEvc0hWS3dwc0ZqQ3ZPM2tCcUNmYm1oTWRQcWg2QmtzUE45clYyMXp4UkJHeUUreXl4cDRyNHdiUmwraFk0ZGJxdkhRSGlGMGtnTWxDMG1RNmJQQXFSL1IrWUorSksyOVFYZWt1RjMwQVYxWGlzY2lZSzdxbFBQOTJtdmtuM0cyNDAvZS9NYWUrbytBU1cvZElLVVpLenF2RlFXTG9DaHd1Um5UVWxUSXpIeWVkLzhjMHdRcE1hVE5xYUNxY2lFWWppZ0RYSTdYSzhaYkZjMEMrVXgyUVhicEU2UEJYaXgzblhlZ01mTnpXYjRCV1daTTF0YjRWQ0orUFFYeTltZHBsT2h3QXpHTE51cEZkNmZqRjc5NERkOEJmNkpFNmttSUFzNjZmUTBHR2YrcmFUWmVzRG9tT3JtandzRk9kb1VzbWFaNXN6R3pjUWVQcFd3d2xoWWIrQVduU3doWEhhRnVLbG5CZDBVamFlbStWOEZHbjNSWEM5c1MvUnExQkl3OWMvRUdBQWl4Y0kzeEo1WC9SMXQrcG9pZGtDZWVYS240NmtUK0h3YUxPb2RCS2VkN20iLCJtYWMiOiI0OWVmM2YwOGJjNWE2ZTU0NzhmMDM4Y2NjZDNmNzE4ZmIyODk4YmU3NTQzZWIzMDM2YjA5OGM3MzUzZWM4ZGEyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201609610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}