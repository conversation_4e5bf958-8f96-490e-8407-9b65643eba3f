{"__meta": {"id": "X16a2c200c4df1b66d30b55cd4cb812c0", "datetime": "2025-06-30 16:04:56", "utime": **********.534504, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299495.959308, "end": **********.534521, "duration": 0.5752131938934326, "duration_str": "575ms", "measures": [{"label": "Booting", "start": 1751299495.959308, "relative_start": 0, "end": **********.331455, "relative_end": **********.331455, "duration": 0.3721470832824707, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.33147, "relative_start": 0.3721621036529541, "end": **********.534524, "relative_end": 2.86102294921875e-06, "duration": 0.20305395126342773, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48094936, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0288, "accumulated_duration_str": "28.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.392235, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.708}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.405613, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.708, "width_percent": 2.292}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.420624, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 10, "width_percent": 3.368}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4233441, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 13.368, "width_percent": 1.632}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.451046, "duration": 0.022969999999999997, "duration_str": "22.97ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 15, "width_percent": 79.757}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.490113, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.757, "width_percent": 5.243}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2108807249 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108807249\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427325, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1739177907 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1739177907\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1736041920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1736041920\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-688405327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-688405327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-234061702 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9nak1aanROM1VDQWlqUlpBa0dCYmc9PSIsInZhbHVlIjoieW5hRjVGbjNkKzFVQ2w5TUF3NStlMGdGSndXZFd1WjBpeG9talByclVTUGpYTStwT0ZVa0lnQXB5K0FRSE9LVTFTQ0RiMWl6N2Z0UGMrMVBzOVFsMitIQXBNRDdscTRQVStvQzA5V0RKTUlCaS9ORUxJQ01kdktLSkZkNzZtU3JoVWhMQkVFMGxHSzMxQ0VqVFJYdjVWRnlFeXlHdng5MlI4MllzVkg3V05yYTYzNnRGTFZTS1c4YTVxc1NXZWpieDR3VWkrUlpibVUwQ01Wa1NQUHh3L1AzSUNQQi9zVjcyNGlxUjBHcmYrRDdoYWFmbVNSbFdwV21TaXNyR0RkM0o2N3BpZUJOZXRneFFuMlVKam94dmJGUEVmNjZEV280UzFQUUVIS3JEdHc1SW1wdWdXSlZjZXh5cGVYUFlmZWJrMHRBc0QwUkliZFIwa0FTWHc3c0Vmc3BwWGpEQnVXbEJyMzFIRGl2K1RYR0hIYnRWVkkrdW40SVlQYjBjUUJhY0FGL0RpUDUyTW1TZkZFK0VZcFd4VUh6WUtQSzVYSWViRXhxSy9GSzRBVXNQeWxnTVFtZW1tc2N3WXNhSWNud29jcEprVHNrV3pjUDRqc3ZvRVkvNVRlYXlVQ29xWktBdGZZVnlJeCthNzlGNGtXeXBJRVIyV3F3MkV2VzFEZ3oiLCJtYWMiOiI2ZDYzZWUzZjllMDAzZDI5ZmM0YjgxMjI4MTEyYzgzNDIxNDEzNDBkZDUxZDExOTlmOThhYzE0YTI4ZjM3ZjkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtoVXh5QWUveHVpdjBRd2pSdmNUVmc9PSIsInZhbHVlIjoiMTRReGsyNlAyZndpbGkwbFl4QmNvNTZOeXZBQzVHYmUyZmVCR1FHai80TFlYZ3pXQ1FaT0NKSUlHa1JHN0lIaTE4dzQyczQ1Y0Z1a1l4MVpMSjhic2xlWUpNcFRjcGtZS2lTUTVZb1UyYk9aQjdwN1F0MGpuS3BkWnRvUXpYUXBYR01zeTZDeWlPd01mbEhpK2pndGpJZWNPRlUxNjA4TkFsM09memtKcVY5c21keEZGZ2xPUWp4aWp2bHpxUEdGeVhqaWtROCtHbEdORklzeUpmK3FlbHRWcEpCQWF6ZlNXOTdmaHNMUWI1dDdlSFhFamNBT0Q3MlpaVzhDRDY0Zk1WL01pS1U4S3ZwdE5KVjZTQTV0UmFET1FqaTQ0ZHR2U3NQOXY5ZHgzdmhlZ0IyYjNUdW1FTk9lc3VORlBGaEN6UGNJWlJsMmkzUnVGcGZlWTRXN3BBeGVFMkNPN3IwOHAybE9YMFZBZlJHSzR4VmJRek1pYXVidmlJdTMrS045VjV3VnE1Y1JYenh5Nm1xbW81bFFOYUw0L3dodTBXOFl6ZkYremZ1aXFjMTlKTjZMcHdtMFBEamVoOXViQnAzTWpERXpXTlhQRUxvWEtUbml5VWtya2NMYi9zMnIvby9tS3RUc0RQWjdKdXlUMkgxYkh2UXNlSFJrZEl2OHAvWC8iLCJtYWMiOiI3NmNmYTM5ODRmY2NhODFjODg4ZTM2NDdmY2M1OGM5NzA0YTAzNTFhNjM1YTBhYmNlZDNlN2JlYWU4MzMzNDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234061702\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-497191672 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497191672\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:04:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZEbmZtcjNMRnNLVTRqWEN5RENZOVE9PSIsInZhbHVlIjoibUpaTWVKZ1lqWkNIdTRSZTBVSUxwSm5meWFXbFhiUXIrTk5RTTNRU2lVamcvVEtIM0YzQkdaOXJWT3RPU1NrY0prbTl6cE9DSU1ubDRaUFo4MkxjcjNYVWJnMUlwc09kNk44WklvZkRtYnhDU0grSDk1dWFTOS8vclhWRHRGd3psWXlFZWVDS3d1eEtBbWpObi9waGhPTFVGWEJzb0cwUHBvRFMxcmEvT2Q1TC9WUkpVdzd2VDdJQ2dzamtrTkgxdEhQc0Y2ZmIrTTVjN1VwK0JldHJHak1kdnRzWGlUWC9JcStyUjhMcm5PeXpBS1g2SUJWazIxV3dhOW1Wazh0eHM2SUZqUWtuZnJRZTRHRDJWRVNQVlNjdmt1RGtQdTFaMVhWK24veGdXTHFhdzJzVDdBZDhtRnE4bUk5WTNrZ2RnazE3Q3A0L1dQNmxkWFRLQnUwd1BHNWk3a0Vwd3hBTUR4d0pFZksvQ3NUS0UweGRWdlFzeFpEcHNZdDZib1dnTG9raG15RkptZUxReStibmV4TEsyTGgydE4vOUYzU09Sc1NNTExHd2JCQ0x6SnBxUkdVaTQzbENjRVlmcTY1SkFLRXdWOTZWV2RJRDRtMm1DTDJEQnFsOVR0NnQzRmlUcGJMbUYxMzBBWEltTTVESnV2WDNWdDFKZE1GSlVBd0UiLCJtYWMiOiI5MWE3OTRhZTFlNDdmODIxNGUwZmQyYzEwNzhlMzE2ODk0MDgzYzdhYTFkNDUyNjI3M2I3OTgwNWM5YzI4NTU3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:04:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlAzcW9yUGd0RndHNmdPZVptdnhsNGc9PSIsInZhbHVlIjoiRVlTUlBXYVhxWlJ5a0cxRVp2QUZPeEV4ZlZjS2pHWlZkUC91ZjkrcXRlcHR1YlY5ZFpsdSsxZ1N5MzI3MlMzckVNeXFCZjU0MHJ3cEYwcU1XKytkcUYxRU1rUFoxRkE5MDRvWndoeE1FbG1ONVdEY245RXJmVmVSZncwRlowTlhJcm1walo0L3kwblZHYU9Sdkx0MDdsU3FGSG83TmJsU0QrRHVObCtFT1d3aXY3aGF5dXZaK3dVODJHRVlsVDJ2QXJ1WDJKUXpDQzRTU3ZMei85SlZQK2E3ek9BV3gvc1FKN2R2eVJyazc1T3ZsSldKSVlHYUFGdHVOZSt3bzZ6bTFEWG9GanVGcDd5T1liM2VGMW1OeFhpWk5WMkh2M3dEajdwRytFbHJZWVlac2R2TGd0bTBNRlJ2QVBCWnpSRUpoRkx3TGlOWnIvbzZjaGJHZ091ay9zQ0RGcmprb0ZXem56VHFEeUZNRk50SnNtOGNqcnVaUVc3QUtUY3QxMGVnbWdmdmVBZ1hOTXJSRHdPRzRvMEhrRlZnOFR6LzhIV2ZjNHkwZVVFWlA3ZlNXSk9ocEtsT21zZ1VRRHB6VTc1U2NIUTA5N01DNXZZbFRLOEVQY1VuUmd4VGRnc0l6VHEwTjZTT1pmSXMvR0VEQUl5VkI0ek9nU1k2d0NZUmZpZ0UiLCJtYWMiOiI1MmI4MDJiZjFlODcwOWFkMjRlN2ZlMDA4N2VlMzBlODFlODkxMmY5ODIxMTk4NjM3ZmViODExMmIwYTdjYzcyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:04:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZEbmZtcjNMRnNLVTRqWEN5RENZOVE9PSIsInZhbHVlIjoibUpaTWVKZ1lqWkNIdTRSZTBVSUxwSm5meWFXbFhiUXIrTk5RTTNRU2lVamcvVEtIM0YzQkdaOXJWT3RPU1NrY0prbTl6cE9DSU1ubDRaUFo4MkxjcjNYVWJnMUlwc09kNk44WklvZkRtYnhDU0grSDk1dWFTOS8vclhWRHRGd3psWXlFZWVDS3d1eEtBbWpObi9waGhPTFVGWEJzb0cwUHBvRFMxcmEvT2Q1TC9WUkpVdzd2VDdJQ2dzamtrTkgxdEhQc0Y2ZmIrTTVjN1VwK0JldHJHak1kdnRzWGlUWC9JcStyUjhMcm5PeXpBS1g2SUJWazIxV3dhOW1Wazh0eHM2SUZqUWtuZnJRZTRHRDJWRVNQVlNjdmt1RGtQdTFaMVhWK24veGdXTHFhdzJzVDdBZDhtRnE4bUk5WTNrZ2RnazE3Q3A0L1dQNmxkWFRLQnUwd1BHNWk3a0Vwd3hBTUR4d0pFZksvQ3NUS0UweGRWdlFzeFpEcHNZdDZib1dnTG9raG15RkptZUxReStibmV4TEsyTGgydE4vOUYzU09Sc1NNTExHd2JCQ0x6SnBxUkdVaTQzbENjRVlmcTY1SkFLRXdWOTZWV2RJRDRtMm1DTDJEQnFsOVR0NnQzRmlUcGJMbUYxMzBBWEltTTVESnV2WDNWdDFKZE1GSlVBd0UiLCJtYWMiOiI5MWE3OTRhZTFlNDdmODIxNGUwZmQyYzEwNzhlMzE2ODk0MDgzYzdhYTFkNDUyNjI3M2I3OTgwNWM5YzI4NTU3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:04:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlAzcW9yUGd0RndHNmdPZVptdnhsNGc9PSIsInZhbHVlIjoiRVlTUlBXYVhxWlJ5a0cxRVp2QUZPeEV4ZlZjS2pHWlZkUC91ZjkrcXRlcHR1YlY5ZFpsdSsxZ1N5MzI3MlMzckVNeXFCZjU0MHJ3cEYwcU1XKytkcUYxRU1rUFoxRkE5MDRvWndoeE1FbG1ONVdEY245RXJmVmVSZncwRlowTlhJcm1walo0L3kwblZHYU9Sdkx0MDdsU3FGSG83TmJsU0QrRHVObCtFT1d3aXY3aGF5dXZaK3dVODJHRVlsVDJ2QXJ1WDJKUXpDQzRTU3ZMei85SlZQK2E3ek9BV3gvc1FKN2R2eVJyazc1T3ZsSldKSVlHYUFGdHVOZSt3bzZ6bTFEWG9GanVGcDd5T1liM2VGMW1OeFhpWk5WMkh2M3dEajdwRytFbHJZWVlac2R2TGd0bTBNRlJ2QVBCWnpSRUpoRkx3TGlOWnIvbzZjaGJHZ091ay9zQ0RGcmprb0ZXem56VHFEeUZNRk50SnNtOGNqcnVaUVc3QUtUY3QxMGVnbWdmdmVBZ1hOTXJSRHdPRzRvMEhrRlZnOFR6LzhIV2ZjNHkwZVVFWlA3ZlNXSk9ocEtsT21zZ1VRRHB6VTc1U2NIUTA5N01DNXZZbFRLOEVQY1VuUmd4VGRnc0l6VHEwTjZTT1pmSXMvR0VEQUl5VkI0ek9nU1k2d0NZUmZpZ0UiLCJtYWMiOiI1MmI4MDJiZjFlODcwOWFkMjRlN2ZlMDA4N2VlMzBlODFlODkxMmY5ODIxMTk4NjM3ZmViODExMmIwYTdjYzcyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:04:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-288912573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288912573\", {\"maxDepth\":0})</script>\n"}}