{"__meta": {"id": "Xf38417165b99324c2e11a9a5eec6d94f", "datetime": "2025-06-30 18:11:27", "utime": **********.649901, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.181786, "end": **********.649918, "duration": 0.46813201904296875, "duration_str": "468ms", "measures": [{"label": "Booting", "start": **********.181786, "relative_start": 0, "end": **********.569093, "relative_end": **********.569093, "duration": 0.38730692863464355, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.569106, "relative_start": 0.38732004165649414, "end": **********.649919, "relative_end": 9.5367431640625e-07, "duration": 0.08081293106079102, "duration_str": "80.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48652904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00588, "accumulated_duration_str": "5.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.606504, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.891}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6161072, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.891, "width_percent": 7.483}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6286361, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 35.374, "width_percent": 7.653}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.630348, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.027, "width_percent": 6.463}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.634494, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 49.49, "width_percent": 6.463}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.637828, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.952, "width_percent": 37.415}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.641248, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.367, "width_percent": 6.633}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1362814721 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362814721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63376, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 11\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 632.5\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-1458048693 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1458048693\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1054656780 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1054656780\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1921645962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1921645962\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1940288419 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5EcHZpODhPVmxzbUd6dm1Fa0NIVkE9PSIsInZhbHVlIjoiL05nUFZlblVzcmcwWG5UeVNxTnBXRFNPeGFKNXQ5RC9zd29xY0VqLzRHTE1sL2tnUkZSZnVsemFaYTRYRURZNVA4YkxPN3lMamdhUzFuMytXUUJCMjRHTVhwRnlQdGlxbllMcm0zQmNhRG5PK29LclJZY1Y2SW5heENDRWhnSmtGRWtTRjE2ekpRL2FFUThvNWZJN1htVTB5cG5OT1NxMjVFZ2Q2VlBpWGRZQjR0cWpVMTRiditCdEU1NWw2TU0xT2ZlMm1xNXJCb0dmUzdjNG12U1FuaThidU5iVnFHaFJCSkxhVzhIS2IvSDBHSG1zZVNsQTdPa282elh6bVBYWmthWFk1S20wSUVPRGs0U3g3eTNUOEhsR2JZUkVBWHJFYjY2USsyNXJTSlE5ZmZabFhJTzVIUytNT05NUEJseVBzclBLUzBDbjE0UUwvTzNoMGxSbmdObi9MaklKOWcvYit2R1VUbU40M1puQnliM3B1SFpyWEJmc29MaGMxUm55TnRucXRBZWVVYVhtMjBrYVA2QzVWazlhQjAwOEFwRVhxdTFLZjZVZzZYcDJaTXl5c3lvVmI0SXpQc1VVb0pkMkdsQ2c3aWRBTnhSWmFyUVhaWmFRSTV5THFZWVd6SGQwbEpubHhpQUlTcTg5OEJwV3FXeWpyTHpxeTNXUTNWWE8iLCJtYWMiOiJhZDgyMWJiMjg0MzEwMTkzOTJlNDQyYjhjMjgxN2Q4OTFiZjU1ZTU3ZGIzOWIzYTIwMzhlNzExNDI3MjJhMWQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjYra3VBNnFlZDhTWm8zNDBQWUFlZkE9PSIsInZhbHVlIjoiZDhRWVd1dmlxQlJCL2ZyZ2ZNMmpCbTlhY09QcDhIbFNEdG8wSE94UnJIVmNvbEI0UXdmUjZEQlJ4d1V1bDhqbDk5VkRBUFcrOXZKd3c3OG5XRXZvdWdGajFGSFZOdHY5Qm44ek5QRkRJdEpkakZkVXNBaEljVjc4N2hjSFo2WHNPTW1WSUpJbWRwNi92MnVlRU9xdUo4ODhsUGdqdk1ZSTdqd3Ftc2gzVGtiS3RIR1hsa3ZaQnlTTUxsU3M4OEE2M0RZbTRLZjJWYUJsQVNsUzdxWjd4Tng1czFZdExLdVZuVzRGMlF0SW44TDB0M0YxVWZZcDFqditGMmNBRjFZMW9sT3ovR2ZFUWk5Ti92ZW5IaS80VDRXZ2VJTUw2dUE2K01LL3N6K1RZVUg4TlYrck1CU2NYcGtSMUp3TStxVGgySENsNGY1NC94Z0FIaFF6MUkzYmpjcE5CR2FPamltR0lIdnRqRStUUDdUKytkZUg5MGNJMTJSMFRsSTNkZjlWQ21xb3l5VGhQZVBwbnNRa2tNUXFaNlQybW9WdE9ESlljcmxmYURkOWdmRHFOWWV2OVFMc1E2TERhR2puRmlqZ3VUL1NCMFBzTHRKSm5aazVqQk5sWXhEWXhpMmowdVFMSkhKK1ZpYkFwNWVtUzVKR2NoQk5WdzRpSkMwUy9TWmciLCJtYWMiOiIyMDk5YTNjMGM3MGEwNmUzNTI5MzU5ZTUwYmYzYTgxNWYwMTRiZjE4MjhlOGIzMTc0YzUxY2QxM2E0OWFmMzgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940288419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1308904171 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308904171\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2045206225 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ3TUZ2dEU2bmFQaTFKQmVqTjBUOVE9PSIsInZhbHVlIjoiUWdhZ3g3S0kwVjkwNkRxT3Zud1lPVDJzbVEraDVBOGRjdENWaUc2cktRVzdPUm9JT1JtZkFGeWdDQjllTHVZY09mMU5nOEdiZkVTR3IyRnh1bHpJcGFYL1R3SUpzR0svd1BLUzVwcThkVERwRGV6OG1QMkpIRE04SHhOSUFaVE4xTy9HQVFQbnh3V2Z6VDVUbmRNRExvNTlMbDMzMGlTSi9jTTFYTDVuZVNYeUVtSEVMRXZMWVg4aXdPejVZWGtKQ1NRaXk4elgxSHI0UlU3cC8ySitOZDdOemJFUWgzbkJxeEJJVFI1VVNWa3o5SDB6d1BWZExBMXd5OU8zKzR1RUJtalBvN0M3SHVscUFRR0VMcFh2Tmx1TTluVm9yUEdkejUwUmRLei9jd1FmRXkycVlFRU5ZZjVXMSszeXh5NkRqMjJHUFpBaTNpVTltRlIwVm1pTFJFdUhueTVjcU9pWXc4T3NsN3I0UnIyblB5Y0VBM0ZJenVMZ3ZmSEk3RE5oc3I4QklmdGIxMWFyK0VZSlgrSmxMcWtuazZHQlVCU2gzOHNGemhQRXd2VmxiSk1OQnhDejRLR0hUa3pvRE9RRWlya2VaVy9pc21QbUYzUjE1MU5jMGVtK2tuc2Q2U3YzVWhtTXU5MWExVzNaN2dSUmF5TENtanF3TXBiSXNmQ3oiLCJtYWMiOiJhZDFlYTUxZDg2YmVhMjE3MzcxMTE1MDRkNzA2ZTY0NTZlYWY0ZTE3MDFkYjczMDk5MjFhYjNkOWMzM2E0YjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJyVEdyL3pjdWNZNE0rbmkwRUtWdWc9PSIsInZhbHVlIjoibTBSRk93YjZwelZKdFptR21UVXdLWWtTUTU0cnB4elVlN2Exd1l4Q2h0c0ZWSlk2THkxTHRZaWtWWSsrTjBydFo3OTNCSzAwYURpNzZqd3hGYVAxS2RGTk1xTkhOVzF2ZVFPV0VVK0pXMUxtMGlkSHFhdGVsbXlJZTdvRnVjNVZwQmlEWGJnOHlvLzVYUUFBdjlwUkQwclpmOE82Q3pMOEp1UElxdmNUVVVBSG8xUjIyanRyL3F1d0h6b0RqSmlFUUZqQUptbzdqUktwVkQ5dGxxcnFhTTZ2WVZWbDFabnY1Q3kwWWxHLzczSDhGRGR1VEpoZGZNM3lLM0doWElLK0FpQ042Z29iQnZGUjF3MXNuN0VJWHNPdnpwQ3pLU2FVckJ1ZEhXYjNreUVITzNjN05LOTRBT21wdXoyNlcyYnhNNENKaHJ1eGFueGpPZS9lRUFyS2w0T29YUVBSMERZL1dJSFJoZUNCWjBRVWxCbkNSS29QZEFvVUF0VEdyK25ucWFGdU05KzU4VlYzUWdhbng0aWhwRzBNaHFkM2M5dmN1NmRZVDVvbWtlbVZ2blp4eXhrUWJzVFkrcHh3TytVL1NqYlBFK3R2aHgzWkVLQ1h0R1hDM0c5bXB5WXY3Vk9CdTJxU285VjE0MlVCOThYUWd0d1FhK3Z2NjRLc0UrTXIiLCJtYWMiOiI0ZWUxM2E0MWUyZDhkNWFjNTQ2ZmJjZGVmYjIxMTJmNWZkYTA1MWRmYjc1ZDdhMDUyODE5Mzk4NGU2ZjUwMjdiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ3TUZ2dEU2bmFQaTFKQmVqTjBUOVE9PSIsInZhbHVlIjoiUWdhZ3g3S0kwVjkwNkRxT3Zud1lPVDJzbVEraDVBOGRjdENWaUc2cktRVzdPUm9JT1JtZkFGeWdDQjllTHVZY09mMU5nOEdiZkVTR3IyRnh1bHpJcGFYL1R3SUpzR0svd1BLUzVwcThkVERwRGV6OG1QMkpIRE04SHhOSUFaVE4xTy9HQVFQbnh3V2Z6VDVUbmRNRExvNTlMbDMzMGlTSi9jTTFYTDVuZVNYeUVtSEVMRXZMWVg4aXdPejVZWGtKQ1NRaXk4elgxSHI0UlU3cC8ySitOZDdOemJFUWgzbkJxeEJJVFI1VVNWa3o5SDB6d1BWZExBMXd5OU8zKzR1RUJtalBvN0M3SHVscUFRR0VMcFh2Tmx1TTluVm9yUEdkejUwUmRLei9jd1FmRXkycVlFRU5ZZjVXMSszeXh5NkRqMjJHUFpBaTNpVTltRlIwVm1pTFJFdUhueTVjcU9pWXc4T3NsN3I0UnIyblB5Y0VBM0ZJenVMZ3ZmSEk3RE5oc3I4QklmdGIxMWFyK0VZSlgrSmxMcWtuazZHQlVCU2gzOHNGemhQRXd2VmxiSk1OQnhDejRLR0hUa3pvRE9RRWlya2VaVy9pc21QbUYzUjE1MU5jMGVtK2tuc2Q2U3YzVWhtTXU5MWExVzNaN2dSUmF5TENtanF3TXBiSXNmQ3oiLCJtYWMiOiJhZDFlYTUxZDg2YmVhMjE3MzcxMTE1MDRkNzA2ZTY0NTZlYWY0ZTE3MDFkYjczMDk5MjFhYjNkOWMzM2E0YjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJyVEdyL3pjdWNZNE0rbmkwRUtWdWc9PSIsInZhbHVlIjoibTBSRk93YjZwelZKdFptR21UVXdLWWtTUTU0cnB4elVlN2Exd1l4Q2h0c0ZWSlk2THkxTHRZaWtWWSsrTjBydFo3OTNCSzAwYURpNzZqd3hGYVAxS2RGTk1xTkhOVzF2ZVFPV0VVK0pXMUxtMGlkSHFhdGVsbXlJZTdvRnVjNVZwQmlEWGJnOHlvLzVYUUFBdjlwUkQwclpmOE82Q3pMOEp1UElxdmNUVVVBSG8xUjIyanRyL3F1d0h6b0RqSmlFUUZqQUptbzdqUktwVkQ5dGxxcnFhTTZ2WVZWbDFabnY1Q3kwWWxHLzczSDhGRGR1VEpoZGZNM3lLM0doWElLK0FpQ042Z29iQnZGUjF3MXNuN0VJWHNPdnpwQ3pLU2FVckJ1ZEhXYjNreUVITzNjN05LOTRBT21wdXoyNlcyYnhNNENKaHJ1eGFueGpPZS9lRUFyS2w0T29YUVBSMERZL1dJSFJoZUNCWjBRVWxCbkNSS29QZEFvVUF0VEdyK25ucWFGdU05KzU4VlYzUWdhbng0aWhwRzBNaHFkM2M5dmN1NmRZVDVvbWtlbVZ2blp4eXhrUWJzVFkrcHh3TytVL1NqYlBFK3R2aHgzWkVLQ1h0R1hDM0c5bXB5WXY3Vk9CdTJxU285VjE0MlVCOThYUWd0d1FhK3Z2NjRLc0UrTXIiLCJtYWMiOiI0ZWUxM2E0MWUyZDhkNWFjNTQ2ZmJjZGVmYjIxMTJmNWZkYTA1MWRmYjc1ZDdhMDUyODE5Mzk4NGU2ZjUwMjdiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045206225\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2086878121 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>11</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>632.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086878121\", {\"maxDepth\":0})</script>\n"}}