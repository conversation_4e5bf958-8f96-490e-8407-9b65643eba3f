{"__meta": {"id": "X99d87b9db88fdbe9f0846f913607f00c", "datetime": "2025-06-30 18:09:44", "utime": **********.233469, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306983.805301, "end": **********.233483, "duration": 0.42818212509155273, "duration_str": "428ms", "measures": [{"label": "Booting", "start": 1751306983.805301, "relative_start": 0, "end": **********.189791, "relative_end": **********.189791, "duration": 0.3844900131225586, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.1898, "relative_start": 0.38449907302856445, "end": **********.233484, "relative_end": 9.5367431640625e-07, "duration": 0.04368400573730469, "duration_str": "43.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43874176, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00199, "accumulated_duration_str": "1.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.221133, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.935}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.226616, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 86.935, "width_percent": 13.065}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 2\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 21.98\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2299 => array:8 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"id\" => \"2299\"\n    \"originalquantity\" => 11\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1269298059 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269298059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1126211895 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1126211895\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1107487104 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR1MTl0NGFrTG0ySzFpWW1XK1ZzQXc9PSIsInZhbHVlIjoibTY1bmltajFTSy9kVE5PTGZVZGhuT29adEJqaENjTU5COVdZL09xNmpRRFlGZGc2QTBPT0hkMWNEQjNFMVEvR0hrZkVwem1jcmhtN1NXV3ZFRTdjQ1dmWHlkeVFwWkNxazBXWEhzaklMbWdHL3VUdStNODFsTURwdVFidjhSYnB4aFQ3YjRZMExyNHd6bXNsVVVkemZLM0J0aFNWT3J3SFpMK1BMWFBWQjkyNHBiZVZtTExIQ3ZwQURtQy9uTEthYy9OaEs4ckd4WnhBSG9QU1J1NS9CSzhqdGlLaGEvR3cwU3JjQVo5a2tzMTlDRUhwVEhhbC9JMmlWZzRhcFdmcW9DdTllSHhPSUVUeGdZQUdnWTM2ODZKNW5lQVdiWWduNGJjNWMwaVZaZHozenZFdG5LQlljOHM2MUkyckRHTWtaRUEwZ1RoU2ZWci9mMkJpcDk3bzRRMExiMGdrUElFamx0ck1MWS9HZXVCWjhuTHl2QmdDQWR5WERPZ0haVEYrNkc3cVZnMVBPMjFjM0hCL2ZNM1FPY0xsVkEraUtvOVZHT1lwbVJKcjV1VlkwYlI0eXB4ZGswUk5wLzZDMktpUUxqV3R2SThJblBTaW9vZ0lvZVN0SXplZHQzRFZHa1AyWUVXWVc3ZXdsbWxYMml5Q0I4dVlDRWxlRVUxeUNBWEciLCJtYWMiOiJhODY5MDY5ZjZmMmY5NDQ3NjA3M2U4YjI1ZjExNTZiY2Y0NDc4Yzc2YzkzNGVjOWQxYzQ3YmU3ZTc0NjRhZDk3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjI3MkNUdEMySHNuUnREZmxGaE9Fc1E9PSIsInZhbHVlIjoiRmZ0YVc3Vm9QQWx4ZnpQcmh0dXJ2TEJ5V2d4T0ZORVQ3M1VIclovL2t4TEVFS1k4MjdtV2s1cUk5elJadnNuUzAvN1gyMVg0clV6T1NJR2tISmtrdFY4bDEzcDhtZVI3OXNFZVZwWHQ2M1dLNHJtWFFwM24zcHpVdTI3Y2Nmd295d1VSb2RhMlI5UkRHeTJubWs2cnhoUEg0UmhaeW9ZSGF4alYzTHpSYnlMUWhRSkhwS253T082N0txOWdXamEzRXV3dzJtTTQzbTNxV3ZNVjBIcVpCb0JGYjhDZzZlbDh6dzNOSDcrb2NrMGJyMUpyeXlxdFpmdktvSGhDeEdyak9ocG1rUDNSYUdjUkp2aHRIU1ZUOEpZcVk4ZnVBMml4bVZxdStpV1FRYStCRFNnY3dvSVdmR3pNMnZFNkJybEFuaWprRGJaUVRoR0dxZzN6RFhEc3lmM1pqdkZjZ09tcGNEUVU1bEw2dDNUS25tR2RJS1ZtZzFERHBVWWNuY00rWHoxZVIxek5Va3VnaWFKbHBxakxHempKUjNYRVlrN2pqSHp4YTl6akNxcmVmMEdKSDJVNjB2ZXhvU3FGVFhhNTg5K0ZncnFwaWtQSEgwZk1FTDJVa0kwWThvNFhUZHJ2Z1BGdVF2SUNaWVFhd3ZrT1hnWWN1ZkoxbDV5S3Rza04iLCJtYWMiOiJjN2Q4ZmIzYmJlNDMxNGFkMWQ0OTYxYWNjY2RlYzc0YTAyYjRhM2VkOTU0OTI4NWQwMDQ5YWJmYTE4MTkyZDVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107487104\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1730172022 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730172022\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2057308641 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI4K0hrK0xtWTJZOFd3aGl4eXJGL1E9PSIsInZhbHVlIjoiNHFlWG9yTkVnbEw5WEpZcmdKNWRzbkUwR2U4WUo0cjdNbUxZa2VrRlFXWlQ4cW5LZFlyVFhrb09ENVkxT0hRT2VrOUhxSXErYlo1QzVNU1VzbjJTKytEYUFjaDZoZnBsKzlTVlErZER2a1I3amNoT3VpZVJIODlrT2ptTC9CNGZtUHJjckptbWpRSDVQVDk0TGJaOUkvZm1SUkx3OFYvakhGNnNMQ1JVai9xRlNXK295V2VqNGRYZlp3VXd3ZTA1alZoVlVZUitTYjVYcmx4eEZpRE9uZTZoSTBONGtKNklKejYrMitUMUwvdEVBNDNJdldQeEFNY3NpdHc1WUpLZHJJcmpsZ3VVQWpsa0xMMnllajRNUUd3eUJEcVZvZEFxaERBeDcwNzBlS0ZYK0VwZFpPMG00ZVNZSENHNFZOWExqeDZ5eXVReGwwZjF1OWtyRldTeWRYOWQwQjZZQ3oxTEtNWmtBVUlwem5sUkhEa2hraGFKY2I1d0JhSVVZSnVodGQ0ZnJtRSsrUjFML2ppQ3pYODlJMy96ZHlxT2t6OUlwMXAvMzBKcjU1YThlZFM3bis1MG9qcFFGaDl4S1M1Zll2d3VhWis1RTRKMjQ1YkliMERic2JzTjBXTnlYOVVHMnRSYnpaTjF2WGgwcDh4Wi9ZNkhBOW9EZmkvWkJJUGsiLCJtYWMiOiIxMjBiM2Y3OGMzOWZhNjYzYmNjNDBhYTE2ZTYxZDU1ZDVmNmE1MjFiNTA2NzUyMjA2N2JmODUzNGI1ZWQyMzY4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InE3UVVQY2hMUXBnSTNUUlVLTFdQMGc9PSIsInZhbHVlIjoiMiszTklsTWw4RFVRUUkrT2lxTGxveHNFNkx0SVVVbUVmM1VHUURZbGRQTjgzSE9mb1FJRUJLZ2ZObGdBcmtaa3BiTTkxL2VtMXY1K0lIdjFBeFVrazRkdCtKVXdTeDBDdUp2dEJFK0RRYUFCZ2d5Q25DVUVkeWVmdk4yNFBacWszSnBLa2VZaWhEellBWDdhUGdHUGMvWGdXQUNYMk9ZbnNCY1lLbWllNjczV2hramo1OEJUcjFnR2Npb2JmQ0JPaURmN1VHckl3anhTWXl5VkoxdVNNWXJkaGdSQ0xFdWxNTzdIR1c0U2ZFZER5VG5WYTBUbXl3SDBYNVBlMGQyQ1J4R2hwU1V5VmJjOVF4RWNWVnN1Y24vdDlqQ1QxUEdqR0dybi9JZlcwcFZoeSsveFdha0tjUVNTczlUc05Ya3ZUWUhTeHYwRHc5em5QTExhNDFlanZla0RSMGFrdkVHYmh5MllTOEtWUU1DcWZEcjFQeFN0c3pCcVEwVXY0N0hYK0loZEFKb1JNd1M3ZTdOVjYxZjJOYUI1bS84YW1IRzl1NEc5NUh3SCs0NTFhK3hLaU9hSWJxVzRpOUhuTzNIWStHNWU2Rlhvdlc3R0wvK2RJUElXQ0pJaXdoVVN5Rmw1bG5GaHNSeUQ5S2swWkxVQ1VGc2dndTFJWE11L1N4bDgiLCJtYWMiOiIwYWRlMmY1NzA3YzIzODFhMjZlYjUwYWFiZWFiYTNiY2VlMGQ3MjIyM2Y0ZDMyMTU0NWVlZDlmMTc2YTU1NzkyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI4K0hrK0xtWTJZOFd3aGl4eXJGL1E9PSIsInZhbHVlIjoiNHFlWG9yTkVnbEw5WEpZcmdKNWRzbkUwR2U4WUo0cjdNbUxZa2VrRlFXWlQ4cW5LZFlyVFhrb09ENVkxT0hRT2VrOUhxSXErYlo1QzVNU1VzbjJTKytEYUFjaDZoZnBsKzlTVlErZER2a1I3amNoT3VpZVJIODlrT2ptTC9CNGZtUHJjckptbWpRSDVQVDk0TGJaOUkvZm1SUkx3OFYvakhGNnNMQ1JVai9xRlNXK295V2VqNGRYZlp3VXd3ZTA1alZoVlVZUitTYjVYcmx4eEZpRE9uZTZoSTBONGtKNklKejYrMitUMUwvdEVBNDNJdldQeEFNY3NpdHc1WUpLZHJJcmpsZ3VVQWpsa0xMMnllajRNUUd3eUJEcVZvZEFxaERBeDcwNzBlS0ZYK0VwZFpPMG00ZVNZSENHNFZOWExqeDZ5eXVReGwwZjF1OWtyRldTeWRYOWQwQjZZQ3oxTEtNWmtBVUlwem5sUkhEa2hraGFKY2I1d0JhSVVZSnVodGQ0ZnJtRSsrUjFML2ppQ3pYODlJMy96ZHlxT2t6OUlwMXAvMzBKcjU1YThlZFM3bis1MG9qcFFGaDl4S1M1Zll2d3VhWis1RTRKMjQ1YkliMERic2JzTjBXTnlYOVVHMnRSYnpaTjF2WGgwcDh4Wi9ZNkhBOW9EZmkvWkJJUGsiLCJtYWMiOiIxMjBiM2Y3OGMzOWZhNjYzYmNjNDBhYTE2ZTYxZDU1ZDVmNmE1MjFiNTA2NzUyMjA2N2JmODUzNGI1ZWQyMzY4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InE3UVVQY2hMUXBnSTNUUlVLTFdQMGc9PSIsInZhbHVlIjoiMiszTklsTWw4RFVRUUkrT2lxTGxveHNFNkx0SVVVbUVmM1VHUURZbGRQTjgzSE9mb1FJRUJLZ2ZObGdBcmtaa3BiTTkxL2VtMXY1K0lIdjFBeFVrazRkdCtKVXdTeDBDdUp2dEJFK0RRYUFCZ2d5Q25DVUVkeWVmdk4yNFBacWszSnBLa2VZaWhEellBWDdhUGdHUGMvWGdXQUNYMk9ZbnNCY1lLbWllNjczV2hramo1OEJUcjFnR2Npb2JmQ0JPaURmN1VHckl3anhTWXl5VkoxdVNNWXJkaGdSQ0xFdWxNTzdIR1c0U2ZFZER5VG5WYTBUbXl3SDBYNVBlMGQyQ1J4R2hwU1V5VmJjOVF4RWNWVnN1Y24vdDlqQ1QxUEdqR0dybi9JZlcwcFZoeSsveFdha0tjUVNTczlUc05Ya3ZUWUhTeHYwRHc5em5QTExhNDFlanZla0RSMGFrdkVHYmh5MllTOEtWUU1DcWZEcjFQeFN0c3pCcVEwVXY0N0hYK0loZEFKb1JNd1M3ZTdOVjYxZjJOYUI1bS84YW1IRzl1NEc5NUh3SCs0NTFhK3hLaU9hSWJxVzRpOUhuTzNIWStHNWU2Rlhvdlc3R0wvK2RJUElXQ0pJaXdoVVN5Rmw1bG5GaHNSeUQ5S2swWkxVQ1VGc2dndTFJWE11L1N4bDgiLCJtYWMiOiIwYWRlMmY1NzA3YzIzODFhMjZlYjUwYWFiZWFiYTNiY2VlMGQ3MjIyM2Y0ZDMyMTU0NWVlZDlmMTc2YTU1NzkyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057308641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>21.98</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>11</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}