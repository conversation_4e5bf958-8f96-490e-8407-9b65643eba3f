{"__meta": {"id": "X1d26b8dd2f0164d06faa6c459030f505", "datetime": "2025-06-30 16:07:04", "utime": **********.30155, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299623.846741, "end": **********.301563, "duration": 0.4548220634460449, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751299623.846741, "relative_start": 0, "end": **********.24889, "relative_end": **********.24889, "duration": 0.402148962020874, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.248901, "relative_start": 0.4021599292755127, "end": **********.301565, "relative_end": 1.9073486328125e-06, "duration": 0.05266404151916504, "duration_str": "52.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028456, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031999999999999997, "accumulated_duration_str": "3.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.276138, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.688}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.288004, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.688, "width_percent": 15.625}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2943351, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.313, "width_percent": 14.688}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/59\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2136718029 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2136718029\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1586844166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1586844166\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-888223857 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888223857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-32570653 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299611552%7C10%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVqT0RieG0xODg0My9PRlJUTHJWSEE9PSIsInZhbHVlIjoiL0RVM1NPdzhydUk3VFNKRnFVSlVGcnh4cXVZbmpOVFVJaTY3eDZvQ0ZXZm1GUXZaQUhiZWo0TklEMDhSK3pvczNERTBUZERHbHpYTHVxODltblZaTURTcW5EMVhTTG5kU0RmbG9mRmdZVFRqMU5qNi91SE5qRkt5NjFucUFZYUdMelltdlRSQUx6Tm8zLzNXdXJFcURnT3lqMEkyUUtYZGZoWEQySU5YemV2Q2J5eEtaNG9oSjJGSmY4amtqT2NwYTNlMVNjakZPMWhXWHJVMkpzeXBwK3ZwNnBKOGw4dDd5OXNFWmRoNWgrSWczdDl0NEwwMnF4UFIwTVRsemVyL3BKUjQxcnhnUlJwNGlONWZkQ29OUk1FRHFxY1Q3REVsVW5mdXQrVDhtNUxYWHpYUWNEWWxXR0FUYnNWTXR2Q3pRbXp2VDBzaCtpUm92SHl5dWtXOWRpK0hkL2RPeFloYUd6N2x5aDRsdlB1Qys3L1pad0xWcGk5R09aUjN1V1d2UzcxSGkybnVsemJJNlVhYXZobEVYbzl6TEZKUk14ckFWZHlqdWc4V0F2U0RVWGpvcFpJSjhVTTQrT3FhclFBSW5uSkp5OEdyNmVnbmg5cFBuM0xuRDhydkpnUlpyRHorbmtYWHE1M1o4eVFUbWQ2VHh2UXlZRkowVTFxSnZtVDciLCJtYWMiOiIyOWNkZDI5ZTc3YmE0ZTYwYzVkNTI2NzgzMDVlM2QzNWE2NTQ3Njg0OWJlMjM0YjVjNTg4YmVlMmNmODg4ZjExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJqaVJEK3dHemxQaDIzdmhNejA1TGc9PSIsInZhbHVlIjoiYzVQaFdGZThxek9BTU9SUFJIOENxUlRwTUtoWWNQNFRMZ0xXa2gwbkYrODJidlZZSldCdVFTS1lFV2RVR2hYMjBhbmxHNXBmMnlwOVdWMWJteVpLcHJ0dW80d2lzMnMwR2RVQ1RpcVU1ZGtEbVhiamhDY2pXOUFsLzc1RXkvazJNVFBxdDFlYVExWjdFRUpjbkFkbXRmb294YTNweGhBRUQrUlhFTHhkVFhaMDJsK2hkc09OT3NPWW5CMFJ5SGx4VGw0V2hVK0d1R3FrTFJWTTE1U1A0YmNmVVhydE1saVR4SXNVVzJCN0dWTU1tcVprOUlibWdQQ3FvY1IvMFFGV3JqU1AwcHVZWFJMc2ZqQ0R1WEFuVUFZUHlDd0VHU1pIVEt3bG9FVHpkRHpCV1RqMTBJVWlra0QwZVNLL3pVcFFoSEpDWmU4ZjFuR08vSENLWkNjYW5YSXo1Rld4dXhZN01uelF6ZmZBdDArOWVaOG1oSERxa0VQOUJ5eTEwSDBsMi9qWGxsREpoSGl0MkJNeGcvRldnR29lcHNudFJ3S0FpaERmTjdCekRkMklGdGNMS2syRHNNTzVBN1JNdjliQ0lTQmRlU1ZJMHdua0hIK3NweVJIejYrZkozSGhkd1QwMGgweURjZnBHL2MvQ0ttYm1MMGduVDN1cGM2VFJYbEYiLCJtYWMiOiI0Y2VhYjhhZWUyMDRlOWZmNDFlNjY3NDIyYWUxOWE4MTc5ODVlNmM2ODAyZjEzZTg4MjA0OGQ3YjNmYWRjOGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32570653\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2097426780 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097426780\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1730488373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdraGZ4elFEV0RzVUd4NTlMR3RabFE9PSIsInZhbHVlIjoickpkeExnWjFCTnUzc1NpSmYwOHZLWVBDU1h1elZYWlVuRlg1V29RR1k3bDMvdXAvOGFHdlVNMWtRQThaLzYwRml5eXY1ZjIydEVlUFp5U2VFc2F6dThYcFRFRWpOUi9HcUZUQytmakhiV0c1Y1dDQ2RxUkgrT2Y3UUl5bVoxeWNlaENNRkQ5bzl3dkE0T29tUXRxY05qTUkxdWRtNDVUWm85UWp4QjBaTGJnK0V6UTVwc0srd0hpQXd4SEhMVFFHZ1hINHY2K3hvN1JUSUtDMHdnZGFnSDlaQ3ZTaUxKVExPM0NBNTJwOFJNZ29vL00wTE44N0hWVW1JNmhzUFgrdkxuaDF3RVlkdGN0dWluWDdBT2tDOVdhNk9VOC9USkFqY2tMNFpIeVY4R2FVZVZKQVhpd1ozc2l6eVZKR2w5cGxXMkhxWTl4WmlTa0tFb0Z1ZXE5Ri96cHRWbnkrajdMYy9Ndkh3VnlzTDRKWTUyM0g0ZVpzWWh0TVlmcnplak10SHVCVTZRNklXMytORlJ5MDQzRFl5YkF2M1hnZ29odkZKYmJXVm5hTlFsNGVRNnZtbWVncE94SVI3ME5QOXdkaWVETXFhNW9XSU1lTUgxYURaRmd4b1FGNU1OVS8zb1lYYWRXSEVGTTE3ZnNBSzg3ZmdtcW5lek1mUGFPZ1UzYlQiLCJtYWMiOiJmMjRiNTcwMzBiNjE2YmUxNzIzMGIyOThmMmVlNTY3ZTY0NWQzMmQ3YjNkMmYyMmI4NjhiNGMyZDljMDkyNDExIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBEZWx0QjBFOVVkQUdKWEsxUndDYnc9PSIsInZhbHVlIjoiSS9taHNCcEJadXR4WmNWYWRUajdpRG5icFEvYXNQSnFPQWFhVkpMSVRJWEtIY0xISnhhZm02RkM4OGhiQUtQL2gvSWRLS0psS1VvUktLTktMdlpQclNENGdiNndTMDlPUmZ6ZFRNTEtPRm1TcGZlbDAybnljdFJ1cUVaVEFWQ2k3KzFJNVkzQnRLdk91ZTduQlZIcU1Kb3ZqemN5YnBvSzRrZ3c4MFlxb2JoUGszRk11Y2hIcE5ZYTlmYm5WeG9Pejd0ZGV5amRjRi9MSytCTEt0SHZySE1vU1MrbTRmeksrMldxR0kvYnBrRitxNWxyeEhzK1R5ZHlLMUxnSkJnWDU3RmVVTmVlVWQxQ1AxSlpMOUprUjk5RFNBeGxGQkYrVUVoM1UxaWl2eFlWWWQ5ZjV0UVFoQ3R6cEVmWVFpc1k5dVVHZXF0V25UNDRMSXhQK0N1d2p6SEtaY2EweXg4STJ3cGFCLytpQ0lIbkowVWlWNmJTdUYxZ0JEU2R1T1JPYjBCQ1J3R2RZeTBiZ29PeE9sRXVCM2tsTWlMbXdIWTFOam5TalNsVGJuU29ueXVxME0zZFVZR0QrbzFtNGFFeVA0TGZ6WERnejdKaEpqQ3pSRWhzTHRURVpkZjI5NGt4cFAvaTQ3NVJKaEpKSktpL0dFbm1idVNsVWcxamwwU3YiLCJtYWMiOiI1NjQ1M2NkYzYxZmRlYjNlOWVhMTQ3MjA3NTAyYzlmYTJiOTAwNDAzZmU1MTcxNDY0ZDZlMTNlZWJiOTM0ZjlhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdraGZ4elFEV0RzVUd4NTlMR3RabFE9PSIsInZhbHVlIjoickpkeExnWjFCTnUzc1NpSmYwOHZLWVBDU1h1elZYWlVuRlg1V29RR1k3bDMvdXAvOGFHdlVNMWtRQThaLzYwRml5eXY1ZjIydEVlUFp5U2VFc2F6dThYcFRFRWpOUi9HcUZUQytmakhiV0c1Y1dDQ2RxUkgrT2Y3UUl5bVoxeWNlaENNRkQ5bzl3dkE0T29tUXRxY05qTUkxdWRtNDVUWm85UWp4QjBaTGJnK0V6UTVwc0srd0hpQXd4SEhMVFFHZ1hINHY2K3hvN1JUSUtDMHdnZGFnSDlaQ3ZTaUxKVExPM0NBNTJwOFJNZ29vL00wTE44N0hWVW1JNmhzUFgrdkxuaDF3RVlkdGN0dWluWDdBT2tDOVdhNk9VOC9USkFqY2tMNFpIeVY4R2FVZVZKQVhpd1ozc2l6eVZKR2w5cGxXMkhxWTl4WmlTa0tFb0Z1ZXE5Ri96cHRWbnkrajdMYy9Ndkh3VnlzTDRKWTUyM0g0ZVpzWWh0TVlmcnplak10SHVCVTZRNklXMytORlJ5MDQzRFl5YkF2M1hnZ29odkZKYmJXVm5hTlFsNGVRNnZtbWVncE94SVI3ME5QOXdkaWVETXFhNW9XSU1lTUgxYURaRmd4b1FGNU1OVS8zb1lYYWRXSEVGTTE3ZnNBSzg3ZmdtcW5lek1mUGFPZ1UzYlQiLCJtYWMiOiJmMjRiNTcwMzBiNjE2YmUxNzIzMGIyOThmMmVlNTY3ZTY0NWQzMmQ3YjNkMmYyMmI4NjhiNGMyZDljMDkyNDExIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBEZWx0QjBFOVVkQUdKWEsxUndDYnc9PSIsInZhbHVlIjoiSS9taHNCcEJadXR4WmNWYWRUajdpRG5icFEvYXNQSnFPQWFhVkpMSVRJWEtIY0xISnhhZm02RkM4OGhiQUtQL2gvSWRLS0psS1VvUktLTktMdlpQclNENGdiNndTMDlPUmZ6ZFRNTEtPRm1TcGZlbDAybnljdFJ1cUVaVEFWQ2k3KzFJNVkzQnRLdk91ZTduQlZIcU1Kb3ZqemN5YnBvSzRrZ3c4MFlxb2JoUGszRk11Y2hIcE5ZYTlmYm5WeG9Pejd0ZGV5amRjRi9MSytCTEt0SHZySE1vU1MrbTRmeksrMldxR0kvYnBrRitxNWxyeEhzK1R5ZHlLMUxnSkJnWDU3RmVVTmVlVWQxQ1AxSlpMOUprUjk5RFNBeGxGQkYrVUVoM1UxaWl2eFlWWWQ5ZjV0UVFoQ3R6cEVmWVFpc1k5dVVHZXF0V25UNDRMSXhQK0N1d2p6SEtaY2EweXg4STJ3cGFCLytpQ0lIbkowVWlWNmJTdUYxZ0JEU2R1T1JPYjBCQ1J3R2RZeTBiZ29PeE9sRXVCM2tsTWlMbXdIWTFOam5TalNsVGJuU29ueXVxME0zZFVZR0QrbzFtNGFFeVA0TGZ6WERnejdKaEpqQ3pSRWhzTHRURVpkZjI5NGt4cFAvaTQ3NVJKaEpKSktpL0dFbm1idVNsVWcxamwwU3YiLCJtYWMiOiI1NjQ1M2NkYzYxZmRlYjNlOWVhMTQ3MjA3NTAyYzlmYTJiOTAwNDAzZmU1MTcxNDY0ZDZlMTNlZWJiOTM0ZjlhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730488373\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-199997564 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199997564\", {\"maxDepth\":0})</script>\n"}}