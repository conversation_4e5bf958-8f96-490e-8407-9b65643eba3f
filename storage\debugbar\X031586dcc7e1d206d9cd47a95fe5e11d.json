{"__meta": {"id": "X031586dcc7e1d206d9cd47a95fe5e11d", "datetime": "2025-06-30 16:10:15", "utime": **********.31562, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299814.886212, "end": **********.315634, "duration": 0.42942190170288086, "duration_str": "429ms", "measures": [{"label": "Booting", "start": 1751299814.886212, "relative_start": 0, "end": **********.272161, "relative_end": **********.272161, "duration": 0.38594889640808105, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.272175, "relative_start": 0.38596296310424805, "end": **********.315635, "relative_end": 9.5367431640625e-07, "duration": 0.04345989227294922, "duration_str": "43.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43873288, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00241, "accumulated_duration_str": "2.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.304356, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.668}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.309206, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 79.668, "width_percent": 20.332}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1545 => array:8 [\n    \"name\" => \"غور\"\n    \"quantity\" => 1\n    \"price\" => \"1.00\"\n    \"tax\" => 0\n    \"subtotal\" => 1.0\n    \"id\" => \"1545\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1208864849 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1208864849\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1184336282 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299801140%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1VRmpYSkdTbFltUVpLWXI0aDBKNGc9PSIsInZhbHVlIjoiMzloMGtIVnZVeDVOeDJaZ09QNGV2OXJodUZWdkZZbXBqUzZRTEl4OWhuVlVTaXVROVc4L2Y1bG5pRkl0QW1jN0R6RmxVemhIRGVnaVVSaVRhbXdwZmZ1bldZdmJCcTRTNGEvT0plTkNQRTRvbitPOXRkRTRCcE52M1U5QjJWdk9wNFBiUlRjRm5SMkZqRVJNeVY2QmNpZ3dqYkxTQWRtcmJvb042QkZvb2VjWXFhVVZod00ySHV4emFTKy9SeDVpYUYva1J6MTRRbE5hM3Z6UG0vVDJvZUlGN21FanA3Z2NZb1ZiNU9OQWxhL0tGelIxaVBHYzR2WFE5d0svdHBsOSsxNjBtRHNDUVlRQ0t6UHBUUFZnSGhhaHE5YjFvNmxaOWM1REMzWktOdy95WWpnTkZITzZIYkFlUTRMSE9taVdkM1d4dXFPWitlWEJZdmVQS3JVbU0yNUEyTXprVTZtQUVvVXhtbHpqcEloMFBCd0lRWnJldHQ2S01rTzdpSFd3NVE3MG9zWjlwdzRSTFp5blkzdXpDWmRHYlk1NmRBbGxNMnhFTDhXUmM4ZHZuOUp6V0p2M3loK0tsRHBwUzhRU3diNXNWOHlTMUc4NXR6MHBjYUV6eXMzaWF3dFhya3oyRGxIRnZyaWhrQWtMU05ERWM0Q3I1YUtTZ2VneFNJalYiLCJtYWMiOiIxN2Q0NGU2YWNmZWQwYTlhZGIyNzU1NDQwYjAxNmFhNDM1NTJmNGQ1NDFhZjEyZDhiM2Y2NDVmYTM3MjJjYmIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBCNTFsWlZuMFFvdFNMSmd3WUxLYVE9PSIsInZhbHVlIjoiQkNST28xUmxjWkxqaGdCaVFWOHJ6TnI2SXhaL1dJUTNYVm5vdDcvVEhjOGo5emFxSEFQM3YySTN1bTZVNVFwbUtHcnB5TWdTM1hzMnNJcCtFbWQ3SnJrMFdxb2tBM0MzcUxkK1lickZJSHB0WGxoZUgwemUxWGg1ei83eGVsc2J6MW9MUGdTVzBSMWdwblQzV0Y2c3V1TUgveDZLZzl3NGhSQlVMaVM0RzBWVDkzWElWWmZnYis2Tm9XRWxVNXB1SC9LYzlqaXlhbkdoVGwybThQdEs3VWxVTGFhVWxESjNPSFZjbGt6YUlxQTRwWWEzTlRtZ01pMGtiaXUrdFdRL2ZFSXdIbWo4Mlpaa0xsWGJ2UTl2L1JBbFV3akdFQ0RVajBRZExiaEtzODJ6Z1g2MldxNWxkeU05blNJYkczWlBIVjNhOTNFdThxTmFQcW5QS3ArYzJwaEsvcnhhSytZZ2NVdGRPanYzS1FOaXhONHR3TGd5UWs3R1M3ZHZGZi93MkZITVUvNFdTSmxxcjhvN25UcjFLRFR6Zmt0cVV2RkdjMkpPUXNGVDRSdEg4NzVjL01KbnVKdndEYlptd2pKVWxOSVBYb2trT0lFUVZXZmJNZVZzMExxaWpnTW1WVmJKQitlbWNnd0tNMC9pRmpLdlVUV2Q2VU50YUVOSjVsa3MiLCJtYWMiOiI2YWIxYmQxNmY4OTZiYWIxYmY0NmFhMzcxYTY5NWE5YTNmNjU0NDEzM2M5Y2ViYWQ2YjQ1Njg5OTU5NGNjZDQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184336282\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1208391079 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208391079\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1483872196 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ4UlFzK3FnQ0FUdm9NRDkzQ09hbkE9PSIsInZhbHVlIjoiSy9FVE5jUlZKR0QydGNhM3ozNkFESFJuSWoxWjNmZk83QmxBMmtmQWZHY28wSHFvaXBtTzFjQ1RiUVdwckx4S2xIQjF3MVcrdCt0b1Q3eXRHNWdJbDc4V2VlY0FlNGg5MkVtYW5TUDJnU1ZsTEwvMDZHSzVVeUtRWFlybXVMS1RNUk5GODc3TkN0WFBkcVlxM09mMjVSKzNTMHRBc0hnWVlDOENydTJ4OGgvekl1VEVSc21vZWRFSG1hSE8raEJWeHpIZSswQ0ZJWUdZUENBQlRTZWVWQW9kQUVEay9GNkRKcjRNMjBWamVZbEV2eUR4SzM3d1BlMExDKy9QRUVsSUFEa3N3R2p0dVd6by9WQlZSRjNLcnVPcm9vc01MdGpMblc3cW52WUozdFBjamFMNmVQL080NkpDU21LVElUTkxlOC9SaUNUclN2RStOMldMclZuWjU4K1crby8vdnlaam9URnB0N1lweDJSZlRoaTRvT25YK2RKZ2lZR0ZKWjJGMG5KTmdZRVM4NlA5OXB2SlVhLzJIbUxYNUd5Q3JWTmljNFdPaDU3Tk1wNjhTd3MvdjZHc2NQOGhIWTBTL3JtZ2dpZGRzcGttY2tVaEV4V1N4VEhGTit0SThCWlhFYUx3VXl5STYyQzRwM0w1VXBmUWtYWml4K2VDaTRWUnBPVjIiLCJtYWMiOiJkY2I0Y2E2YWFmMWVhYjQ1MmY4MzFjODIyZjlhMjJlNGI1OThmMGE5NzIzMGQ3ODEzNTZkNTAxZjJhMDAyZjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNGSXNmekhlbjZSd0R3bjJQTGY1TUE9PSIsInZhbHVlIjoiZTdPc1J4TUdOQ1RwLzZKRFZCaENQbU1OS2J1UDVEK1FyRkdrWFlyTmdpSDBqTGl4alBudUkxWGlTcFZhdm9KcndsczRucGFSVWVmbEIvcTUyNitEUHFWYktaWTBOSkppWHdsSklYbzllY3FCUm5oVW1EME1ZeDlVVVpsaVVucERGQkl2RTRCcVIvQWZVbS9iTVh3NkV6VkZwVFdKS0RCLzYxckxERTloMTRQRS9sOGI0L3RqRzJjNWtQNHRNWVkwanRTRjJNdlludXEyaDFTQmVlbWczTFNsSVBHM1RFYjh1RFptVVEyY2JKNi9pYS8wb1ZCb3lBZmlTbDdmZTUySGZRRWxxVCt0TlJ0c3UzU2ExY2U1MTB0WW0xR2FZVlFGOFg2SVJkVHk1Z1ozQWdvMDlBKzBqYmRmRVdLck9YV2VFeFlvTWkrcGgyOEZxeHVhQmZUSi9hZFVjQ2UxY3lQdnJDYUlDV2JDNWVZNW5Ra2NiTFZoQWFDd2ErTEdkcStKbThIc0JMNnJzRmtDU2ZNMm52N2JTcjBtQ25LNDRCUzRuQU51cWYwSEo4ak1GWjVzanAwWWUxZ0R0WDNPTmVDREl3aXY1YXVvVHhwak9XWTBuQmhPdjZIUENqVDk5YTY3SE9MYzJFc0lZNko4RDVPdnpTbDZJZ0tMbDd0YXIvN1UiLCJtYWMiOiIyOGI5MDFjYWRmYTgwOGM2YjlmZjk3ZmQ3OGVmMGViODQ1ZDBkNDdlNjAwMTNlOGZkMzc4ZDBjMDNhYjZjZGY4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ4UlFzK3FnQ0FUdm9NRDkzQ09hbkE9PSIsInZhbHVlIjoiSy9FVE5jUlZKR0QydGNhM3ozNkFESFJuSWoxWjNmZk83QmxBMmtmQWZHY28wSHFvaXBtTzFjQ1RiUVdwckx4S2xIQjF3MVcrdCt0b1Q3eXRHNWdJbDc4V2VlY0FlNGg5MkVtYW5TUDJnU1ZsTEwvMDZHSzVVeUtRWFlybXVMS1RNUk5GODc3TkN0WFBkcVlxM09mMjVSKzNTMHRBc0hnWVlDOENydTJ4OGgvekl1VEVSc21vZWRFSG1hSE8raEJWeHpIZSswQ0ZJWUdZUENBQlRTZWVWQW9kQUVEay9GNkRKcjRNMjBWamVZbEV2eUR4SzM3d1BlMExDKy9QRUVsSUFEa3N3R2p0dVd6by9WQlZSRjNLcnVPcm9vc01MdGpMblc3cW52WUozdFBjamFMNmVQL080NkpDU21LVElUTkxlOC9SaUNUclN2RStOMldMclZuWjU4K1crby8vdnlaam9URnB0N1lweDJSZlRoaTRvT25YK2RKZ2lZR0ZKWjJGMG5KTmdZRVM4NlA5OXB2SlVhLzJIbUxYNUd5Q3JWTmljNFdPaDU3Tk1wNjhTd3MvdjZHc2NQOGhIWTBTL3JtZ2dpZGRzcGttY2tVaEV4V1N4VEhGTit0SThCWlhFYUx3VXl5STYyQzRwM0w1VXBmUWtYWml4K2VDaTRWUnBPVjIiLCJtYWMiOiJkY2I0Y2E2YWFmMWVhYjQ1MmY4MzFjODIyZjlhMjJlNGI1OThmMGE5NzIzMGQ3ODEzNTZkNTAxZjJhMDAyZjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNGSXNmekhlbjZSd0R3bjJQTGY1TUE9PSIsInZhbHVlIjoiZTdPc1J4TUdOQ1RwLzZKRFZCaENQbU1OS2J1UDVEK1FyRkdrWFlyTmdpSDBqTGl4alBudUkxWGlTcFZhdm9KcndsczRucGFSVWVmbEIvcTUyNitEUHFWYktaWTBOSkppWHdsSklYbzllY3FCUm5oVW1EME1ZeDlVVVpsaVVucERGQkl2RTRCcVIvQWZVbS9iTVh3NkV6VkZwVFdKS0RCLzYxckxERTloMTRQRS9sOGI0L3RqRzJjNWtQNHRNWVkwanRTRjJNdlludXEyaDFTQmVlbWczTFNsSVBHM1RFYjh1RFptVVEyY2JKNi9pYS8wb1ZCb3lBZmlTbDdmZTUySGZRRWxxVCt0TlJ0c3UzU2ExY2U1MTB0WW0xR2FZVlFGOFg2SVJkVHk1Z1ozQWdvMDlBKzBqYmRmRVdLck9YV2VFeFlvTWkrcGgyOEZxeHVhQmZUSi9hZFVjQ2UxY3lQdnJDYUlDV2JDNWVZNW5Ra2NiTFZoQWFDd2ErTEdkcStKbThIc0JMNnJzRmtDU2ZNMm52N2JTcjBtQ25LNDRCUzRuQU51cWYwSEo4ak1GWjVzanAwWWUxZ0R0WDNPTmVDREl3aXY1YXVvVHhwak9XWTBuQmhPdjZIUENqVDk5YTY3SE9MYzJFc0lZNko4RDVPdnpTbDZJZ0tMbDd0YXIvN1UiLCJtYWMiOiIyOGI5MDFjYWRmYTgwOGM2YjlmZjk3ZmQ3OGVmMGViODQ1ZDBkNDdlNjAwMTNlOGZkMzc4ZDBjMDNhYjZjZGY4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483872196\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1906588221 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1545</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1594;&#1608;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1545</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906588221\", {\"maxDepth\":0})</script>\n"}}