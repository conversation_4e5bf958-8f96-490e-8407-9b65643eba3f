{"__meta": {"id": "X3d024ebbe71be448683a81a50bd3c6e2", "datetime": "2025-06-30 16:07:13", "utime": **********.053164, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299632.588892, "end": **********.053179, "duration": 0.46428704261779785, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1751299632.588892, "relative_start": 0, "end": 1751299632.999981, "relative_end": 1751299632.999981, "duration": 0.4110889434814453, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751299632.999993, "relative_start": 0.4111011028289795, "end": **********.053181, "relative_end": 1.9073486328125e-06, "duration": 0.05318784713745117, "duration_str": "53.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00286, "accumulated_duration_str": "2.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0338452, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.685}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0445619, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.685, "width_percent": 19.58}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0471811, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 84.266, "width_percent": 15.734}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-2126310342 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2126310342\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-298102128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-298102128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1284121000 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284121000\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1218740719 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhJTVJ6R2FJdU94Yjk5c0k4ZzZDN3c9PSIsInZhbHVlIjoiQjdSb0JiUkx6QnpqMUwvQXdFbFZLZXZrL0tabkZJOVZEZVl0elJHSFdabUhHN3JEbVdieStMWE81K1JmQWtBcXRFSVRsSmZQM1lmN0tHdVhFZ2FOLzVKaFBLWkM0MW9jS2c4eHZ6bUFJRmZoRE9YcTNIcVV5R1ZRczBIL1dlVzk4czJabWFGQXVDNDI4eTdzTXJBQTB2QVZrb2Z4NjEvMGNuQS8xWGpRcXYrNC8rSjM0NUY3QlpuTTVGcFY2bjVvK01rVTNlZ29QUFVXQXh0OEN5KzAwcVg2MlkvcDFnQTFLZW0xclBDdDF2dnJCVDZwbWRGaFlPb01pQmZSckZaL2Nka2ZwaU1OcW5YU0x1VnE4V1JRNDU3N0RXM3lEWFFFeEZmemFhMlBGZlA4Zmhkei9KWVpSMWp1ZFBPa2V6WFVVZU5Qem9yRXBzM3UwbFBoU2hRZWRwMXM0cHNxd29WaHZBSlNrVzYraDVQbFdrQnVqQVNHL3hLMWhRUVJTRVZyb1lzTlNmYlNGdnlONkxmQjZBZUltd1pvaTBnMlFVZExQaFNhWXhNamVTZ1RIUUk4OXpPb2VpaUlIZ0RVcVRPWkVHMnVUNFYwZVhjS2tXMXlmdFpvMHBxdXN6R3RBajRPVVF4SVRhaUtLdGVBaVVRMGxHdlVDZjlWYUhhdGlDdVYiLCJtYWMiOiIxYTJjMjA5MmE2ZjQ3MzM5MjcxNmQ4ZDM0NzgzMGIwODM5NGMwZTZhNmFiZDc4OGQwYzhmZTViNzY1YWE1ZTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhBR0NvMDlpR3pSeW9qQVA2elpvR3c9PSIsInZhbHVlIjoiSkd5VlA0Q3JSN3dYa2Evd0hUTUJQSkZYekx4b2RHQThCMS82Z2pLa1pmQlNXTTkwYndGOXE2UVRpMktuUnZLaTQzczh1N3BRR2RrLzU1a3M3c3JwM1NUYThoNzVhbDJPV05LdlRNMjhJdURoV1hGS1pYMkpYb2lsVE01N0hqMldUazNVSVNhekl0aTVKaXV1MExTdTVsMS85Vy9jWlV0c2tLY2VIOXZRMi9pOHNzb3k0NmVsSWNuVjB1Z2dwbUNBekRFMldEM3FCNWVvQWlrbzNkZUY5T0Q1Zlg2NmZJLzBMTjlNUjVDalQ5WnJCMFppQVVuVUp5ejFHY0tDWGJUSlpKbGlKbEpyeXMwZzAyNmRaenNtczlOVDdhOFg1dG9PSXA2cG5aNGNFMzlqc3YvaUxDdTMrcWYrU0pDbkp0YzhMb1JwSTErWFVuMGVsb0lKS25UVkRDdE9UbDZnaXB0QTgwOUpVbFJPWERYMWVPN1RsdlJjd3FhaXIxTHBRald5T3p3OXR6ZGt0dHlMbXlpblZ2ZjZaaThObnRRdFE5dllyQjdDZWZSakU3ZnN0RjFZZlMzb3UvdnpZWTRNVDNyTThjenlxMHBVd3krNXhPcjhwT0pSSnc4N3orQXJIZjJGNHhqbk14cG9hbkgrTi9QQlJ6QitYYTNDcVltcWIxTloiLCJtYWMiOiI5MDY3ZTFlOGU4ZjI5MWRkOWU2ZGFmYmYxYzc0M2YzOWZlYjdiZGUwNWZjYzFmYTBmZTQyMDdkMTI0ZTk5NWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218740719\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1471364329 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471364329\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1866093647 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InI5S3RHRWdyUFNXeFlnOHMySXVadGc9PSIsInZhbHVlIjoiZ2tjU0tKK3Nmd2dkTHZOTHVsL2g3RFhpMk95Q1RyenJiZXYra0Vsd05qbmd1YUJDM01vQnEzVU1vbDl5K0V1a0JuZys2RXQwT29waGduL2VlbXdvOG96SExsY05UZERhMnJuZnBiUmFJSmNYQ2ZWSVFoTGpwdExYTTRGVEE1SUY2WnV1cFltWjVsajRLaVFQS1R4OVpndVkyRkpZUzRlZjdFdGIxTHdaMm1aTXA2UmxnQ3N2N29GbWNDWWxkY21YRnF2MUhLNGdrUm1Dem8wSVNNZzhPWjJMc3VXUytZdTkxTExBVDFweS8wdFJ6QWUwUUVYcWFrZ1FrNWJoS2tPT2lzOU4vdEdpZUpLM2QrVDNqNDd2MSs0czA5Yk1LbWlKTVllR3N6aXQ5dG1jNHVhUTZYOHpMTndrbnNYcHhyUjFubE1uRWpEam9oKzVyNXJLcGdvOGJKNFJTb1FYMWp5Nnk3K2k4TGZYQ2Z1T0tidXFENHFFZitidllBY2NsdFRMMWNROFNXbXJlSVZGNGZIVXZJRjJraGJGbkxRZkNJWjB4NlQxT3FLa1E3MkhoMTNQMHdDd1ZVYjJEaVYySGJTVlJPbENKeWIvemlTNmV5OVBCS1BGS0hjcXJ5WHJoMjNDRm4zSWNhVWh2UGJtbkhpd2F1T090Mk4wa1hTMUJWZFoiLCJtYWMiOiIwMjJhMWI5ODBjMWE1MDI5NDRmOTI3ZGJmNjk5ZjUzZDgwMWEyZWMwNGJmYTBjOGRkOGM2MWE1Mzg2ZWVmM2NlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5GQkVNMFFLcjg2NWRLYVRUQnZ0M0E9PSIsInZhbHVlIjoiM0lsaXJLdmRsK1BMSVAvN29ZbGJkelhzZzRLaDVNZHR6Z0VhbkF2L0ZMb3NXaGRLVTNKVFRjSUttZSs2WjJQNkIrUzV6K0ROUWk0TkVlNURodFY3OUxFN3pmZHBnVy9tdEdxRm1hWTRyRVlBZllIbzkxdmJpODJ1ejlIQ1AyTUZoQ29XWU5WYlBGR1lMRDFBRmpSVjhnVGE0QmZNYnNiSjBwV2pxckNVcTBaQWVMbXh4ank3NSs0ZW9XYTd4VEhZOEdMeFgybzVTanN3Z2k2L0tlSjRtM2dMUDVnTTJaYXUxOEtLcXUwNEk3STkydUl2UmxYcVhNWmh3L241T1BVY3lSK1hiL2c4SGJUVnZDRVpxVFNWSUUxdFd0TTArblIwQXcxbHJYbWZvSzRORHduT0xaSGpzcGFHYnNWRXk0ZS9vZnVyTmRPczZsNktoblhFNkx1TWIyNURrZnJaNm5YemlvOWNOVjFFQkU0b2l0cFMzMmNLTkZ6R2c1dWhXTnBNQ0Y2cXdWYU94ZjdMcjBjUTJWNGNGS2M2S2g5bTZ0QkxvWTFsU3V4aHRpd25Yb1BLWWY2dVA3UXVIQkJqUmx5VklQZS8rcDU4cmU5WWUwVEQwWmc2K2RPc2Vab1R5a2lEQVV0RmVLaDJFUkNHQ1JENFZjTnlEOWZWNmVBaGRsYmYiLCJtYWMiOiJlMzA0NGUwY2EzZTBlZGViNGE5NTM3N2RjN2IzNzhiZTEzZWI5YjkwMzc2NmYyZDBiMzJmNTA5Zjg1MWZjOGUxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InI5S3RHRWdyUFNXeFlnOHMySXVadGc9PSIsInZhbHVlIjoiZ2tjU0tKK3Nmd2dkTHZOTHVsL2g3RFhpMk95Q1RyenJiZXYra0Vsd05qbmd1YUJDM01vQnEzVU1vbDl5K0V1a0JuZys2RXQwT29waGduL2VlbXdvOG96SExsY05UZERhMnJuZnBiUmFJSmNYQ2ZWSVFoTGpwdExYTTRGVEE1SUY2WnV1cFltWjVsajRLaVFQS1R4OVpndVkyRkpZUzRlZjdFdGIxTHdaMm1aTXA2UmxnQ3N2N29GbWNDWWxkY21YRnF2MUhLNGdrUm1Dem8wSVNNZzhPWjJMc3VXUytZdTkxTExBVDFweS8wdFJ6QWUwUUVYcWFrZ1FrNWJoS2tPT2lzOU4vdEdpZUpLM2QrVDNqNDd2MSs0czA5Yk1LbWlKTVllR3N6aXQ5dG1jNHVhUTZYOHpMTndrbnNYcHhyUjFubE1uRWpEam9oKzVyNXJLcGdvOGJKNFJTb1FYMWp5Nnk3K2k4TGZYQ2Z1T0tidXFENHFFZitidllBY2NsdFRMMWNROFNXbXJlSVZGNGZIVXZJRjJraGJGbkxRZkNJWjB4NlQxT3FLa1E3MkhoMTNQMHdDd1ZVYjJEaVYySGJTVlJPbENKeWIvemlTNmV5OVBCS1BGS0hjcXJ5WHJoMjNDRm4zSWNhVWh2UGJtbkhpd2F1T090Mk4wa1hTMUJWZFoiLCJtYWMiOiIwMjJhMWI5ODBjMWE1MDI5NDRmOTI3ZGJmNjk5ZjUzZDgwMWEyZWMwNGJmYTBjOGRkOGM2MWE1Mzg2ZWVmM2NlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5GQkVNMFFLcjg2NWRLYVRUQnZ0M0E9PSIsInZhbHVlIjoiM0lsaXJLdmRsK1BMSVAvN29ZbGJkelhzZzRLaDVNZHR6Z0VhbkF2L0ZMb3NXaGRLVTNKVFRjSUttZSs2WjJQNkIrUzV6K0ROUWk0TkVlNURodFY3OUxFN3pmZHBnVy9tdEdxRm1hWTRyRVlBZllIbzkxdmJpODJ1ejlIQ1AyTUZoQ29XWU5WYlBGR1lMRDFBRmpSVjhnVGE0QmZNYnNiSjBwV2pxckNVcTBaQWVMbXh4ank3NSs0ZW9XYTd4VEhZOEdMeFgybzVTanN3Z2k2L0tlSjRtM2dMUDVnTTJaYXUxOEtLcXUwNEk3STkydUl2UmxYcVhNWmh3L241T1BVY3lSK1hiL2c4SGJUVnZDRVpxVFNWSUUxdFd0TTArblIwQXcxbHJYbWZvSzRORHduT0xaSGpzcGFHYnNWRXk0ZS9vZnVyTmRPczZsNktoblhFNkx1TWIyNURrZnJaNm5YemlvOWNOVjFFQkU0b2l0cFMzMmNLTkZ6R2c1dWhXTnBNQ0Y2cXdWYU94ZjdMcjBjUTJWNGNGS2M2S2g5bTZ0QkxvWTFsU3V4aHRpd25Yb1BLWWY2dVA3UXVIQkJqUmx5VklQZS8rcDU4cmU5WWUwVEQwWmc2K2RPc2Vab1R5a2lEQVV0RmVLaDJFUkNHQ1JENFZjTnlEOWZWNmVBaGRsYmYiLCJtYWMiOiJlMzA0NGUwY2EzZTBlZGViNGE5NTM3N2RjN2IzNzhiZTEzZWI5YjkwMzc2NmYyZDBiMzJmNTA5Zjg1MWZjOGUxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866093647\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1141884518 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141884518\", {\"maxDepth\":0})</script>\n"}}