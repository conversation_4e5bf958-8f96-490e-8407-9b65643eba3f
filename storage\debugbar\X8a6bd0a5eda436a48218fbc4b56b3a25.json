{"__meta": {"id": "X8a6bd0a5eda436a48218fbc4b56b3a25", "datetime": "2025-06-30 17:58:38", "utime": **********.893551, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.427759, "end": **********.893563, "duration": 0.4658041000366211, "duration_str": "466ms", "measures": [{"label": "Booting", "start": **********.427759, "relative_start": 0, "end": **********.804014, "relative_end": **********.804014, "duration": 0.3762550354003906, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.804023, "relative_start": 0.3762640953063965, "end": **********.893566, "relative_end": 2.86102294921875e-06, "duration": 0.08954286575317383, "duration_str": "89.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.019950000000000002, "accumulated_duration_str": "19.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.83662, "duration": 0.015210000000000001, "duration_str": "15.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.241}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.859853, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.241, "width_percent": 1.704}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.873095, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.945, "width_percent": 1.955}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8748732, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.9, "width_percent": 1.353}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.879189, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 81.253, "width_percent": 11.529}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.885033, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.782, "width_percent": 7.218}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-635133770 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635133770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878212, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-24435345 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-24435345\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1531134045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531134045\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1391845219 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391845219\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1074317670 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhkZW8yenk4a2Nyd05pZVE1Rkg0aXc9PSIsInZhbHVlIjoiTFRKalB0ODFXZHYvbTExelZZcXNmbVQyV0hsbFNuZ2UvbjZlL1I0SWF2SmpxeHpBSXRLWjlxZDJCOWRhaUxHV0hMdnFrVHp6dXc0emYxQktuVi9ndVNqeGF4cUZnOGVMTXlaWEhxQVd3R0FIZ3dSVjZ3SkpRU291QlJDT0FJcGhPZGVrTTM5T0NRSkJLY3pEUkgxT1lmWTI5bW5EcCtFVnJFeGU4RTV6ZUYySE9LWENtQlpCb29aaHlpalJ2TW1VOWNNODZpYksyWlF6bnFFZ0kyMU5SNnpTZGI4Tm9KTWVtZDJGbHpBY0l6MFpkL0JhQ1hKdktiWXR0ZUhRYVJoNGp1Y29wdEtlTFlFc3AwbzVtMXg2YkhHU2hyTlhGUVY3UTVyc3lhK1NsZE1EZmtMTGxRQTFjcWdhOGFaSUtCWjZyK1pNVFE3MnZhSHhtU09wSmNpR0s0OTk5QWRZV0lyeURoZXJxelFKWE90cnJiUjRqbmw2OFlwNG0wbjRtY1lCRkh0b2FNQTgwVlhnaUc4OTIzMFk5Ynh1RnJNWXg2a1U0MVhBcEZUTm9yNnU0NFhDSUdmYXRNb2tvTytWRUNHVUJuZXJ4QjEyc0Y3dGppTDJJTnVLSFBXNU5YK1o2anN0L2lHbkp5WnU2cnBHTmcyNUtXZjBTcVFiLzAwQ1FDRW4iLCJtYWMiOiI3OGFjOWYxZDVmMWVmZmY2NTE5YjViMDU5OGU0MGY4NGYyY2UyNmE5MDRkMGY3OGIzMDEzZDcyYTUyZjRiYWI5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIrMXNLRC9tait2eERzeEVUVEVTQVE9PSIsInZhbHVlIjoibmFRNUp4ZkpwOVV6MldYUW8xeFBOdjFvby9mQmZuSXowYTdFcnRVMWNDQWZlVEpCdDVrUFFHZkE2YXVJZlF1K1ZhVWErSm51a0VDQ1l1dWlrc3pOdDN6WEErb2p5VTF5N2RCT2JxYlA5cG1XTFZxcnZhNkNvYVFtbEp5aFJWRnJjTlV0WklqSXNJYWFMdnFBU1dhaVl5MmxvanozWC93VDMyTU1TUXZFZ3h6U1BvRkFsSGYvbUtzcnJiYkRNLzYwUis4WG56R1kydmY3T2dVMFAvMGF4Ky9HYWFEQWRsZWhWWkhoMjRDMlEvdnc4cGwvTXNLbHpObjZqckpYTE00RzlIUUJVSmtjWklwZVo5R2dnVHZiamRwR0hXMmV0ekV5SUhIVkkzV2pkMlc5YnZYd3RHM1dKcXB0Y3FCQXBFTmw1RXZEbW16eGVMRTkvMDhyLzF2dlhTemJUZVBuTkNqbzNqVnNSMGIrV0ZsME05MUlHWDBoME5pQ0NFRzBDRzVucVBTOGdQYnZnc1c4UW1GVzFxeTMwNVJFdVp5cGF6S1kwdVNPeVJHdDlMT0ZRRldWUVlJdzJtSlI1dWJjSVBWeW9GVTEvaWsyKzFTck9BTU9HdTB0VFBZZGI3MHlqajltbDJ3VlBDajRjVGdRcExaM3RKT0VhK3lTTWcxTzZuNGgiLCJtYWMiOiI2YmE5OTUzMTQ0ZGM0NmQyMzljOTE1OWFiZWY0ZjI4YTEyOGNmOThkNDAwYWM4NGM3ZDIwOTQ0NjM1ZDI1ZDA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074317670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1865967437 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865967437\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-943776739 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:58:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNiUDhEbmlzSE5lOEk2M0xLNE0zUWc9PSIsInZhbHVlIjoiZ0pHSnVBUFNrMGdGY0x3MXZnSkJRR2VJNmlsN2ZmNlVjZXpzSjM1Vm5tR1FiRklnR3NKMDZmdUhoTUx0Snlxb3VwR0xsTXM2ek5uMTNqckMyS29LeUwyeWpOR1RUdkJabHpYVnhGYUtqT0g4eVllRDNvRmJtZ1pzYThxZFNicjZPSWxtampjcnRwc2tKSTV3aUsybWdBU0NXckIzdnI1UGpxLzA5bWs0T1VzckF1b3NDYUo2aHdOdEtxTmdXVlV1NDVzZjlGQmpUcjFaa25LMDRuRUk2K3V1YUQ1eEV5Tk5BRFFSZmZiNXJzblVWRzZWNlhncVdTSWFNU0VBUnpvUE9pcERLMCtGNnp4UThSU2duVUZOTDdBRHp6cTcrWmlWSkEwR2FyMm1BMi9BV0FJQm9rQ3U5cnJwS0I1Rm8zYWk0OHZVMndqclhtSXpBNms2YzBBM0w3amJGZDRnaGtVS1pLd1haNUd0ZmkvUktKalBOci9FanhnM1dOaVE1NFVrWjErUk9LMnNlN0NrU21Kbjdzb0JIa29hV2U5MzlaazY0WElyRWpoVjN6akFZTVoxQmFUSjNqOGg3d2J5K21iZ2MrNTZ6MEU5dHk5U1kvVThjQnNBSTNFaXlMdjRTUjNnUlAvTkZmeCtRZ2dwdnRIYXNQWlcvVjdVNDIvcnhNaVgiLCJtYWMiOiIxNGExNjE3MTFkYTZiYzc2ZGFmMTVmM2EzNmM1YmM5YWMyNTRiMTAyMmJmNmYwZWZhZWI1ZmViMzgwMjkxYzVmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJpUUt4VXNuUjQ4VitocUFud0QyN3c9PSIsInZhbHVlIjoiQ0VBWGJSVFNYeGJuM01VMzBvMHp1Y3ptbG1KeDVmWnpxR1RNbmxWa2duMXh5RXFBaGFobmpiMk80a2pNd2I5cmNYaGdXMEpnNE1oZXZBNndhTXcydWU5SU5pcE5jMHZEaGNEUHBPSkJDYlVVMXp4SDhjVHFOTkRiZFZ6bFQwQmNMa2d5WDdjWk1kNXpTVUZockxtM3Fobm9TamNqYzdpT3RrdnlhbHJ5bWh1eUo5Q0JYMnpLUktGNy8zUDlJNnByT1JjdHNNVFZ4YmlzNVE2c0VnbDUxb2IzcDRRRXRSeTQyaGFvSFJMZEdDVmt4czEyazRlLytwekFBRVBnY0Z0QXl4eXdVRENWcGtwMVRNYjU2NjM4WHczOWEzQzhpZVYyUlRaNGFjWmpWaUE3M21lZU8zUEhFa1QzVGNwUTlmbTdPZytRRStuTzhPRDRoeFNDVGl2NmZ3aGRlOUc3R0FaUTlhZDl0cEJ5TmN5aVpxSE40akxaeFlLOE8vZHNrREJ2U3pBT2VGS09xUkVHWjNtQXhLS1FvdVo5ZGtRdjZNRUJJMjRlZWtJQWJxQXgrR08wQWgwaGhKZmhNTXRjVmVwYW5kM0ZlTzdxMW4rNkZrRXRqSi9aRFQxU1d1R24ybWlycEI0bE5JQXdoV1R6Nmh6MFJSa0RpRDhpQVZjL2UvNWMiLCJtYWMiOiJiNWNlYzExNWFjY2ZkNTA4ZjVlNzUzMTlmZTE4NmYzMzVkNTUzYWU2OGFlZDA1OWViNjFhZmRiM2I1MGY0ZGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNiUDhEbmlzSE5lOEk2M0xLNE0zUWc9PSIsInZhbHVlIjoiZ0pHSnVBUFNrMGdGY0x3MXZnSkJRR2VJNmlsN2ZmNlVjZXpzSjM1Vm5tR1FiRklnR3NKMDZmdUhoTUx0Snlxb3VwR0xsTXM2ek5uMTNqckMyS29LeUwyeWpOR1RUdkJabHpYVnhGYUtqT0g4eVllRDNvRmJtZ1pzYThxZFNicjZPSWxtampjcnRwc2tKSTV3aUsybWdBU0NXckIzdnI1UGpxLzA5bWs0T1VzckF1b3NDYUo2aHdOdEtxTmdXVlV1NDVzZjlGQmpUcjFaa25LMDRuRUk2K3V1YUQ1eEV5Tk5BRFFSZmZiNXJzblVWRzZWNlhncVdTSWFNU0VBUnpvUE9pcERLMCtGNnp4UThSU2duVUZOTDdBRHp6cTcrWmlWSkEwR2FyMm1BMi9BV0FJQm9rQ3U5cnJwS0I1Rm8zYWk0OHZVMndqclhtSXpBNms2YzBBM0w3amJGZDRnaGtVS1pLd1haNUd0ZmkvUktKalBOci9FanhnM1dOaVE1NFVrWjErUk9LMnNlN0NrU21Kbjdzb0JIa29hV2U5MzlaazY0WElyRWpoVjN6akFZTVoxQmFUSjNqOGg3d2J5K21iZ2MrNTZ6MEU5dHk5U1kvVThjQnNBSTNFaXlMdjRTUjNnUlAvTkZmeCtRZ2dwdnRIYXNQWlcvVjdVNDIvcnhNaVgiLCJtYWMiOiIxNGExNjE3MTFkYTZiYzc2ZGFmMTVmM2EzNmM1YmM5YWMyNTRiMTAyMmJmNmYwZWZhZWI1ZmViMzgwMjkxYzVmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJpUUt4VXNuUjQ4VitocUFud0QyN3c9PSIsInZhbHVlIjoiQ0VBWGJSVFNYeGJuM01VMzBvMHp1Y3ptbG1KeDVmWnpxR1RNbmxWa2duMXh5RXFBaGFobmpiMk80a2pNd2I5cmNYaGdXMEpnNE1oZXZBNndhTXcydWU5SU5pcE5jMHZEaGNEUHBPSkJDYlVVMXp4SDhjVHFOTkRiZFZ6bFQwQmNMa2d5WDdjWk1kNXpTVUZockxtM3Fobm9TamNqYzdpT3RrdnlhbHJ5bWh1eUo5Q0JYMnpLUktGNy8zUDlJNnByT1JjdHNNVFZ4YmlzNVE2c0VnbDUxb2IzcDRRRXRSeTQyaGFvSFJMZEdDVmt4czEyazRlLytwekFBRVBnY0Z0QXl4eXdVRENWcGtwMVRNYjU2NjM4WHczOWEzQzhpZVYyUlRaNGFjWmpWaUE3M21lZU8zUEhFa1QzVGNwUTlmbTdPZytRRStuTzhPRDRoeFNDVGl2NmZ3aGRlOUc3R0FaUTlhZDl0cEJ5TmN5aVpxSE40akxaeFlLOE8vZHNrREJ2U3pBT2VGS09xUkVHWjNtQXhLS1FvdVo5ZGtRdjZNRUJJMjRlZWtJQWJxQXgrR08wQWgwaGhKZmhNTXRjVmVwYW5kM0ZlTzdxMW4rNkZrRXRqSi9aRFQxU1d1R24ybWlycEI0bE5JQXdoV1R6Nmh6MFJSa0RpRDhpQVZjL2UvNWMiLCJtYWMiOiJiNWNlYzExNWFjY2ZkNTA4ZjVlNzUzMTlmZTE4NmYzMzVkNTUzYWU2OGFlZDA1OWViNjFhZmRiM2I1MGY0ZGM5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943776739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1352902895 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352902895\", {\"maxDepth\":0})</script>\n"}}