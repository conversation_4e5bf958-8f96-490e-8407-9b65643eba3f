{"__meta": {"id": "Xc3f38d98808cce392bda1d4dbd67a1f4", "datetime": "2025-06-30 18:08:19", "utime": **********.616199, "method": "GET", "uri": "/search-products?search=&cat_id=25&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:08:19] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.597702, "xdebug_link": null, "collector": "log"}, {"message": "[18:08:19] LOG.info: ✅ انتهاء searchProducts - الوقت: 13.22 مللي ثانية - المنتجات: 2", "message_html": null, "is_string": false, "label": "info", "time": **********.610686, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.135577, "end": **********.616217, "duration": 0.4806399345397949, "duration_str": "481ms", "measures": [{"label": "Booting", "start": **********.135577, "relative_start": 0, "end": **********.531856, "relative_end": **********.531856, "duration": 0.39627909660339355, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.531865, "relative_start": 0.3962879180908203, "end": **********.616219, "relative_end": 2.1457672119140625e-06, "duration": 0.08435416221618652, "duration_str": "84.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47762944, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.008740000000000001, "accumulated_duration_str": "8.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.566291, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 19.336}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.577044, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 19.336, "width_percent": 5.492}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5916169, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 24.828, "width_percent": 4.233}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.593532, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 29.062, "width_percent": 3.432}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`category_id` = '25' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5985048, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 32.494, "width_percent": 59.153}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.605708, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.648, "width_percent": 4.691}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.608359, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 96.339, "width_percent": 3.661}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-439410149 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439410149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597057, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-2001786092 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2001786092\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-976584765 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976584765\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-751369113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-751369113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1098417626 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdlQzUyWUtxZXZsRGZoVUVkS05OdWc9PSIsInZhbHVlIjoiTTNEM0hPUkFTcEtvQXhhSGNZOG5jYXFtbWh2QUt6ZjRDSWM2dDZxMDl5NldBSGJVYnY2bDFUV3JBUHJ3aVdNeUhTZmFNelBHMlVlazhIbmdscm5vbUcyeUtKdkc5RTNaUmhWVU1nZUN0MlNuMXJHZHJhQzFVR3p3SWU4a1B2WitDSVJyV1N4YkhpNVFLM2ZTNTM0NDNpVm9ERU15aGVPTlNqUkZRQnFXMitqdmpoK0g0V0JQTVRQdVpOVW5MSUNScVBhRjNKOGkwMXl6UUtlbnVmTmMwQ05Edk5TSTl5eTFrcGdFZ0VkNUFiY0lBQXo4UWFJbWQ3RkR0MEIzR2U0YkhQTTVCL2JQelJUM3VkYUZ1ay84cVZLZVMrWUJSRWpCbnF4OW13c0JQOGw4d010Ry9DTFFHK3Z0OFNnMEtzVTVadlFoaCtjR2lzbmluMlFMTmFCUU05dHJHU0RTVnNjdFhKT2VwRzdhYnlydkxvOW9sTHRXTU1QUTlqc2xnME1oOVc4NGRIZ3ZFWHpGZnpRUHNEaHUxdGN0Y2ZqSDZ3UnBySXNndVQ0TG5ObGJzbHJhekpudnU0VWp5SHptbldRc0NwSENwN1NiY1M3bXEvTGxrWGZYRkhJa2JSSFFYZDBuWWppcjl1UnV5UG51bGVIRzkvZlhBUjJ6VlBUVDQwRTUiLCJtYWMiOiIwOGRjOTYyMzQ4ODA2MGZhMDdhNzA0Y2Q4ODQwYWI3NjZmZDE0YjFhOTBmNDNmZDFiNWU2NTQyMjExOTRkZjIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtYdmhFK0NuQlVYY2IrdjFvODFhUEE9PSIsInZhbHVlIjoibVNtblZmbG9XNVpibGFBL0FKMXNyeVJaZXBNd2ZWVStkMk5URU1BQ2lmREVPZEs4eVlwT05WVFpYNmo4L0NqSHErQTZtenlZYllkbkd0NGd4NGM1MHVBOXJrMnZ5bk9Hb05CcXN3MWdhaHBCT3BvNWU0cDYvTEQybUp1cVpPR2ZGTldoZ1JsdTUxWjhyckZmSEdURjhyaEllLzZZNkNMNVZvQmZmbjk0OFlETXBYREpDYU9FclNUQ3dvSlF4eXozUklVMitmRktjMnZ2STJwK1JsdkN6R1dFM1hXRWpvUmlFR2dtK0g0YlhnZ0dBcGZFeVBOTDJCRmJ5QklFaWMvRTJsMmlzSlFTVWJ3SExneFJrYmtaUlRVZFI3TS9ZQVR5RzhrU0ttYzJPK3hQVTBsdnl0OFhzYVR5L2Q0UjFZSSs2V200NEN2MStGWVFCU1A2U0VMdE9kU1p2bEdFaWJQSkVuRDlhU0pqM2dPbEpRMHNTN05zeDRqdWpMODVBOFNBUC8xc2pOazlTNHdOU1ZzZEZjMnF3amZIdHBZWkc2K2pBbjVCcTVDdU5wcHEwL1pvWGZUTjA2aG83Wm8wQkI2dy84V2xpdUpEUnhHUWVrSy9WWGpKdFZmWDlpZC9zRGYrZ0F3U1lxUjFidFdPOG93TFlRdHVDaGhSZTZnWDVTclUiLCJtYWMiOiI5ZTk4YzRmNjhlNTFkMDY5ZTJlMDE2MDFiZTJiNzBiMTA4YjU4MDJhMzIyYWIxMTE3Nzg3NDBlNGFkOTMxNGEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098417626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-843161880 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843161880\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1151379925 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:08:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktYTnVKVE8yMkNuMHloOW9xRWRXaUE9PSIsInZhbHVlIjoiQXlMQ1Q4NksxQVkwbmhoZnQ3WFBpUlZPL0wrTVlMTXN4TjhQaWFQV2d6RDdYT2NHamh1aDFIekhpaDYvTjlnRGtENnA5MWFjRkwxbUFCeE1IN0ZkZFJ2K3RZR1puRU85RlJmaUwwSkl1MVNXV0FlalZvS3ZYNTdBN1gvUWpxR0JTZnkxbndnMityNHdHL0VhKzNyL1RGVlJmZzFRQXlvejlXRGZQMUpjemkrRDJmUjN5bWRoS3VycGNnOFZkYjgzOW8rZllsaFFKRDBFVTFLK2FWRnZvdXhJcTI2VjdKQ2xpREJxM3kxZUVnUWRSeklpRzB1VHRkNCtFc1M4NFVsdEdsTUgrWDZLUXZBS25YNTNrc05ZbnFENHhZVlZmMU1VU09TZ282dm5STjdhU0FkeW5Bc3E4QzliNUZ5OWJZUHo5V2F1eTV4aERxUDBNQjQzam5mZ2VXNlUvcFhnRzVZTUY1Z3FaWGcySFZWd1BkU2YreVpZMzVEZU0wMjNSUVI1Z2tTNW1NL0FGaE05QytZTU43MEpqdUlVNnVtUEFJSkJ4S2E1MHhHVjlCaXhrdDUrczZwZUNGQ3RLb1R4VDd0SE14U0dqR3MvZ3poOVNPbTFuMWtKbnh6R1N1akNKcXdIaDV0b2VkcjF1ek8wUUF1aGkra2M2TjJRVE9lUFVYSVciLCJtYWMiOiI1OGRlZjNkOTJjNzgzY2YyM2Y3NmE0M2UzYTIzYjZmNWUyYzRmNWQ4NDNiYTk0ODk0YmU4YWFkNDBlOTE3Yjg2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZOTC9ua3ZDOHBKVlZTOVdEcEJLTXc9PSIsInZhbHVlIjoiQk5nTGkzcndLb3FHTWVVM2VQQ2tadWt3MmhEUnRnbHhaSUVLVmI3RkpUdmh1cUhEcWlHMFJvUU9kcmVaTGovWWxKejBQTDh6c3JGR05rU3dWcXNGQnN5NG1MbTN3S0EyeElSQklnVWxDdmZrMERub0pDbXBiODlieW1pbVlqZGtBdllIVDQyQ05HRUYvZEVFaXRhSnY5MytKczJoZit1Wmt3ME9UVWRibHNJb3lBRzl1aGk0dTlZSFhJLy9pT1QxRnVZOU4xNkVJbnBOTHdSM3U5UHc0RnoreGtBaExSRno1TlZJcmpoMFJ0QmhkSGQ5d05DaWlLd2dxQU0vVmJWWmRnMTVKdmo1ZFNySXNuQ3pKaXVLRG9zeTBMYlNmZy92RzRBZmsxL1VTaUhKWGZDSHF2NHRFUzJjWDhsdE9Ma2dZVmZyRVkwT1F2TGYvM20zK2NTb0hOdXpnRllvZGxZWWZTU1VkOVRHaklYVjZpOXNpY0ZPZTFBSWthUWpoUzAva2tPQTdJSTAyUWJUcHJiQU10UlppZ3MrL0V6UndqUDkzWHo1TG1kaWZPTkJEVGwyTnhCL2c3TXdzdldSVWJYcDFWNWVsbm9XaGtkbS9BWFRKYm9ZdmdyR2VDVU1OSmlIMmdmU3VwWXhWQTBzZ3NKUjY4RXNhTlp2RlFsOHRVeW4iLCJtYWMiOiJhZTZiM2Q5NTg4ZTAzYjBkNTBmODhmMGU5MGViN2Q1OGFkMWZkNzI5NGFjZjdhNTczYjBjMzdhMDZjOGM1NGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktYTnVKVE8yMkNuMHloOW9xRWRXaUE9PSIsInZhbHVlIjoiQXlMQ1Q4NksxQVkwbmhoZnQ3WFBpUlZPL0wrTVlMTXN4TjhQaWFQV2d6RDdYT2NHamh1aDFIekhpaDYvTjlnRGtENnA5MWFjRkwxbUFCeE1IN0ZkZFJ2K3RZR1puRU85RlJmaUwwSkl1MVNXV0FlalZvS3ZYNTdBN1gvUWpxR0JTZnkxbndnMityNHdHL0VhKzNyL1RGVlJmZzFRQXlvejlXRGZQMUpjemkrRDJmUjN5bWRoS3VycGNnOFZkYjgzOW8rZllsaFFKRDBFVTFLK2FWRnZvdXhJcTI2VjdKQ2xpREJxM3kxZUVnUWRSeklpRzB1VHRkNCtFc1M4NFVsdEdsTUgrWDZLUXZBS25YNTNrc05ZbnFENHhZVlZmMU1VU09TZ282dm5STjdhU0FkeW5Bc3E4QzliNUZ5OWJZUHo5V2F1eTV4aERxUDBNQjQzam5mZ2VXNlUvcFhnRzVZTUY1Z3FaWGcySFZWd1BkU2YreVpZMzVEZU0wMjNSUVI1Z2tTNW1NL0FGaE05QytZTU43MEpqdUlVNnVtUEFJSkJ4S2E1MHhHVjlCaXhrdDUrczZwZUNGQ3RLb1R4VDd0SE14U0dqR3MvZ3poOVNPbTFuMWtKbnh6R1N1akNKcXdIaDV0b2VkcjF1ek8wUUF1aGkra2M2TjJRVE9lUFVYSVciLCJtYWMiOiI1OGRlZjNkOTJjNzgzY2YyM2Y3NmE0M2UzYTIzYjZmNWUyYzRmNWQ4NDNiYTk0ODk0YmU4YWFkNDBlOTE3Yjg2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZOTC9ua3ZDOHBKVlZTOVdEcEJLTXc9PSIsInZhbHVlIjoiQk5nTGkzcndLb3FHTWVVM2VQQ2tadWt3MmhEUnRnbHhaSUVLVmI3RkpUdmh1cUhEcWlHMFJvUU9kcmVaTGovWWxKejBQTDh6c3JGR05rU3dWcXNGQnN5NG1MbTN3S0EyeElSQklnVWxDdmZrMERub0pDbXBiODlieW1pbVlqZGtBdllIVDQyQ05HRUYvZEVFaXRhSnY5MytKczJoZit1Wmt3ME9UVWRibHNJb3lBRzl1aGk0dTlZSFhJLy9pT1QxRnVZOU4xNkVJbnBOTHdSM3U5UHc0RnoreGtBaExSRno1TlZJcmpoMFJ0QmhkSGQ5d05DaWlLd2dxQU0vVmJWWmRnMTVKdmo1ZFNySXNuQ3pKaXVLRG9zeTBMYlNmZy92RzRBZmsxL1VTaUhKWGZDSHF2NHRFUzJjWDhsdE9Ma2dZVmZyRVkwT1F2TGYvM20zK2NTb0hOdXpnRllvZGxZWWZTU1VkOVRHaklYVjZpOXNpY0ZPZTFBSWthUWpoUzAva2tPQTdJSTAyUWJUcHJiQU10UlppZ3MrL0V6UndqUDkzWHo1TG1kaWZPTkJEVGwyTnhCL2c3TXdzdldSVWJYcDFWNWVsbm9XaGtkbS9BWFRKYm9ZdmdyR2VDVU1OSmlIMmdmU3VwWXhWQTBzZ3NKUjY4RXNhTlp2RlFsOHRVeW4iLCJtYWMiOiJhZTZiM2Q5NTg4ZTAzYjBkNTBmODhmMGU5MGViN2Q1OGFkMWZkNzI5NGFjZjdhNTczYjBjMzdhMDZjOGM1NGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151379925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1732130891 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732130891\", {\"maxDepth\":0})</script>\n"}}