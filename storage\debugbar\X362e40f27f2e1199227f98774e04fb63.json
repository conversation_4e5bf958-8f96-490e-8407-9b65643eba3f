{"__meta": {"id": "X362e40f27f2e1199227f98774e04fb63", "datetime": "2025-06-30 18:49:56", "utime": **********.239168, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309395.807523, "end": **********.23918, "duration": 0.431657075881958, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1751309395.807523, "relative_start": 0, "end": **********.161416, "relative_end": **********.161416, "duration": 0.3538930416107178, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.161425, "relative_start": 0.35390210151672363, "end": **********.239182, "relative_end": 1.9073486328125e-06, "duration": 0.07775688171386719, "duration_str": "77.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48080352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00842, "accumulated_duration_str": "8.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.193701, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 39.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.205456, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 39.667, "width_percent": 5.819}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2196732, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 45.487, "width_percent": 5.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.221603, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 51.306, "width_percent": 3.919}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.226201, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 55.226, "width_percent": 29.216}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2310588, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 84.442, "width_percent": 15.558}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-11992209 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11992209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225269, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2037757039 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2037757039\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-289546735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-289546735\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-576496923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-576496923\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309393964%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFqWnpVeHlUODl4dTRvWFRqckgydUE9PSIsInZhbHVlIjoiTWRRZUdObDM2YmxmTVEzYllBOEZnWE5YNjFab2I0MDgrRGFUS2t1cW12K0k0bmhYajdhV2c0L3grYVdPbjU4ZVJqUG9VMVVRdTN5QVkvTjkva0pQcUZrdUdacytrUzJCVENxak9UZnQvNUx3bkxvTmFIQ01SQ3NKbHhFcjZMbkFtcWwvMTFkUGJMUmwyNkFrZlcvcXdnSG1aWUU1RUdiSG9Kb3VSbFVFbndZY0ZVOUdlSkVqbTFjaVF0Q2E0RzNZM0dmR0xFQkdVMDg0TkZ4cElsTGJsanA4LytxWC92Qk9GUUh4T0FiTGIreHNmU1RCKzVmcUhaL1V4N0lqeXdLdmJmWFJGNUUrQzRzWTh1N1hsTjlRNEpWN3VkRmM4bm5LclBTQTFkVjlSeFQwZGxRejhodjc1aDlZZmJLbDBuL3g2cXhRYUlXdHdPcVQ0TVVHWWRvVW5wM3FzYkplL3ZyRE15R1NlL1BzSkNnZWRIaWFHQm5mc0FkOFhWd1U2djFRUUpjaitVandScVgrSU9iaE9IZTFBV2VDL2pES2VKSm1oWDQ4R3RPOGMyWmJxRXBXU1FpSzVkSUdyblV3WW93dDBJSUZXZnJZUWVSYkZ3L3RaSCtSNHFGc3RCQjhTK1VwYjBOeTMxTlRPckJpZlZjaTdvb2NxLzdLdk1JTXI1NVAiLCJtYWMiOiI2YzkxMGVlYjk0NmI3YTZiNDA0ZjkxOGNkYzkxNGNmOGFmMWU1YmRjN2NhMzFkODZiZjg4NjMzNmExYTYyODUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imlna3lTazQ5TGlSV2RRUzhpS1d5blE9PSIsInZhbHVlIjoiQ3IxbFNMdExnSm1nRkFNbFRGRTVoUVZBV0gzMmJ4V3pXeEVhNGNjK2NtSVJpd0RiU05jS1hsUmFFczhBRStqS0dhcEt1Rmt4eHdMUWZ1VGJ2QUk3MGFDTEJWQ1F1anY3ZWNxSnhjV3VaRUtua1VYejVRempMeEVUMFdmdCtOTDlxbnJaN2pYOHNCMko0T3lkeS9FTWhudGljVWNkTFdkNEZZZEdrVE5lNHBpdER4Ym55RlJkby9yRzlobGRVUzFLb0dOSUU0R3NZZTVIeTdkSllPMWFnbDBwQWxzdHNIRk5SQ2lNVjBBbndkVGhpRWpxdjlkOUcvYzFHc1pjS085SndqbDBna2lHTFdvdS9ZUHc0ZkRBaUt4elg4UFdHNFJ4MkdvTG9HZVVRdDl2RFljcWFwNGd4cGN4YVdvZXBPTjNUaGF6bUp4QnZoS2hCZUdjQzVtR0FIOWtLMCtiRmh1ZzFvQTd1dmtlUXdXTUdhNUg5N2M5MHVYVmp2T3JhdjNwZHdzcitHcVd6Z1lwak9BbjNpU2U5ZjNUaDI0MHIxdnR6bjBDZXpBc2pjT1ZiUlVKNTFnVFpHY1JmTjBySERNZ0R2WWlNV091MlRBNjgybEtxb3M2OXJNK3B4SFdMUmp0OXlTR08xZVpRQUJpaG85bzlOYXN5MW13a1JObHpERnYiLCJtYWMiOiI0YzZkYzM5OGEyYjJjN2Q4YzE4MjFjN2FjMDY0MjIyY2YzYWIxYTllZDcyNGY1MGQyYzM1Mzc3ZjQzZDQxY2E5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1870195777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870195777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktZcHpIS1NTajBIQjUrdkJFNHltM3c9PSIsInZhbHVlIjoiZmtyUERna1VCM21zQ2xHdXJyYTFEVHVkMWpST2FFZE9HQWgxWWF3ZFJuYlNrQXpEanhocHVKZ2FHN2JFUmhBeldickJJUWs2OUF6TUVNUzRRTnhFTzVQWDRBVkU3VjlCbFRtMURnaitsNy9SR2hIQXZBdXRHY1IrNWJCUkhlcXN6VmZNZ2pvYmZKVW1wY01ML2czc0J6dWp4OVhLakRQOFBMSEQyeE43Zm1YMm1tZXJjdk5KUmZVRWRPcHNHenJvTjZrTUFpeElLVHk4dFBDZWZoTHlxM1p5K0FJNGJ3dDBFdGZGN3lUeTBUVGxoaFFLbDY1VVJWWUgwbDhsLzYvYncrTXpKbjlMcG15Q00wVTFFZzlCK1dkQ2FSUU5Jdk81d05FMHpFUUQxLy9mUzdPcGtxUFBoTEFPbEUvaEJYb05JYk5nTUFmN2toSllyVnFFNEkwL0FpQkVlRlhXN0ppMC9yMjhQQmJZK05TaEpYT3A2WVVFRndIZHdhL3Y0cHJqcFF2dmcvcGI3NlRRSElQcEU2VkhpSGI4a3BMSHI4QzJDNWJld1RwVXUxbElvczBuMXFVUlBoeFNzUHk5d050NnAxUXdYN1BjS1QxdU9jbWFHdHhwbjBmbVZhc2ZmbnNUZHRhaXZuTlZaakk1eHF3RXVJRDdlMnNOOEQzWU5IUFkiLCJtYWMiOiJhNGQyNjc2NTYxOTA5ZmVjYzg4Nzc2MTcxYTM2Nzk2ZTdjNTUxY2Q0MmNjMDE0YzNmNDNkOGVlMThlZjY5NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxWTGlTSkNwMzQzYThxdFZ4aW44SEE9PSIsInZhbHVlIjoiZHlvOWpMdHRldW9COHF0bXh4Vkk3OHJvWkZlNDk3STMxbUhtMklNU1M3UDBJbk9JdndvV0xPb05tcWc4NVkvbjRUYkpzZDFjdENBNVFYbEhjM0lwTDNXcGowckZUZXY3SzRXQTNoU0pCSEdiSEZmWENYWXNSeHQvbUxhVkxGTjVjWUwvRmgxZ0dqeXhVeGR3VHBaOGQrdHQwNS9DRUJYSDAyc0J1MmthSEFkT3JLM3NvYW12VGZORk9YNXFSM3NraGN6ZkV2YVR3b0RIdmxvc2JndWtZS1VkNFo2Y2tKbEhnSU9ZaEs5SFgzdzN2VkJaZFF4RnE0dm9Lc0lDOHU0S1lNVDNvNzRmMjd3WVpjOTN2VTZtOXB2VUJnSkdMM1d3RzJCWUxzVTJ0bklTNUNJM0tmL2pkbGNUTkRrTzhQMzFxcCtkd3QrOVA1MEIvNXJtUWcrbm9zWjNXb3NEVDVGNHlMTXpQbDNNL2RYV1NYRk9zSmFnU1Nia0laeWFkM1ZRR1FSRDhzOWcvdjJOKzVKa1VKeXFoMThrdk8xam1NMkExYVBOV0t4NkZhQ2NaZnpFSkZnaFY1QlB3UDFML3Ryb2doZlYzMjRvcXBYVGp6Q1hYVklrNCtObVBwS1hYZC9tRVhYRDJacnRBemw2MkZydGF2QU8rSER6aEhOanQ4dEQiLCJtYWMiOiI4NGJiNTU3NTZmYmM0MDc3NjEyYzlmMjhiN2I2ZDhhY2I2YWE2Y2JkMDJmNDNmYmZhYzQ5MzNmYzNjMTk4MjYwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktZcHpIS1NTajBIQjUrdkJFNHltM3c9PSIsInZhbHVlIjoiZmtyUERna1VCM21zQ2xHdXJyYTFEVHVkMWpST2FFZE9HQWgxWWF3ZFJuYlNrQXpEanhocHVKZ2FHN2JFUmhBeldickJJUWs2OUF6TUVNUzRRTnhFTzVQWDRBVkU3VjlCbFRtMURnaitsNy9SR2hIQXZBdXRHY1IrNWJCUkhlcXN6VmZNZ2pvYmZKVW1wY01ML2czc0J6dWp4OVhLakRQOFBMSEQyeE43Zm1YMm1tZXJjdk5KUmZVRWRPcHNHenJvTjZrTUFpeElLVHk4dFBDZWZoTHlxM1p5K0FJNGJ3dDBFdGZGN3lUeTBUVGxoaFFLbDY1VVJWWUgwbDhsLzYvYncrTXpKbjlMcG15Q00wVTFFZzlCK1dkQ2FSUU5Jdk81d05FMHpFUUQxLy9mUzdPcGtxUFBoTEFPbEUvaEJYb05JYk5nTUFmN2toSllyVnFFNEkwL0FpQkVlRlhXN0ppMC9yMjhQQmJZK05TaEpYT3A2WVVFRndIZHdhL3Y0cHJqcFF2dmcvcGI3NlRRSElQcEU2VkhpSGI4a3BMSHI4QzJDNWJld1RwVXUxbElvczBuMXFVUlBoeFNzUHk5d050NnAxUXdYN1BjS1QxdU9jbWFHdHhwbjBmbVZhc2ZmbnNUZHRhaXZuTlZaakk1eHF3RXVJRDdlMnNOOEQzWU5IUFkiLCJtYWMiOiJhNGQyNjc2NTYxOTA5ZmVjYzg4Nzc2MTcxYTM2Nzk2ZTdjNTUxY2Q0MmNjMDE0YzNmNDNkOGVlMThlZjY5NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxWTGlTSkNwMzQzYThxdFZ4aW44SEE9PSIsInZhbHVlIjoiZHlvOWpMdHRldW9COHF0bXh4Vkk3OHJvWkZlNDk3STMxbUhtMklNU1M3UDBJbk9JdndvV0xPb05tcWc4NVkvbjRUYkpzZDFjdENBNVFYbEhjM0lwTDNXcGowckZUZXY3SzRXQTNoU0pCSEdiSEZmWENYWXNSeHQvbUxhVkxGTjVjWUwvRmgxZ0dqeXhVeGR3VHBaOGQrdHQwNS9DRUJYSDAyc0J1MmthSEFkT3JLM3NvYW12VGZORk9YNXFSM3NraGN6ZkV2YVR3b0RIdmxvc2JndWtZS1VkNFo2Y2tKbEhnSU9ZaEs5SFgzdzN2VkJaZFF4RnE0dm9Lc0lDOHU0S1lNVDNvNzRmMjd3WVpjOTN2VTZtOXB2VUJnSkdMM1d3RzJCWUxzVTJ0bklTNUNJM0tmL2pkbGNUTkRrTzhQMzFxcCtkd3QrOVA1MEIvNXJtUWcrbm9zWjNXb3NEVDVGNHlMTXpQbDNNL2RYV1NYRk9zSmFnU1Nia0laeWFkM1ZRR1FSRDhzOWcvdjJOKzVKa1VKeXFoMThrdk8xam1NMkExYVBOV0t4NkZhQ2NaZnpFSkZnaFY1QlB3UDFML3Ryb2doZlYzMjRvcXBYVGp6Q1hYVklrNCtObVBwS1hYZC9tRVhYRDJacnRBemw2MkZydGF2QU8rSER6aEhOanQ4dEQiLCJtYWMiOiI4NGJiNTU3NTZmYmM0MDc3NjEyYzlmMjhiN2I2ZDhhY2I2YWE2Y2JkMDJmNDNmYmZhYzQ5MzNmYzNjMTk4MjYwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1003024082 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003024082\", {\"maxDepth\":0})</script>\n"}}