{"__meta": {"id": "Xa881c0a531227d2d0809b9a93ca62fea", "datetime": "2025-06-30 16:06:01", "utime": **********.656847, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.23004, "end": **********.65686, "duration": 0.****************, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.23004, "relative_start": 0, "end": **********.604237, "relative_end": **********.604237, "duration": 0.*****************, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.604246, "relative_start": 0.****************, "end": **********.656862, "relative_end": 1.9073486328125e-06, "duration": 0.052616119384765625, "duration_str": "52.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00296, "accumulated_duration_str": "2.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.632214, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.432}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.642209, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.432, "width_percent": 14.865}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6496131, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 72.297, "width_percent": 27.703}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C**********091%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdYeFRXK1MxVnZPYWFCMkxBSGZHdmc9PSIsInZhbHVlIjoiOUlrT1BOWkxCenpzekRpcWMvNW1JRVZKVjFoditpYzloZmh5TTZWOFJmVGhERnRidjFwbGxVd2hMbHl5clpCOTdBUElReWFtNHVOY0tPcWhFWGZ0Y3d5VGxNL1ZTWWVpK1dKbWVzWTFFZUhRUGlyYkRlOGo4d1NIMG9WSWQ4cS9iZWpjODRocEpMUGVCekRFM2QwY1pCcDlLaUROLzlDdHV0Z0pKOURFUnZwRGh1Yzl4dzg5ZTR5aXZScE5MTEpEWWR4Z0FPOUdMdkpyZ2dXWFVwRDZMT01qQTZoWFJMOEd2Nnk2dHpwc01yTS9lYjhsTWJINmordjRVRkFiSENycG81V3Z0cm94RUx4NXlHWWxWR3JWSXlmS21aa1dXeU91Y1N0QVl0RHBSVzdEaTJWTmZxTGJ1NE81RG1XYzFjYXB3a0dGVy9BZDlBVWN3eVcvM1NJUkg0Mi90U3pRT2tqSlJxaERRQmtKRThUS01vTVkwc3I1UFhwb0VxbTJpN3FKS3IvVkdqT05hM2JTdUt0V3FpSWs1NFI4K2d4ajBhM1ZvQWZ6NjFTUTNSTjhOcUZMS2t3VDJpZzRvYW54bjdNYmJma3NPdVRIN1B2ZXRDRDdqQXo0K3pENEpTYUR4S0YxS1d0R3AyRmtMY0czRFRGRExwME9PeUJrZWZjOHFEZEYiLCJtYWMiOiJkY2Y2ZjY0ZGZmMzE4ZjM4YmY3MTI4MDVjY2UxZTE5YTEzNTU2NmI1MjFiOTg0NzY0M2M1ZDlmMWZmZmIwZWQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNpVE9IUk16NVhKaDNwUnpTRmJhdFE9PSIsInZhbHVlIjoiWG13c0RDbzh6bXFKbVZKenNhOUVvQVJJU2phanBDTUZwaWNUNzRDcE9pRTJpY25BSEtZM01BeXlQb1NnN0Yvd1l0MDBKQU1MVzd1MWFkNDhHMmxYNkx1NC9ZMytCR0hPbXJwTDRwTEkvalh4bzBlcXVzRDdhWkp1eUY1WkFFeGVlWFlFQkhCQWV4MWt4RklnV1h3SmRwZFFEeWI3dDlCUDJXZTJ5RmZVNFQwV3ZJeVNJZ0VGTVUyMUIzM2hkUk5JRFFPcENuQ2J4NmVqTVdmRTE5ZmVBWmY3VnkwNUFod0ROTC9hbDFzaEVnWGRTc1lNT2NEL2d6VTJzNnFpYURDcHY4RE1BMDBRMGVqeGRUWnp4SVFTQmdDelBMY0hqQS9HNWlQQ1dCUFJueERCZUJSL0dUK1grY0MwWkVCVU0xZGR1Rm1oM05MWXBPeTlBTFplV0RxOFZDN04rbUpwaFd5M3A3a1JlUE9ZY01mZ1htSlk3K2tzRDhQa1k2MVpCUjUvMkxHZWNORjRsNlZSNTI2WXhPM29XRGd6MEtuS3NFbTBqV2xFbkVJN09lZmJaZmlYNmx0WndVWTFaREh5Zk5YL2UwVnNJalhXVVNMSFJxTnlQcGZEYUlMUUxhWDYvcjVaVDhwMTFpN29QWjlNTXB0ZEhHbGdwOThoZHZ4YXcrK3oiLCJtYWMiOiJmNDYwN2JiZjI2ZWRmMjk2N2UzZDViOTdkZDQzZjlkM2IzY2I4Nzg2YWFiOGJlMTUyY2FkZjIzMTc5ZGE3ODQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1106317129 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1106317129\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-232974867 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVHc3VveVVQL3c2a3dETVJwU1BtZGc9PSIsInZhbHVlIjoiN1pVZnBHbXpwcWVIcDBVbVVOSlE1U1dUa2pnVWJHSzBHUlBMRU1manh6ZXRZMU9KVmdWQW1VanRkWHBoRWZxR0svcDVoMGxZOHNZbUJXWHdKbkg5cUJ4SlljWSsvUXRBeDVhRWdoeGhpOHhoNFZzdElLVTdUTHFxOERXYWthSS9VNFJUL0RwTEQxZ0o2YjBXK2NzYVdoM2sxNituSzlRK3FaL0ppcVZXTlFzTVRxSlI5aW9yRkF0MDZqYnMweFNPT3YvUFdXZVYwQ01VRGxvMldxU0hkZGQ5RXVMSkF2Ym9TOWZaL1M5VlJzZitCRWcrbStielZTTWw5eXhLd1RZTlR0MGdPOGJQTXhXYXhUcS9meUxnYnQvSGFqb2I1eGFlaE1IVnU1cG5ISCtva0JVMUV2UTdNT0VxWXRHV3oyeEFSbDJsNkh4Vk8yNGtHbmpzZ2x6NjVzOXlnbVVHZk5CVzEwaXFjV1c4Wlc1R0p5Ky9WblFXZDdYUW9NN25Bd3ZlS085dk5pcTRDV1NBbUthNVVya3JZdEdoMGQzclJrUFk1R3RnVll4UXU1ZHhwYWdLMzBHSHdoY3RnN0lCbmVQNVY1VE9nT1pVM1h2dW5NNUx5N3BJdXozOVdPZ0RLeXFZUmRhVmI3Ri9ka1hPV0s0VkhTQThBbXRLVlJmYmJPTnoiLCJtYWMiOiIzZDIzMjlhNDE1MDdlMWI5Y2E5NGYwNjRhYWVhNTY5ODFhYWY0MDYwMzI4M2JmM2JjOGRiNzAyYTA3NmYwMmE5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxCSVplNGFDLzFVVW5sMDE1dWptUlE9PSIsInZhbHVlIjoib21zcjliRWxCWXdpUytuaXJmUHUrUVZmYllLb1VZUThOTkVHaW1jV3d5WVRlL245aXpHRU1HdWlJeTZyMGFMQjlVNGNLTWZCSFNiSHF3TGMvMnRCclhJbkpRUzUzNHFxdEg0WWFJajZuWHVDQ3NhRkZqVWZrZ3JZQWpMYkxnbVBva2xsQ3ZFUXRiSHVlRmxXdEVsSnVCN3ZDcmF3Tlk0RkMrVTRUN0lmSmJsSFJhVytmUXluR1N3VHVyVXRPaWlYTHUzdkdCWVBSVnAzc0J1eUtvcS9TVDhEZHNIVEZtempzMFUzcVhYMkV5cnh4anY4cVRzWFZlSUVrUk5CYTF2ekRCeE9LSWViQVAvbjBkRmp2cUIwSFpTVGkwRWpxRW50OStEYlpCeVE2VzBGQUJGRnZBbmgraU5QdnpGRlhIWElzajlNOHo3UzUzY2VXU1h1WDNvUVVQYUlKMmVxc2dMOEVISnpZa29IMWpMdThwMHhONTM2T283TFd4V21TRWJMNkI2dGpYeDdwWCtvQVE2Ti9INzR4enlYUDJsalFwVTVhMjlKUlZxQVhIc2NpK3NLT0xZOUdWdkJpcWVOSGJ1QUI5T3ArT3ltMWZpeWlPYWZHMnJtVjNrM2FEeUpKSC9kRmE4RUcvWmhYSTRUdndFMEJWeWpsNytyTTRMN2JOSWYiLCJtYWMiOiJmZjRlYmQwNDBiMzIyN2Y2NzQwYzg2N2YxMDhlODRmYzNmODQ0MTM2NDQxODI0ZmMwNzE1Y2IyY2QzMzU5ZjExIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVHc3VveVVQL3c2a3dETVJwU1BtZGc9PSIsInZhbHVlIjoiN1pVZnBHbXpwcWVIcDBVbVVOSlE1U1dUa2pnVWJHSzBHUlBMRU1manh6ZXRZMU9KVmdWQW1VanRkWHBoRWZxR0svcDVoMGxZOHNZbUJXWHdKbkg5cUJ4SlljWSsvUXRBeDVhRWdoeGhpOHhoNFZzdElLVTdUTHFxOERXYWthSS9VNFJUL0RwTEQxZ0o2YjBXK2NzYVdoM2sxNituSzlRK3FaL0ppcVZXTlFzTVRxSlI5aW9yRkF0MDZqYnMweFNPT3YvUFdXZVYwQ01VRGxvMldxU0hkZGQ5RXVMSkF2Ym9TOWZaL1M5VlJzZitCRWcrbStielZTTWw5eXhLd1RZTlR0MGdPOGJQTXhXYXhUcS9meUxnYnQvSGFqb2I1eGFlaE1IVnU1cG5ISCtva0JVMUV2UTdNT0VxWXRHV3oyeEFSbDJsNkh4Vk8yNGtHbmpzZ2x6NjVzOXlnbVVHZk5CVzEwaXFjV1c4Wlc1R0p5Ky9WblFXZDdYUW9NN25Bd3ZlS085dk5pcTRDV1NBbUthNVVya3JZdEdoMGQzclJrUFk1R3RnVll4UXU1ZHhwYWdLMzBHSHdoY3RnN0lCbmVQNVY1VE9nT1pVM1h2dW5NNUx5N3BJdXozOVdPZ0RLeXFZUmRhVmI3Ri9ka1hPV0s0VkhTQThBbXRLVlJmYmJPTnoiLCJtYWMiOiIzZDIzMjlhNDE1MDdlMWI5Y2E5NGYwNjRhYWVhNTY5ODFhYWY0MDYwMzI4M2JmM2JjOGRiNzAyYTA3NmYwMmE5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxCSVplNGFDLzFVVW5sMDE1dWptUlE9PSIsInZhbHVlIjoib21zcjliRWxCWXdpUytuaXJmUHUrUVZmYllLb1VZUThOTkVHaW1jV3d5WVRlL245aXpHRU1HdWlJeTZyMGFMQjlVNGNLTWZCSFNiSHF3TGMvMnRCclhJbkpRUzUzNHFxdEg0WWFJajZuWHVDQ3NhRkZqVWZrZ3JZQWpMYkxnbVBva2xsQ3ZFUXRiSHVlRmxXdEVsSnVCN3ZDcmF3Tlk0RkMrVTRUN0lmSmJsSFJhVytmUXluR1N3VHVyVXRPaWlYTHUzdkdCWVBSVnAzc0J1eUtvcS9TVDhEZHNIVEZtempzMFUzcVhYMkV5cnh4anY4cVRzWFZlSUVrUk5CYTF2ekRCeE9LSWViQVAvbjBkRmp2cUIwSFpTVGkwRWpxRW50OStEYlpCeVE2VzBGQUJGRnZBbmgraU5QdnpGRlhIWElzajlNOHo3UzUzY2VXU1h1WDNvUVVQYUlKMmVxc2dMOEVISnpZa29IMWpMdThwMHhONTM2T283TFd4V21TRWJMNkI2dGpYeDdwWCtvQVE2Ti9INzR4enlYUDJsalFwVTVhMjlKUlZxQVhIc2NpK3NLT0xZOUdWdkJpcWVOSGJ1QUI5T3ArT3ltMWZpeWlPYWZHMnJtVjNrM2FEeUpKSC9kRmE4RUcvWmhYSTRUdndFMEJWeWpsNytyTTRMN2JOSWYiLCJtYWMiOiJmZjRlYmQwNDBiMzIyN2Y2NzQwYzg2N2YxMDhlODRmYzNmODQ0MTM2NDQxODI0ZmMwNzE1Y2IyY2QzMzU5ZjExIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232974867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2068386190 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068386190\", {\"maxDepth\":0})</script>\n"}}