{"__meta": {"id": "X22f7435acb64aea6798452deba785a96", "datetime": "2025-06-30 16:05:55", "utime": **********.469538, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299554.950732, "end": **********.469555, "duration": 0.5188229084014893, "duration_str": "519ms", "measures": [{"label": "Booting", "start": 1751299554.950732, "relative_start": 0, "end": **********.385197, "relative_end": **********.385197, "duration": 0.4344649314880371, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.385206, "relative_start": 0.43447399139404297, "end": **********.469557, "relative_end": 2.1457672119140625e-06, "duration": 0.0843510627746582, "duration_str": "84.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024630000000000003, "accumulated_duration_str": "24.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414099, "duration": 0.02249, "duration_str": "22.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.311}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4452932, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.311, "width_percent": 3.329}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.454651, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 94.641, "width_percent": 3.37}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.460566, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.011, "width_percent": 1.989}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1717328546 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1717328546\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-67395226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-67395226\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1054560176 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054560176\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-740980479 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InUyODhnWTh3dWJSOGxVVlYwZGR5V2c9PSIsInZhbHVlIjoicXd4TGFSb1d1eXlJb1dVUm90WitYdDlQUWczUngySnpVYk5LMk1GUk5BMUwrTnFzanZ6cHF3Q2lOWDZXOWQybHF5THlJcFlzNXNaTkxhZFpkcEFtWkFxa2E5K3ZIQitxMUFDUFBFMFlVUUpEMm5YRFJhMGEvbGlsL0ZPamgwRVRodXVhdm84Qklua0oxeEFXSmtTdzY5emNJajFnZytVMlZRakpGREFwSDJYbi9RUHlIamRzd2JxdmpBeEpQZjVIRmQreXpFeGd3K2pRZ3ZyWjd0enc4SFozalJrYjNBcVNZUVFuUjNBV2xPVkZKcnB3VUJJRGJ1QlpLd0E2bjZ4c0VyOVQvMmlqRHN3QThBb2hrY1pVdE1QQ1V0NkxwV21zQVE1eTBWK3R6cVZrT3VpV21TTThwZ3FyVGRiRjN2YVRwY2lncEY4N204Z3JTTEpORTd3ZkpUVlBtbmVQOG5SOXlvNEE4RStOeU9zVVNVUnpVZE9teDVqTjcrUTM3dWVDeG9WcDhpbDNCdG4vM2VXL3ZjRnhvM2xEZG9VaXRXbk5Wa2YzMlU1Zk0vdmNIRUJUZjE2VnlHQy9aWFpCdXRuN2MvMmtveWNFMmZuUU56M1BSR1ZXWU12RmFndEtGc0lRbzhTQnJIK1IrRlF1UDBreFkzVXhuSzkwYjRoVmtuZEoiLCJtYWMiOiI5YjU2ODdjZDBmNGMyZjYxMWM4ZWZhNGYxZWQyYzAzYWMyMWUwNmZhNWZlZDlkZjJjZmVhNDFjOGViY2QxYzgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InprNEo1UHNZbVZiMlVJVTZzRERFTVE9PSIsInZhbHVlIjoiSXpCdXplVVpzR1B3MkczZjJxR3NQVDN6MExHNGZVcVJrN1pRTkdqSXV6cy9NeWt5dml0eTBSeXA3V3hwN2tuZHBIOWlPODg0M1NVUmRCNTg0Nk5ESTNPdXE0YTRDVDdxMVZmTVlDaENDbHZXM3V1THlNbUphSGVadXV4YUtGQSswM1h2Z2tSQ01rSHhCdTYraXNLMWl6aStjWlJLY0ZWb0x0SDFFeEhCL1N0cm1ES0krL0ppdFk4UHBCYmpheVNGcklrWVF6WXN3RitLTkdVWUZpb1A2TndEaGhnbS9oazVRaEdVbjJXdmtJYWVCU21nSklZYzNSWXoycEs5ZWhkU05RNUFONzZNZXpyM1hFSHBCeUFnR25nM1ZqMWNGREZuMVRyUnRWN1lQUHpLK3RLeEpPU0JVMHJIUnZYcVZJaUtYcEtSNk1lTUZVK1krbi9wSlZvRStPVGY1WlBhMlVWYnh5Mko0bG5HOGNqaWZmZzFLa2UzVG1jRytzVHNPbW5oWDlHejVNQkFEd2cwVi95V2VoMWx4bzBXRENpN2FIaVBRNUhXeUNRd1JhTmRIRDN5REtZK3NhTjg0K2lzc2ZhSktJbTc2WVlOaHQ1U1phZ2tPM05yYUFuMDg2Z21mMnlFV3Erald2ZU4zbE9DRXVrRC9mQTB4MjJlM01TeWdXbWciLCJtYWMiOiJmZGFkYzZmOWMyNjY1Y2JlYWE3ZDc3ZTIyZWY0ZGJiZTFkOTMxYjEzNTlkMmEzNmI1YzkyZjhjNzYxNTdmNDk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740980479\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350978544 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350978544\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1521685112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik41N0k3ZTQxVGw1Y003NXNwYVhVdGc9PSIsInZhbHVlIjoibE1uSGxoQXlLbUk2SXhxeFFlNEhJcE56T1NOZVN5WnVWbzBZY0I1dnFrc0hqbUM0M0tNdjhic3BIRldLZjJwWGI4QUVEdUlsRGM0T1VvcDdhdUJIcldwN3NjRi80aEFiTTA5aVIzVWN5MnZ2WXo2cnUzMlljMTU5WEx1ODcweW9yMmFKVWlnR2tQWkw2OHY2d1RlSkxiYXJFaTM2cjIxZVJkRWd2bE5PWGpOVmZzWnF0cVNHMHNSQWxBdmlYRVF3em05UFN6T0lkQUlSZW53WnZaWmNZaFZEbmpJcWZyNTg4bnBMTEg1NHlHYTZlSGUyeE5LZ0ZTbnVVb1hERy9WS1hOU1IwMEUyeDNpUnlTeVpTUWhSRGtha3V3enhnZGs1QW0xeklteHR6NGg3T1BRWEl3bzRHOFM5OUxPMnFkK3lHU2ozck91WUlJZXpnNEc3UWE5bHA2YUVPYThYQWxpVTZLTnhhZEUwRlV5OUZabDVyZjdYaVhlTEkwRTFJb1RWOWxUdVBFZnRwNmxXMGM1bmlNR3NhMmRYa0YxNEFZOStkcUtTcmJydnVZWlgwcVUxazIyUDZZZStaalRhWnA4cFJGVWVoOGxuOXdRZGV6OW5IUWV4V2tIQUE5WnBZa2tFUW9mU0ZGSE40NXcxZWczRnl0RHByTnJtMXoyeERKR28iLCJtYWMiOiJmNzVlMmJiZDcxNzJhY2IxODExZWZhMjE2MTY3MjdkNTM3Yzk2YzcyNDRiMTFiMjNjODAyYzU1YTQ0ZTcxOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkN4U0R2dXNkWURwWDZqUHdwZm1aZEE9PSIsInZhbHVlIjoiWUN2ZjdYZnRJYjZtZHVXWGtHaU11Q3NEOVBkM2txYTFRaDFTTG9INVZQV3gxNnZtRHpYaFUzbC9qRmxpRndEVjlYZ2lVTVdKNnRCQU9OVlVBc3ZPWlRTYXBBbVQzdkU2cVNLV2ZSRTlDc1lzbE5hbGZxM1MwNldDOFJ4SmdiM2YzR3J6TmZBbzBxOHN4dSs0ZDVmbDdPSm9BRXFHbFB6clV4TG9FNFBtZ1M1UmhOVmdRZDN3SG1ORDdRNjlmcXR6d2pEOGJjSTRaaHE2WnhWMVdzdUFkdG1hbklTamRSU2t1aUVXcFBUamVjTXdsVThySHNGQXpXaVVDdXExMDRsaisxTVhqZHh6a0I1S29CaENtcDRuWkVjMWd4NjQrTTRBL2tnLzI0NGJpYVUwWnNZeG1LYVl2bzVtVGsvV1BxeTRiY0RkUlpXL1pHMVEwRXhRYkF3Qkl6ZlorbXluQWl5ZFE3MlVmUVVKREtCQ1JZZ2xJYzFyMXNKeG5KWGRlSFQvQUNxekRmNDJ5YkJJZjgrMjM2UWlFQVhrekd6dDBjRmFwM2hoTmtiTEpZemNtcEJyMVE4VzNPNEZzT3FHTWcwaHdqNHdKQ2NZK1Jla0ZzaUFKLzN1U0pJdTkwRW5la241R0pHejJzbEFZb21hVlBMQ2MxVG1qL21TbkY2R0dndjEiLCJtYWMiOiIwYjNiZTJhNWVlOGRjYmRmMmNkYzBiOTliZTBmYjVmNDMzNDM4YWZiMTc2ZTI1N2ZhM2ExYzEzYzYwZmEyMDBiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik41N0k3ZTQxVGw1Y003NXNwYVhVdGc9PSIsInZhbHVlIjoibE1uSGxoQXlLbUk2SXhxeFFlNEhJcE56T1NOZVN5WnVWbzBZY0I1dnFrc0hqbUM0M0tNdjhic3BIRldLZjJwWGI4QUVEdUlsRGM0T1VvcDdhdUJIcldwN3NjRi80aEFiTTA5aVIzVWN5MnZ2WXo2cnUzMlljMTU5WEx1ODcweW9yMmFKVWlnR2tQWkw2OHY2d1RlSkxiYXJFaTM2cjIxZVJkRWd2bE5PWGpOVmZzWnF0cVNHMHNSQWxBdmlYRVF3em05UFN6T0lkQUlSZW53WnZaWmNZaFZEbmpJcWZyNTg4bnBMTEg1NHlHYTZlSGUyeE5LZ0ZTbnVVb1hERy9WS1hOU1IwMEUyeDNpUnlTeVpTUWhSRGtha3V3enhnZGs1QW0xeklteHR6NGg3T1BRWEl3bzRHOFM5OUxPMnFkK3lHU2ozck91WUlJZXpnNEc3UWE5bHA2YUVPYThYQWxpVTZLTnhhZEUwRlV5OUZabDVyZjdYaVhlTEkwRTFJb1RWOWxUdVBFZnRwNmxXMGM1bmlNR3NhMmRYa0YxNEFZOStkcUtTcmJydnVZWlgwcVUxazIyUDZZZStaalRhWnA4cFJGVWVoOGxuOXdRZGV6OW5IUWV4V2tIQUE5WnBZa2tFUW9mU0ZGSE40NXcxZWczRnl0RHByTnJtMXoyeERKR28iLCJtYWMiOiJmNzVlMmJiZDcxNzJhY2IxODExZWZhMjE2MTY3MjdkNTM3Yzk2YzcyNDRiMTFiMjNjODAyYzU1YTQ0ZTcxOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkN4U0R2dXNkWURwWDZqUHdwZm1aZEE9PSIsInZhbHVlIjoiWUN2ZjdYZnRJYjZtZHVXWGtHaU11Q3NEOVBkM2txYTFRaDFTTG9INVZQV3gxNnZtRHpYaFUzbC9qRmxpRndEVjlYZ2lVTVdKNnRCQU9OVlVBc3ZPWlRTYXBBbVQzdkU2cVNLV2ZSRTlDc1lzbE5hbGZxM1MwNldDOFJ4SmdiM2YzR3J6TmZBbzBxOHN4dSs0ZDVmbDdPSm9BRXFHbFB6clV4TG9FNFBtZ1M1UmhOVmdRZDN3SG1ORDdRNjlmcXR6d2pEOGJjSTRaaHE2WnhWMVdzdUFkdG1hbklTamRSU2t1aUVXcFBUamVjTXdsVThySHNGQXpXaVVDdXExMDRsaisxTVhqZHh6a0I1S29CaENtcDRuWkVjMWd4NjQrTTRBL2tnLzI0NGJpYVUwWnNZeG1LYVl2bzVtVGsvV1BxeTRiY0RkUlpXL1pHMVEwRXhRYkF3Qkl6ZlorbXluQWl5ZFE3MlVmUVVKREtCQ1JZZ2xJYzFyMXNKeG5KWGRlSFQvQUNxekRmNDJ5YkJJZjgrMjM2UWlFQVhrekd6dDBjRmFwM2hoTmtiTEpZemNtcEJyMVE4VzNPNEZzT3FHTWcwaHdqNHdKQ2NZK1Jla0ZzaUFKLzN1U0pJdTkwRW5la241R0pHejJzbEFZb21hVlBMQ2MxVG1qL21TbkY2R0dndjEiLCJtYWMiOiIwYjNiZTJhNWVlOGRjYmRmMmNkYzBiOTliZTBmYjVmNDMzNDM4YWZiMTc2ZTI1N2ZhM2ExYzEzYzYwZmEyMDBiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521685112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-764527087 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764527087\", {\"maxDepth\":0})</script>\n"}}