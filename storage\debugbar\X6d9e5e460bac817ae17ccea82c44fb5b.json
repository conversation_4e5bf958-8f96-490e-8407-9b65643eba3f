{"__meta": {"id": "X6d9e5e460bac817ae17ccea82c44fb5b", "datetime": "2025-06-30 18:09:12", "utime": **********.280448, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306951.833818, "end": **********.28046, "duration": 0.4466419219970703, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751306951.833818, "relative_start": 0, "end": **********.183273, "relative_end": **********.183273, "duration": 0.3494551181793213, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.183281, "relative_start": 0.34946298599243164, "end": **********.280462, "relative_end": 2.1457672119140625e-06, "duration": 0.09718108177185059, "duration_str": "97.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02496, "accumulated_duration_str": "24.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.215056, "duration": 0.0195, "duration_str": "19.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.125}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.245955, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.125, "width_percent": 1.923}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2603118, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 80.048, "width_percent": 1.522}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.262044, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.571, "width_percent": 1.282}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.266433, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 82.853, "width_percent": 11.018}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2718282, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 93.87, "width_percent": 6.13}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2054747149 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054747149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.265404, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1521610743 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1521610743\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1154277662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154277662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-40948387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-40948387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1606297838 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQ1bzBQNGRqbk5PT1hZZGtnRUVQRVE9PSIsInZhbHVlIjoiTk5pWEtOSHozTDZienlaR2o2czd0aDlvcjJJaEhpSlQxQTJPeHllNytrT3ZkdDdLMFdRSitsNm9sMWN2dzgyQ1VxVmVSVnV0SkZKd1o4NkwvbElJd0JiUjJqQlBhTnNOSFpqQWlDekpnRE5JOGZTWTNzUzBab1FxdklKQyt1Wnphd0R2RzJyZ0ZLWlVKWUtETFJJTEtnaU9DdHErVVVva3lHTVhhd2pyZTBvbmd0dmdpQzdzcGpscHpqQWRneVUyS01wa1hWY2ZHb2MwUEFkQ3lOV281d0xoam8xYy80UlpScCswSjMwMXdGeFFKYnEyR1pJakVMWW5jVUVvS1dadDBlY0t5emFXci9OUGV5SnkvbGtOK29TYnk4YU5ZTE9IdHlLZHlWdTdxWHVnVlJacTJncllqRDNIbWNyWFFXMXlOMnE5NGRnVmJJak9QaFZFOVd5WStqQkp4NmNrUGRDUUwxcU1wcEk5cTZiWDRCZWxCSHhta3gweHEvdmhsb1RNUXo3bVpnRUc1bDJmOXVqckhCYWcrbGtrZjMxRDNMVjdwNG96NktNT0F5UlllOUQ5aTRNK3NnWkJoUlRmZkVZbUlOL1JnZlpqeHJrVzRWWmZ6enlMbWxxd1hLa3FRQ2pxQzZIM3dnRGFaSFljUHF0SW9WWlg4OFRETDFwYkpMMGciLCJtYWMiOiJmOWM4MmE5NDNmYTJjOWNkODljZWE4NDUzMzNiZDIzYmQ5NjA2ZTFjODY1ZWNhYjQ4Mzk4NTgzZjJkZWJmZTA2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlQvOC9DYzhEejlCcHJyL0Z2YldZVnc9PSIsInZhbHVlIjoiSEx5R01OYmlKM2VlblJiRW1aY01la1FzSEFHMDdub3Z1a2t3bHdHMFlaU3hBT0NQU3hzU3k1OU5ucXBoTklpalBuM0JIMlBLRGYwY3ZReXN2dmVHNlNRckdIUG9YcFZHZUQrcElMT2Iwc2hYcHpuVWZzQ001ZVNjVlpDQlYvZWJlb2hPUE14V0RvaEFIK3BGNjRuK0tMb2pLaGpYbTQ4V2txMVJBYUdtbm1HaWdIVmlGanlUSzh5akU2OG5hZTJkNEhtZkM4N3lBbXRsbUVTR2ROOGgyam1rdHIydDQvVTBDUnJnTmF2VTQ5TXhVbVhYZWlSNU82TCt4SkhmVXRSMDZoaHYwRkxqUVA4RFBtR1U3UVd6ajRMZjUxdkE4eEJOKzl3YmNtWHhBOE5UWjQrd3JKRVZDK3NZNWE4eWJ5S2RkZjZBNE9tWXBxWWhzWkhXV0d1T2FsNmphcWJ3RFF2YWFFZ0k1YWFTWjdWUytmeHZ5ZVRFL0lTMnpjMW5jTlN3cWVXcWxIT3h1K216RFN6eFRpSkRUYzhMRENxWEpLa0M0cWUyVXNyVDhYczZCQkg2OEJtRUxyU0RJQXVFR1hCTDE3U3YwZTI1T2xVT2s2YU90L1MxeHZpYUtsc3o0S2VIdHBVdWl3RnR4VUkybHlVaW15QmFVU3NydnZUcDBYU2QiLCJtYWMiOiIyMTRmODkwN2UxZTE3YjdkZTZjYTkzNDgwYzhiZTE3NjBhYWUyMmNlY2NmNDAxZTI4MGJiMjg3NDI1OTkxYTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606297838\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1997323903 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997323903\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-17971687 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNuV1ZlTm44VWM1ekxyd0s0c2NzaEE9PSIsInZhbHVlIjoiTUNkekZuUVNUVTBJVjJxaENTbC9lZHhjM2drZ0ZQY3h4VEpGK0U5TlNzbTdYZjJnbCtwNW9iY2x1NUo1TWFCd2d5VlJaemVmY3dEQTBEdXkvL2xNL0dyVm0yRDRmQjh1cDF3bzVYeVpmYWhQRWJQQTBWKzI5TjAxVllkVkw0U3cwUForVVdhREhvR0wvKzVub0UycFlTbHVkTzFUbmp3UnI4ZkV3L3ltdCtFMUJCT202Y1YycVcyUXBMUnd2Q0RUYU1EbllsY1g4NFJ5bFUyRW9OMjVDWDFyR0kxY2J0QWI0eHZSa1Y5SGF1MXZUN1AwZHMxM2xBSkREbWY4NTJ4ZHU4WUtvOWM3cy9sMWt2bmNpZFBxVWRleXpncExPUWtLYnVJRXpQcTh3ajhzR3BtUVIvUVdKVStBQk1yWk5PVExDdmRjMFdzTE5rYmJ1SWJCcHNuRTBsL0wwWWNXdUZlWlgyU2N3NFg3OFBGNm4xcnZrbWdIcjNUUExaOTIvY1hQc2JvdmNha1NjSll1Zk9oNGkyY3NCa0lrQjM1MDVvZS8wbVdHT2JxSDFpZTBWQ3RWTzdncVYxV1JBUXRZWXgvZ051SVlkQjh4eWhnVEI4cUpaa1R2R0p1OVVDZDJ1Ymx2QTRRcnVORVlzemFsbFM5OVY3TEVTTDdBcGtkUHZobW0iLCJtYWMiOiJiZGEzNzM2YjFlODBkZWJkNzZlYzVlYmM5YTZmOWQwZTlhN2VhMzk2NzE5OWYyZjY5Y2MyNTE5M2FkZGJkMjMwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBjM0Q0djVQMHVhenhPZUtpMGxiOWc9PSIsInZhbHVlIjoiTUNBdTBvYzhkd2F4MXlMNEticnFUaGZpeGU0ZGZHUFhMQ3J2NStIbXgzTVdTODdtZHFJUFJ5YkdYQ0lTNlJFOHMyYUNxNVY4bjN3bmh1RGYzbzBPU2ZxTEJaNTlFTHQ4MHVpaTZKcFZMbVZpMEdjWjFCVGE2YmVzWk11STVxeDhabGVRcXpBUGsxQlQ3V21vWmhQYnVLNVc4UjUzSTFMWHQzUVg4ZmlQRWxOUUh1cmxpZFlLK2NmRmk5UnFvVGRpWFRJOGxRYXlhamkwSHlEL3BZRGdvcTd4Q1Z5MlhsUmNqYnpNR1NhbklLcU0zOS9GbzZFa3VJOHk0RHEza1hmbUozemJoc1ZhWFhZNHFzcURxVjE3dms3OTlyeGFHcnplUDJGdzZHMm81WTR3Z040ZEo2dzczMFlkaWpEZEV4MG1KVXgvaTliRzl0d2IxU0Q2SXFXWk9NZkZNNUJSb0NHZGMvYUxIT2ROdlpKell4MUlwMllHUUs1dVc1Qmo5N2Fqd0dQTm45dGZmbERuZ2Y1U0ZtVWUzd090MlhzeDhjd1BWMWxtTURreTl1bk8xZkF5K2RKSTQvZzVCazlUc1RTYngxdEVuQVY1K0ZDMU5WY2hEek5xQ2VhckVNVWZ6RzhsblJBbEUrOWM3WTJJVUNyRlBQRkIvL3dwZk5yQXova3QiLCJtYWMiOiI1YjcyOWQwZGU2ODljMWYwZTQwN2IyMjQ1MzY4MDE5NzQ1MDJkMzg5NTNmZmU0MWE1NDM3ODM0OTcwZTc0MDE2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNuV1ZlTm44VWM1ekxyd0s0c2NzaEE9PSIsInZhbHVlIjoiTUNkekZuUVNUVTBJVjJxaENTbC9lZHhjM2drZ0ZQY3h4VEpGK0U5TlNzbTdYZjJnbCtwNW9iY2x1NUo1TWFCd2d5VlJaemVmY3dEQTBEdXkvL2xNL0dyVm0yRDRmQjh1cDF3bzVYeVpmYWhQRWJQQTBWKzI5TjAxVllkVkw0U3cwUForVVdhREhvR0wvKzVub0UycFlTbHVkTzFUbmp3UnI4ZkV3L3ltdCtFMUJCT202Y1YycVcyUXBMUnd2Q0RUYU1EbllsY1g4NFJ5bFUyRW9OMjVDWDFyR0kxY2J0QWI0eHZSa1Y5SGF1MXZUN1AwZHMxM2xBSkREbWY4NTJ4ZHU4WUtvOWM3cy9sMWt2bmNpZFBxVWRleXpncExPUWtLYnVJRXpQcTh3ajhzR3BtUVIvUVdKVStBQk1yWk5PVExDdmRjMFdzTE5rYmJ1SWJCcHNuRTBsL0wwWWNXdUZlWlgyU2N3NFg3OFBGNm4xcnZrbWdIcjNUUExaOTIvY1hQc2JvdmNha1NjSll1Zk9oNGkyY3NCa0lrQjM1MDVvZS8wbVdHT2JxSDFpZTBWQ3RWTzdncVYxV1JBUXRZWXgvZ051SVlkQjh4eWhnVEI4cUpaa1R2R0p1OVVDZDJ1Ymx2QTRRcnVORVlzemFsbFM5OVY3TEVTTDdBcGtkUHZobW0iLCJtYWMiOiJiZGEzNzM2YjFlODBkZWJkNzZlYzVlYmM5YTZmOWQwZTlhN2VhMzk2NzE5OWYyZjY5Y2MyNTE5M2FkZGJkMjMwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBjM0Q0djVQMHVhenhPZUtpMGxiOWc9PSIsInZhbHVlIjoiTUNBdTBvYzhkd2F4MXlMNEticnFUaGZpeGU0ZGZHUFhMQ3J2NStIbXgzTVdTODdtZHFJUFJ5YkdYQ0lTNlJFOHMyYUNxNVY4bjN3bmh1RGYzbzBPU2ZxTEJaNTlFTHQ4MHVpaTZKcFZMbVZpMEdjWjFCVGE2YmVzWk11STVxeDhabGVRcXpBUGsxQlQ3V21vWmhQYnVLNVc4UjUzSTFMWHQzUVg4ZmlQRWxOUUh1cmxpZFlLK2NmRmk5UnFvVGRpWFRJOGxRYXlhamkwSHlEL3BZRGdvcTd4Q1Z5MlhsUmNqYnpNR1NhbklLcU0zOS9GbzZFa3VJOHk0RHEza1hmbUozemJoc1ZhWFhZNHFzcURxVjE3dms3OTlyeGFHcnplUDJGdzZHMm81WTR3Z040ZEo2dzczMFlkaWpEZEV4MG1KVXgvaTliRzl0d2IxU0Q2SXFXWk9NZkZNNUJSb0NHZGMvYUxIT2ROdlpKell4MUlwMllHUUs1dVc1Qmo5N2Fqd0dQTm45dGZmbERuZ2Y1U0ZtVWUzd090MlhzeDhjd1BWMWxtTURreTl1bk8xZkF5K2RKSTQvZzVCazlUc1RTYngxdEVuQVY1K0ZDMU5WY2hEek5xQ2VhckVNVWZ6RzhsblJBbEUrOWM3WTJJVUNyRlBQRkIvL3dwZk5yQXova3QiLCJtYWMiOiI1YjcyOWQwZGU2ODljMWYwZTQwN2IyMjQ1MzY4MDE5NzQ1MDJkMzg5NTNmZmU0MWE1NDM3ODM0OTcwZTc0MDE2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17971687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1328188303 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328188303\", {\"maxDepth\":0})</script>\n"}}