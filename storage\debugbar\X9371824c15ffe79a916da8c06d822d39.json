{"__meta": {"id": "X9371824c15ffe79a916da8c06d822d39", "datetime": "2025-06-30 17:58:35", "utime": **********.263327, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306314.765399, "end": **********.263345, "duration": 0.49794602394104004, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751306314.765399, "relative_start": 0, "end": **********.196177, "relative_end": **********.196177, "duration": 0.43077802658081055, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.196187, "relative_start": 0.4307880401611328, "end": **********.263347, "relative_end": 1.9073486328125e-06, "duration": 0.06715989112854004, "duration_str": "67.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708984, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033900000000000002, "accumulated_duration_str": "3.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.229688, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.451}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.242371, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.451, "width_percent": 14.749}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.248945, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.201, "width_percent": 11.799}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1545561465 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1545561465\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-56792328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-56792328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2014678797 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014678797\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1871037971 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306296830%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBGczRQSk1jcWdJZVcvdjlaeitNNXc9PSIsInZhbHVlIjoiSEVZZHp1aU5vdzBPU2hERFB3RS8vMFVoTUEzUGo2cXB2dDFTTk1WdlhuSkduN2paM0ZtZVpwS2pzTkJyNkxUOXpoWkExb1VyOXQybFpid0RrcGF5cTUzdjRXVWptVW12QnZ6K2Q2aVlFZ1VDYXUxU2ZKSDFudUtIUzlRRkVqYU9WMDJSQXpINXVTN1RZYmdNUnp2YWVIZk5mbUloamR6bjRDaFFabmswQzZMQzVIQUhrR3dIOWs1c1AraHlhdFJsYmxrbGc2M0NpL2xod3BVdW8yaEEwRmJKRkdRZnlqQWYybkhnb3JsQ0tNTDZxS2xSOGZqdlB6R2FaR0FUZUNma2ZjbnVoVUg2VFV5c3FqaXl6TWNZcDJJYWlXQ25pK0RucFgrbmh0MTBxUjhxSVhUK2I3Ly9HMXJYTWVNREJHdkZuMzk1OE9PQnU4OFZESDNBVjlNbERsekt2SVZQSmVOWHNzQXRackpMdnhwTEl0aVV3ZHNOVG9ZcWdQZXdydWNXUHFBMWJEOVpmU2tITCtJL0NzdjJpRzdQMlFEd3BGNW54ejVsM1RFcXJrYmdpVHBMM1RWTytXQ2pzRjFIOCtNcGdVeklxMVVON3NWdXVKeDNpY1RzM1JKVkFTL0p4dHFSQWNUTUNoNGdicTdIU2ZRQ0cxQ3ZMVWQxdzNURnNnUUMiLCJtYWMiOiIzMDgwNmEyZjQ3MjE0ZDg0OGNhNDQxMWI4ZmFiZTczNTJkOWY4ZGNjN2IyNzQwNjNmZjE1YWUzODY5ZjhjMzEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijc5azBTWGZxbFFLMHBITmJGQWpMVEE9PSIsInZhbHVlIjoid0dFTGdaclJiRzJXSDVUTlhYUTV3bS94STBBcC9WM0QyMkRHWkt2VG8vM1p4NHliRDgzOGFTVkRMZFNUUHQ2cEtsSjlVR0dwK2JsWUdGOVdLU0NIZUQ2TmR3N2poMUl2S0p1bGd1c2lDcENxWmp3VjFGenFUYmNrRjAvc3hzTEplb0pEdTVVclVKVUN6c2s0QThmckFOdmlSUjVnalJuTjZEZFdVMTNYbm52ajlFOHBIWGhzbjFIREFheHQ2bDJWQ0UzYTQ0RmFPOW1Za0srUTV2ZHArL013K0taQko1Rm9wbXpiY2VCa1hHTDUrNjU2YVdTMDNiKzdpTnRCTjBYN3p3NFZmNUNFS2UxRUNKaW1xS2tQVjBIL2E5WWhDVFgzdXBrMTJ4cHJ5a1FHWGx5azdGU0swajI4MEJMdVNoaHM1MjFIUFJCTE5LVU1xelA3ZkE0S29rK1BMYko1aW9sZHhlYm13UzhBWnJIR2h3a09KOGlzMzlCQzhocFJ0V0RvQXI3U1BIL0xOQk9ocWJvS3Fyb05RSW5JT0N1RmsrblR0RE5TdGpqRkRwYXlhZk5pNkZLRkpNc1pUeWY3NE40d0twQmY5a2J5bmlOa0lJT01rc3hoY3I3OXA3Wm5HQkE5ZUlIYjhJK3dGbER2b0NkQ2w5RjMwbkYyMEtEOWxjNVYiLCJtYWMiOiI2MzU0ZDE2YWQyYTg1MGVhMzY1YjdkOTVhYmNlZDFhN2VkZjQ0YjkwYWEyMTk2ZTVjNWFlYjc0ZDFmNDk3YTZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871037971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1160911226 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160911226\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1029894780 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:58:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9MV1dJTW52QjlsakloWHd2aGdEN0E9PSIsInZhbHVlIjoiR2VoOE9PcXdKWDlOTWdVL09lcU1FL2pHd1VEb0tpcWI5bGo4aVZSRnNuR0xqdXNWN3FNd1B2NWZPVmZVdE0zR2NXTFhCalpiZG5xUHcrT2dKVkVFT01ZaDY2WkJYKzdpMGdXb0FtMjEzT1JBVUVtbmd0VTFmeFRSVS9pb1JleGtZaDViRDk3bjNIaWFnMFNicGFUMjEyVmZyeVRrMDlVYlY4ZmZjNmFPSDJOK0JxVk9BNEdsNWN3Um54UFJUZnJ4M3gxWVdsWjVWaUFMMVNBMTI4bTZHTnhwcUt5RzlZcE1QZHRXdktBU0x4cDJvNzlFRVBUTnc1OEdQbm1oYmIvQWRkSVlLL1ZLa1dqeWJra1JMKzZIUmUvL0lsRjZXOVV0Tlo5UFovWUxiUmhmaGxEaUt5MkRwTEUwaEo3cGNPVFF3d0lHbkRGYzJLVzFlbEtmU2xyUHNrQjFjdVlsSDlkT3c2dkhwR2I1RmJKUGdlQlF1Mmp2NHRaRFA2UStoN21JbUN3Sm83amNDdGR3MDFFeUEybkZOQnU5WVF5UmkvOWlNM3VWTTE4VlRKcHZPYzZQdWdxR3hDenljdmtHc3NSQXR3YTJiZGZIOTFtcWFxWldGNjF6NWZ2Z2pDOFEvSlAzVmprVk5kQ3l1MUFrME1lZ2xpLzQ1K0FvUG1qeVhtQnMiLCJtYWMiOiJkNTJiOTQ1YzQ5Mjg0NzFjY2UyYmI2NTQ0NmQxMzE0NzgxMzZmMzMwNjM4M2IzNTRhOTRkYmNiNjIwY2QyOWZjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtTSUZldGtlZERiVDB2ZGlsMk0zK1E9PSIsInZhbHVlIjoiR3FZcjVHNXhnTW1zbFBXRkhWQy9KN3E5VGZoNEh1cWZMektiTEdjUElXeHE5OURRWEJCazZabVlUMUtxVE5WcVQ1Wmhibk5mTmtlT25HMzdTVFNCSDVmV09TMUNSWC9icGtXTlFCd3hnNTd6OWhWTUdMVHpwMjRtWGlNZHBZWnRaRTlQcnZaKy8zTTNnNUFMZnY4Z1JVR05tU001Q1NaSDZyWE9hbk9ueTdtTUltVWRXOGY5d2N3emJCazRuNHRuZXRlQjBVd3piUnd1VXFORjJiT21rRkxxTWhVdjNERzBYTnd5NVF3T05yWE9FV0dBc0lZaGJyem9DWG5pVnNNMWNDREtqTE01d0xZUSswQVZCUExQVkQzK291dU1sWjZEWEh1Rk4ra2lOSnZNcytIclV2Uk5VZ2VJcDF2N0VsVGlMMjBRWWZUV2xRYVFRTWw2eTAvOXZ0dzlOWTgrVXFuZzc5Q0N1aHZ2Y3ByQzVBdG5nTGtkWVRzemFPMm1GT010OW9jOHExMEFMOXNDRmJmK2M0MnZ4ZGZVYm8wWDBjSWhRRXdTNzhsTFhXS28yLzVPd0xHeElRS1NoazVEclpaNlViMys4SVA1RU1uVmR2QmhsdjZjTS9ZbDg1T2tvK3lzbmNET24wRHZJU2c4YzB5M05pWXYzNXF6SGtML2RUNmsiLCJtYWMiOiI5YmM2NGU1ZTkzNzE0NWQwMmYzNmZkZDVhZTE1MTA5NjkyYjk2N2E5YjljMzVhYjQ3NWM2NTVmMDY5NzMzYzAwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9MV1dJTW52QjlsakloWHd2aGdEN0E9PSIsInZhbHVlIjoiR2VoOE9PcXdKWDlOTWdVL09lcU1FL2pHd1VEb0tpcWI5bGo4aVZSRnNuR0xqdXNWN3FNd1B2NWZPVmZVdE0zR2NXTFhCalpiZG5xUHcrT2dKVkVFT01ZaDY2WkJYKzdpMGdXb0FtMjEzT1JBVUVtbmd0VTFmeFRSVS9pb1JleGtZaDViRDk3bjNIaWFnMFNicGFUMjEyVmZyeVRrMDlVYlY4ZmZjNmFPSDJOK0JxVk9BNEdsNWN3Um54UFJUZnJ4M3gxWVdsWjVWaUFMMVNBMTI4bTZHTnhwcUt5RzlZcE1QZHRXdktBU0x4cDJvNzlFRVBUTnc1OEdQbm1oYmIvQWRkSVlLL1ZLa1dqeWJra1JMKzZIUmUvL0lsRjZXOVV0Tlo5UFovWUxiUmhmaGxEaUt5MkRwTEUwaEo3cGNPVFF3d0lHbkRGYzJLVzFlbEtmU2xyUHNrQjFjdVlsSDlkT3c2dkhwR2I1RmJKUGdlQlF1Mmp2NHRaRFA2UStoN21JbUN3Sm83amNDdGR3MDFFeUEybkZOQnU5WVF5UmkvOWlNM3VWTTE4VlRKcHZPYzZQdWdxR3hDenljdmtHc3NSQXR3YTJiZGZIOTFtcWFxWldGNjF6NWZ2Z2pDOFEvSlAzVmprVk5kQ3l1MUFrME1lZ2xpLzQ1K0FvUG1qeVhtQnMiLCJtYWMiOiJkNTJiOTQ1YzQ5Mjg0NzFjY2UyYmI2NTQ0NmQxMzE0NzgxMzZmMzMwNjM4M2IzNTRhOTRkYmNiNjIwY2QyOWZjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtTSUZldGtlZERiVDB2ZGlsMk0zK1E9PSIsInZhbHVlIjoiR3FZcjVHNXhnTW1zbFBXRkhWQy9KN3E5VGZoNEh1cWZMektiTEdjUElXeHE5OURRWEJCazZabVlUMUtxVE5WcVQ1Wmhibk5mTmtlT25HMzdTVFNCSDVmV09TMUNSWC9icGtXTlFCd3hnNTd6OWhWTUdMVHpwMjRtWGlNZHBZWnRaRTlQcnZaKy8zTTNnNUFMZnY4Z1JVR05tU001Q1NaSDZyWE9hbk9ueTdtTUltVWRXOGY5d2N3emJCazRuNHRuZXRlQjBVd3piUnd1VXFORjJiT21rRkxxTWhVdjNERzBYTnd5NVF3T05yWE9FV0dBc0lZaGJyem9DWG5pVnNNMWNDREtqTE01d0xZUSswQVZCUExQVkQzK291dU1sWjZEWEh1Rk4ra2lOSnZNcytIclV2Uk5VZ2VJcDF2N0VsVGlMMjBRWWZUV2xRYVFRTWw2eTAvOXZ0dzlOWTgrVXFuZzc5Q0N1aHZ2Y3ByQzVBdG5nTGtkWVRzemFPMm1GT010OW9jOHExMEFMOXNDRmJmK2M0MnZ4ZGZVYm8wWDBjSWhRRXdTNzhsTFhXS28yLzVPd0xHeElRS1NoazVEclpaNlViMys4SVA1RU1uVmR2QmhsdjZjTS9ZbDg1T2tvK3lzbmNET24wRHZJU2c4YzB5M05pWXYzNXF6SGtML2RUNmsiLCJtYWMiOiI5YmM2NGU1ZTkzNzE0NWQwMmYzNmZkZDVhZTE1MTA5NjkyYjk2N2E5YjljMzVhYjQ3NWM2NTVmMDY5NzMzYzAwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029894780\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-590004780 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590004780\", {\"maxDepth\":0})</script>\n"}}