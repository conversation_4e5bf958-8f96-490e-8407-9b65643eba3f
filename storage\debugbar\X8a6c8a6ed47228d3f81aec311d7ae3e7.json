{"__meta": {"id": "X8a6c8a6ed47228d3f81aec311d7ae3e7", "datetime": "2025-06-30 18:11:24", "utime": **********.900821, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.443333, "end": **********.900833, "duration": 0.45749998092651367, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.443333, "relative_start": 0, "end": **********.806221, "relative_end": **********.806221, "duration": 0.3628880977630615, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.806229, "relative_start": 0.362896203994751, "end": **********.900834, "relative_end": 1.1920928955078125e-06, "duration": 0.0946049690246582, "duration_str": "94.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48652904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.017519999999999997, "accumulated_duration_str": "17.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.844829, "duration": 0.01383, "duration_str": "13.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.938}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.867094, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.938, "width_percent": 2.283}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.880088, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.221, "width_percent": 2.055}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.881729, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.276, "width_percent": 1.484}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8857758, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 84.76, "width_percent": 1.484}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.88926, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 86.244, "width_percent": 11.872}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.89259, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.116, "width_percent": 1.884}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-524151618 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524151618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.88502, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 5\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 287.5\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1916460440 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1916460440\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill2QzRYbkRPTEVQQklHSGJMR3hqc3c9PSIsInZhbHVlIjoiNkpKUVREQmwwQlF0aTRBcDJtckJIT3YvRExDdTkvblkwcjdiSG02MzBlc01ra2FWcmVEbGdQaW5nMnRhWFhvMGQ0MjQrQWsvVEt6aFo2THJhL1ljd2ZnTjByVG5mUEtkaE1mS3FrZFdVQUJ4NS9mcEViWjJFWTNnNWtZSndFMUNlVm9XKzRRK3RCU1BhMU4vWHNFWCthMW15RDZiMDQxaDRIYmMwQUZ3QXVLZTlqK0QzN3JhSGc2a3J3aUl5c0V5ZUR1RDJmN3Erd0JIa0FWQ0N5ZWhJbE90czJqSXY5K0FVVi9YcmZMbDZadG1vbko0d1FLR2xBbEZnSUxGZ2RwRlFrektMTktKaTVzcy8wSzZwcW0xZmlNOHhoMXJOZFQxaE0reHlyM2hzR0tSaVVLWnRvcmNuc3hoMVJDNlVIL3I3YWlGaEh2QTBMRXV4RTl1OEZCdjFBSmE3UVhmc0ZyQi9zblZIMUh1Zm1IK2lxOG1heXh5NW1CMkFldSs1R1dvNU1pdkM0RjQzK1UzU3VkN2pVYUM5andoNDcyUFN4SVQ4TnVpbFNyZXpaQlg1blB4Q0tmTXlqdXJYYkczWlNCYkswbzdmenNhTGpuVzJPRDNVbndsRy9CaE55b1MrSTVNSlFKbVFlNnB2M2JZbGY1eHFlSStJNWVBWjdBYU9ubWUiLCJtYWMiOiI2NjE3YzM1ODBlMDkyMTkyYTU0YWIzYThjNmVhZjY4NTBjZWYyYWYwYWE2ZDhkNmU4OGYwOGNlNDcxMGY5ZDFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1UczlzSG5CZ1BhODVWK08zOHpaZkE9PSIsInZhbHVlIjoiaGp5VmROTmJGYngyVzU5K2RvbWRjZUt5Q0RFUm9UMkZhSWpaSy8zMisrcVp2OVZnTCtFbWtJelJBVmdobkt0VGs2SjFHMW5BM1pveTB5bk5WQlNXN241M1FBbFhCRWRmVWZmelVSZzJybWhSeXkyZm5sdnU0RGhyZ0N4MzJTS3R6RlZLRmRhUjV5dkxmSnFnYkN5RjNGeDlFTUJqbU1Ya3dzOCtyTkJCV3ZPYmU2UC90UGZiQVIxVVBTTXlXdmhNOVE2MytjK01sY21iN25mTTdJekFyZXh5VlFJY3NlQ1B6eE9ZMzNSWm5Ic1VlL0pBL0ljQVR0OTJ4OFIxallqeHhhYnUwLzBqTXdndG56dnFGR0plNS9ub1VCdk9HaEQrRXlvYXJjNklMSkxLNjFJRWFGK3d0WkdSVFVMNUIxVlBkaWhGdVMyVlc3dzVWdmNoNTFIa2FJVVgrMGNScnJhU2RVVWlyNi94SjZtNGJkZGc5MFZqRUM4TDFPZktWZW9UcldaUXNJalEvcEl3THozZEtFcUlJNWZiZkFtMTBQS1k3ZHlBaEVud2FuL0E3MXdYdVAyaTVTN0hFSmI2V2xkb3hnLzZuR0RCM0tMWVZpSW9sWjJCZXkyRktnbDFVS3VLUXN5bHB0cWY0QXlXenc0OW9FTXYvYk9vYllWMlB5elIiLCJtYWMiOiI5YzlkYjNkNWVjNTJhYjc1MTEyZjY4YjEzYjI4ZGUyNzMyOGQ0NmY5MzAxODlhZWJmN2Q0ZDU2MWE1YTNlNmYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1447122639 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZPVUVUd2pWRHFkUDF2SDlFMExlYmc9PSIsInZhbHVlIjoiYlVDeWVtcDl1d2dYZHp0SWdSVmJWSEk0ejBzYzc4dkdOMnp2SHF3Tlp1LzJ6cXNKZFZmb0VXRkJ0WEN5NnVtWEVYRkc2RVFEZmZWR1ZobHpWdDNMOEU3eXVaNjc2b3ZQT1hsYnJsRDM0ejM1RThpdEpGV1BobU1pZzV0ZmNoM1RqaXUvTHlmc3RkL1BYU3lpdkNpU2ZTeVBmaWgvVjdBcURVQmc5K2VwSnI2Sm0xS1d2dklqaWZVWTRkMVhrN2lmMDRIREYxR09WUkhRYm5EMlppWC8wT25qQWYyVnZUT1RVZXBFWk8rK3ZaNS92K25kZlB2WTN6V0FZTGU4T1dXN1lGWmtwZUM1b0d1elZaZjIzQXlsWUVXbnlBQkFmRTVvU3RhbXByOWo3R2xXNmwyQnQxMnRoankxajFDVmtmZ0FKdmdJV0dLUWtmRWVRckp3andxTzg1Vlo5K3lJbTlTU2ZzbGJrMWZ1Uzd2aURFdHVRZ2d1NWlrRVRxaFc0eWgvY3cvV1pyQ0N6ZkdxTGo4NlJ5QnhvVGswMitBRDN1TFdZVzI1aDhOS3F0ODFUbU5lWkYzMEpZVmhpam9qN25Md3MxQkpUbkNaQ04vZm14Y21kZ1J0dGY3ZHk1eXVlbHJLZzBrazI0M3BCR0w4V2h3eWJNSThENEFZTnRIS1pXOWoiLCJtYWMiOiI1YTAwMThmZjk4MTVhMWQ3OWJkN2I5ZGMyMWJiMTZlOThiZjE1NWY2ZmI1Mzg0OTNkM2ZjZDMwMWQyNDYyNmRiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRwME4zMWhWMDh3ZEh2Z3VHV0Yxa1E9PSIsInZhbHVlIjoiVUlQVURVejNqTTJSOE9oOFBxdHc0Z1ZCc3N5UUR0K29Dazk4RUxvT1QvYjRXSlRadWNOSnZOcktFaTMyaE12Wm5xYkpFUG5FRXVJV1hkdTJzNTRpeGZia1NaYVBtQzMrSUJYc3g2N0RnQi9LalFhV0theElXSWwvZ0tvREpNUEVvNEdDTGpHNlpSNnh4WFNzTjJvaHJJQ1d2aHd3V0tWS0IrcmgrMGVHczZFUlFyMmhMM3FINHQxRVBhOGVFWFZGOVdxNm1Dd3d0VXIvNVlsQjE0cE1FdWoydndUUEJxTE5hcTVFOUhtdFBwNTJKcHBTMzJVNnArWUZmS0Vpem85RHN0STMyM0dZeEFTYkllc0JwMm9oMmtscVlvQ3BySndwKzc3Z3F2aFZDVjBjVzFKY01EcC8zbXU1dHh5R3FXUmdRbVVPNm9mNURjRWRkWUczZndOZHV0amh4YTRpZmhaYlZXNnZaZnFKZmxvQnlLTyszNGg5SG9FMVBkSlhhRGtxY0ltNTFnL0lDZ3JpZWFmSEFFOHJVYzZiYTV0TWpIV1IwWWdnc2NNSTRJdDU1cVFxVzdQV3M2TGsyUkJFVnNBRGFtRUl0NFBhZmdyMi84ZWd3bTFIM0ZOd1BYYkdLRjVUOEd3WkZ4ZDV6UmgvaHpva1FUbXVvdStobWNFTkNZZDciLCJtYWMiOiI0OGFlYzQyNjRjMWQ3YjU4MDgyNWIwMTI0ZTIzY2M4OTkwZTEwNTUyNjJhNWE5ZTZiNWQ2MmZmMDQ1NTViNTFhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZPVUVUd2pWRHFkUDF2SDlFMExlYmc9PSIsInZhbHVlIjoiYlVDeWVtcDl1d2dYZHp0SWdSVmJWSEk0ejBzYzc4dkdOMnp2SHF3Tlp1LzJ6cXNKZFZmb0VXRkJ0WEN5NnVtWEVYRkc2RVFEZmZWR1ZobHpWdDNMOEU3eXVaNjc2b3ZQT1hsYnJsRDM0ejM1RThpdEpGV1BobU1pZzV0ZmNoM1RqaXUvTHlmc3RkL1BYU3lpdkNpU2ZTeVBmaWgvVjdBcURVQmc5K2VwSnI2Sm0xS1d2dklqaWZVWTRkMVhrN2lmMDRIREYxR09WUkhRYm5EMlppWC8wT25qQWYyVnZUT1RVZXBFWk8rK3ZaNS92K25kZlB2WTN6V0FZTGU4T1dXN1lGWmtwZUM1b0d1elZaZjIzQXlsWUVXbnlBQkFmRTVvU3RhbXByOWo3R2xXNmwyQnQxMnRoankxajFDVmtmZ0FKdmdJV0dLUWtmRWVRckp3andxTzg1Vlo5K3lJbTlTU2ZzbGJrMWZ1Uzd2aURFdHVRZ2d1NWlrRVRxaFc0eWgvY3cvV1pyQ0N6ZkdxTGo4NlJ5QnhvVGswMitBRDN1TFdZVzI1aDhOS3F0ODFUbU5lWkYzMEpZVmhpam9qN25Md3MxQkpUbkNaQ04vZm14Y21kZ1J0dGY3ZHk1eXVlbHJLZzBrazI0M3BCR0w4V2h3eWJNSThENEFZTnRIS1pXOWoiLCJtYWMiOiI1YTAwMThmZjk4MTVhMWQ3OWJkN2I5ZGMyMWJiMTZlOThiZjE1NWY2ZmI1Mzg0OTNkM2ZjZDMwMWQyNDYyNmRiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRwME4zMWhWMDh3ZEh2Z3VHV0Yxa1E9PSIsInZhbHVlIjoiVUlQVURVejNqTTJSOE9oOFBxdHc0Z1ZCc3N5UUR0K29Dazk4RUxvT1QvYjRXSlRadWNOSnZOcktFaTMyaE12Wm5xYkpFUG5FRXVJV1hkdTJzNTRpeGZia1NaYVBtQzMrSUJYc3g2N0RnQi9LalFhV0theElXSWwvZ0tvREpNUEVvNEdDTGpHNlpSNnh4WFNzTjJvaHJJQ1d2aHd3V0tWS0IrcmgrMGVHczZFUlFyMmhMM3FINHQxRVBhOGVFWFZGOVdxNm1Dd3d0VXIvNVlsQjE0cE1FdWoydndUUEJxTE5hcTVFOUhtdFBwNTJKcHBTMzJVNnArWUZmS0Vpem85RHN0STMyM0dZeEFTYkllc0JwMm9oMmtscVlvQ3BySndwKzc3Z3F2aFZDVjBjVzFKY01EcC8zbXU1dHh5R3FXUmdRbVVPNm9mNURjRWRkWUczZndOZHV0amh4YTRpZmhaYlZXNnZaZnFKZmxvQnlLTyszNGg5SG9FMVBkSlhhRGtxY0ltNTFnL0lDZ3JpZWFmSEFFOHJVYzZiYTV0TWpIV1IwWWdnc2NNSTRJdDU1cVFxVzdQV3M2TGsyUkJFVnNBRGFtRUl0NFBhZmdyMi84ZWd3bTFIM0ZOd1BYYkdLRjVUOEd3WkZ4ZDV6UmgvaHpva1FUbXVvdStobWNFTkNZZDciLCJtYWMiOiI0OGFlYzQyNjRjMWQ3YjU4MDgyNWIwMTI0ZTIzY2M4OTkwZTEwNTUyNjJhNWE5ZTZiNWQ2MmZmMDQ1NTViNTFhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447122639\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1840113852 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>287.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840113852\", {\"maxDepth\":0})</script>\n"}}