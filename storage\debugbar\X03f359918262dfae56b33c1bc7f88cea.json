{"__meta": {"id": "X03f359918262dfae56b33c1bc7f88cea", "datetime": "2025-06-30 18:37:32", "utime": **********.904319, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484559, "end": **********.904334, "duration": 0.41977500915527344, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.484559, "relative_start": 0, "end": **********.839998, "relative_end": **********.839998, "duration": 0.3554389476776123, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.840007, "relative_start": 0.35544800758361816, "end": **********.904335, "relative_end": 9.5367431640625e-07, "duration": 0.06432795524597168, "duration_str": "64.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872608, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02455, "accumulated_duration_str": "24.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.87043, "duration": 0.024149999999999998, "duration_str": "24.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.371}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.897635, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.371, "width_percent": 1.629}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1287 => array:9 [\n    \"name\" => \"KDD حليب شكولاتة 180مل\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1287\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1013848082 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1013848082\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1607811439 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607811439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1079990507 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1079990507\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308318634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlUyamJPbGhUK2JQMW1lRzJPYjhmTnc9PSIsInZhbHVlIjoiMHoyWE56TzlDMXRZUDduMjdjRGZsUVU2cjZtU09YUGk0STZrU2xqSzJ4aWZRbUc2OEpNMXBmbGl1OVJWRzRaVGhndEhCKzBJSXU5N1pHRzJFa0F0Y0hXMFJFL0x4dUNKbWs1Wk9GODVXZTBSZGUwL1lFTkthQUlSZjQ1cVRzVys4VnZVdmFCNmk0cWVRcXROL1hwS3ZlZ29qYVhsZmI5dXpQM1B1ODNQdjFKd0kxdjdQMndXdzlQMnZndjFUQ083bUMvN1hGNXNjclZleVFzYlJ0WDE2TTJEaHNNK1VJYTJ5dVk5YnVuU0phWis5VitCWXNWMlVQNUxWQkJTWWtTWU12WVlkbjlrQllSWVJWWXlPZUhHaGorall4UHJ0ZVFuS055REpqZ0c5K1FVTDlMeGE4c1lFeGZXR2p4ZGh4bHVHaUs3bWY0anNwamFGR0habncva0hTdXhYTlE0a09zRUIybEgzUENzYzZ5WE53RDBXSXFvWXA3cHRqWVhtZndDQlhuazZZdnYyY2wrMUUrd24zOUE2U0IxRkM2UUxya1ZSVW5Udnl6cCswK3NBZG5RTW1TZTk2enhDbWd0SldCODd1OEw0VFFKZzdnOE83L0hLbzErWnRkVWNGUTZYbndleEgxdjUvVlhjZFZ4c1hiYWNQdkFyaHIvZmRWdmpMcnkiLCJtYWMiOiI4OWRiMWI0ZDNjZGNkM2FmMzJhNDBmNTgzMjI0ZDM2ZDUwMzM1MGQ4MWY5MzA2YjIxNjJlZTAzMjE0MjAzODY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5jVzVGWW54YVAvMDJoSjhBd20rVkE9PSIsInZhbHVlIjoiandMTW9jTmNIcXBzZHgxTG00S0syNDhIOUJSSEZNcit5blRaaDluT2JPQ0QyY29HemU3NkJFUWN6UWxTTFNna3RIOVphNi9ueXFwaUROejB2cUR3Qm9NaUhXRzY2RTRNMjRDQ2FZRFVUcnY2elpnT1ZHVWdKNlhZRkRCN1Y1ekpTYVQ0aUNGMS9PRFFjZFpZMFNKUDZrekxqdjNIRnlBQ0Ribnl2SW9UL0J4NkpJNHJDLzB1T0NMa1NDWmhUaExqelBTR0JIMHRLZVlDQVc2WXNvandkK1ZjZE1sZDF3OUhSTnZxVWcvLzJmWjE4VFFVUlJ1eTE4TWV5ZGh4K0NlZjFsajNjb2Vqd3h3Tm9JeCtrSnNqbG5NYlpWSERDc2c0a0l1MVZ3RURyRUZETmIrNkVZREcrc0VIckJEU2lxOTlsRXB3Y0ZmZm42WWlNRnBkNUJGUkUyaFFhZzA2VEFYc0xMRkw3VklPYUhkV05yNFhhUW5naVlBVzdiUnVZNUcxbFFxbnltdUFVclU5Z1kvYTNoZ2R1T0c0aVI0M2ZrdTh1bmtveHdoWExuQ3JLaWx6REdCbW0wTGRvWE40WFFRZC9FeGNtN09Ga05yeE5Hd3hSMEJLVmc4MnZSUURWTVNmUGVnUlRVMTlGQ1grd3lMRGE1N2NGcSt6MU0reWUxUEYiLCJtYWMiOiJlMGM3OWJkYjJlMWNmZWFlZjI0ZmUxODUwZmJkY2YwMmU3ZDBjYzM4ODcyZDkxZDUxOTZkNWE4ODBiNmRhMTZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-850149236 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:37:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBWVnNJMllSbVR2TlRWQ21OYVY4aVE9PSIsInZhbHVlIjoiZStGeDVUWGFKVmxFQVhqMG9RWEJHNnJrdWE3NHpWUFRNUlJQeURCbld0TzBjbittWlQxZk1ldkNGU2R6QVhhL1lVSHVUdlNhNFFUVlcrZXFRRlZHSXl2aTI5SXFUd3FWSCt2RmVQZS95MU9EdDlXcmRzTHZaMXFSY3duWld1aEJ6cjdneFpwMmJRNFFpR3ptMEFFQThOVDJ5cXFpSXlIOGdoTmVIdnNlVXdDWkZHeENlZDU4QitxaElMTGYvQVNtZ2wzdmQ2Vk81Q045aDBnQTJUMkZONmt3YW51SFgzU290Njhyc3FOMFQwZHUrWHZCb3ZSOFlhVXZ4QnFYSHoyVk92TmthTVUzNURVWXVRdlRoTUYwbnUrTU41TDJFVjBHMjBUTUFHK2I0U0NMcDhOZjNHT1RvVk52SnpkT1V2ZzZmZlAySmpseW9DdjZ1dVBlcmsyT3ljcFhtS3Zid2ZPbk9JNG9jbjVLT3RtTmxQd1hJN2JvRUNROHpxWis4ZzNaRWt0UWx4QTBWMnk1MVIzL2w5S1YxdW5ENStlZ0FkUWZCTUZkZW81VjhZVkJCV1A2b3gybWNmVTdYQWpBQ1ZvY2poL3lHYTN2Yk9jcDFDdFdyZUpWdmZvNFI2WTR3QTJCalZEd1M2VXRHZUFQNS9WK1c4SXBqOVNuUnppaFRiUkIiLCJtYWMiOiIxMTE4OTUxZjI1NTk2ZDMwNGMyMGU2NTEwYmNmYjdiMDE0YWE0ZTk4NTRiNzg2MDdiMzY2OTA4YmE5ZGRlMTk5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:37:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1FdFhzNGZCeWIySjZ0UmZtTnBGRkE9PSIsInZhbHVlIjoiS0dCVlVkaFdwK3FPWXJQVGN6Ti9ialY5YTJKODZ4dTdOZzlSQ1pOd05XUkNTQkNTU2NaWmFsSTFQS0d3akNsODBWYVBGMWVJWnc1NFRhZVdNZHBsUFRHTy90NWJKUnRrS0FhUnZMMXJGOG1MRUplcTViN3RjRTg2RGtwM1VIWjU5bEFWS3ZFR0d1ZHRLWmZhWllzeFR0VHBhenlIbW5naDArd0s2Ymx5a0RFVy9tbERaSGZ4cDN6MnlPcXZ6bmlBUHh6YWwyYUNMdTFyWE91TWRmMnZlSlNhdmpRS29vV3VJaUhIQnlOb21iRXNXOWNIVDJwbTZTemx2am1JWEx5WG5aeDBjMUM2RVQrMlc1bEt3QjJGclYzZFBZUm9oS1VyWnd0UkhGaUxQN1p3Ym9aRlZTVm0va3VsbXArbk0xOUQ1c0hUang2ZkR0N21OenJZS09RQkhyK0l0QmNZSmtXcEQrRWtPbms2cEYvM0hzdjFla3ZnbklMT1RTZHhUWlZHa1hUdnR5TU9yK2xTemg5UVYxSVZ2Q2xNR2Z2MGg5QTg3Q0tNeW5jWEYvS0lpY2tjcDlVYnZZOW82c0tkNnQ3NjhmRHpNNHlzMTlsazFtMnVLWUNTSlFvV1lsV1Y2YkNqcVExbU9QeVZKRG9RanFKaG9NTDh2d1JCN2x5TTlWT0giLCJtYWMiOiI3OTM2MjFkNTUwNWE5MzNiYzI4YWJlYWI3NGU0YTFkY2ViNjg0NjhjYjUxNzEwZDBkZWVlOTIzYWFhMjI2MzJkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:37:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBWVnNJMllSbVR2TlRWQ21OYVY4aVE9PSIsInZhbHVlIjoiZStGeDVUWGFKVmxFQVhqMG9RWEJHNnJrdWE3NHpWUFRNUlJQeURCbld0TzBjbittWlQxZk1ldkNGU2R6QVhhL1lVSHVUdlNhNFFUVlcrZXFRRlZHSXl2aTI5SXFUd3FWSCt2RmVQZS95MU9EdDlXcmRzTHZaMXFSY3duWld1aEJ6cjdneFpwMmJRNFFpR3ptMEFFQThOVDJ5cXFpSXlIOGdoTmVIdnNlVXdDWkZHeENlZDU4QitxaElMTGYvQVNtZ2wzdmQ2Vk81Q045aDBnQTJUMkZONmt3YW51SFgzU290Njhyc3FOMFQwZHUrWHZCb3ZSOFlhVXZ4QnFYSHoyVk92TmthTVUzNURVWXVRdlRoTUYwbnUrTU41TDJFVjBHMjBUTUFHK2I0U0NMcDhOZjNHT1RvVk52SnpkT1V2ZzZmZlAySmpseW9DdjZ1dVBlcmsyT3ljcFhtS3Zid2ZPbk9JNG9jbjVLT3RtTmxQd1hJN2JvRUNROHpxWis4ZzNaRWt0UWx4QTBWMnk1MVIzL2w5S1YxdW5ENStlZ0FkUWZCTUZkZW81VjhZVkJCV1A2b3gybWNmVTdYQWpBQ1ZvY2poL3lHYTN2Yk9jcDFDdFdyZUpWdmZvNFI2WTR3QTJCalZEd1M2VXRHZUFQNS9WK1c4SXBqOVNuUnppaFRiUkIiLCJtYWMiOiIxMTE4OTUxZjI1NTk2ZDMwNGMyMGU2NTEwYmNmYjdiMDE0YWE0ZTk4NTRiNzg2MDdiMzY2OTA4YmE5ZGRlMTk5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:37:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1FdFhzNGZCeWIySjZ0UmZtTnBGRkE9PSIsInZhbHVlIjoiS0dCVlVkaFdwK3FPWXJQVGN6Ti9ialY5YTJKODZ4dTdOZzlSQ1pOd05XUkNTQkNTU2NaWmFsSTFQS0d3akNsODBWYVBGMWVJWnc1NFRhZVdNZHBsUFRHTy90NWJKUnRrS0FhUnZMMXJGOG1MRUplcTViN3RjRTg2RGtwM1VIWjU5bEFWS3ZFR0d1ZHRLWmZhWllzeFR0VHBhenlIbW5naDArd0s2Ymx5a0RFVy9tbERaSGZ4cDN6MnlPcXZ6bmlBUHh6YWwyYUNMdTFyWE91TWRmMnZlSlNhdmpRS29vV3VJaUhIQnlOb21iRXNXOWNIVDJwbTZTemx2am1JWEx5WG5aeDBjMUM2RVQrMlc1bEt3QjJGclYzZFBZUm9oS1VyWnd0UkhGaUxQN1p3Ym9aRlZTVm0va3VsbXArbk0xOUQ1c0hUang2ZkR0N21OenJZS09RQkhyK0l0QmNZSmtXcEQrRWtPbms2cEYvM0hzdjFla3ZnbklMT1RTZHhUWlZHa1hUdnR5TU9yK2xTemg5UVYxSVZ2Q2xNR2Z2MGg5QTg3Q0tNeW5jWEYvS0lpY2tjcDlVYnZZOW82c0tkNnQ3NjhmRHpNNHlzMTlsazFtMnVLWUNTSlFvV1lsV1Y2YkNqcVExbU9QeVZKRG9RanFKaG9NTDh2d1JCN2x5TTlWT0giLCJtYWMiOiI3OTM2MjFkNTUwNWE5MzNiYzI4YWJlYWI3NGU0YTFkY2ViNjg0NjhjYjUxNzEwZDBkZWVlOTIzYWFhMjI2MzJkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:37:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850149236\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1287</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">KDD &#1581;&#1604;&#1610;&#1576; &#1588;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; 180&#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}