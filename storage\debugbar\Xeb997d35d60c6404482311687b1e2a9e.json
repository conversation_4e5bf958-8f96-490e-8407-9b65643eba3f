{"__meta": {"id": "Xeb997d35d60c6404482311687b1e2a9e", "datetime": "2025-06-30 16:10:14", "utime": **********.867519, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45408, "end": **********.867532, "duration": 0.4134519100189209, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.45408, "relative_start": 0, "end": **********.814677, "relative_end": **********.814677, "duration": 0.3605968952178955, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.814686, "relative_start": 0.36060595512390137, "end": **********.867533, "relative_end": 9.5367431640625e-07, "duration": 0.05284690856933594, "duration_str": "52.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45125008, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024700000000000004, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.844937, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.348}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.857429, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.348, "width_percent": 15.385}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.860602, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 77.733, "width_percent": 22.267}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1545 => array:8 [\n    \"name\" => \"غور\"\n    \"quantity\" => 1\n    \"price\" => \"1.00\"\n    \"tax\" => 0\n    \"subtotal\" => 1.0\n    \"id\" => \"1545\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299801140%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRjWmdlbjBSdENNYVUzeUVaRzR6NkE9PSIsInZhbHVlIjoiUTJjcFptZ1dSdXRKd2toMTdOYnZpMjNCcFJteU5wUUt1NktkdzJLaks5YTBUbUwxbTRtckpaSXMvaUNrQlc3d3hTdjBnS1VIbmd0eEdtVkNOcGRDN291c3A5U1kvOG1tbUlVcDFiMmR1Q21KcmZkTGJ5QnppUHppcTRsTHE3aHh2TVlFaFc4VTNUNGZtZzdITmxxZjcyTVNyZjJHYjhWYmxSdzhuRXBoZHo1WSt1VTdtdzZzdE9Mc21nMGRPRzdKc3o5NFpoLzl3WDZsTGZEZi9adzZFanNYSDBTczczRisxV21YRHAzMjJpKzBtWFF4TlpwZGgzNHVpaFRwRW9EaGF0V0VGNlNBWnU5REQzZlcxWGFUcmZlbHlpdTl3Y0YvTmg2bGNxVVRmNUpvZ2VSdFFsZU1QSUlPN3JXcnR1UVZOemdyazRqd2I3ajNSR2MzWFdPQkN5TzdjblBMRjNwVlZUR3RieWE1cnIwN3U3UzVyRjF3dDlDcTQxaU1mcFpyTzRFMGtBclIwYWpieTZibitxTDF2MlZOTjNqN1Z1M1pSdk1XZmdFRFJlckdOVys0b3MzZnFDKzFiL1JPemdyOVdEWDYwTDdRUDFGNkhsN0ZDMlIrQzRsYXBPTkZyRFpZL0VxcGFFd2lFaGhsSWJjWUUvWFJnV01FQ3BaUDY5WmIiLCJtYWMiOiJkZWVmNGE4NDRiNDJmZjQxZDZjYzkwYjNhY2E5ZjMxZDZhNmNjNTA5NmZhZTVhZjVhY2FlNDU5OTYzMmY4OGMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJxVnZaQXZxUi9LOG1OL0FrbFRsSFE9PSIsInZhbHVlIjoieTBBUkdEY1ZYRjFVQ2pRekkvYzd4cHdEOGNjOStEM0pUeHg4ZW95ODBpWForRXVib3ZUVC9EZFZ6d0FiNjNJQVhRODJSUXpUSWZvczk3bW0wb0JNSy9PYVg3WCtWR3FrVXRDRmpiekozMUhwR0JHVzhwZjlQNllCOTBhWFM4Z0dPdXI5U2ZrZUxNL2xTTWNPWVpySWxiYWJuRXlaRzlqUFF5SDhicVpJS0ZMWVUxNnNzM2NqR25EbmRjYU5td1hSb2RkWGdTbFArekVHWTl3NHRoelRHMWVjam9pbnlnK3JUMWgyRkFrcmUvSW4rQWNYY1BGNUVFUldNNHNrVlhSbm51WEpZNG1ER3BVa053SkQzNG0vY3RreUxQSk1zUUUySVR5N2NZY0hiM21IT3ZQcXEwZCt6dzN4a0NCY0Q3V2gvTGwxTy9KYVloRUhzOFZxYzlmSlRPbzhOcE04ZmcxUUZjV3V5eXlNeU9hR0ZMY2hLdEZxTlpqRUVIU0taaXB2Z3JXSHd5bUxxQjJYZTZQZFA0OVpxdjBUaTBjV2ZxZWxXZXorUHliSWJ5SFdtaENrRVJlb3FoZUFwMDVzRHlFbHZrRUpueXQ4QnRCd0JSWHJETm5XZ0lwVm5rVnNCY1dGeWxwZU9DVGx0aEo1cVZTOWlTbkl5MjFQWXVuQncxQjgiLCJtYWMiOiI5MTRlZWUwYmNmZGI1ZGI5NWQzOWU2MDNjMjY4NTU4OTg2ZWYxZThkZjZlNjdiM2QyNjlhM2RmZjkxMGI5MmExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1264415440 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264415440\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-772501432 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1VRmpYSkdTbFltUVpLWXI0aDBKNGc9PSIsInZhbHVlIjoiMzloMGtIVnZVeDVOeDJaZ09QNGV2OXJodUZWdkZZbXBqUzZRTEl4OWhuVlVTaXVROVc4L2Y1bG5pRkl0QW1jN0R6RmxVemhIRGVnaVVSaVRhbXdwZmZ1bldZdmJCcTRTNGEvT0plTkNQRTRvbitPOXRkRTRCcE52M1U5QjJWdk9wNFBiUlRjRm5SMkZqRVJNeVY2QmNpZ3dqYkxTQWRtcmJvb042QkZvb2VjWXFhVVZod00ySHV4emFTKy9SeDVpYUYva1J6MTRRbE5hM3Z6UG0vVDJvZUlGN21FanA3Z2NZb1ZiNU9OQWxhL0tGelIxaVBHYzR2WFE5d0svdHBsOSsxNjBtRHNDUVlRQ0t6UHBUUFZnSGhhaHE5YjFvNmxaOWM1REMzWktOdy95WWpnTkZITzZIYkFlUTRMSE9taVdkM1d4dXFPWitlWEJZdmVQS3JVbU0yNUEyTXprVTZtQUVvVXhtbHpqcEloMFBCd0lRWnJldHQ2S01rTzdpSFd3NVE3MG9zWjlwdzRSTFp5blkzdXpDWmRHYlk1NmRBbGxNMnhFTDhXUmM4ZHZuOUp6V0p2M3loK0tsRHBwUzhRU3diNXNWOHlTMUc4NXR6MHBjYUV6eXMzaWF3dFhya3oyRGxIRnZyaWhrQWtMU05ERWM0Q3I1YUtTZ2VneFNJalYiLCJtYWMiOiIxN2Q0NGU2YWNmZWQwYTlhZGIyNzU1NDQwYjAxNmFhNDM1NTJmNGQ1NDFhZjEyZDhiM2Y2NDVmYTM3MjJjYmIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBCNTFsWlZuMFFvdFNMSmd3WUxLYVE9PSIsInZhbHVlIjoiQkNST28xUmxjWkxqaGdCaVFWOHJ6TnI2SXhaL1dJUTNYVm5vdDcvVEhjOGo5emFxSEFQM3YySTN1bTZVNVFwbUtHcnB5TWdTM1hzMnNJcCtFbWQ3SnJrMFdxb2tBM0MzcUxkK1lickZJSHB0WGxoZUgwemUxWGg1ei83eGVsc2J6MW9MUGdTVzBSMWdwblQzV0Y2c3V1TUgveDZLZzl3NGhSQlVMaVM0RzBWVDkzWElWWmZnYis2Tm9XRWxVNXB1SC9LYzlqaXlhbkdoVGwybThQdEs3VWxVTGFhVWxESjNPSFZjbGt6YUlxQTRwWWEzTlRtZ01pMGtiaXUrdFdRL2ZFSXdIbWo4Mlpaa0xsWGJ2UTl2L1JBbFV3akdFQ0RVajBRZExiaEtzODJ6Z1g2MldxNWxkeU05blNJYkczWlBIVjNhOTNFdThxTmFQcW5QS3ArYzJwaEsvcnhhSytZZ2NVdGRPanYzS1FOaXhONHR3TGd5UWs3R1M3ZHZGZi93MkZITVUvNFdTSmxxcjhvN25UcjFLRFR6Zmt0cVV2RkdjMkpPUXNGVDRSdEg4NzVjL01KbnVKdndEYlptd2pKVWxOSVBYb2trT0lFUVZXZmJNZVZzMExxaWpnTW1WVmJKQitlbWNnd0tNMC9pRmpLdlVUV2Q2VU50YUVOSjVsa3MiLCJtYWMiOiI2YWIxYmQxNmY4OTZiYWIxYmY0NmFhMzcxYTY5NWE5YTNmNjU0NDEzM2M5Y2ViYWQ2YjQ1Njg5OTU5NGNjZDQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1VRmpYSkdTbFltUVpLWXI0aDBKNGc9PSIsInZhbHVlIjoiMzloMGtIVnZVeDVOeDJaZ09QNGV2OXJodUZWdkZZbXBqUzZRTEl4OWhuVlVTaXVROVc4L2Y1bG5pRkl0QW1jN0R6RmxVemhIRGVnaVVSaVRhbXdwZmZ1bldZdmJCcTRTNGEvT0plTkNQRTRvbitPOXRkRTRCcE52M1U5QjJWdk9wNFBiUlRjRm5SMkZqRVJNeVY2QmNpZ3dqYkxTQWRtcmJvb042QkZvb2VjWXFhVVZod00ySHV4emFTKy9SeDVpYUYva1J6MTRRbE5hM3Z6UG0vVDJvZUlGN21FanA3Z2NZb1ZiNU9OQWxhL0tGelIxaVBHYzR2WFE5d0svdHBsOSsxNjBtRHNDUVlRQ0t6UHBUUFZnSGhhaHE5YjFvNmxaOWM1REMzWktOdy95WWpnTkZITzZIYkFlUTRMSE9taVdkM1d4dXFPWitlWEJZdmVQS3JVbU0yNUEyTXprVTZtQUVvVXhtbHpqcEloMFBCd0lRWnJldHQ2S01rTzdpSFd3NVE3MG9zWjlwdzRSTFp5blkzdXpDWmRHYlk1NmRBbGxNMnhFTDhXUmM4ZHZuOUp6V0p2M3loK0tsRHBwUzhRU3diNXNWOHlTMUc4NXR6MHBjYUV6eXMzaWF3dFhya3oyRGxIRnZyaWhrQWtMU05ERWM0Q3I1YUtTZ2VneFNJalYiLCJtYWMiOiIxN2Q0NGU2YWNmZWQwYTlhZGIyNzU1NDQwYjAxNmFhNDM1NTJmNGQ1NDFhZjEyZDhiM2Y2NDVmYTM3MjJjYmIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBCNTFsWlZuMFFvdFNMSmd3WUxLYVE9PSIsInZhbHVlIjoiQkNST28xUmxjWkxqaGdCaVFWOHJ6TnI2SXhaL1dJUTNYVm5vdDcvVEhjOGo5emFxSEFQM3YySTN1bTZVNVFwbUtHcnB5TWdTM1hzMnNJcCtFbWQ3SnJrMFdxb2tBM0MzcUxkK1lickZJSHB0WGxoZUgwemUxWGg1ei83eGVsc2J6MW9MUGdTVzBSMWdwblQzV0Y2c3V1TUgveDZLZzl3NGhSQlVMaVM0RzBWVDkzWElWWmZnYis2Tm9XRWxVNXB1SC9LYzlqaXlhbkdoVGwybThQdEs3VWxVTGFhVWxESjNPSFZjbGt6YUlxQTRwWWEzTlRtZ01pMGtiaXUrdFdRL2ZFSXdIbWo4Mlpaa0xsWGJ2UTl2L1JBbFV3akdFQ0RVajBRZExiaEtzODJ6Z1g2MldxNWxkeU05blNJYkczWlBIVjNhOTNFdThxTmFQcW5QS3ArYzJwaEsvcnhhSytZZ2NVdGRPanYzS1FOaXhONHR3TGd5UWs3R1M3ZHZGZi93MkZITVUvNFdTSmxxcjhvN25UcjFLRFR6Zmt0cVV2RkdjMkpPUXNGVDRSdEg4NzVjL01KbnVKdndEYlptd2pKVWxOSVBYb2trT0lFUVZXZmJNZVZzMExxaWpnTW1WVmJKQitlbWNnd0tNMC9pRmpLdlVUV2Q2VU50YUVOSjVsa3MiLCJtYWMiOiI2YWIxYmQxNmY4OTZiYWIxYmY0NmFhMzcxYTY5NWE5YTNmNjU0NDEzM2M5Y2ViYWQ2YjQ1Njg5OTU5NGNjZDQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772501432\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-34874942 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1545</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1594;&#1608;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1545</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34874942\", {\"maxDepth\":0})</script>\n"}}