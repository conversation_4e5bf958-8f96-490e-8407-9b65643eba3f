{"__meta": {"id": "X05160b33770f9ed7f69d3cdcc7d4b83a", "datetime": "2025-06-30 16:06:56", "utime": **********.228087, "method": "POST", "uri": "/branch-cash-management/close-shift/59", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299615.797598, "end": **********.228105, "duration": 0.43050718307495117, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1751299615.797598, "relative_start": 0, "end": **********.157886, "relative_end": **********.157886, "duration": 0.360288143157959, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.157896, "relative_start": 0.36029815673828125, "end": **********.228107, "relative_end": 1.9073486328125e-06, "duration": 0.07021093368530273, "duration_str": "70.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45692464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST branch-cash-management/close-shift/{id}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\BranchCashManagementController@closeShift", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "branch.cash.management.close.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=151\" onclick=\"\">app/Http/Controllers/BranchCashManagementController.php:151-193</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00425, "accumulated_duration_str": "4.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1940699, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50.824}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.204002, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50.824, "width_percent": 12.235}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 154}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.208314, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:154", "source": "app/Http/Controllers/BranchCashManagementController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=154", "ajax": false, "filename": "BranchCashManagementController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 63.059, "width_percent": 0}, {"sql": "select * from `shifts` where `shifts`.`id` = '59' and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["59"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 157}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209349, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:157", "source": "app/Http/Controllers/BranchCashManagementController.php:157", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=157", "ajax": false, "filename": "BranchCashManagementController.php", "line": "157"}, "connection": "kdmkjkqknb", "start_percent": 63.059, "width_percent": 8.706}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 16:06:56', `closed_by` = 15, `shifts`.`updated_at` = '2025-06-30 16:06:56' where `id` = 59", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:06:56", "15", "2025-06-30 16:06:56", "59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.211846, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:168", "source": "app/Http/Controllers/BranchCashManagementController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=168", "ajax": false, "filename": "BranchCashManagementController.php", "line": "168"}, "connection": "kdmkjkqknb", "start_percent": 71.765, "width_percent": 12.235}, {"sql": "select * from `users` where `users`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.213843, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:171", "source": "app/Http/Controllers/BranchCashManagementController.php:171", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=171", "ajax": false, "filename": "BranchCashManagementController.php", "line": "171"}, "connection": "kdmkjkqknb", "start_percent": 84, "width_percent": 7.294}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 16:06:56' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:06:56", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 174}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2154179, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:174", "source": "app/Http/Controllers/BranchCashManagementController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=174", "ajax": false, "filename": "BranchCashManagementController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 91.294, "width_percent": 8.706}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 177}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.221157, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:177", "source": "app/Http/Controllers/BranchCashManagementController.php:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=177", "ajax": false, "filename": "BranchCashManagementController.php", "line": "177"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/59\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/branch-cash-management/close-shift/59", "status_code": "<pre class=sf-dump id=sf-dump-831553668 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-831553668\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-385756125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-385756125\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1354993639 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354993639\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-953278751 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299611552%7C10%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhsQ0Y3QnlnVkpDR1BwMnBQQVZmVmc9PSIsInZhbHVlIjoiZVV3N2NqZVZ4ZTA3dVRXMk5XT0pCcUNzVGxrWGpzTXBLMUJkQjRnMEhSNENpM3h1WTI3Q3pIU1ZMNnk5eUFHdW1iajJCclpBclptSFhNRnpCeFUzUVpCYWl1TUdBYmNxN3NycVZBZyt3VHV5cm1hZFp3aVcvUEFlbHhnODlsdVRCZWZzRzNJY29YUHI0TWdtSFpSbmwxdHI2TE0xMFVLQnlyMEpqOU1JY0xhaVRQYW4xMTJZVWRpY1VuS3kvR2dGdzVPTWJRRXFlbERpKzRmWUdIOXVyOFk3ZkZ1dzZoNE9nNXozT2I3SFk2cFQ4clpXalNVMWV3KzFVQzBPMHRGcDNrVHVKMWxMRWoyb1BTY1Q3QjN2eGdvU0ZUdTR6UjJJOHlqM2M0S1o1YjNPejh2NVRrd2VJYTdhTXNDOGhTb2JtdGgvdHFpb0pNdXRqWld5enQ1M1hsKzZoRlBIWmdFd0h4TE55UlpqZ1d3eFA4d3NMNTc4Mk84SFBweTU0cXZVWTFmTVFEN3dSS2VFMC9wSkNKVFNxWDlVL2RsUlRNU0xuM3VrUXp5WHlxaWo0WElteXB6S2QxR2RRbVgxNjlUdm10Q1RmTEJkUUZjWXk1T0NxMlR2aGQ0eVFEOWxSRUs5TmROb0RYbEQremNKVU53VVRFR2QxSmFJY3VNYVoxSG8iLCJtYWMiOiI0ZGJiNDQxMmQ0MjJiNjI5ZGQyMDg3MDRiNDlkMjQ3ZDNjNDQ2NmIzMmEzZTJhYTY0NDMyNTQwMzFiZWE5OThmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpoRTAwckIzVXRxd1JlWS83NFFFZ0E9PSIsInZhbHVlIjoiVFZlckI0Nkt4Nkh6TGNWQ1FLTkFGTkJWVEFHUmExa3pqREQwYVhUdW16QmRsWUdzc3NjdDVTYkFUUnF5Y1dyc2FDS2dXZWFrOWhDYlFEZzliSUxsWWl1cU90WGRsSG9ITjRiM2VIVGlWR0J1NFFzUEhYRkhmVGJ6OHlOMHFSSnl3bHVDNlkyQjM5cjFrVWRlWmZWcWZXdHlmU3cvL3o0OHBUcnNHbm1zL2FKMCtYSmxYTWdLeGZWbTdvVVFyblZJcUgra0Y4NmdxUmhmaVBVRHFpTC9iYVp0djFQUWROUERiMXQ5eDlsZUxpR01HdlgxNVRyYzlGNm9YVDRpSEVrem1xQmZWdVpyc3Y1MGo5bjJzcXdwL1hSTUUwT2RxN1VSdUF1ekhlV1RNaVJaNkxJMVIwbml1NWkvM1p2WFJad1NyZEVpMm1vWEdPN2tMWHBpQXJxeTVIcWNEdktSaDhJbHduWGFuMUhkeDJ0NUx4cUtZQTUvbHJmUXNZMFYrS2U4VU9ZVDhWdmdtbkp5VkpuSXJGZ09kTno1aWhVMVk4UXVJZjZqN2c5QlZvQzY2ZTQzZkVTMmdWTlNxTGJyN1huQ3huNDdoVzJLOTN5WVlPWXJ2R0RQL2hQS0czOVJsWk4zcDNZOE9zRE5WQTFCbnF1Q0lLTmh1bkFIdG1sUHFHT3EiLCJtYWMiOiIzMmU5OGY5NWNkZjM4MjJmODMyYzk5ODczMTkwNGYwNmY0N2MxODY5YzAwYWQwMmQ0NTc1YjZiYzU0MGIxNGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953278751\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-687972480 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687972480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-81372006 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5JMk92R0F5TW45VHozT1IvRWgreVE9PSIsInZhbHVlIjoid0c1M3RQOVh1c1JZcG0yc05YN2diR05CZEo3b0V1UVN1WWQxTUxBOE9QQi9vTW1BMVdCaVkydXQ0VkFEamp1a0FDbXJnSU94OWtpN2IwcHl6UzFlR3lQR2xPWmg5a01NYlRHUFJxSzQrL25BUWJ5alIvK0hLODZLeWpMbjA1ZDFHU1dITUQ1aHZIQUlMSFRySXRLRmtUOHZwYVZnTVYzT0hTOEZTWHltcG1ZMmZBM1Q0cTVCNURrbWJndUNUK0NZRHJ3SElqcGF1V3hzTGRpdlZCZmRPL2dSSHpxNVMvdTNPa0lXb2RMK1pmTTIzbnJyQ29NUXdUSHNNM1hUa2xBeHNTdVVJeHNGT3QzdTdqN3dEUTVGbHEvN29PeWRTbzU1cjNsRXpYbFJwNFZWa1hSbWhpNVliMWhxMmJHYkhOSFNtc3FodThPSjFsZSs4ZWV2UjQ2RXphQVFQTFlvWHllcXZzaWJ6QTR0cG9XWHJobUovVUpNV2U0cWwwNjg2OTk2c0lDL2VreVlucGpsWFNsejFGUHBwdGNwWHAzamU3Rkl6S0dtNkZRTUt2VUhsUXM5UGk0MWJxMDNGYXkrU3NNU2dSZUZWazYwMGFxUDRRQWtnSks5dFZRVjhORnM1aWtkK2R2UVgwZ3ByYi9KTFZOQXhxN0hycGdSamt4bk55UkUiLCJtYWMiOiJiNzUwY2E1MWEyNzAzNTQzZWVkNDlhODNhMzNjOWM4YzI2OTlkNmRlMTBmYzliYWRlYmI5NDBiZDQ5OTRiODE2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijk3ZEk4Ykc2ekV2Z2Z3eTJwQncrSkE9PSIsInZhbHVlIjoiNVFlR2VLM0ZsMlNlMkJMNTF2QnQ3endBeit5c3p3aGsycnY5Q1pyRnJ4RDNXVGp4MUE0aFNTYUdaa095VHFLZlRoRFhZbkFtT3g3TTlhaTRFSHUzWDBBTkkreUR5eGJvMXdGd1NSbkJrSjFCMEVBdkJ0aFFwa20rUGd0VHlIOGNPZ0kzbkt0aDlsby9FbXI5RVFJT2tOWUczcTZNWm5oUkJnelhMd25LYndLUVNpWGo4ZzBOWDFqdHRpTG5hVDdnRXVNL1g4eTlzMnQ1azloNmxVQTJrakJnZm85czNnQ2lvWk15TENldzBSNVBvRVZONHhOSlhCK1FFcVM5eklmNWV4TllIYmRBUW9SQmpxemNXSEd5LzNsUEc4TTVMcnhiTjJ6ZWtHWVkzSi9MTlJ5TzNZeHkxc251REgxM3RFSU1KNUp2djFoRFpGWmFBRktsR3YvaEgxQytlc1RMemlhNXUzVWJvK2ZTR3pEOTJsZmZ5VTFRZ1d6cTUzeFdrYVB2TzVqYWNZWG0ybGxRdWFQVC93Y1oxWW1XeCtjYzUwMCtaU3JMK3AxSnVYMVBJNVpiaC9ZMmdxUHM3T3UwTEdiL0ZLV1QrSDFYMWlEc3QzY3ltYVNLOEVNbm1VYzRyY0FJdG8zUWVPOUtJcGR4MFREZGkyVEwwTzkyZUFNNE1SK2MiLCJtYWMiOiJjMjlkNDE1ZmRkNGQwN2FmM2Q0NWUxZjZiYmU3MGIxNjdhNGUxNjlhYWVhMWNmM2Y2OGZiODEzYmMwNzM4MGNiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5JMk92R0F5TW45VHozT1IvRWgreVE9PSIsInZhbHVlIjoid0c1M3RQOVh1c1JZcG0yc05YN2diR05CZEo3b0V1UVN1WWQxTUxBOE9QQi9vTW1BMVdCaVkydXQ0VkFEamp1a0FDbXJnSU94OWtpN2IwcHl6UzFlR3lQR2xPWmg5a01NYlRHUFJxSzQrL25BUWJ5alIvK0hLODZLeWpMbjA1ZDFHU1dITUQ1aHZIQUlMSFRySXRLRmtUOHZwYVZnTVYzT0hTOEZTWHltcG1ZMmZBM1Q0cTVCNURrbWJndUNUK0NZRHJ3SElqcGF1V3hzTGRpdlZCZmRPL2dSSHpxNVMvdTNPa0lXb2RMK1pmTTIzbnJyQ29NUXdUSHNNM1hUa2xBeHNTdVVJeHNGT3QzdTdqN3dEUTVGbHEvN29PeWRTbzU1cjNsRXpYbFJwNFZWa1hSbWhpNVliMWhxMmJHYkhOSFNtc3FodThPSjFsZSs4ZWV2UjQ2RXphQVFQTFlvWHllcXZzaWJ6QTR0cG9XWHJobUovVUpNV2U0cWwwNjg2OTk2c0lDL2VreVlucGpsWFNsejFGUHBwdGNwWHAzamU3Rkl6S0dtNkZRTUt2VUhsUXM5UGk0MWJxMDNGYXkrU3NNU2dSZUZWazYwMGFxUDRRQWtnSks5dFZRVjhORnM1aWtkK2R2UVgwZ3ByYi9KTFZOQXhxN0hycGdSamt4bk55UkUiLCJtYWMiOiJiNzUwY2E1MWEyNzAzNTQzZWVkNDlhODNhMzNjOWM4YzI2OTlkNmRlMTBmYzliYWRlYmI5NDBiZDQ5OTRiODE2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijk3ZEk4Ykc2ekV2Z2Z3eTJwQncrSkE9PSIsInZhbHVlIjoiNVFlR2VLM0ZsMlNlMkJMNTF2QnQ3endBeit5c3p3aGsycnY5Q1pyRnJ4RDNXVGp4MUE0aFNTYUdaa095VHFLZlRoRFhZbkFtT3g3TTlhaTRFSHUzWDBBTkkreUR5eGJvMXdGd1NSbkJrSjFCMEVBdkJ0aFFwa20rUGd0VHlIOGNPZ0kzbkt0aDlsby9FbXI5RVFJT2tOWUczcTZNWm5oUkJnelhMd25LYndLUVNpWGo4ZzBOWDFqdHRpTG5hVDdnRXVNL1g4eTlzMnQ1azloNmxVQTJrakJnZm85czNnQ2lvWk15TENldzBSNVBvRVZONHhOSlhCK1FFcVM5eklmNWV4TllIYmRBUW9SQmpxemNXSEd5LzNsUEc4TTVMcnhiTjJ6ZWtHWVkzSi9MTlJ5TzNZeHkxc251REgxM3RFSU1KNUp2djFoRFpGWmFBRktsR3YvaEgxQytlc1RMemlhNXUzVWJvK2ZTR3pEOTJsZmZ5VTFRZ1d6cTUzeFdrYVB2TzVqYWNZWG0ybGxRdWFQVC93Y1oxWW1XeCtjYzUwMCtaU3JMK3AxSnVYMVBJNVpiaC9ZMmdxUHM3T3UwTEdiL0ZLV1QrSDFYMWlEc3QzY3ltYVNLOEVNbm1VYzRyY0FJdG8zUWVPOUtJcGR4MFREZGkyVEwwTzkyZUFNNE1SK2MiLCJtYWMiOiJjMjlkNDE1ZmRkNGQwN2FmM2Q0NWUxZjZiYmU3MGIxNjdhNGUxNjlhYWVhMWNmM2Y2OGZiODEzYmMwNzM4MGNiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81372006\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-842154747 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842154747\", {\"maxDepth\":0})</script>\n"}}