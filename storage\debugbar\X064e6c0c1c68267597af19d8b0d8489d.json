{"__meta": {"id": "X064e6c0c1c68267597af19d8b0d8489d", "datetime": "2025-06-30 18:42:15", "utime": **********.844345, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.419928, "end": **********.84436, "duration": 0.42443203926086426, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.419928, "relative_start": 0, "end": **********.793848, "relative_end": **********.793848, "duration": 0.3739199638366699, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.793858, "relative_start": 0.3739299774169922, "end": **********.844361, "relative_end": 9.5367431640625e-07, "duration": 0.05050301551818848, "duration_str": "50.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029100000000000003, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.822053, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.605}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.833685, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.605, "width_percent": 17.869}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.836957, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 82.474, "width_percent": 17.526}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1287 => array:9 [\n    \"name\" => \"KDD حليب شكولاتة 180مل\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1287\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1087868479 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1087868479\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1690522840 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690522840\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1663865309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1663865309\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1335700068 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZjSG1RZGs3anprK25OUy8xekorTXc9PSIsInZhbHVlIjoidTYyclR0aDlhSElEdjBGcWpjTU9mN3FHK2l5MEZNQkJlRVJZczlWNll1VGNoMHlaMXMxd2JET002WHdtYlBZb2tQSURUT01MV2hDQUZtT2hZZEpBQkRLMEdERkJ1dFJtalI4YTRlSHZjUDdQdFhMNW5MR04zU3l2Mk1iNXVac0tXcTFWK3VJNWpSRDZkcmVKQ1MxVVdNSlNmL0pPaE1jUjQwZWhJWVl3SEJSb2ZBR29TYzRvaVRCY0hpam9FbmppREZMQkdhUmprcHhOZmxDUlFqdis5SkJMUGtKNExsM2J0QTUrL25YODdXNzVCVXRRTmJRc1dJWWVmWDJzS0dVTzRsa3dydDJ6cXN6cEV2WVozY3VkSmZZaUtyNDBZWUJBMnhsWE15cnozS1F0aFMzMmtISzJSTUk0azlrUDFCL0w4dUhsRWxlOXRNTU9EZ1dpMldxTDFQNndDL2NMUmx3bDFveVYvTkN0QWZvZmhEdUU0RVp0cDdlZEdCWFdSZGJXeDA4ZDlacUlwcnV2ZWQ4U3VsVWd4bFRzVm9CTXFuYTQ3V2ptclZySmV2T3hhdTZFUzF5dGZNYWIrYU9QNnJHSlRxdGR4TmdlK2l5L0JQK21vTDVnUkFCTFR1ZkVhTU5pN1VTVERrV2ZWcEhubk9wbkx6UTJSU2IzeTc3ZTR1RGYiLCJtYWMiOiIzZDlkMjcxZTdiMjNkYzM0NjFlZjBhNTQ5NTIwNmJhMzU5MjA0ZTVhOGFkN2Y3OWRiMTZhODBhODEzMzU3Y2ViIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1Kc0pvUkhsZ1UyK2xjQkdFaHpkVUE9PSIsInZhbHVlIjoiVjFVazdYZGZMRnpvSU53TC9ibDIyaTloSldSYWZjQUdGVTcwN01rRldCdXJuSjBMY0xnb1BUeDQ5VTFKVGIwVHJkZFVOTUFoNTdlOGlua0QzQ2Q4UmhtVjFpWDVMN1B0a2JzZlI0VGQ5R2d0djJJQXY3QVF0ak9OV0N0UGprUnhLVzc1djBxZUJYVlVWTXQwWE5MamgzMENaclByUWs3WWc2bFYxdzBMM1U3ZmhidzI4YjR0L3NJZm81TDBmcW1NZzg0dStZdjFZUGZSVEFpLzR0S2x4NlRwSHFFMEM3TmdqeXRoZ2JoT2d1MFczTGVTTXdqMUpDQWpIWGx2NVJSMlluOGJBclNFTEV5ajFLUTFjRVFxQU8yZXBEVW0wQVR3M2UrSjFPbFlHbklLVHQ5M3Vrb29IejJjYjhSUHIzYlNjQ2xlSzMvdGxveFRkMDVrT0pJTEdIY1pVNUE1OXFuRSszdldLSVpuZEViZis3MWlJaVAzRm80SGVVNGRYSFNkTmNMeUJ6SktKdjNLaXJLTWkrRlBka0svKzBXVnE5NUdCNG9ZbkFsTmZhYUo0bVE5Z05yektHZTJOZ1ViaWxudjdlbEJ0UVBTUnBhWFJSSzRNWkZmMHduSzB3Uy82a3ZHOXdvcnh1WXhrTlhpbHNINFROenRCRHY5Q1JLbGVKYmgiLCJtYWMiOiIyODgzMGFmNWNiMDI3M2NkMTk1NmVhZWIxYjAyYzEwYzRkNzMyZmNjMTY3MjkxZWUxYWE0MDNlOTBjMzg4NDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335700068\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-578582720 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578582720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:42:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktrdUlKU2tXNnRvN0xSdmpRbk1JT1E9PSIsInZhbHVlIjoiY0o4ZkJVK1VRc0IwQ3RobmFYM1E0ZGlHRUwvK003VnV5TmxLVDBJQ05FNXptbC9weTN3RGY4RXpmVEoxZWZFMUtTdTlKUUVCbzV2cnZweVR0T2ZCR1VvQ1d2Y0FFZWJXd0xTNVJudUhTK2lyVVY1ajhQLzFwRnBhRXVBRXRscW5OOU5PVkowajhqL1FrcXc2cWxGN0ozNm5lWVIxQXFvWVREdHZaV21XaHJ5KzRLNDl0ZnNleWJxNzZySm95QVllOXJpTm10NUYxUWpNUHVMbldRVVFmTjFnSForRU4zcjZzVmZraFpXTFVBY1NSMUNTTGJFd2xHYWRwNHpQSGQ0MEhVOHRxVnZiYmdrVUVHQ1NiR1cyenJZRHU0OVlkK1hHMFlGeVl1L3RBNVRvckNIVFp6cUVhY2xkRG1mWFFRK2xKVkpKVXI4d0V3bjVRRkVqelE1dzF4cEVEMm1JWE9KMUk3czcraFZJR090VzlXL0pVdW92NWY5bnM3UG5GT2pHNTduQjQzMzY0QWgxVHNmbk84UlNOWGJSMU1oMnFHbWFjdDJaNGJSNXdJR2FZcUIxUFVNS0JZSFVJck44MEJadHlLMkN1TEQ0WmRrYmFtVGV2ZzhZTWtyWjFYbytkSXNMOElscm9DSkdPK2IzQ0NZcHFqc3dHRTVLeVcwcmhlWVQiLCJtYWMiOiJmYTNlODQwM2M1MWVmM2ZkODYwMDEyZDhlNzg4MDQwOGNiNWI3ZTc0MTQzZmUwMjljZThkNDgyOGUzNGE2ZmU0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdnWXh4NHl2QkF4b2gySmExcFl6OVE9PSIsInZhbHVlIjoieUVYRk15d1AxdHYvN0pCbTJRbkdGQ2wrbUpMOE1kZDJpOXViNzE2Y3E1VEQ1aTJuTng4dUQ0Z3FRWTluVUxSNmU2NkR0RDVwdm9JVG81dU9KNGZSZkpaMnBpaXZiY0pRTE9Ldy9QdDkzYjEzaHoyVzEwWm85cWJzTEQ1cWwxa0cvNEgzbVlQbWFPejIzanVXSkFsMjNsOEVXdll1N2Zpa1kzZWdTaTNHOVZ3VEZCd2gwWGxnVVBGZ2d1ZEErc2E2elNEdExrV0ducGdLZXVlRzFFTFRYdDNxQ01qWjlUVUJJM0RqNlV3bFN3OE5SUXl6MDMwTUl1Y2dlajVuN2U5QlpnNVA2NTZsZDV4a1BVcm1UV0dZcXV1TVBEcGxWM0U3dWcvUTZObm5WVFFJS3hTYkpFUCtxY2F1UklFanpqOTM3OVh0Z1BJSlBUOTRmVlp6STRYeXB2ZkthNy9CdGFhOUFIVGgyY0FocXVKeTlPR3ZuMnc3TFNSZXRVYjNnVm9sVkExTm1nSzBjZU8xcWkwVTRMWVc2cnZ0NWtRc3lPSmp4RE9UR3MxSWNIcDFad2p5ZkJpcTdBNzU4WlZjY3Z1c2U0WUFCQ05xSFRPeWxTNWtwRDJUNHhxNjJ1R1R2S0ZvT1JkRllhNVNBYXUvTmUrY05wK21uTWVrZDRxUnp3cHMiLCJtYWMiOiI5NzZiMDViZGJkZjc0ODE5YmM1NTVkOTFhNjcxNDUxNGVjZDU0Yzc2MGU4OTliOWUyY2UyM2E1MTA0ZTIzN2Y5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:42:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktrdUlKU2tXNnRvN0xSdmpRbk1JT1E9PSIsInZhbHVlIjoiY0o4ZkJVK1VRc0IwQ3RobmFYM1E0ZGlHRUwvK003VnV5TmxLVDBJQ05FNXptbC9weTN3RGY4RXpmVEoxZWZFMUtTdTlKUUVCbzV2cnZweVR0T2ZCR1VvQ1d2Y0FFZWJXd0xTNVJudUhTK2lyVVY1ajhQLzFwRnBhRXVBRXRscW5OOU5PVkowajhqL1FrcXc2cWxGN0ozNm5lWVIxQXFvWVREdHZaV21XaHJ5KzRLNDl0ZnNleWJxNzZySm95QVllOXJpTm10NUYxUWpNUHVMbldRVVFmTjFnSForRU4zcjZzVmZraFpXTFVBY1NSMUNTTGJFd2xHYWRwNHpQSGQ0MEhVOHRxVnZiYmdrVUVHQ1NiR1cyenJZRHU0OVlkK1hHMFlGeVl1L3RBNVRvckNIVFp6cUVhY2xkRG1mWFFRK2xKVkpKVXI4d0V3bjVRRkVqelE1dzF4cEVEMm1JWE9KMUk3czcraFZJR090VzlXL0pVdW92NWY5bnM3UG5GT2pHNTduQjQzMzY0QWgxVHNmbk84UlNOWGJSMU1oMnFHbWFjdDJaNGJSNXdJR2FZcUIxUFVNS0JZSFVJck44MEJadHlLMkN1TEQ0WmRrYmFtVGV2ZzhZTWtyWjFYbytkSXNMOElscm9DSkdPK2IzQ0NZcHFqc3dHRTVLeVcwcmhlWVQiLCJtYWMiOiJmYTNlODQwM2M1MWVmM2ZkODYwMDEyZDhlNzg4MDQwOGNiNWI3ZTc0MTQzZmUwMjljZThkNDgyOGUzNGE2ZmU0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdnWXh4NHl2QkF4b2gySmExcFl6OVE9PSIsInZhbHVlIjoieUVYRk15d1AxdHYvN0pCbTJRbkdGQ2wrbUpMOE1kZDJpOXViNzE2Y3E1VEQ1aTJuTng4dUQ0Z3FRWTluVUxSNmU2NkR0RDVwdm9JVG81dU9KNGZSZkpaMnBpaXZiY0pRTE9Ldy9QdDkzYjEzaHoyVzEwWm85cWJzTEQ1cWwxa0cvNEgzbVlQbWFPejIzanVXSkFsMjNsOEVXdll1N2Zpa1kzZWdTaTNHOVZ3VEZCd2gwWGxnVVBGZ2d1ZEErc2E2elNEdExrV0ducGdLZXVlRzFFTFRYdDNxQ01qWjlUVUJJM0RqNlV3bFN3OE5SUXl6MDMwTUl1Y2dlajVuN2U5QlpnNVA2NTZsZDV4a1BVcm1UV0dZcXV1TVBEcGxWM0U3dWcvUTZObm5WVFFJS3hTYkpFUCtxY2F1UklFanpqOTM3OVh0Z1BJSlBUOTRmVlp6STRYeXB2ZkthNy9CdGFhOUFIVGgyY0FocXVKeTlPR3ZuMnc3TFNSZXRVYjNnVm9sVkExTm1nSzBjZU8xcWkwVTRMWVc2cnZ0NWtRc3lPSmp4RE9UR3MxSWNIcDFad2p5ZkJpcTdBNzU4WlZjY3Z1c2U0WUFCQ05xSFRPeWxTNWtwRDJUNHhxNjJ1R1R2S0ZvT1JkRllhNVNBYXUvTmUrY05wK21uTWVrZDRxUnp3cHMiLCJtYWMiOiI5NzZiMDViZGJkZjc0ODE5YmM1NTVkOTFhNjcxNDUxNGVjZDU0Yzc2MGU4OTliOWUyY2UyM2E1MTA0ZTIzN2Y5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:42:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1287</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">KDD &#1581;&#1604;&#1610;&#1576; &#1588;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; 180&#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}