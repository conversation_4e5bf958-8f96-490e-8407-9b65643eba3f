{"__meta": {"id": "X1aab14007013abeefade061c48d1c335", "datetime": "2025-06-30 16:17:33", "utime": **********.072195, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751300252.547961, "end": **********.07221, "duration": 0.5242490768432617, "duration_str": "524ms", "measures": [{"label": "Booting", "start": 1751300252.547961, "relative_start": 0, "end": 1751300252.98552, "relative_end": 1751300252.98552, "duration": 0.4375588893890381, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751300252.985529, "relative_start": 0.43756794929504395, "end": **********.072212, "relative_end": 1.9073486328125e-06, "duration": 0.08668303489685059, "duration_str": "86.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45938208, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.036407, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.042698, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.063357, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.066165, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00999, "accumulated_duration_str": "9.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.01568, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.021}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.020261, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 21.021, "width_percent": 38.138}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.02711, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 59.159, "width_percent": 5.405}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.036941, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 64.565, "width_percent": 4.004}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.043492, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 68.569, "width_percent": 6.707}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.053346, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 75.275, "width_percent": 9.009}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0572238, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 84.284, "width_percent": 5.906}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.059733, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 90.19, "width_percent": 5.305}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.064342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 95.495, "width_percent": 4.505}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1856648946 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1856648946\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1164259376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1164259376\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-388126520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-388126520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-966171202 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IlU3aDc4MHlQV2pxUHRNZ3ZBbUNQTUE9PSIsInZhbHVlIjoiZitMcnM0aHlJaXI5aHNFRUt1QkdxTGtHTmlscVF4bE1PeXBoS0FRNkd4N3kraitXOUt6TUsxRFcrZytpR3RBOEJIclhvdDUyeHk4NDhIT2JyaTdyUUxLdXZGS3FGczhEaWw4RmhKVmRxSDY4NXI1QzAybmhodWt3eWlmMlJqSTlQN1hHeGtHTGFOM0FIenp4eGd0SnE4M0dFWTdkam81THNyaGs2MzZla2g5bDlPNGJZa0gzMnpubU1iZ0Uxa1I5MEV1bEpSdkZ6Qm1VeHVYTFlhRWh2VSt2Q1o2VFppalBZYkdqQWNrZExyWUZFNjdtNW9ySjZrM3hnbmtPQURUZ2FrTkdTWnVGNVNHbzB2WTNZOTNFK1JscHJQRW5jZ1NhY2xwWCtlNyt5ek9pSTBzTktrZ1pydVU1dmlKYXYvZTRBZHB1ckpiZEtabk9FRjVlY1JmL2Y3ZEQwc3o2cEtZU1o2a1o4Skl0VzEvUGhMdDBYU0FKa2JWeW5JWGxSdDV5ZVhmcnlxZkZRNlZxblpIVXY1K0NJT1I2dW56TjVmTUZMOU5zWW1YMTNWL3J6WXkvNStwUE1lZkxBeGtzTGdYUEJxRFR4Zk1QTDRxMGF4d2Vmb3A3aXpKd05kdm1RVW0wUCtaYW9CbDkrRmNibmI2dzlVT1JRZEdJRmVkQjhadTEiLCJtYWMiOiJjZDU2ZjRmMDY2OTAxNDRkYWYzYjMzMjQ1OTFmNDM0YTRmOGFmNmZkNDJjMjg3NjllYTdjOWYyYzQxZTAwNGUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1ZWEhxMWVUY2VURi9Uc2lRZmsrVGc9PSIsInZhbHVlIjoiUG45UWYralc4SUgzaUVNc1F0YmZFUEpsWE5vRUkxVDRxcW8rWmJVcFAzdDZoakhQdHB3SUJ5M3IydXZxMUNNWUFIT0xlNUxxZFJ1OXl5NWErWGhWajI1cVBFWDgxNUMwQkxmVy9IM2lFSjlPVWxUclhOWlVyWDlWdDlJUzFEUm50Tk8rSGJMVHViWm9nekJKaXRqMWZHSFBmTGkwTzhseU9MQUZIdjRORmhHMFBzOTNrTE9OckRSNWwwSCs4U2h0L04vSlN5eXMyRnlLU3NGQ2s5WDR4TnE5MUgwQm9venhQVEg1eERKN3QzSlNCMnlQL1hmNE9QTnMvVHgxQ3NRdW1BZkpOSkFEVVJDeG5DSW9pOGp1S3l3NGtzMUk4Q08rajBnc3RtQ04xbTkrTFBXWnNXcm5BZU54NFpDdHVrRTFaM3BJcE1HRmRQZ0p2eHZ5MTZIblVWbnFiN2NyVlNTaG5aSTdKNWpQbDVELzNyRGVqQ2s5eHIrbUVKM1FLdUJ1Z040b1M1VXdWQWJPTVdsRTJWalVwVUpzU1NGN0hsZXlkMldxMmx6M3JlQzVYRlJZMFEzZ2d5eWJEYXVidmJlVkV6eTFyOGE0MFUzeFpkK2haTG9DWS8xZ3Vqb2czTER4clRLbk8rUms4eC83OEc0dDVZelJrYzAwSnVqUzNPN0ciLCJtYWMiOiIyMDVmMGVmNTQ2OGNiYTI1ZDJjNDU5ODIyNDY1ZTc5NDA2MzY0NWZjMTkxNjE3YmQ0MDEwOGNhMTBjYWY5MWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966171202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-163999675 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">24632tD8ZfzoNWlbTseZcTmC2MmrHNwvZ4fTsg6B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163999675\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876929357 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkyZThvTkVmcWtiWkJvdjhOVlg0S1E9PSIsInZhbHVlIjoiWVNCcXYxZnZ1SjBCeHNnMUE2cDRGMkxpdzlRUnRuR2l1NGNaazBJVzMrcDltd0Q0TVN3UEo2TzFkd2lEQ3FRa3JYQmRaZHRlWWdzMnZFWFIreFl3ZzQ1QkkxMHM1NVRYbGtFQmgyWjVBRTlVQVAwRGg5cFYwV3MzOWdseXE0MHhqTElnUCsrWVZxTWp4RUF6QTdjS3M2c253aUxDU2xieUp0NjF4TC81YmV5OXNlaTB2SFdtSnlJM2Rwc0tpa2pDUVZZaHZ6VlYvMjlIMEFQWHdsbUlkWUJwdUV6aW9jY1dwMk42b1o5bURMSnJOUzVSdlgwQ2ExcEpPR0dCeDJNeWxDMk5iTyt5QXl5VVdRbUhNSTNIeXhXbURIa3FpZmFWTHltc2xnL0E3TkNpTGpuOE5VbS8yQTNXUzV1K3UrZzV4QlQvb3dnT1FKZm1HMHFlRy8xY0xXZkR3eTloU3BldXZWTjZWZGFWVHlLQmo3ZHhqSjd3ekcxbU5KTzdjWFJNUkZldUZFU1QyRlhncHFOSjNsZENvak5ieVc0SVEyNVNpeDRqbWcwTS9NcldrbitrTWxLOU1TSGduRlNnaVJScklFN0xRVTRlMEwzVjRiZkh3a1lYQ1h1ZFNubVNPbkdINWVPNHNjSUt3WjExQkFRZHR6WnM4VW1nVDd4VTNQMDQiLCJtYWMiOiIzNmY4YzZjM2I3NTA5ZDRlYzhmNjc1MmI1ZjA0ZmU5OTU3MGRkNGJjNWY1YTE3YjIzM2VjMTkzZDVmNmEyNTkxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImM3ei9ad2FjL0JFL3FwbmVTYW5sYVE9PSIsInZhbHVlIjoiL3E4M0kwRGwydHJYOFpHNkJ0U0E1WkliTGs0cjFCTEtoRXNqazhwam9GWTJIdXp1RzUrQWxVUFpxV3BFZnJGeldnaUVxR0lMWHFmMWJ1TGZRUFRUOC9tSitLNTZhdFdXMnNRKzlOQXpoQnBwcGNKd0J3VzlsMkhFeXNwR3J4bk9nZzBodnZMNy9XVmpSNW85Q0RSalFGdkI2K1FxQ0c3dGZNRSttbWhZeXQySlljMk53S3ZwVHdNVGhwMmpkc2Jna3dmeXUyV3hLTTZPOG5TbHlzeEVFYURmQVB4Z0V2RXQ5bE5pL1ZIVzc2eEU5LzdjeU1YUm5KQm05c1Yrc2JhOFpNSjdaUGREcHV5ZzA0aFVld0Q4OVFXOVFENVpucW9lMmQ5V0tJcURGdGVCMGNiVjFOaVdlUnREK29RVXBXZ1dJeHdiL1JCenJqeExzMllIVFdLcW1mMmRpZkFnVi90NWQzaGFWMEN1aDM1NkpkTVBoam1rUjhoeU81UkE3b09nWWlZclBGTzhiWXRTUm5abnZHaU5MZ0pKYjNucUw0ZUtHcVZ0TWpVM1JZdGlOcFRjNWhZUzBmMHBRYVNlOUFGREFQeEw5eUgwRStEYzVQN2wzNmlIQko3RkZIeEVQclRwekhmMW5XT2ZGenlraENiVWZ6anlXbnhNQUpOZ2lPTHciLCJtYWMiOiI1OGU0MDk0OTA1MjFlNDc4ZGM1MzI5NTNiZTNhMTQ2NTFjZWE1OGEwOGFiOTQ5NGQwYmU3ZjBmYWFhMzBjNjQwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkyZThvTkVmcWtiWkJvdjhOVlg0S1E9PSIsInZhbHVlIjoiWVNCcXYxZnZ1SjBCeHNnMUE2cDRGMkxpdzlRUnRuR2l1NGNaazBJVzMrcDltd0Q0TVN3UEo2TzFkd2lEQ3FRa3JYQmRaZHRlWWdzMnZFWFIreFl3ZzQ1QkkxMHM1NVRYbGtFQmgyWjVBRTlVQVAwRGg5cFYwV3MzOWdseXE0MHhqTElnUCsrWVZxTWp4RUF6QTdjS3M2c253aUxDU2xieUp0NjF4TC81YmV5OXNlaTB2SFdtSnlJM2Rwc0tpa2pDUVZZaHZ6VlYvMjlIMEFQWHdsbUlkWUJwdUV6aW9jY1dwMk42b1o5bURMSnJOUzVSdlgwQ2ExcEpPR0dCeDJNeWxDMk5iTyt5QXl5VVdRbUhNSTNIeXhXbURIa3FpZmFWTHltc2xnL0E3TkNpTGpuOE5VbS8yQTNXUzV1K3UrZzV4QlQvb3dnT1FKZm1HMHFlRy8xY0xXZkR3eTloU3BldXZWTjZWZGFWVHlLQmo3ZHhqSjd3ekcxbU5KTzdjWFJNUkZldUZFU1QyRlhncHFOSjNsZENvak5ieVc0SVEyNVNpeDRqbWcwTS9NcldrbitrTWxLOU1TSGduRlNnaVJScklFN0xRVTRlMEwzVjRiZkh3a1lYQ1h1ZFNubVNPbkdINWVPNHNjSUt3WjExQkFRZHR6WnM4VW1nVDd4VTNQMDQiLCJtYWMiOiIzNmY4YzZjM2I3NTA5ZDRlYzhmNjc1MmI1ZjA0ZmU5OTU3MGRkNGJjNWY1YTE3YjIzM2VjMTkzZDVmNmEyNTkxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImM3ei9ad2FjL0JFL3FwbmVTYW5sYVE9PSIsInZhbHVlIjoiL3E4M0kwRGwydHJYOFpHNkJ0U0E1WkliTGs0cjFCTEtoRXNqazhwam9GWTJIdXp1RzUrQWxVUFpxV3BFZnJGeldnaUVxR0lMWHFmMWJ1TGZRUFRUOC9tSitLNTZhdFdXMnNRKzlOQXpoQnBwcGNKd0J3VzlsMkhFeXNwR3J4bk9nZzBodnZMNy9XVmpSNW85Q0RSalFGdkI2K1FxQ0c3dGZNRSttbWhZeXQySlljMk53S3ZwVHdNVGhwMmpkc2Jna3dmeXUyV3hLTTZPOG5TbHlzeEVFYURmQVB4Z0V2RXQ5bE5pL1ZIVzc2eEU5LzdjeU1YUm5KQm05c1Yrc2JhOFpNSjdaUGREcHV5ZzA0aFVld0Q4OVFXOVFENVpucW9lMmQ5V0tJcURGdGVCMGNiVjFOaVdlUnREK29RVXBXZ1dJeHdiL1JCenJqeExzMllIVFdLcW1mMmRpZkFnVi90NWQzaGFWMEN1aDM1NkpkTVBoam1rUjhoeU81UkE3b09nWWlZclBGTzhiWXRTUm5abnZHaU5MZ0pKYjNucUw0ZUtHcVZ0TWpVM1JZdGlOcFRjNWhZUzBmMHBRYVNlOUFGREFQeEw5eUgwRStEYzVQN2wzNmlIQko3RkZIeEVQclRwekhmMW5XT2ZGenlraENiVWZ6anlXbnhNQUpOZ2lPTHciLCJtYWMiOiI1OGU0MDk0OTA1MjFlNDc4ZGM1MzI5NTNiZTNhMTQ2NTFjZWE1OGEwOGFiOTQ5NGQwYmU3ZjBmYWFhMzBjNjQwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876929357\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-820211678 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820211678\", {\"maxDepth\":0})</script>\n"}}