{"__meta": {"id": "Xc988db855aedee7f9d609cd1e18589ba", "datetime": "2025-06-30 18:49:16", "utime": **********.30764, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309355.879086, "end": **********.307654, "duration": 0.42856788635253906, "duration_str": "429ms", "measures": [{"label": "Booting", "start": 1751309355.879086, "relative_start": 0, "end": **********.230857, "relative_end": **********.230857, "duration": 0.35177087783813477, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.230865, "relative_start": 0.3517789840698242, "end": **********.307656, "relative_end": 2.1457672119140625e-06, "duration": 0.07679104804992676, "duration_str": "76.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00729, "accumulated_duration_str": "7.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2632651, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.273447, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.846, "width_percent": 6.996}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.287206, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.842, "width_percent": 5.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.288893, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.918, "width_percent": 3.841}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.293288, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 43.759, "width_percent": 34.156}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.297927, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 77.915, "width_percent": 22.085}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1589568402 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589568402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.292409, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1068267600 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1068267600\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-315755811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-315755811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-461135820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-461135820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309344290%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im01SkJrYmxKN1NmWTJ1bEFwWTNteFE9PSIsInZhbHVlIjoicnFPTUJYVk5GNHJacjFlcUZjelZOeEJIUWZrK1FBN2VobkhWN3F5RWpLZjRUeC9ISEVCWGQ4MXV2aEl6VFpuVVJTZUsvb1Y1ZXd3Wm1iRW5mTzNyeGZJeDQxdVI2akJTcS92N1pqb0diWC9Jblg1UFBMcFA2ZHh2UWNGaEpRaFRybldmQ2M5TmVKYys4R3E5c05Hb1ZTOWVJK2FXSEJvNTNxb0pqNkNqOUpWZjJYWHBWRnFEd0dlTTNnTVJIT2l1YjY3eXpKeGVqOEpOVVVGVy9FZXR6US9YMCtyN3p0M0pQR2drSTJQdW1Zdktob0R3NVRNRzRXY2paT3hZcnlsanYrTjBMaEYxYkhjcjY5WnNJNE9vOU11clBPNjdWeGFMNTZGUXZXR01XR1BXVmJjK1UvelZtUFdGL21hYlJ6SHkrcENaQUZ3bWdMa2tVcmpNWStxVm9ZcWtPRjU0bnc4ZGhKSFNQckxsVFBBZHZZaFJwVGJ2UEVCQ1Zmb0t4Vjg3bC9OaTdJdHFpaDRiS0lmV2tHRDhVajZONVRnZG1Ub0Y4K05VTmxBMWxFNEMrVGtTcVZMRGMzb1J6aGRtZDNpUzlGSHdvbExpMCsydFVJR3RTQkpqL21URHprY3lJbzRQZG1VWnd1VEpkMTVrcEVPYm1jS2JrdU5rWVZXeFdiZjMiLCJtYWMiOiJhNWZkYWI5ZDRlZWMxMmViNDU5MjlmOTc1YTFiZWU1OGRkMjA0N2Y3Yjk4ZTUzMzcyYzc5YmRiYmQwZjc5ZmZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdSNzlDdkZoVzRxRHlNekFVVW50RkE9PSIsInZhbHVlIjoiSDJNTndkTGcyVmN3Ri83Q0thbFNkT29KNmU3RTBBUGNaUUJqUkJhanFUU3BtUUtJSW1tS0V0Y0YvbkFNVElKaVNacjd5QVJ3eEl3Q1EzS0RwakRWVUhlQTRHTG0yLy92cXdsamF3MEl0WncvNURmaDFoM1RoUVlCY3JNR0NCRFcydlJ1RzF4clZkQXNvWHFDcjNPV2JsZlpBOHl4cFVlV0dLZlRVQzRMK09vdXRTZDQyaDBlOWh0Snk1Z1RGSjhic0FGUGFaUjljTks4d0RKZUR4K1pqY2tiUW0yTGxZdVdjVmhXN2M2ZXBuMHI5bThCbFBBMi92MzhIcHhNL3hzMjBBY2FpOHRIUTBvMUhiMlZLT2dmZzZTekdFRE1YelRqSHhPZlMrak1BWURNY0RkOUNYemdUekxZRm9MZUUvYXJLWUpIdXk2UnFXM3FjcnRUQklHQ0dBQW93djFMMlVEdmluQUJtaXhHQ2Y4ZFdpT3l4L0ZTQW02WnZYOFNBWHNmT3FUMnF1RTFVTVIyS25TbWp5eVpiNlpKdU00Ymx4QkcvQ0RRcHJCeVpDeEhPc0tpVVlXVlNHU2FnSDlva2sxelhna0ZzWjdMMzlrNzI3UUc2SFZPMWN6ZWhXQW5reFkrclpzdmtRSjhSdDRKRUxremRYY3F5TmRIdXllVDBpWE8iLCJtYWMiOiJmOGU0YTVmMDkwMGNhZDdhYTkxYTM1OTc4MDRhZmM5ZTAzNTZmNzEzYjY5OGQzNDkyY2EzYmJhOTUyMjFjNjM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-587761142 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVPWWV0L1lXcVhpcGtXaFp3U2dua0E9PSIsInZhbHVlIjoielM5SnExT0V1dHAwUjVMalhqMVFodmpvdXVKSEtEZkpDVzQ4aGdWOHVJMWdZZXFqV2h2RFlrZXVUcXBNOXRKYVhtOUdEUCt4N2o2cjVyREZUaGJUSlUyam9QazJQNGdJR0J3ZFdTTzE2ZHA1MFlWTTFnQzVMN1RWV3pNOGo5azRjKyt1UUxqTmRKTS8vQm0xdGhoNGZEOUVXVkpmei9OMExVVTVZNURTWGJZU01wYVZNQWlpU294cmdGVU84QUJ6K1BwSnFiWWgyMWdHTnhoSzNZdElLRmNwcldITW96d3MxQUFEZ2ZnZThIaFRoenBjdUZnODhzZVEyMmFaYzg4OTEwaXVLTzVySG44MFhqajJtN2hINDF5OWpqek02UHo1b0kyRkU5aVFsMzVNZE05TWNjMlltS0NscDJ0ZmlkWnBFMnVrQVNodlpDSitORWR1WU01TXFzODhjOC93YUFsQTNrUE9IbGFYK3JzaWhvdzNoanY0UGlpOVBwaWI2YkpiYmhoZWxYTXMzUGZuOUFMUXFGYzBLdDFDdUFYWjJpcWlkbFBYbVVoanpKczBhb2JhV1J0OVRJWnpGQmEwK1EwSFdVcEtiMFB0NlplOElHbyt2ZGUxeURrS1BwL2kxWTIvZ0plS0NuRmxYMldIWWVGa3pJbFRjbnlYUEVXNTdSSkgiLCJtYWMiOiJlOGY4YmI1YWZhZjMxZjE5NjVlYTliOTU5NmYyNzU5OGQ0ODU3ZTU2YWRiODM2ZDMwNDdiOGRiZGQxYjBiOTY1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRiRzFQRForT0tWSzFneTNlaUhFY3c9PSIsInZhbHVlIjoiakR4WDlpR05TTjV2NmVSSk5MSnd6ZmhRVlJuWHpZaVVhRXpjODlQNGFHZUs1SzJPVmJJSkVhN05DYnB6UlhHdHVrQjB1MTlHWDZ4cElUWUNkTVFVWU9VbWJkUFJpc3lKMzd0MUVhNnlLVmdEUWlQSGZ0MXhqOG9PNkcyMmdaOWJDa1BTR1ByeUFCWHV4NGpNMDg5WFhSY2M4cGQ5ZmU0Q2pMeDVqQzlwWWxVQmVub3V5cmpFQVVTSG5PeStaSU9yY2hTbHZ6V1VYcW1yR0tma3dQaHpoODdLek1xRHR1d3oyMFQ4NjVxV1pUejdoVkFZK2VuMmgxMWk1SkxiQW9wNzFoZmlrazJJRHJSOGV2RjNpd3A5SVBHUzNxOHp4LzlpTjNac0RMZ05ERnMrR0NKcXFjL3FPWUdNcjVVNy9ZYzNyekhNNjBtdWY5VWQxME9OT0VtQ2VBSDcvM0VUWVo5UVNSSk9GMmU2THFJQy94anpQMzEwMktidkFmZU9XUjhtZ1RlMzByamtNVXhYVWVNOERmYUQ3eEFXcENwVFdDeVBxeTEzVUQ1c09LV29tNmVjbXhVZzJCbEFhQ3dmcjM4cWpuQUx4OEswN2d5NHk3K0xaOFFYRWVzMnhSUkRCc0VlbStMMHN5dks2cXNXUEI4NTVNbEw1aTQyeGRvVE0rbjUiLCJtYWMiOiJhYmRiOGI4NjZlMTIyYzQzMWM2MThlMzc0YmQ2NTg0OGQzMGMyOTEyZGM1ZmI4NmE5MzlkYjMyNjVhNjhmMTYwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVPWWV0L1lXcVhpcGtXaFp3U2dua0E9PSIsInZhbHVlIjoielM5SnExT0V1dHAwUjVMalhqMVFodmpvdXVKSEtEZkpDVzQ4aGdWOHVJMWdZZXFqV2h2RFlrZXVUcXBNOXRKYVhtOUdEUCt4N2o2cjVyREZUaGJUSlUyam9QazJQNGdJR0J3ZFdTTzE2ZHA1MFlWTTFnQzVMN1RWV3pNOGo5azRjKyt1UUxqTmRKTS8vQm0xdGhoNGZEOUVXVkpmei9OMExVVTVZNURTWGJZU01wYVZNQWlpU294cmdGVU84QUJ6K1BwSnFiWWgyMWdHTnhoSzNZdElLRmNwcldITW96d3MxQUFEZ2ZnZThIaFRoenBjdUZnODhzZVEyMmFaYzg4OTEwaXVLTzVySG44MFhqajJtN2hINDF5OWpqek02UHo1b0kyRkU5aVFsMzVNZE05TWNjMlltS0NscDJ0ZmlkWnBFMnVrQVNodlpDSitORWR1WU01TXFzODhjOC93YUFsQTNrUE9IbGFYK3JzaWhvdzNoanY0UGlpOVBwaWI2YkpiYmhoZWxYTXMzUGZuOUFMUXFGYzBLdDFDdUFYWjJpcWlkbFBYbVVoanpKczBhb2JhV1J0OVRJWnpGQmEwK1EwSFdVcEtiMFB0NlplOElHbyt2ZGUxeURrS1BwL2kxWTIvZ0plS0NuRmxYMldIWWVGa3pJbFRjbnlYUEVXNTdSSkgiLCJtYWMiOiJlOGY4YmI1YWZhZjMxZjE5NjVlYTliOTU5NmYyNzU5OGQ0ODU3ZTU2YWRiODM2ZDMwNDdiOGRiZGQxYjBiOTY1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRiRzFQRForT0tWSzFneTNlaUhFY3c9PSIsInZhbHVlIjoiakR4WDlpR05TTjV2NmVSSk5MSnd6ZmhRVlJuWHpZaVVhRXpjODlQNGFHZUs1SzJPVmJJSkVhN05DYnB6UlhHdHVrQjB1MTlHWDZ4cElUWUNkTVFVWU9VbWJkUFJpc3lKMzd0MUVhNnlLVmdEUWlQSGZ0MXhqOG9PNkcyMmdaOWJDa1BTR1ByeUFCWHV4NGpNMDg5WFhSY2M4cGQ5ZmU0Q2pMeDVqQzlwWWxVQmVub3V5cmpFQVVTSG5PeStaSU9yY2hTbHZ6V1VYcW1yR0tma3dQaHpoODdLek1xRHR1d3oyMFQ4NjVxV1pUejdoVkFZK2VuMmgxMWk1SkxiQW9wNzFoZmlrazJJRHJSOGV2RjNpd3A5SVBHUzNxOHp4LzlpTjNac0RMZ05ERnMrR0NKcXFjL3FPWUdNcjVVNy9ZYzNyekhNNjBtdWY5VWQxME9OT0VtQ2VBSDcvM0VUWVo5UVNSSk9GMmU2THFJQy94anpQMzEwMktidkFmZU9XUjhtZ1RlMzByamtNVXhYVWVNOERmYUQ3eEFXcENwVFdDeVBxeTEzVUQ1c09LV29tNmVjbXhVZzJCbEFhQ3dmcjM4cWpuQUx4OEswN2d5NHk3K0xaOFFYRWVzMnhSUkRCc0VlbStMMHN5dks2cXNXUEI4NTVNbEw1aTQyeGRvVE0rbjUiLCJtYWMiOiJhYmRiOGI4NjZlMTIyYzQzMWM2MThlMzc0YmQ2NTg0OGQzMGMyOTEyZGM1ZmI4NmE5MzlkYjMyNjVhNjhmMTYwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587761142\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}