{"__meta": {"id": "X75c3d2854a8eadc0994c3fbd3deed0c6", "datetime": "2025-06-30 16:05:55", "utime": **********.412359, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299554.951731, "end": **********.412382, "duration": 0.4606509208679199, "duration_str": "461ms", "measures": [{"label": "Booting", "start": 1751299554.951731, "relative_start": 0, "end": **********.346771, "relative_end": **********.346771, "duration": 0.39504003524780273, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.346783, "relative_start": 0.3950519561767578, "end": **********.412385, "relative_end": 3.0994415283203125e-06, "duration": 0.06560206413269043, "duration_str": "65.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45062000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00456, "accumulated_duration_str": "4.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.378489, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.772}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3900552, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.772, "width_percent": 10.526}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.398138, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 69.298, "width_percent": 16.228}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.404632, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.526, "width_percent": 14.474}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-236867027 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-236867027\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-901050801 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-901050801\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-153058787 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153058787\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1605365854 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InUyODhnWTh3dWJSOGxVVlYwZGR5V2c9PSIsInZhbHVlIjoicXd4TGFSb1d1eXlJb1dVUm90WitYdDlQUWczUngySnpVYk5LMk1GUk5BMUwrTnFzanZ6cHF3Q2lOWDZXOWQybHF5THlJcFlzNXNaTkxhZFpkcEFtWkFxa2E5K3ZIQitxMUFDUFBFMFlVUUpEMm5YRFJhMGEvbGlsL0ZPamgwRVRodXVhdm84Qklua0oxeEFXSmtTdzY5emNJajFnZytVMlZRakpGREFwSDJYbi9RUHlIamRzd2JxdmpBeEpQZjVIRmQreXpFeGd3K2pRZ3ZyWjd0enc4SFozalJrYjNBcVNZUVFuUjNBV2xPVkZKcnB3VUJJRGJ1QlpLd0E2bjZ4c0VyOVQvMmlqRHN3QThBb2hrY1pVdE1QQ1V0NkxwV21zQVE1eTBWK3R6cVZrT3VpV21TTThwZ3FyVGRiRjN2YVRwY2lncEY4N204Z3JTTEpORTd3ZkpUVlBtbmVQOG5SOXlvNEE4RStOeU9zVVNVUnpVZE9teDVqTjcrUTM3dWVDeG9WcDhpbDNCdG4vM2VXL3ZjRnhvM2xEZG9VaXRXbk5Wa2YzMlU1Zk0vdmNIRUJUZjE2VnlHQy9aWFpCdXRuN2MvMmtveWNFMmZuUU56M1BSR1ZXWU12RmFndEtGc0lRbzhTQnJIK1IrRlF1UDBreFkzVXhuSzkwYjRoVmtuZEoiLCJtYWMiOiI5YjU2ODdjZDBmNGMyZjYxMWM4ZWZhNGYxZWQyYzAzYWMyMWUwNmZhNWZlZDlkZjJjZmVhNDFjOGViY2QxYzgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InprNEo1UHNZbVZiMlVJVTZzRERFTVE9PSIsInZhbHVlIjoiSXpCdXplVVpzR1B3MkczZjJxR3NQVDN6MExHNGZVcVJrN1pRTkdqSXV6cy9NeWt5dml0eTBSeXA3V3hwN2tuZHBIOWlPODg0M1NVUmRCNTg0Nk5ESTNPdXE0YTRDVDdxMVZmTVlDaENDbHZXM3V1THlNbUphSGVadXV4YUtGQSswM1h2Z2tSQ01rSHhCdTYraXNLMWl6aStjWlJLY0ZWb0x0SDFFeEhCL1N0cm1ES0krL0ppdFk4UHBCYmpheVNGcklrWVF6WXN3RitLTkdVWUZpb1A2TndEaGhnbS9oazVRaEdVbjJXdmtJYWVCU21nSklZYzNSWXoycEs5ZWhkU05RNUFONzZNZXpyM1hFSHBCeUFnR25nM1ZqMWNGREZuMVRyUnRWN1lQUHpLK3RLeEpPU0JVMHJIUnZYcVZJaUtYcEtSNk1lTUZVK1krbi9wSlZvRStPVGY1WlBhMlVWYnh5Mko0bG5HOGNqaWZmZzFLa2UzVG1jRytzVHNPbW5oWDlHejVNQkFEd2cwVi95V2VoMWx4bzBXRENpN2FIaVBRNUhXeUNRd1JhTmRIRDN5REtZK3NhTjg0K2lzc2ZhSktJbTc2WVlOaHQ1U1phZ2tPM05yYUFuMDg2Z21mMnlFV3Erald2ZU4zbE9DRXVrRC9mQTB4MjJlM01TeWdXbWciLCJtYWMiOiJmZGFkYzZmOWMyNjY1Y2JlYWE3ZDc3ZTIyZWY0ZGJiZTFkOTMxYjEzNTlkMmEzNmI1YzkyZjhjNzYxNTdmNDk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605365854\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1108470590 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108470590\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-870519117 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw1cUw1cjhISmt5RXdqWCtycjZ1UXc9PSIsInZhbHVlIjoiZDdhTUdjN1lkRnlkeTNKa2VjS3pIVUxGcWlETEcxZERCcmJ1bEloMS9tajkrV2pxZFV2dGJELzZrbEh0TlhVL3kyL2QvQ0xZdGlLYWZPZkFraThRdWJFSitpNjVJUlJKU1VCTFBBcjkzYVdKZWRlNkIwRlIyWHdvSEpBcFV0ZC8xeUs3ZWpJYjcxcmRqdFd5Wkp6bjVuZm5TQUtEZFJ5WExuYisxaURXMzhOc3U2NkF4SHlQMG40VnJ0TXp2YTlMaFVNV1lJUENxUmV6alJ5R09vYjBsLzJSMi8xVHNBeGV4Q0tLUGFiRy9UOGJJZTU5Vk5ZeUxtK0NjbUo5RXE1Z2FXcXJycXZqL3poeEkwekJwR2J4cnNvN08waklTM1Z0U1orMkQxR3oxbVViOEJUMmwrby9uYzFCbmhSdXZ0Q1N6NmlGTm9IZlYwYzdBbEhuYkc1RXYyTjV2RVJrWHJNRDRvTEpPM0JGZVRuaWtaM29UbGI3QW9pU1ZleitJaGJpZVBxSkY4QTIxZXJPYytLZ2pKUHcyVktiSWxFZjhXRkprakMrVkRtR1lSbm1KTldTUlFNdU80Y0VEeGRybll3WFp5RnQyTjU3cjQxM3FOMXJET21rcjBkeEJ6Z1ZGc3krU2M2bUJ5OWVVcG5oSWtZRFFpUXZaZUhwZlJmekRFRUYiLCJtYWMiOiIyMmE5NTc3Mjc2NjU1YmYzNGUwYzhhODQ5OGQzYzMzNjYzMDlhYjNlNTU2OGY4OGYyNWI3YjRkNTA3ODRmMjk1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpaK3NRMFRGM0taWEluRDEyTy93UVE9PSIsInZhbHVlIjoiemZQWmx2M1UrN3U2Z3lmOXhuVXFUWFdmMHVuRkc5a3JaZmJYMTdMRGpUUFJVdkhJQjJxaFNwYUs0ekR0amk0b0RXTjhTZVpaSWNUdXdxMTVXVURZNFVybjU0eTVQSzluRFdoV3FUYmZxWWNKUVpMcnc0ZCszemh3SzllZUZ4enFOUE9CQ2hjdkFpWStNMzNnNFQvcGY4ZG12eTljT2t5a09aUmtNUTJlUnAyYjBxVDZ1bmM4QnE1WWVKc3ZCbHQ0Q293OVdWaXpOMHR1NHQwV24vMW1ZaGdoNWhIMlZzM3FDZ3kraXc1V3MxRTR4MHJPbG1lR3pFN0dFU0ZwZlY0ajFKNW9ITXo3QTBnViszWTdjWFdFOENTbVFlV0Y4TWdaQ3BkWTFvVEppUTUvSkNhSUpBS2syZDh2clFVYW5kSWtTNzRQL091QUZoSHNGSlBmMEcrR1dJZVp1NlRMRzdHOHFydXNIUGQ5bnZnRFM4YU5ncVd1a2g3N1FnQzhDS1hkeXhKM2VGUWh2ODJDU0lsWGJrNDJudVJkWURkRmhLWlFIdGFzRlJuTnRHMDd3UGd5THFqRGtTTVltUzFqMGZ0aHJIZ3JrRHlyK0w5em4wc0dZNWJpdm5zTGVDa2ZxeXN1ZkVueS9KYzlZSXhzTG8xeDFpdUwyMmZ4bVBJRjFrTFEiLCJtYWMiOiI0MjI4MGRjZDk5MDM4MjAzNzkwMzBiZTcyN2NhMjE5ZDQ0ODA4Y2Y5MjZlYzUxNmY1NGExZTk1ZjI4NWRkOGU3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw1cUw1cjhISmt5RXdqWCtycjZ1UXc9PSIsInZhbHVlIjoiZDdhTUdjN1lkRnlkeTNKa2VjS3pIVUxGcWlETEcxZERCcmJ1bEloMS9tajkrV2pxZFV2dGJELzZrbEh0TlhVL3kyL2QvQ0xZdGlLYWZPZkFraThRdWJFSitpNjVJUlJKU1VCTFBBcjkzYVdKZWRlNkIwRlIyWHdvSEpBcFV0ZC8xeUs3ZWpJYjcxcmRqdFd5Wkp6bjVuZm5TQUtEZFJ5WExuYisxaURXMzhOc3U2NkF4SHlQMG40VnJ0TXp2YTlMaFVNV1lJUENxUmV6alJ5R09vYjBsLzJSMi8xVHNBeGV4Q0tLUGFiRy9UOGJJZTU5Vk5ZeUxtK0NjbUo5RXE1Z2FXcXJycXZqL3poeEkwekJwR2J4cnNvN08waklTM1Z0U1orMkQxR3oxbVViOEJUMmwrby9uYzFCbmhSdXZ0Q1N6NmlGTm9IZlYwYzdBbEhuYkc1RXYyTjV2RVJrWHJNRDRvTEpPM0JGZVRuaWtaM29UbGI3QW9pU1ZleitJaGJpZVBxSkY4QTIxZXJPYytLZ2pKUHcyVktiSWxFZjhXRkprakMrVkRtR1lSbm1KTldTUlFNdU80Y0VEeGRybll3WFp5RnQyTjU3cjQxM3FOMXJET21rcjBkeEJ6Z1ZGc3krU2M2bUJ5OWVVcG5oSWtZRFFpUXZaZUhwZlJmekRFRUYiLCJtYWMiOiIyMmE5NTc3Mjc2NjU1YmYzNGUwYzhhODQ5OGQzYzMzNjYzMDlhYjNlNTU2OGY4OGYyNWI3YjRkNTA3ODRmMjk1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpaK3NRMFRGM0taWEluRDEyTy93UVE9PSIsInZhbHVlIjoiemZQWmx2M1UrN3U2Z3lmOXhuVXFUWFdmMHVuRkc5a3JaZmJYMTdMRGpUUFJVdkhJQjJxaFNwYUs0ekR0amk0b0RXTjhTZVpaSWNUdXdxMTVXVURZNFVybjU0eTVQSzluRFdoV3FUYmZxWWNKUVpMcnc0ZCszemh3SzllZUZ4enFOUE9CQ2hjdkFpWStNMzNnNFQvcGY4ZG12eTljT2t5a09aUmtNUTJlUnAyYjBxVDZ1bmM4QnE1WWVKc3ZCbHQ0Q293OVdWaXpOMHR1NHQwV24vMW1ZaGdoNWhIMlZzM3FDZ3kraXc1V3MxRTR4MHJPbG1lR3pFN0dFU0ZwZlY0ajFKNW9ITXo3QTBnViszWTdjWFdFOENTbVFlV0Y4TWdaQ3BkWTFvVEppUTUvSkNhSUpBS2syZDh2clFVYW5kSWtTNzRQL091QUZoSHNGSlBmMEcrR1dJZVp1NlRMRzdHOHFydXNIUGQ5bnZnRFM4YU5ncVd1a2g3N1FnQzhDS1hkeXhKM2VGUWh2ODJDU0lsWGJrNDJudVJkWURkRmhLWlFIdGFzRlJuTnRHMDd3UGd5THFqRGtTTVltUzFqMGZ0aHJIZ3JrRHlyK0w5em4wc0dZNWJpdm5zTGVDa2ZxeXN1ZkVueS9KYzlZSXhzTG8xeDFpdUwyMmZ4bVBJRjFrTFEiLCJtYWMiOiI0MjI4MGRjZDk5MDM4MjAzNzkwMzBiZTcyN2NhMjE5ZDQ0ODA4Y2Y5MjZlYzUxNmY1NGExZTk1ZjI4NWRkOGU3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870519117\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-305882067 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305882067\", {\"maxDepth\":0})</script>\n"}}