{"__meta": {"id": "X3eb12e2c3e58631f19fb1fff45c5f125", "datetime": "2025-06-30 16:03:38", "utime": **********.64358, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.06082, "end": **********.643599, "duration": 0.5827789306640625, "duration_str": "583ms", "measures": [{"label": "Booting", "start": **********.06082, "relative_start": 0, "end": **********.537617, "relative_end": **********.537617, "duration": 0.47679686546325684, "duration_str": "477ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.537629, "relative_start": 0.4768087863922119, "end": **********.643601, "relative_end": 1.9073486328125e-06, "duration": 0.1059720516204834, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45553392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00368, "accumulated_duration_str": "3.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6077049, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.402}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.62054, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.402, "width_percent": 13.859}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.627103, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.261, "width_percent": 21.739}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1589434638 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1589434638\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1704619409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1704619409\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-445576240 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445576240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-546935500 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299403618%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndVd1ErOSsvRWhVMjU3a1ZkS25FbWc9PSIsInZhbHVlIjoiekhqekRIeWVZS3NvejZlZWFpY211ZEhtWGVxdkt5aUVZayt3UlRQV3VCUk56cm8rMW0yY2FBaytFM2xqbHB4NFBtZGtna09iYjRnWlBDcjdYMHpwTUllQ0J6OTFQckRzMFdZQXU1UFJWVlJ2NW90a1pkeVBxMkk3NmgzNlR4bkNjYk51b0RMcFdhbGZBQUYwWmJoV0Nrd0d0Y3d0Slc1RkR6NnJReTJHUm1kQVAxbENtTzFPSWUxRXhpZnViM3FmbUhKdDRBWDNjM1M3Tm1nZGFwaTdCSUtCNExYQkMwOWNWeDlJWjF2c01GSnBLaFc4NWhLSUd2Z2xqYWkvYjFtYlU0Z0FFM010WVRmazdMMUxQMXpna00vVnJHMlM1WHVZa3hTN3NESFdTT3YrZEN2V2pVRGFOTUs3c1YyY284cSs0ZW5xdWp3Y1piUEhhWVNqSzhJNnhTZXdTcWJLbGxaOGp1WVdxUFlZLzIrQ3RIYllqQ1VwbEpMYkw2NTVwU01SSWNOZnRzOCsxckljMXZBaG1GSkR5aWsrSmNxVThTcjFZbm5JWm4xa2tySEpTYTc1KzBDSVFtbnYvTGx3QzRneDFqUGtwcVF2VjNSS3Z0dEtjcUZpTTBQbGFsc25wQXByTm82YWRLSmVWUXZlYS9RR0pQMS9JVVJuZzl5eXAyNkMiLCJtYWMiOiI1NWYyNjc3OWUwM2IwYWU5ZmVjMzgxYzM4OGE5ZWE5NGM2NjI4NzFiN2M0NDBkNDVmY2U3MTY0M2YxZjQzNWQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZHcGlEQ0QvRVRiTVk4dDlZOWxsSUE9PSIsInZhbHVlIjoiZ3NxUWFWWmVFVnZsZTRPbTkrWDM4MDlyVFBGUm9zSnNTY1JRZ0tsTFM3Qklmc0syOVo0eTE2Q2xVa0xPOWZKSlRuN2dDUkYrMTVGWGYrKzdpY2VOUzVZUmxINUVvbS9pZi9oWWYxODIrVHc5eDBHWEVQWDNpbS8wY0hLc2szMm1McWtoWlY2bDNaODBDTmZpcnRSS3RNZXNxcUJNVlZSWDFmZXJNUWdiNkhhUjgrVEIrRFRRd2ZRQ1J1QkpQMFBvenl6SitucGI3QkVCQW4vK3A0M3RzZnByNUZMQlMyM0M1ZUR4VmtESWZESDZQdXpDZ3dTcDd4L096RkdVc0VMbG80bW9WdzhDVjFLMG5EbFdialVKYnluVWNVQkdoUlp3R2swa1BySE5TRERTQVkyVEdaOFlYRklnUGV6Uk51amc0ZFc2dzdDbEFPSXVMYkJzaHB4QVI0YzJ2TzNaQUhYc0FnaUNXNk45ckVuMWhoYVROS1pxaGVzSkxlMTF0NWUxM3F2WTlxOVVVNUZtS2pxZUVaZDdKc2ZMblJrNERkcjFaT29jOFpvR2Q3SkEvblBFWHpkZ05BakpiSG01V3dJMkcyanlSV3BURHZRUUd6QzRnZEJVV1ZrQWJyU2tYbUduMmhLT2NOYW9kMzFNZEpyMWQrYkJERU0wK2FxMjc3MWciLCJtYWMiOiIxM2U0M2MxZmRkYjc0YzZkZjc4ZWM5ZDI1YjAwMzBiMmFlZmJhMzVjMzY3NWY3OGIyZGE0NDkxYWRkZGE2Nzg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546935500\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1676611819 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676611819\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1960580449 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:03:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgyWjI2K1NJdVhzSkRxUGV0MU9rZ0E9PSIsInZhbHVlIjoiaXlyMVE5MTNKTEhMN1dDRk56emRVSURoUzdUemRiOEtTZWduRjdsdkNVWmovVkpBT3FiSHdGUzFXU1dldHBSREdpL1c0c2FjY2V4Z3B5R3oxWVdLc1loRmFpR3lUVkhJVkI1ZCtWRi9zY2x4c2x2R2h5bW9DQnRSTHBZaUtPYUtZU0Q2eG01dzJMSDQxcGhuMzhZSm1WaUtxQkt0elZTR0NLR0VHbWZ5L2pLQmI2cGwxK2pFZHBqU3QzY0xPQ3JGR3k1UEFUOHJIQUErb2NSR203N3Q4Q2thZ3ZoUFpDbmJWRkZnakZ5dld6RFpqUWh4UEkySWNySmk0R05hb2xiWjVweDZXRUpCUzJzNmYwK2RESG9IaHYyYVlieFpvZ3U1RFVFVjh5SEFTSG1SWmdnSUFTRjhNWWFSVDVxdnIzTWwydW9UMXFEUTd2dE9MR295Q3ZkY0x6clk5S09ER2orQWJVQWNCZnlXSFcxaXBNMHp4SWpqQXZkdlVrb2lMbW0zYytKQXk3YzFZN0NoL1A2ZXBPU21rcWh6U2F3YnJoK3RWaUpkUUNJS0tZU1doS2d1cnNMUEhsVVhSTm9nV1piKzZKcUJLTjF1WHR0SGtPcGNnWThVQzMrWXNiMnlsY0t4SjU5K0kxeUJ1bk9oNVlSUGJhRzY0bTFyWWV1ZEFNeGYiLCJtYWMiOiIyNDRiODFjOWM4OTBmYTNkMzdiMWU0MmI4ZGZjYjM1ZTVjNTBiZWRmMDgyMGE4MTBhZWZiZTJmYjI3OGE0ZWY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhHUlhZZk5XNmIwTG5YMitubUdZaGc9PSIsInZhbHVlIjoibVhIT3FaU2ltWlRMaWRZMEgwZWZtNlFocG5MK1ZTc1JabXZCOG1QZzBSQ1d0bG5vcFhoMlUyRWx4ZHJJZCsydjN0UWlaOGdONkZPaTIzUnJCeFJxM3lyNFF6bGhIZVNOTkUwN2ZvQWM4cXhXRUlvRnZxTmR5NS9HZ3NOUXlzMG56Q3hLcjVKQ3pyNzNzQkVNYU5LNnVOVERhWVdodW5FZitoTFR0ZktQcGpxcHJzTm9sYkFDYTRKZ3g4TExyVytUbUZlbUtUem9UaWE1ZUx2Sk1DVTlrckRYYWovQjBhS25QSFQvNjZwMzlMVXJNc0hkWThQSE9CTlo3VzJYM0ErblJyZm5GcVVuUEdCQUhqUjhWbXBiUk4wNW5ybkFvNjF0SFNDSlRTQ2s4RWJMR2hWbnpuNk1GdnhVMHZuOFQ5cm5WcDFKRGZYM1VsWWJoVDRLVDkrRkdGSnZaMzRBZzd1dXVSZ0ROaXJtSWNIR0w3ck5KU0s4ZTBIazk1eWpuMjNOQitxOUgvckxmVUJubkNleEM0L3p3UkR5Y0tWbFRaZ2dyV2paMkZYSzdJM0FQMUtVRGxUL3h2K0ZHT2xVKzNZcGtzNnV3Rk1yYU5lNnNNaG9NT1RPY2grdlQxTlNGU2NYV1JXVWFVWVR2WXBpM3F5SHVtZ2FEY0JSem9JZmlCS1oiLCJtYWMiOiJhZjcxMDA1OWMwZWI0YWI2ODgzZTIwYjdjNGFmYThiMzBiM2I0MGU3NmE0N2EwMzQyOTM0ZGRjZjFmYjVlNTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgyWjI2K1NJdVhzSkRxUGV0MU9rZ0E9PSIsInZhbHVlIjoiaXlyMVE5MTNKTEhMN1dDRk56emRVSURoUzdUemRiOEtTZWduRjdsdkNVWmovVkpBT3FiSHdGUzFXU1dldHBSREdpL1c0c2FjY2V4Z3B5R3oxWVdLc1loRmFpR3lUVkhJVkI1ZCtWRi9zY2x4c2x2R2h5bW9DQnRSTHBZaUtPYUtZU0Q2eG01dzJMSDQxcGhuMzhZSm1WaUtxQkt0elZTR0NLR0VHbWZ5L2pLQmI2cGwxK2pFZHBqU3QzY0xPQ3JGR3k1UEFUOHJIQUErb2NSR203N3Q4Q2thZ3ZoUFpDbmJWRkZnakZ5dld6RFpqUWh4UEkySWNySmk0R05hb2xiWjVweDZXRUpCUzJzNmYwK2RESG9IaHYyYVlieFpvZ3U1RFVFVjh5SEFTSG1SWmdnSUFTRjhNWWFSVDVxdnIzTWwydW9UMXFEUTd2dE9MR295Q3ZkY0x6clk5S09ER2orQWJVQWNCZnlXSFcxaXBNMHp4SWpqQXZkdlVrb2lMbW0zYytKQXk3YzFZN0NoL1A2ZXBPU21rcWh6U2F3YnJoK3RWaUpkUUNJS0tZU1doS2d1cnNMUEhsVVhSTm9nV1piKzZKcUJLTjF1WHR0SGtPcGNnWThVQzMrWXNiMnlsY0t4SjU5K0kxeUJ1bk9oNVlSUGJhRzY0bTFyWWV1ZEFNeGYiLCJtYWMiOiIyNDRiODFjOWM4OTBmYTNkMzdiMWU0MmI4ZGZjYjM1ZTVjNTBiZWRmMDgyMGE4MTBhZWZiZTJmYjI3OGE0ZWY2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhHUlhZZk5XNmIwTG5YMitubUdZaGc9PSIsInZhbHVlIjoibVhIT3FaU2ltWlRMaWRZMEgwZWZtNlFocG5MK1ZTc1JabXZCOG1QZzBSQ1d0bG5vcFhoMlUyRWx4ZHJJZCsydjN0UWlaOGdONkZPaTIzUnJCeFJxM3lyNFF6bGhIZVNOTkUwN2ZvQWM4cXhXRUlvRnZxTmR5NS9HZ3NOUXlzMG56Q3hLcjVKQ3pyNzNzQkVNYU5LNnVOVERhWVdodW5FZitoTFR0ZktQcGpxcHJzTm9sYkFDYTRKZ3g4TExyVytUbUZlbUtUem9UaWE1ZUx2Sk1DVTlrckRYYWovQjBhS25QSFQvNjZwMzlMVXJNc0hkWThQSE9CTlo3VzJYM0ErblJyZm5GcVVuUEdCQUhqUjhWbXBiUk4wNW5ybkFvNjF0SFNDSlRTQ2s4RWJMR2hWbnpuNk1GdnhVMHZuOFQ5cm5WcDFKRGZYM1VsWWJoVDRLVDkrRkdGSnZaMzRBZzd1dXVSZ0ROaXJtSWNIR0w3ck5KU0s4ZTBIazk1eWpuMjNOQitxOUgvckxmVUJubkNleEM0L3p3UkR5Y0tWbFRaZ2dyV2paMkZYSzdJM0FQMUtVRGxUL3h2K0ZHT2xVKzNZcGtzNnV3Rk1yYU5lNnNNaG9NT1RPY2grdlQxTlNGU2NYV1JXVWFVWVR2WXBpM3F5SHVtZ2FEY0JSem9JZmlCS1oiLCJtYWMiOiJhZjcxMDA1OWMwZWI0YWI2ODgzZTIwYjdjNGFmYThiMzBiM2I0MGU3NmE0N2EwMzQyOTM0ZGRjZjFmYjVlNTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960580449\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-902507702 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902507702\", {\"maxDepth\":0})</script>\n"}}