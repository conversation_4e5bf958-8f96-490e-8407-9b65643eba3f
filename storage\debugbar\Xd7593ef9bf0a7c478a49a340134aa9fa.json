{"__meta": {"id": "Xd7593ef9bf0a7c478a49a340134aa9fa", "datetime": "2025-06-30 16:17:32", "utime": **********.543272, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.101946, "end": **********.543288, "duration": 0.4413418769836426, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.101946, "relative_start": 0, "end": **********.481884, "relative_end": **********.481884, "duration": 0.37993788719177246, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.481894, "relative_start": 0.3799479007720947, "end": **********.54329, "relative_end": 1.9073486328125e-06, "duration": 0.061395883560180664, "duration_str": "61.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43346776, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.019969999999999998, "accumulated_duration_str": "19.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.51069, "duration": 0.019969999999999998, "duration_str": "19.97ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2139168485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2139168485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-711517114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-711517114\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-665460490 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665460490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-363126607 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363126607\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-232274860 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlU3aDc4MHlQV2pxUHRNZ3ZBbUNQTUE9PSIsInZhbHVlIjoiZitMcnM0aHlJaXI5aHNFRUt1QkdxTGtHTmlscVF4bE1PeXBoS0FRNkd4N3kraitXOUt6TUsxRFcrZytpR3RBOEJIclhvdDUyeHk4NDhIT2JyaTdyUUxLdXZGS3FGczhEaWw4RmhKVmRxSDY4NXI1QzAybmhodWt3eWlmMlJqSTlQN1hHeGtHTGFOM0FIenp4eGd0SnE4M0dFWTdkam81THNyaGs2MzZla2g5bDlPNGJZa0gzMnpubU1iZ0Uxa1I5MEV1bEpSdkZ6Qm1VeHVYTFlhRWh2VSt2Q1o2VFppalBZYkdqQWNrZExyWUZFNjdtNW9ySjZrM3hnbmtPQURUZ2FrTkdTWnVGNVNHbzB2WTNZOTNFK1JscHJQRW5jZ1NhY2xwWCtlNyt5ek9pSTBzTktrZ1pydVU1dmlKYXYvZTRBZHB1ckpiZEtabk9FRjVlY1JmL2Y3ZEQwc3o2cEtZU1o2a1o4Skl0VzEvUGhMdDBYU0FKa2JWeW5JWGxSdDV5ZVhmcnlxZkZRNlZxblpIVXY1K0NJT1I2dW56TjVmTUZMOU5zWW1YMTNWL3J6WXkvNStwUE1lZkxBeGtzTGdYUEJxRFR4Zk1QTDRxMGF4d2Vmb3A3aXpKd05kdm1RVW0wUCtaYW9CbDkrRmNibmI2dzlVT1JRZEdJRmVkQjhadTEiLCJtYWMiOiJjZDU2ZjRmMDY2OTAxNDRkYWYzYjMzMjQ1OTFmNDM0YTRmOGFmNmZkNDJjMjg3NjllYTdjOWYyYzQxZTAwNGUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1ZWEhxMWVUY2VURi9Uc2lRZmsrVGc9PSIsInZhbHVlIjoiUG45UWYralc4SUgzaUVNc1F0YmZFUEpsWE5vRUkxVDRxcW8rWmJVcFAzdDZoakhQdHB3SUJ5M3IydXZxMUNNWUFIT0xlNUxxZFJ1OXl5NWErWGhWajI1cVBFWDgxNUMwQkxmVy9IM2lFSjlPVWxUclhOWlVyWDlWdDlJUzFEUm50Tk8rSGJMVHViWm9nekJKaXRqMWZHSFBmTGkwTzhseU9MQUZIdjRORmhHMFBzOTNrTE9OckRSNWwwSCs4U2h0L04vSlN5eXMyRnlLU3NGQ2s5WDR4TnE5MUgwQm9venhQVEg1eERKN3QzSlNCMnlQL1hmNE9QTnMvVHgxQ3NRdW1BZkpOSkFEVVJDeG5DSW9pOGp1S3l3NGtzMUk4Q08rajBnc3RtQ04xbTkrTFBXWnNXcm5BZU54NFpDdHVrRTFaM3BJcE1HRmRQZ0p2eHZ5MTZIblVWbnFiN2NyVlNTaG5aSTdKNWpQbDVELzNyRGVqQ2s5eHIrbUVKM1FLdUJ1Z040b1M1VXdWQWJPTVdsRTJWalVwVUpzU1NGN0hsZXlkMldxMmx6M3JlQzVYRlJZMFEzZ2d5eWJEYXVidmJlVkV6eTFyOGE0MFUzeFpkK2haTG9DWS8xZ3Vqb2czTER4clRLbk8rUms4eC83OEc0dDVZelJrYzAwSnVqUzNPN0ciLCJtYWMiOiIyMDVmMGVmNTQ2OGNiYTI1ZDJjNDU5ODIyNDY1ZTc5NDA2MzY0NWZjMTkxNjE3YmQ0MDEwOGNhMTBjYWY5MWFjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlU3aDc4MHlQV2pxUHRNZ3ZBbUNQTUE9PSIsInZhbHVlIjoiZitMcnM0aHlJaXI5aHNFRUt1QkdxTGtHTmlscVF4bE1PeXBoS0FRNkd4N3kraitXOUt6TUsxRFcrZytpR3RBOEJIclhvdDUyeHk4NDhIT2JyaTdyUUxLdXZGS3FGczhEaWw4RmhKVmRxSDY4NXI1QzAybmhodWt3eWlmMlJqSTlQN1hHeGtHTGFOM0FIenp4eGd0SnE4M0dFWTdkam81THNyaGs2MzZla2g5bDlPNGJZa0gzMnpubU1iZ0Uxa1I5MEV1bEpSdkZ6Qm1VeHVYTFlhRWh2VSt2Q1o2VFppalBZYkdqQWNrZExyWUZFNjdtNW9ySjZrM3hnbmtPQURUZ2FrTkdTWnVGNVNHbzB2WTNZOTNFK1JscHJQRW5jZ1NhY2xwWCtlNyt5ek9pSTBzTktrZ1pydVU1dmlKYXYvZTRBZHB1ckpiZEtabk9FRjVlY1JmL2Y3ZEQwc3o2cEtZU1o2a1o4Skl0VzEvUGhMdDBYU0FKa2JWeW5JWGxSdDV5ZVhmcnlxZkZRNlZxblpIVXY1K0NJT1I2dW56TjVmTUZMOU5zWW1YMTNWL3J6WXkvNStwUE1lZkxBeGtzTGdYUEJxRFR4Zk1QTDRxMGF4d2Vmb3A3aXpKd05kdm1RVW0wUCtaYW9CbDkrRmNibmI2dzlVT1JRZEdJRmVkQjhadTEiLCJtYWMiOiJjZDU2ZjRmMDY2OTAxNDRkYWYzYjMzMjQ1OTFmNDM0YTRmOGFmNmZkNDJjMjg3NjllYTdjOWYyYzQxZTAwNGUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1ZWEhxMWVUY2VURi9Uc2lRZmsrVGc9PSIsInZhbHVlIjoiUG45UWYralc4SUgzaUVNc1F0YmZFUEpsWE5vRUkxVDRxcW8rWmJVcFAzdDZoakhQdHB3SUJ5M3IydXZxMUNNWUFIT0xlNUxxZFJ1OXl5NWErWGhWajI1cVBFWDgxNUMwQkxmVy9IM2lFSjlPVWxUclhOWlVyWDlWdDlJUzFEUm50Tk8rSGJMVHViWm9nekJKaXRqMWZHSFBmTGkwTzhseU9MQUZIdjRORmhHMFBzOTNrTE9OckRSNWwwSCs4U2h0L04vSlN5eXMyRnlLU3NGQ2s5WDR4TnE5MUgwQm9venhQVEg1eERKN3QzSlNCMnlQL1hmNE9QTnMvVHgxQ3NRdW1BZkpOSkFEVVJDeG5DSW9pOGp1S3l3NGtzMUk4Q08rajBnc3RtQ04xbTkrTFBXWnNXcm5BZU54NFpDdHVrRTFaM3BJcE1HRmRQZ0p2eHZ5MTZIblVWbnFiN2NyVlNTaG5aSTdKNWpQbDVELzNyRGVqQ2s5eHIrbUVKM1FLdUJ1Z040b1M1VXdWQWJPTVdsRTJWalVwVUpzU1NGN0hsZXlkMldxMmx6M3JlQzVYRlJZMFEzZ2d5eWJEYXVidmJlVkV6eTFyOGE0MFUzeFpkK2haTG9DWS8xZ3Vqb2czTER4clRLbk8rUms4eC83OEc0dDVZelJrYzAwSnVqUzNPN0ciLCJtYWMiOiIyMDVmMGVmNTQ2OGNiYTI1ZDJjNDU5ODIyNDY1ZTc5NDA2MzY0NWZjMTkxNjE3YmQ0MDEwOGNhMTBjYWY5MWFjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232274860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-189804059 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189804059\", {\"maxDepth\":0})</script>\n"}}