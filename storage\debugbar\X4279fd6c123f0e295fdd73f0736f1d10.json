{"__meta": {"id": "X4279fd6c123f0e295fdd73f0736f1d10", "datetime": "2025-06-30 18:10:17", "utime": **********.310804, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307016.938481, "end": **********.310816, "duration": 0.37233495712280273, "duration_str": "372ms", "measures": [{"label": "Booting", "start": 1751307016.938481, "relative_start": 0, "end": **********.253629, "relative_end": **********.253629, "duration": 0.31514787673950195, "duration_str": "315ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.253638, "relative_start": 0.3151569366455078, "end": **********.310818, "relative_end": 1.9073486328125e-06, "duration": 0.057179927825927734, "duration_str": "57.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139720, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01532, "accumulated_duration_str": "15.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2793472, "duration": 0.01467, "duration_str": "14.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.757}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.302155, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.757, "width_percent": 2.546}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.304844, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.303, "width_percent": 1.697}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"American coffee-قهوة امركية\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 105\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1892 => array:8 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"1.50\"\n    \"tax\" => 0\n    \"subtotal\" => 1.5\n    \"id\" => \"1892\"\n    \"originalquantity\" => 200\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1673599173 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1673599173\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2039861437 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039861437\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1023494367 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpCVi9RUHJZd2RrWDlGVmo0ZjZML3c9PSIsInZhbHVlIjoicVZMbEtwbUEvSXdTUU5uTi9rNVFhaTI3TXhNSVlhWmVtZlRRWTNjSVJFdWF3cVVDUGQzakFBR214Um5VczFrQ1FONGtKeFRnbnFMclQ1cDdxd2NxZEpZMFFkWEhlbEN2aXdtaWl4ZXhCTEJKd3ZseXJoNC9wQnVCWVJ6WkFSbGhONHNFT2ZOWEVKVUdBemdJYnBBSE12dDBNS3JxaDk3SE9MUkQrVkpSN2UwWG8veGhYNlZLdVo3Vm8vYWFKZ1ZIWEE5YkhEeXN4Y3VGQmZsQUpOUmFNUHVBMjJ4WlBCMEF0bXd6bzhOanA5Q3lodjFjUXBLVTdmV1pZUVc1QlBNck9oQkZzL2VkVVUrbjM1UUN2dDQ5RGRYekhBRUFUMFlOK1BkSXBoUE91VGYyVVZicmJRQjVoMHhUTVpDazFWSk54VWtTMFNpZTFWWFlYL0RlZVZzR09jZGZFSm1pM1kxbW5DVS8xN3pwSVY4M3d3ak0vZENhbnMveWYvdFE4Vkl1c2xuYVg0SHNKOXlvbHFYK25oQVk1ZTEwVFJqTStQVzVnUmtHbzBUQTQ0V09qcHhpRDhNczVBT0dDa1lENFhNN0tYU1Jud1NybEZpdndNUjlTaG5SQkRPT0RzZGt2N1ZhYXp0dCswU0lmenBWUitIM2lyUHBFVDVKb0JLQkZvWUkiLCJtYWMiOiIwZTUxN2E5NWQ3YmE5MGJmNzQ2ODBmNjBmOTFmMTZkNjk2YzM3ZmVmNTNlMGY5MTk2MDY4NzQ2NGM4ZDcwNDQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFBMTNTTDF2VGViWEdLREkyODZtQXc9PSIsInZhbHVlIjoiZUxteEJaYUZSQTEvTmZvM3pha2xqZm1jNm9uTGtNb21saXd6OWNMWFdOUC9tN1ZEVFV4VmljL3p0alFFMmJ1OGw2RytDSWFYc1B0bmo5RytUWWQ3dDI3UFVoQWpxVGVlU2pTVDVtWW5zYTFIQ2dLRVBQRXhmUSs5NDNCMHZFMGhyUHJ6Nkk1cmpVY3ZpVzNCdVdGZHVick9IUkIzQXRhZXN2RXNyclZDTDhMRUVMVEw1VVJxM2NsaFIrUFRlK2xCdVEveWRNamxXM0N1SDNIWkRpU2hka2RzcWI3OHhFR1VUVVVvMHNFelNYSEMrNkJ2U2lXYzNuMCs3ZVlkaTl1eFdyQVB2MkpqVGNzVksxWmplWVFCTlVvMVB0Z3k3SWRiNnJVV1E3UEZpUWxFQk1TSngvZExyWFNmTkJCa2YwUDhwK2dmSEJ0S3FjdnFVM1RRcWNWL2wyYnlPaXczRGFRRDFBalBGMkpsKzZpWHpSZmhnbG9iZEdQaDh2SWZtOWNwcUw5Z2JLbWJjMjRMblQwVXBSOEJNVjJlR0ZpSGRVK3lCaW1VQ0pTK2ZxNFJQQzBEOGdhTTg0cTkwM0h5R2lyTm82VUk3b0YvaTRNZTllcStlbklzd2pmeEdvbDlHb3lKTE1GUjdWWXh1ZUJBWGl5YWlDN1RPUG0rMUVWLzE3WEEiLCJtYWMiOiIxOTJiMjAyMDJlYTlhNjAzMTc2NWRhMjM0NzJmZmM4MjEwNTg4MzhmYThhZDI5YWY2YzNlNTI1ZDkyM2RlMDMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023494367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1155731505 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155731505\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1977920530 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5FanEwYzRnUGQ1b1lhODhKaXNSOVE9PSIsInZhbHVlIjoiZVAyNUR1alN4Z25vUDdyTThjcGxKZWhZbk9jaW9xSmFMclEvSXE4WmNqak91akhqeS84cVBORTVIM0pVMlpmZEVSRTJIMkhlVENFT2h6VnBnOFJTZko4VUFMZmppd3JHMkZvbURiZ1ZOMDVjMlNIYXJCWmNCdmZBeWJmejd5R01xTkRxckU0YndFNVJuSjJ1Q0IvbTZsbTZTamY4NjkvL3RKOUovVU0xU05BZU9TZWZyZjJWV25xNFA5QjVidFcvbVkyTUlDdzNCWjlwbE1XUmZleXR5V2I2LzJ3bHdLMjVLTDR0M1hGMEdmcGJhSUxlUWlqUlk0Zy9uNStBdUh0L2JObzF6aTJ6OFBCR1RGaGJLdzE5cGJiRzBVRTZuZUV4VzlQMnM1ZEozS3dOZm1Fa3dXeGUrZFE2M0NibmlVRTA3b0VJZWNrbHlIYTNCWVY5TU91Ryt5dSsxMlh0OWJtSlBKZ0JEU0l2Z21KcVRkM3BZUHJkbXBsVk9FNXFxTW5qYUhKTUNTSytJM0JwdXZveTluTVRwTzltcFdBSDdIMG93aXRXeUQxR2FMSW1sd1JSUDdiR05pOFBvd2FwRm5iSjNuTWUwdXU5ZWtjVDlZR0VtbXVXM2UvTG1sQmF1UEg2U25seEwveE9RbUo3L3VnQmIvaEdaT0cwQkJNZlJLN3kiLCJtYWMiOiIzODZjN2VkZGU5ZGIzNGMxNDRiNzc2Y2RjZjcyYTZhMjE4YzZlZGNlYzcwYzJiN2JlODNiYzk2MmY1ZDFlYjQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InprbS9HQml1MFhpOW0wVVQ3b2k1RUE9PSIsInZhbHVlIjoiUENFVjdva2R0UURvS2RIa2YxM3YwL1pTbGNaMGVFSHZWZlczWk5OQWZ6YTMyYzJWT0x6NEhPa2dNQm5JMEZmRm0yYmVIdEYrQ0VuOWpsZkY2T3VyZk9od3ROVjdVQ0l6Q0J4QUFOcFUxR1UxOXZKcUkrdzNjYjN3VGdJMUEySUFaT1FzTW9xaEU5SWZzeEcySHU1NDBmczRjTzg0UXdrNGttMmhSQzRxNnRlUmdQUGVHbnlmQnFjL01Wemtnb2lGWmJlTlN3VEMrYWZLS1BrOUNHUTZjOU56WG05ME1NQlNUNXJXTUJSWUpOb3hqalZnWmVnbEh3N3dWWXJpNHlua1djRGlvWE1OVjlBdDIyYjdEeVRwdEZCVlAycEwrdGlqa3VLeCt3UWxUNlBZUHBDSjZvUUhnNDFhRFl3cVdqMEd4MHZjdFoyNjVjRHRXbGttSno2SS9rd0ErOG9IZEw1dld3OUlRVWdvb3lzMHFEeGFUSmRuVUV6UVM4Rk5kbjFFb2gwSkxGR25zTmx5RmxnRTZNMElYS0hxVlRzelF6emJvdG1iRTRlaTVEZnNxUFAxVml3QW95N21KY2daQzFpaWhQVWEvY3RGV2Y4MGtaWlRvVkduWWlxbWMycTA5RjU2cGs4TjA5QW42aGJpT3RDa0E5YVFQdFdjUTVLbG15UWoiLCJtYWMiOiI4NDM2NjM2MWUyY2Q2YzEwMWQ3YjUzZTI5N2M0Mzg0YTU4MWU5Yzg2ZDUzODAzMjRlMzVmOTEzYzZlY2Y5ODYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5FanEwYzRnUGQ1b1lhODhKaXNSOVE9PSIsInZhbHVlIjoiZVAyNUR1alN4Z25vUDdyTThjcGxKZWhZbk9jaW9xSmFMclEvSXE4WmNqak91akhqeS84cVBORTVIM0pVMlpmZEVSRTJIMkhlVENFT2h6VnBnOFJTZko4VUFMZmppd3JHMkZvbURiZ1ZOMDVjMlNIYXJCWmNCdmZBeWJmejd5R01xTkRxckU0YndFNVJuSjJ1Q0IvbTZsbTZTamY4NjkvL3RKOUovVU0xU05BZU9TZWZyZjJWV25xNFA5QjVidFcvbVkyTUlDdzNCWjlwbE1XUmZleXR5V2I2LzJ3bHdLMjVLTDR0M1hGMEdmcGJhSUxlUWlqUlk0Zy9uNStBdUh0L2JObzF6aTJ6OFBCR1RGaGJLdzE5cGJiRzBVRTZuZUV4VzlQMnM1ZEozS3dOZm1Fa3dXeGUrZFE2M0NibmlVRTA3b0VJZWNrbHlIYTNCWVY5TU91Ryt5dSsxMlh0OWJtSlBKZ0JEU0l2Z21KcVRkM3BZUHJkbXBsVk9FNXFxTW5qYUhKTUNTSytJM0JwdXZveTluTVRwTzltcFdBSDdIMG93aXRXeUQxR2FMSW1sd1JSUDdiR05pOFBvd2FwRm5iSjNuTWUwdXU5ZWtjVDlZR0VtbXVXM2UvTG1sQmF1UEg2U25seEwveE9RbUo3L3VnQmIvaEdaT0cwQkJNZlJLN3kiLCJtYWMiOiIzODZjN2VkZGU5ZGIzNGMxNDRiNzc2Y2RjZjcyYTZhMjE4YzZlZGNlYzcwYzJiN2JlODNiYzk2MmY1ZDFlYjQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InprbS9HQml1MFhpOW0wVVQ3b2k1RUE9PSIsInZhbHVlIjoiUENFVjdva2R0UURvS2RIa2YxM3YwL1pTbGNaMGVFSHZWZlczWk5OQWZ6YTMyYzJWT0x6NEhPa2dNQm5JMEZmRm0yYmVIdEYrQ0VuOWpsZkY2T3VyZk9od3ROVjdVQ0l6Q0J4QUFOcFUxR1UxOXZKcUkrdzNjYjN3VGdJMUEySUFaT1FzTW9xaEU5SWZzeEcySHU1NDBmczRjTzg0UXdrNGttMmhSQzRxNnRlUmdQUGVHbnlmQnFjL01Wemtnb2lGWmJlTlN3VEMrYWZLS1BrOUNHUTZjOU56WG05ME1NQlNUNXJXTUJSWUpOb3hqalZnWmVnbEh3N3dWWXJpNHlua1djRGlvWE1OVjlBdDIyYjdEeVRwdEZCVlAycEwrdGlqa3VLeCt3UWxUNlBZUHBDSjZvUUhnNDFhRFl3cVdqMEd4MHZjdFoyNjVjRHRXbGttSno2SS9rd0ErOG9IZEw1dld3OUlRVWdvb3lzMHFEeGFUSmRuVUV6UVM4Rk5kbjFFb2gwSkxGR25zTmx5RmxnRTZNMElYS0hxVlRzelF6emJvdG1iRTRlaTVEZnNxUFAxVml3QW95N21KY2daQzFpaWhQVWEvY3RGV2Y4MGtaWlRvVkduWWlxbWMycTA5RjU2cGs4TjA5QW42aGJpT3RDa0E5YVFQdFdjUTVLbG15UWoiLCJtYWMiOiI4NDM2NjM2MWUyY2Q2YzEwMWQ3YjUzZTI5N2M0Mzg0YTU4MWU5Yzg2ZDUzODAzMjRlMzVmOTEzYzZlY2Y5ODYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977920530\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">American coffee-&#1602;&#1607;&#1608;&#1577; &#1575;&#1605;&#1585;&#1603;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>105</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1892</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1892</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>200</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}