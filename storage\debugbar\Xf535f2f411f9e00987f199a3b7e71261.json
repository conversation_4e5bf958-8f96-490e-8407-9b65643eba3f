{"__meta": {"id": "Xf535f2f411f9e00987f199a3b7e71261", "datetime": "2025-06-30 16:07:32", "utime": **********.441775, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299651.95224, "end": **********.441789, "duration": 0.489548921585083, "duration_str": "490ms", "measures": [{"label": "Booting", "start": 1751299651.95224, "relative_start": 0, "end": **********.370752, "relative_end": **********.370752, "duration": 0.41851210594177246, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.370762, "relative_start": 0.4185221195220947, "end": **********.44179, "relative_end": 1.1920928955078125e-06, "duration": 0.07102799415588379, "duration_str": "71.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021379999999999996, "accumulated_duration_str": "21.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.398731, "duration": 0.0202, "duration_str": "20.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.481}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.428479, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.481, "width_percent": 2.572}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.434776, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.053, "width_percent": 2.947}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1748776570 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1748776570\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1333905227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1333905227\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-649875405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649875405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299649939%7C13%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhaYk9scElGMlh2L3R4SCtoMnk0ZGc9PSIsInZhbHVlIjoic2ZzckU1RFgzaGMvc2NzRnZTUDdjR05TWjF4MkMrUkEyTG44ZGFUMGVkcmlhMUNCL05rRkNDK0llWUxod0VJYjQvS0tJa0Q1SDNkQVBGVGdOa1JhVTlYUTJQY3RFM2l5UVFGTkdsSjE1T1gzdUF2eGlmUWRxOHFoYWdEcTlSc01hRWpTWXhnUWFwSCsxU3YydEpNQkdMYUxtUkFZUlZuaE1YTC9CNFZGemVLYmZCQk96N1c4T0Mwa3RzL3p2YWVib0hSeWtPT21jTEZTeDBrWUV0RlRGN2V6RmNQaWtVajhQS3NqOXUvUkNSNVBZeGI0OGpyN1ZOM0lOeithU1JEMW85TXpwQ0l6ZUJUNHZGQ2lSRFVySDAxY0QyQXJxQ1J0MG54bFNOV0FnZ0wrS1RiNThLM0J6dFV5aXkwN09LL3ZCd3lmYWdFL0dEeGVNckJoTkExWFBSWFN6aWIwNE5aQTJXcXdQei9UWDR1Y0JCVDVGbG91YlFUMS84Vk1FeGRRZDNQUHdFUGRsQnlYL3ZyL2VhZjk0NHRBZ0k2dmRHZ3N0ZDJDRW1NZG9PZUdVL1EzUkFxNjU4MkkwMDVJVGRDQVl1NWlNc2NZUFhId0JUaU5jRGZ1UmdaWGNMQ3BYUGVKMHk1VXR4anI2RHBnREZrRWd6NVIrMFZSTUpneHEySzAiLCJtYWMiOiI2ODE0N2EzMmY5ODkxYzdkN2Y1MDZjOWQ2ZmNiNDQ2M2NkN2I1OTJmOTcyN2U0NzQwMWVhYjQ2ZWRhNWYyN2UzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJ1Q1cxL092YjlYaWtJUnI1Y09Xdnc9PSIsInZhbHVlIjoibWFhcjZjdm5vejlQWTRKSGtNcjlESXE0ZVQ1RmNham1NWit5VEZMam5hUFFOOG94bVIyN21mR2ZBMGR5Y3g2ai9scFMrdkUyMkVyUmZ3OWpTbDdRcDJYUzhoK1VkT1Y0cmFKbXBkeC9PenhSMDYrU1RMdXQ5c3NSUUNEdUc3UkJBdlFVNjZGMnV6Mk9hcnl1dzh5eU9ORFZKOTE2UUJIZFdyZTZqR095MVhtbjFuR2J0d0hybGUzcjV4ckFYdThwd3VPNTBKK0MvRGNpL0JPSkZYQkdPbnQ1cnBaMVRnOFVXWU14S3JubGpoSEIyN29LZmxqd3VYcDEySVh0NWhkMHVPOHk1TGp0Z2p0cWNRS3AzU3pVWmVJdmEvQk02TFZBNElWSlFBcVJpVmdlMi9tbDlhMmlkb2dqRTdwVkJpd3BIZmgxSTdpREQrMjhlb0ozQUwrV0ZHVW5ONytEQVJCVFVIZHJNTnJEVmpGZjNhNXNRN3k5MkFubnBNM2Q1Qlk2U29DRkJIc2pVSGRVTFJRMGx2UC9SSWhvUWcyUzJveXczakxobWxuSHZLejZnSm0xOWJ5Tkp5UXhnY2RmN1JPSzJTQU9SMDRDRk1TMU1IQkJpNE92TlpQOUNwR3dIQTRNTkFzY04rZGhYM1IraHZKSk9BY2tqMWNwSFk3LytCRG0iLCJtYWMiOiI4YzdjMGYxOWI1YzZmZTFlNDQzN2FkYzI1NTdlZTUwYTNlN2JlMzA5M2RkMzg3MzU2ODg3MmQ4OTg0YTI3NWQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-428537528 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428537528\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1671440647 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZyajZTK1ZGZDBOcFNWcGFud1ZSWmc9PSIsInZhbHVlIjoiT1FNemFEZmVJdHhTTDJMV3phbVhibTNudnF1QTdrSEJ3bDI2alBmSkplbDc4K0dRWjU2M1NyWVNid2MyVUxIUzlPUnpicFpjdk53ZTM5VTZrdTFGSUhreU1JOTZLQUxWWENYUVVxbDRsNjF1aFlaN1NFMnQvVnZvYkJXVlBLZ3FMbnNic1VSeWpJSVhDUXFrVkd6NktmN0NlVUo5WW5xdkpyS0ZORlZJRTc1aVBXQ0h6eE5nMDZkU1pUUlpSNHJzZWprYkpCa3E2c3ZJMzZySjRxamZuTlMxVUp6RGJGckJrTGRkT1F1c0MwQkRxcFZvSFV2MDdpWWwrOFlFbGZOTzZBTUw3Y2dRS1ZWRi9Za1FqZGtFWUhYZzUyR3ZReTVRVmtvV2xRVmxyUTRnbk5pR2xPK3BPb1RpM2JxdGt6RnI3RDBTcTZPRVZ3a3B1VytXNmkxc1dNd2JwRlg5bFhldXU5aVE3Q3dPdnM2ZEM4Z2dMbzR1MHA5eVdOUTFBbndlWW45bFg2YmRGd3FNNVQyZUl2clJUTGJtUGNqOVZEWUI1SGdtREVXb3VnSmNJUStTZXRnazJFNVR6OENYTHBmTXdMVVcwTDZiTEFxUlRkSXRBdUJtN3BrUkhIZ0xKWjExdU9nRFgrTEFvUkhhNTdiS25tbFptK0RENkVqLy9aYWEiLCJtYWMiOiIyZTg3OWM2NzYwYjFkNTdlNzU3YmYwMDhhMmZhOTgxMWU0OGZlNzNmMjVmZDZkOThhOGRlMzM4MzIzMzJjZmZhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJZdm8xVUUySk96cTRDczdlOVkwWEE9PSIsInZhbHVlIjoiOFVDVTNKWmI2T004WEJlaEdKbkZ4Z21BbCtuN0ZyNTZPNHpoTXBKUnBhQzhzWVBNWjRERGJxTHlEeW43N1FvR1lqNkVVS25KZ0ZvNW4wUTRZLzRxcTJWSGEvYUZDUjBUZnJ6MEtjM1dHQUcxTUd2TXhiVU9RUXZUTGY0dWVHVHM3V1FYS3VGUEZQWSszV0hXbHd5dlRBNnhYVTg2UUFXTVFacC9BNmd2WXVjNzdvY21LNWFiclR4RVpJVUtYVE1uOXBpOTJSY0tsVUlJcDlqbnczWm14cnYwN0pkYU40TWwrdC81dFo4ZFBGT1dYK3pBMDVmNE4yT1VOT0RyYWZEK2FIdFVQWUxqcFRqWHZjaW1mRmg5UUNwM0NIdDVvOFJpb2E5Tzd6RmdBbTJheW5qdHViV29iTERKaDVkTHh1M3lCWkp1OUZZU09oeVZadDBZcEZWckJzMHR6bVhSSmEwR0M4SnpXcktvSiszU1dMaWQ0cWYrZk5yY211OVRKRWdzR0x3RkJ4N0daVmxDOEVMenNtcUZzZ0Q4OFpjcWl1R0YyMjFmbUtJbzJSaEJ4a3pVcVNIRmdlWGZPSXdOUGpqL1RrcStHQmRvWk1jTXViemlvWUxkVzcxa2hTM0Z6bURRRjJ0VkJyQXd1VkZVMzdMVHpjNm5wTUlWelU5UloxOUkiLCJtYWMiOiIxMWFkOTk3NmVmMmZlZTI2MjMzOTg0MTMwMWY4YWE2M2E5MTQzMWUwMDRlNDM4ZmFhYWM2YWQ5ZWZhODAwMmVhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZyajZTK1ZGZDBOcFNWcGFud1ZSWmc9PSIsInZhbHVlIjoiT1FNemFEZmVJdHhTTDJMV3phbVhibTNudnF1QTdrSEJ3bDI2alBmSkplbDc4K0dRWjU2M1NyWVNid2MyVUxIUzlPUnpicFpjdk53ZTM5VTZrdTFGSUhreU1JOTZLQUxWWENYUVVxbDRsNjF1aFlaN1NFMnQvVnZvYkJXVlBLZ3FMbnNic1VSeWpJSVhDUXFrVkd6NktmN0NlVUo5WW5xdkpyS0ZORlZJRTc1aVBXQ0h6eE5nMDZkU1pUUlpSNHJzZWprYkpCa3E2c3ZJMzZySjRxamZuTlMxVUp6RGJGckJrTGRkT1F1c0MwQkRxcFZvSFV2MDdpWWwrOFlFbGZOTzZBTUw3Y2dRS1ZWRi9Za1FqZGtFWUhYZzUyR3ZReTVRVmtvV2xRVmxyUTRnbk5pR2xPK3BPb1RpM2JxdGt6RnI3RDBTcTZPRVZ3a3B1VytXNmkxc1dNd2JwRlg5bFhldXU5aVE3Q3dPdnM2ZEM4Z2dMbzR1MHA5eVdOUTFBbndlWW45bFg2YmRGd3FNNVQyZUl2clJUTGJtUGNqOVZEWUI1SGdtREVXb3VnSmNJUStTZXRnazJFNVR6OENYTHBmTXdMVVcwTDZiTEFxUlRkSXRBdUJtN3BrUkhIZ0xKWjExdU9nRFgrTEFvUkhhNTdiS25tbFptK0RENkVqLy9aYWEiLCJtYWMiOiIyZTg3OWM2NzYwYjFkNTdlNzU3YmYwMDhhMmZhOTgxMWU0OGZlNzNmMjVmZDZkOThhOGRlMzM4MzIzMzJjZmZhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJZdm8xVUUySk96cTRDczdlOVkwWEE9PSIsInZhbHVlIjoiOFVDVTNKWmI2T004WEJlaEdKbkZ4Z21BbCtuN0ZyNTZPNHpoTXBKUnBhQzhzWVBNWjRERGJxTHlEeW43N1FvR1lqNkVVS25KZ0ZvNW4wUTRZLzRxcTJWSGEvYUZDUjBUZnJ6MEtjM1dHQUcxTUd2TXhiVU9RUXZUTGY0dWVHVHM3V1FYS3VGUEZQWSszV0hXbHd5dlRBNnhYVTg2UUFXTVFacC9BNmd2WXVjNzdvY21LNWFiclR4RVpJVUtYVE1uOXBpOTJSY0tsVUlJcDlqbnczWm14cnYwN0pkYU40TWwrdC81dFo4ZFBGT1dYK3pBMDVmNE4yT1VOT0RyYWZEK2FIdFVQWUxqcFRqWHZjaW1mRmg5UUNwM0NIdDVvOFJpb2E5Tzd6RmdBbTJheW5qdHViV29iTERKaDVkTHh1M3lCWkp1OUZZU09oeVZadDBZcEZWckJzMHR6bVhSSmEwR0M4SnpXcktvSiszU1dMaWQ0cWYrZk5yY211OVRKRWdzR0x3RkJ4N0daVmxDOEVMenNtcUZzZ0Q4OFpjcWl1R0YyMjFmbUtJbzJSaEJ4a3pVcVNIRmdlWGZPSXdOUGpqL1RrcStHQmRvWk1jTXViemlvWUxkVzcxa2hTM0Z6bURRRjJ0VkJyQXd1VkZVMzdMVHpjNm5wTUlWelU5UloxOUkiLCJtYWMiOiIxMWFkOTk3NmVmMmZlZTI2MjMzOTg0MTMwMWY4YWE2M2E5MTQzMWUwMDRlNDM4ZmFhYWM2YWQ5ZWZhODAwMmVhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671440647\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-64272213 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64272213\", {\"maxDepth\":0})</script>\n"}}