{"__meta": {"id": "X51edf63b28c9b2d527ee71ee5c710def", "datetime": "2025-06-30 16:17:46", "utime": **********.893485, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.434008, "end": **********.893512, "duration": 0.4595041275024414, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.434008, "relative_start": 0, "end": **********.813196, "relative_end": **********.813196, "duration": 0.37918806076049805, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813208, "relative_start": 0.3792002201080322, "end": **********.893515, "relative_end": 3.0994415283203125e-06, "duration": 0.0803070068359375, "duration_str": "80.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01931, "accumulated_duration_str": "19.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8551428, "duration": 0.01833, "duration_str": "18.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.925}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.882689, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.925, "width_percent": 3.055}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.885611, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.98, "width_percent": 2.02}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-747657030 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-747657030\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-777390130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-777390130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-424023946 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424023946\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-129141116 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; _clsk=1w4ne3l%7C1751300255121%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjQ2MktZY3pWSmdncnJ1RUwzNEpwUVE9PSIsInZhbHVlIjoiMElTcm5kWTZIbG1JengyQ1RxZjlkVi9QZWRiL2dzUXJKVkpTTkg2RzFmQUhPWnk3aW1xY1NScVFaMThzbGMvR21ZOUplT2hoZUJBRUpzTnYyYmYzUzRPT1VXaitra2wzcTFvVnQ0R0R1dktpMk9lb3REd05jM2ZyUEdXb1JrRllUWWhWZmhpbDJ6amNJc3QvZlZZampDTGdRTDNrYUpvNmpydDRxaXcvZDdQNHpDc3BvQzRlY1oxb0VSb0x3aXdUQVJHMGd5Vk1vdXpoMjlCTFNDRWtaRStJcjZMcVpJWUNabXVGZjVYQ05LcTV0aUdlbnJFMXk3bXpOSnhCUW5DOVEvc3lCVFkrQ3I3ZHhEeUhQeHNCa2M1NHBPSGFEK1BIQXQ0UVlvbGR6VG9hMVhsb1dGZ0QrR0QzZUhleWhFUFRrZWhUb2NEbWNiREwrMHdjV1RoS1FzcWNxRzFEb3NHc1pFRVR6d1lCYUVBV2RIMUxxZXI3T09QSmxXb2RaSDhCYUpQWWE2WUxFRVBMOUlybmllR3B1NGRudWJlMEpKNURNdU9obnQxUXI4WHpTK0pWMm1yNlJGNDdyMUQ1c29hek5EQnpOSWh4YXN1YXdBeUZzZER6aFJ5RlJYaFc3ay83NGFZd0FQUzJIR0tSODR0TDkzdFgwekM3ak9OZGZsRkciLCJtYWMiOiJkZjA4NTZjOTI0YWRiODhjM2Q4M2UwYjQ4MDVkZGE1M2JlYTFiMjc5MmI5ZTNiNzI3NDY2MGNiMDFiY2QyYWE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5EL2ViWS9hMHFuYTZwa1ZjbUZ4NUE9PSIsInZhbHVlIjoiUmRQVFYwSjQ1ZTJ3S3JkQlFIV2tHYVVtcGIxSy9sOWQrQ3pMVHY5R21jd2tlam9hRGN0Qjk4YUFZb1ZtZjVHSVJFN2NCYUExYVl2NGtpeFVDbHNZWXB4bURObmRld0gwYVJ0STZFUVNyZWlMbnorRWNlOHd3YmJ5eU51STY0RUJSbVBZSU4wRnZJQVZYL01URGp2Z1dGTTd1QklZUnFqUFZsVGs0T0FpU2FhaDFPY3NsbUVkN0hJcUZYc3l3NVM3cDJCdkhSbFA1QWgzVjFzOXIybVAwVld1Z3U1NUpjYkZGQUszT3pCdFhybTNJQTRaN3ByOFVlTVZuUitvOFJIZWY5a2hSSnhxUGJqa3dtNGQwSW5RU1JXOERrUDRzeTlHc2xFK1JYQ3dxdExMMnNUdXZQN0NSalU1NXhKcktJQ0ErcWFiTDJOWFlxb0NrVW0zblJCalRnMENjYjlzZWwxYXRlT0NCMVdwd2tiZWZwREZGNGdmNkliM3krSDNBL3BEZk9WSTN3bWZ0R0wwbzgvSmgyNWhOUnRUellObHhKSzRzTkdsWmFzOXh5NUNZT2xmbVNIVUZTd1UwbVEyZGlNaXlkMmNvVkdVM1hMYVFOYWtoek1pM0JOV25OYVUvTlZIMlEvRlZ6Tm90Ly9LR1kyamxUQVFHWjAycVRnZU0wRk0iLCJtYWMiOiJjMzMwYjEyZDFkNzc5NDI3Njg0NDBjYzk5ZDI3NDc1NDRlNjM2ZWUzMzNjY2NiODM2ZGFjNDViMWMxNDYzODIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129141116\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1542286970 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542286970\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-742118722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikc2clNjU3hsZFhROU5NMU9RQ3JsK3c9PSIsInZhbHVlIjoiRlV0K1dwaytJVDdaVlFEVHZZcThQa1FQc2JyR3VIcWxBR2tOQ2g5cHN4dFh3dGJOWVgrQ2w4S2puRjNFazU0SDVJM0JEVEdtNG5pWm1WUExONVc0a3JlTEwxa2NiendaWmhUcDNmNEIrdkxqTGZmeDFpa1NJcDFQK0VYRzB0WFJuK3JqR1grZnRKNlV2RDFCVHJ4WXlUdTRZZXQ5U3B2VStzWnBnUittYURxbEZZMlgrRUdqNUFwTHgyQnlsWm9CSW15Y1VmdjE4Ti84Sk5ZdlR1SGhndW9GblBVUXEyQ1Vac2NzanUvVE9nenBLNmdsM2p2Wk0ySjVxRWpSNS9zR2hLYUZ6Ymw1YTdkWjRZSWpBTEdVeVlHT1NNMm15U25xMkEwUGdCSW9rU29HZEhSa05ST3JKVlE1NVBvTWFnZE9lSFRGaUIzTEFGKzkrNWVqMEVSUVRMUWZCRmhZVzFhSGhZdWgwVDg4MHc4M0VDeWhxVUFSNWxDSlBYakltK0Y0dlVGMVVCdkZ3RmZ3L0tzQktPOGQ1cFlNajNRdnBNVUxRc1k0UUZ3Z0tuOGZSOWhNR2VHMnQ5VmU4NkJFUU8vSmNTaVVqSncwS2NNWWlLOUw1eWdrbGJmcEkxZVpCZW83WlkwZlc2dlJ3aDk2VGZnMUwwRXBUY3B5ZHA2bEE4ZFkiLCJtYWMiOiJiMWZjMGYxYjBjYmMxMTUzM2U4NTA1NzM2Njk0ZDc4NDlkZTRjYjAxYjhhYWUyZTg5NDY5YjAyNjA2OGQ2OTJmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFxVUxSZXZuemRTKzR5NzE5dFFuV1E9PSIsInZhbHVlIjoiaDlQRVJZTGFVVXhZSllUYVU4SzFjZmxTS0kvaTd3azF1ZVNBSStmS2JPekZ3K2p0eUY2UnZWeW5VUzdHMTNNVHQwQWg2eFVWbGtIMUd1S0c5aDVmbCtUWnRtQll2TjJuNG4wdC9mS09zMVhjdmVjMnMrMmlQdE5XcThqVTBhSXFlYkN6dVVwbmtYdFI4M0M0YzVuMVZYQkE3K0VpNWV4V3hTaWlLZkZvVUx6dytOVXIzQmVsL2E0S3B4YjBmb0ZibmlFNmI2YjZ5cTg2RnFSWDJTUGhSVHBxRGVKTUZHbTAvR0hrWnpONkJDMjlKNmpieGRzaFJlSGRkeCs3SlZuN05BN3FZYmplOGE1cDJNVGZhQTVRYSttMC9oejZvZlZxcDJpaFl0MHlxZGlSb1ZTRTQwREtLN0ZIdUtpY0RObnRURWZwc1lhTUNGUmxjMDAyRGRMWGYyMmJZWkFKL1JHK2trUm5YR0dCY0Y1dXdTbmg0VmFPZVNBOENSQnpIcHRVeE9ZMi9jclhZenNobU8rRzhlbFUwdHJIazBJVGZ1QmlQWnY2eU43N0U0cU5wSWdHeHE4TzgxZUpnY1R5VG9kRUZDMjM0R1ZydjRaU2hIUVlkcUNvSVI4bGZxa2thVm9JK1pHK0JnYW5Xc0JFdEJpYVFwT0lJL0tmelZ1c1ZaOFciLCJtYWMiOiJkYWM2YzJiYTAzOGU2ZDhjNzI1MzY5MjQyYjhmNTlkM2M5NDMzMzkwNzY0YzRmZDgzZDI0NjU4YWQ1NTBlZTcxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikc2clNjU3hsZFhROU5NMU9RQ3JsK3c9PSIsInZhbHVlIjoiRlV0K1dwaytJVDdaVlFEVHZZcThQa1FQc2JyR3VIcWxBR2tOQ2g5cHN4dFh3dGJOWVgrQ2w4S2puRjNFazU0SDVJM0JEVEdtNG5pWm1WUExONVc0a3JlTEwxa2NiendaWmhUcDNmNEIrdkxqTGZmeDFpa1NJcDFQK0VYRzB0WFJuK3JqR1grZnRKNlV2RDFCVHJ4WXlUdTRZZXQ5U3B2VStzWnBnUittYURxbEZZMlgrRUdqNUFwTHgyQnlsWm9CSW15Y1VmdjE4Ti84Sk5ZdlR1SGhndW9GblBVUXEyQ1Vac2NzanUvVE9nenBLNmdsM2p2Wk0ySjVxRWpSNS9zR2hLYUZ6Ymw1YTdkWjRZSWpBTEdVeVlHT1NNMm15U25xMkEwUGdCSW9rU29HZEhSa05ST3JKVlE1NVBvTWFnZE9lSFRGaUIzTEFGKzkrNWVqMEVSUVRMUWZCRmhZVzFhSGhZdWgwVDg4MHc4M0VDeWhxVUFSNWxDSlBYakltK0Y0dlVGMVVCdkZ3RmZ3L0tzQktPOGQ1cFlNajNRdnBNVUxRc1k0UUZ3Z0tuOGZSOWhNR2VHMnQ5VmU4NkJFUU8vSmNTaVVqSncwS2NNWWlLOUw1eWdrbGJmcEkxZVpCZW83WlkwZlc2dlJ3aDk2VGZnMUwwRXBUY3B5ZHA2bEE4ZFkiLCJtYWMiOiJiMWZjMGYxYjBjYmMxMTUzM2U4NTA1NzM2Njk0ZDc4NDlkZTRjYjAxYjhhYWUyZTg5NDY5YjAyNjA2OGQ2OTJmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFxVUxSZXZuemRTKzR5NzE5dFFuV1E9PSIsInZhbHVlIjoiaDlQRVJZTGFVVXhZSllUYVU4SzFjZmxTS0kvaTd3azF1ZVNBSStmS2JPekZ3K2p0eUY2UnZWeW5VUzdHMTNNVHQwQWg2eFVWbGtIMUd1S0c5aDVmbCtUWnRtQll2TjJuNG4wdC9mS09zMVhjdmVjMnMrMmlQdE5XcThqVTBhSXFlYkN6dVVwbmtYdFI4M0M0YzVuMVZYQkE3K0VpNWV4V3hTaWlLZkZvVUx6dytOVXIzQmVsL2E0S3B4YjBmb0ZibmlFNmI2YjZ5cTg2RnFSWDJTUGhSVHBxRGVKTUZHbTAvR0hrWnpONkJDMjlKNmpieGRzaFJlSGRkeCs3SlZuN05BN3FZYmplOGE1cDJNVGZhQTVRYSttMC9oejZvZlZxcDJpaFl0MHlxZGlSb1ZTRTQwREtLN0ZIdUtpY0RObnRURWZwc1lhTUNGUmxjMDAyRGRMWGYyMmJZWkFKL1JHK2trUm5YR0dCY0Y1dXdTbmg0VmFPZVNBOENSQnpIcHRVeE9ZMi9jclhZenNobU8rRzhlbFUwdHJIazBJVGZ1QmlQWnY2eU43N0U0cU5wSWdHeHE4TzgxZUpnY1R5VG9kRUZDMjM0R1ZydjRaU2hIUVlkcUNvSVI4bGZxa2thVm9JK1pHK0JnYW5Xc0JFdEJpYVFwT0lJL0tmelZ1c1ZaOFciLCJtYWMiOiJkYWM2YzJiYTAzOGU2ZDhjNzI1MzY5MjQyYjhmNTlkM2M5NDMzMzkwNzY0YzRmZDgzZDI0NjU4YWQ1NTBlZTcxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742118722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12401344 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12401344\", {\"maxDepth\":0})</script>\n"}}