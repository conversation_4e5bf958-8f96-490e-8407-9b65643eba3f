{"__meta": {"id": "X64c1cfe25ccdd83b969a3e850e51e2cb", "datetime": "2025-06-30 16:07:41", "utime": **********.124891, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299660.59854, "end": **********.124906, "duration": 0.5263659954071045, "duration_str": "526ms", "measures": [{"label": "Booting", "start": 1751299660.59854, "relative_start": 0, "end": **********.050186, "relative_end": **********.050186, "duration": 0.4516458511352539, "duration_str": "452ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.050196, "relative_start": 0.45165586471557617, "end": **********.124909, "relative_end": 2.86102294921875e-06, "duration": 0.07471299171447754, "duration_str": "74.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02513, "accumulated_duration_str": "25.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0789769, "duration": 0.02395, "duration_str": "23.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.304}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.112016, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.304, "width_percent": 2.149}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1180031, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.453, "width_percent": 2.547}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1105360996 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1105360996\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-606680243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-606680243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-219289364 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219289364\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-241864490 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6Ik1sMlhZa1ZlT25VMWVUS0xubnVKMnc9PSIsInZhbHVlIjoibkN0TktQdWU1V25Vc0RBY0NWbWVJdmJzNW54RUkwWndDQXJYaU11NFRuZ0JYaXBCbTM3Vk1VMDVrVVhodHl4QnVhNFFqTmFCRVUwQjdtWFlYWm1sUWN5UG56NWZWL0dTTlFsdFNidnlkVUYxZFFPK0ZsUzVwQ2NKUC9lZitzTFhVWWhyaDJHdkhXYWM1THRpbktmd2tDRE9hUzRSYm9zazJuNjRmNERRQU9oYktNWkpLalZSSUR1eHkrdkxRRkxwWjlKVEl0eFJnc3czaVNHSWdFaU9oYmVhOGZMTEtWQmZ3S3FYZWpxcXRua1RWczFRMXMvRS9veVhEYkFSU0V3bW9JY29rYnVYMTJsN2xGWURzazlFKy9OL1AzOUlHYzVvZlRmQzJUY1p6am5ZRWpaMkUxOEt1WmVOUEQ1bk43cyt3Z2tJUnB5TEMzUFhYaTYwZ05idXlZV1R4dy9TalB3K3ZEY1BGN0k1am9BRXd5eU95b0FuR2l2R3NNQUhQbDVTZTArdkVGZHQxZ25aRzgyU3VLang4MEFXOWdHYjNGWlR3N0t3cGFWdVhSb1NxQU9MVmJkbkFKSWp3UkhOdVEvS1phaUtxd0ZZYUpTU0NCVVNKemQ4VDY3UVFVOW5ka3BtcHdka2gyQWNmcUEzeDhUVTd5d0NKcjdVYnlFWm1wODAiLCJtYWMiOiIxNTcxMDlkZDQ1MjAyOWEyNzk5YmIyMmFlOGFmOTFiMjA2NTQ1OTYzNjBjMTUxZDIzN2JiNWVlNDY2NjBiYTcwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImM2ZEZUNGFUZ25qUVk2eERUdTk0Y3c9PSIsInZhbHVlIjoic0VJeDM4cFpzOWI4ak9yRm95OHZrVEp3ZUU3aHJuTWpJa3RnL1BRM0VtbXF4S3J4cE1iM0tKdnZNeDNYc3N4NVpLTlRmNW1mRGM4d1pMYXcxc2M1bWN5d090TVhiTmZuckNVTXpmSCtkRVJpUUFDOE9KSlpwUU1mZ3J3YUFHLy8rdUo2SS9HeUVyQmwzbHNOSmdlQ0p3RmgzSXJEQ0RvTkJhWURQbytPQWRCcktyL2dBNWlsWkRZU0d1ekRMNHVxOTJKelViUTNKTW5mT1N4R2l0RWZhMFV2N0EvQjRhZzdzN0dVdmpwMU80SlNxdU1JVWRsV0orcE5RYTNMSEhrQzYvQXNVSnQvZlF0bzRDc1EyM3NyTnpFNjd5VUJiVmFxc3h5ekFOY3BnUVR4cWprZ0U0UUltRldQM1JkbEVleFdSSkNaOVpidi9oclFPZnp2cTRxcHFzakw0WTk3ZTllNkw3MjY1cXFZekNxdlliRUFVOWNlckg1ekVoTEt6UnU5MVJZOGRRVTFvREVIQXIrK3NlT2NPN2RlOFN6R2RQRk0xNDdacGVTUVZ5UXBienluTnY4bE40Vm5aWVJySWN3NzVyd1JYSUUxemxxVnFKSFJ3d3VuSUNBckdaYnFCQm0yZlJrVVdBWTJTS1A2Q2srTXpveDlyeFZiTUNPTnVnZGIiLCJtYWMiOiJjZTI0MGYxMTkxZmE4NDk4OWEyOTk3YWZmOWYwOWJiZjViYjI3NjJjY2I2YmQ0NDFmZGRjZWIyZTJhZTA3MDJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241864490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-340019506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340019506\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1444640583 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlM3bmNwTy9ZM1ZUNVNQNlpwWElWdWc9PSIsInZhbHVlIjoicDV5RGR4c0E0eXl6SWsrbEc1QUdQNHJIamJrZzl0Z0t1MVdMUUlScXdEVEpialRSUUdFTVdWejMvb0F5ZW5nZlp2TzBOSVZlN2w5V1dma1dQcUJ1a2tHUEpUNHJtODUxVHFOV21PNnV6ZXdHS1l3K3lmQUljelkxb25SN3liMDBocHQ1aHhBUVhkZHhIdHVVRUZOakhPM0pwNG9sc3NaYnFNQVF5M3VCYWdKYTdzT1IrYXpqSTk3eW1LUEt1a3I3K2VKOFllOGFFaDhTVG4zTGlFVHBkSkpiTFJYb1BkRmNweWpVVTR2dkFkSjBGYjM1VmdUZ09pVHRGemRKeTdjK3RlLzNKNjJaR0JrNlVZUTAwQzg2TXBuNnhiN3M4WVBYK1ZVU0NOaS9RZlhPNG1WY2lGUzRFbi9kYUhiVlFEdDY0RnByRGhYUGFmb2dBTTcyd3ppWGM0S1VVTjFjblgvKzFGY24zaTFOWXRyZWNTL2YvUW9CWGh3aFcrSHFUTUZGVnJvRVU4M05US1JLRnhhOHlHTU5xS2YxeCtlamhzUi9pNzA2UHhMUEl0WEt4dGE4Z1JTWVloSllpRU0vbmZnWERyYzdFRjQ5b1RJbDJvVkw2TFdSQVNheTJUL0E3RHVTRWlwUzNmaUhZNjlrTUFuOG14bnBLN3lNK1BCZVFCU3AiLCJtYWMiOiIxZTk4ZmI5MmRmNDVlYzI2YzQyOTIzMTg4MjFjYWY4ODhjYmE1Y2Y4NDI5NDFjMjYyZjY5MWNlMmQ4OGY2ZWMyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ink5Z3h1MVRUWk96VzRteXdqZG9Ra3c9PSIsInZhbHVlIjoibG9QZ2xXZlErbWw2cTY0N0JqdkduckpRcys2NHVKZDEyNis0MURYYUN4WGQwMEo5ajZrVXB2SzVGWlpxWGh2bzdwdStGZjZCd1B2OHBRdXdGSk40ZzBXNHlsbzBvUHFZMHRPd1BBMFVZSGNaRkdnYmQ3OHNSVXRrZ0ZFdUp6a3hMOHVMTm82SE0rYllvQ2JQOTJhdjA4eEVCN216WFBCNlY2RDc5RUh6UFRaNGRIa1pLNkY4Y29xckwraGQ0amRuY2c2MnZFTlNHeGIyUWp3Ni9wWi9YbGRsQlZLRDJoemwrdGd0ZXU4LzRJeU05Z1IyM0hweGFTK0hZVE1EbGNWbmJzN0xlaXpRdERIbTVNNzRPaTJ1WDZocVJnVzZqUE1DRGJKOHVHY280ODRlV1FLcmlNN0xsK2phdWtrbjlXMllmMjIraENqczN6T0syZk5EaUdTTTV6SXJLeDNjSEdlV3VrK3NsL1FZdGtMTjdoT3NlNlVCVFl4a2JKeFd4ck93S2tYQkV2NnViRnBTV0QvRGcxV0YrY1ArcFlNdzFCblRjc010RS9GWWh2TTFMaW9UNFhybXp0aUZtUGJOVGhXRUxxeS9KZzZ1UXcySUJmYmhjTURGWVVxL2xoQlZNUDFZS1YveUhNcUxkeFFVWjhhRVJ1SlQxdGQrNEp4SUpIcTMiLCJtYWMiOiJiNzg3ZGZiMzQzZTdhMWI0M2QxY2M1MjYzYjY2YTUzNzlkZjVlOTZlNTEwN2Q3MjE0MDcyZDgxM2RlNDQ0ZWRhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlM3bmNwTy9ZM1ZUNVNQNlpwWElWdWc9PSIsInZhbHVlIjoicDV5RGR4c0E0eXl6SWsrbEc1QUdQNHJIamJrZzl0Z0t1MVdMUUlScXdEVEpialRSUUdFTVdWejMvb0F5ZW5nZlp2TzBOSVZlN2w5V1dma1dQcUJ1a2tHUEpUNHJtODUxVHFOV21PNnV6ZXdHS1l3K3lmQUljelkxb25SN3liMDBocHQ1aHhBUVhkZHhIdHVVRUZOakhPM0pwNG9sc3NaYnFNQVF5M3VCYWdKYTdzT1IrYXpqSTk3eW1LUEt1a3I3K2VKOFllOGFFaDhTVG4zTGlFVHBkSkpiTFJYb1BkRmNweWpVVTR2dkFkSjBGYjM1VmdUZ09pVHRGemRKeTdjK3RlLzNKNjJaR0JrNlVZUTAwQzg2TXBuNnhiN3M4WVBYK1ZVU0NOaS9RZlhPNG1WY2lGUzRFbi9kYUhiVlFEdDY0RnByRGhYUGFmb2dBTTcyd3ppWGM0S1VVTjFjblgvKzFGY24zaTFOWXRyZWNTL2YvUW9CWGh3aFcrSHFUTUZGVnJvRVU4M05US1JLRnhhOHlHTU5xS2YxeCtlamhzUi9pNzA2UHhMUEl0WEt4dGE4Z1JTWVloSllpRU0vbmZnWERyYzdFRjQ5b1RJbDJvVkw2TFdSQVNheTJUL0E3RHVTRWlwUzNmaUhZNjlrTUFuOG14bnBLN3lNK1BCZVFCU3AiLCJtYWMiOiIxZTk4ZmI5MmRmNDVlYzI2YzQyOTIzMTg4MjFjYWY4ODhjYmE1Y2Y4NDI5NDFjMjYyZjY5MWNlMmQ4OGY2ZWMyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ink5Z3h1MVRUWk96VzRteXdqZG9Ra3c9PSIsInZhbHVlIjoibG9QZ2xXZlErbWw2cTY0N0JqdkduckpRcys2NHVKZDEyNis0MURYYUN4WGQwMEo5ajZrVXB2SzVGWlpxWGh2bzdwdStGZjZCd1B2OHBRdXdGSk40ZzBXNHlsbzBvUHFZMHRPd1BBMFVZSGNaRkdnYmQ3OHNSVXRrZ0ZFdUp6a3hMOHVMTm82SE0rYllvQ2JQOTJhdjA4eEVCN216WFBCNlY2RDc5RUh6UFRaNGRIa1pLNkY4Y29xckwraGQ0amRuY2c2MnZFTlNHeGIyUWp3Ni9wWi9YbGRsQlZLRDJoemwrdGd0ZXU4LzRJeU05Z1IyM0hweGFTK0hZVE1EbGNWbmJzN0xlaXpRdERIbTVNNzRPaTJ1WDZocVJnVzZqUE1DRGJKOHVHY280ODRlV1FLcmlNN0xsK2phdWtrbjlXMllmMjIraENqczN6T0syZk5EaUdTTTV6SXJLeDNjSEdlV3VrK3NsL1FZdGtMTjdoT3NlNlVCVFl4a2JKeFd4ck93S2tYQkV2NnViRnBTV0QvRGcxV0YrY1ArcFlNdzFCblRjc010RS9GWWh2TTFMaW9UNFhybXp0aUZtUGJOVGhXRUxxeS9KZzZ1UXcySUJmYmhjTURGWVVxL2xoQlZNUDFZS1YveUhNcUxkeFFVWjhhRVJ1SlQxdGQrNEp4SUpIcTMiLCJtYWMiOiJiNzg3ZGZiMzQzZTdhMWI0M2QxY2M1MjYzYjY2YTUzNzlkZjVlOTZlNTEwN2Q3MjE0MDcyZDgxM2RlNDQ0ZWRhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444640583\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1507569649 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507569649\", {\"maxDepth\":0})</script>\n"}}