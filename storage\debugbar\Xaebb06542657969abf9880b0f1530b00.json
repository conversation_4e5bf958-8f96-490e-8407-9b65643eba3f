{"__meta": {"id": "Xaebb06542657969abf9880b0f1530b00", "datetime": "2025-06-30 18:10:49", "utime": **********.420258, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.058143, "end": **********.42027, "duration": 0.36212706565856934, "duration_str": "362ms", "measures": [{"label": "Booting", "start": **********.058143, "relative_start": 0, "end": **********.37582, "relative_end": **********.37582, "duration": 0.31767702102661133, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.375829, "relative_start": 0.3176860809326172, "end": **********.420271, "relative_end": 9.5367431640625e-07, "duration": 0.044441938400268555, "duration_str": "44.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139744, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00225, "accumulated_duration_str": "2.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.402098, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.333}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.411587, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.333, "width_percent": 16.444}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.414506, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 85.778, "width_percent": 14.222}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2143 => array:8 [\n    \"name\" => \"STC calling card200\"\n    \"quantity\" => 1\n    \"price\" => \"230.00\"\n    \"tax\" => 0\n    \"subtotal\" => 230.0\n    \"id\" => \"2143\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-37593048 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-37593048\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1539045381 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1539045381\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1387093915 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR0VTVpdFVGbitLdXdKSFliVmRoNlE9PSIsInZhbHVlIjoiUVNXTXA5UWdiMGk4Ykd1YXBtU3JnbXByMmsyRXc5cDB1UnBMMEZEcUE2VmVGU05PYTFmRUVvMzgwVmM5STNBNWZmTUl3cERzYkplemNWUkJreHBVM1FnaWFReko3OE9MRmVsaHZlakhZcVNubE93YysrMjg0LzR0SmxDYUNxVlphcWZsdGZCalpydXlxaGl2VWlpclNEbmRvYnZzbDNGbDNzbDZPUnlMRXNWZU9tY3hBWHRyTWxTRTRMQnBXNWlaSWY1aDhmOVp4VEpub3VaQVdUUWJHUWJXbk5Kc2QvMnlTYU85VGpLT2hmRGR4Y0duS3Jvd1JQMU9MS1pFK29pa2pTUVA5WnN0K1pydVY3eE9wVXJqR1pzVG1rZUhlcjVwRkd6MGFqd3p2MUREb0crTHJMOHNJazR3NFEyQjdyMVhkcWx4eDNiZitNRWJqVEQ3Yk9KUXF4WVJoUEFMY3ROMmdaS1hhbzc2WkdxZU5IM3RXSTl5bGhWd3NNREpwZ0l5NWJSaHUyU203NlY2M3Y3dDh5SS9oclp1d1Zna0Y3RjZDQ2grQ2tpNWVHa2ZIOTczaktxQnlsZEtDSzVoL21qbFpsbHdlSm54UTJGeHBLNlA5TDhFQ1hMMkJTQ204MXZDM0xqZzNXN3VsMk1PZjZmVmUxd3hYNjVtcTBIVXQvUjQiLCJtYWMiOiI1OGM5MmU1ZTlkNjNlNDQ3YTRmMmQ2MDI5OTQ0MTgyNDUxMzU5NDNkY2MxOWJjYWM4NzYzODZiZmJkYzVlNGE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJzaFpmdVBJN2pmMmF0YWNIVTcybnc9PSIsInZhbHVlIjoiS2JCRXBOWncwVk5RampoQS9pMU90WHF1WkFwb1pZZHZJSFIvK3hPVS9Id1FSa1c4YlVVQ3hrZFlqSnVZV1hMeHFFUHZ4TUFoOXVtdVZ6eGVXemEvMldvZjRhVkErdkdjRkNMS0orVTZaS21tYlQ0MGw3NlBTSC9xazl6T2VaaEREa2FxOUZLdHhCNkZvSlFMS3JDVVB1K3cvVVFqTDh1KzVlM3FVMHBRQzR4Z1h1cXZyWDlxbEh0NDU0YWZyVW52alFzaE9YYVJtelhxd0ppelY4ZEFWei9HWHFZRGNYV2cyU1FXTHUxTnNYVm14MVJUVEI5SnkrcS9hOGwyTmRoVWdGeE54NEVIRjN2RDFOTTZoZzBEZlhEd0hiUjhSOVdCM21uVFhMei9MWUUvYUlBRmkwbmpJeXhrakRLSGxFMDhhMzJ0OEVoK3F0VHZaTEY0TEJKYmtlTEtvTkNsb3BzMExrNFpseWhSbFM1U3JZd28vc3pxdENxbHpySUFqellyTTZCLzEzRFN2Nk9jQjJVak9JM3JLbjc3TXF4SGZybjAyWGdKd20yRTRNanljQmtOd3hsMG5WNVZ5dW1yY1VPY09UYm5jejJtSHZBbThoTDVpR0J1TVliVStUN0txTkRVWXh2Q2tRZk1rVGxMbExXTno2dnp5VWhLNm9uOXpYeEsiLCJtYWMiOiI2ZTM4YjhmZTQzOTdlNDBiYTc3MzA3YjhkNWJkZTQ3YjVkM2E2N2YyYWM1NzkwYzk2MGIwMTYwMDQzZGM2NTc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387093915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1530561200 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530561200\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1173228206 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9xa2lSeVNleFdITU1lZ0lFTHFVYmc9PSIsInZhbHVlIjoiYk80SlRVakpaVUUwRVAwRnZwTk0zb210K3hYQkhlanpVbUpDYWVnYnVLVGh5b2Y2Mm0vY3c1d2x6cEFkblo0c2g4UWVDcFg5dGFxblF2Z25rQnQwRU16Q1Z1S282ZHlkVVp4bWFVS2t6RE5JL3RoQXNqckZKNjlmSVRwWVJ4d3U5UnpTMnp5OGk3b082QVhuenJWNWs0OUNSRWtYVm90MDFuWnZCUkk1SFlQWGRudk5NKzg5aXBYU3FGcVgwSldtRVdUYnM5REFXNGJDVnFFWnFHUHlOVVRjSStUK1o0U3dJa2pxSGt5UDdRMFRPc2E3QXUxeGdMNmh1WE1jZWphUit4L3h6RWlHVmdZSnpwajJUcmlsUDRpUXhnMkVHYmJ0S3dvekdiMlYyUGNtOHM2WFQvc1U5eDV0cHErSjJOZVN2bEh5VWkyanhEQWdORktra21rSS9ha3VUMFQ1dTRBaUdFc20rcEh4S0FWR2Zsdm9JMmpsdmxIaHBnRGVidU5pbkhQSVZIYmZmQWJHTlJ6Nnp1bjZwOVZCOGtUOHJCYnNGaXRxakhhRWcyUFJtbDg4ZnROTG1nSXBpeWVHVVlwc2kwS1V0Wmc0YkhyTi9iUjAxM1AvRUtXNUM1SFUydWMrVFk3NzNMdDVkTlkxakcweWVPVkpGdHUwY24xNHBWQngiLCJtYWMiOiIwYmEyY2Y3YWQxNGFlZjU2NjQ1ZWQ0MmU3NjExYjJmMjIxYTJiNWEyNTNlNzExMjVjZTViNmUxNDE0NDU4Zjc1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVaTlJrWFlORlJtcDkvc2pEaDlTQVE9PSIsInZhbHVlIjoiRFJnbVg3akFmazNDZWc1TkNvRURRVk03Y09aZDRQZkRUQ1NwZDhBSU1GY0JVZWw5ZGMydGh3aHBMUHAyZ2NEVzhvQlErYkl3M3Z6WWhmR056UnJWYk5rR21lZ3pTQWd1MjdMTmxHd0VHY2dWd2x3cDIvcXdiUkorczBrZ1QwMmg0eTdXZ1pOU2pSNHdQOTFGMUN4TnN5em1xUmx0R09ERnJaTzNxbnQvTWNsMUdKdUNCQTFqZWR4MlZ6N2hONVZLaHpDdmFrTXpneFNWUmhrVzd2M01SYkQ0aXFmOG5UYk51Y0VTbWdiY0YvdndhcEhqQ0ZMVU5MRU5KWFQxeFBQU2ZHSytoNWZIQmdwbCswR0xNWXcvZFUvdE9wYk1XMXU0MVlVTE55UmFkU1BWQXZsbjBYZzFoU3lFeFlEbDRsclBDNFR1S29vVkFhOU9MRU8yelVqbnFqcElucHdwNG9lY3JGZExNUjFLTEVZaFN2Qm9ZaFRDRFZOL0p5Zk5HTWtKYmttV0NXNUk1WkFWYmZoejk3U0hFWVhScVlENmp5WmQvSEZybjFYMVhud2R0OGtwUjVVLytjdGw2ZXdGL1JiYysvZ1JnUXVUYTJET0syODc0aXRnSWIvN3lYSzRtQitiT0l3aUx4Rm84UjArclhwS3hqZndxWG1WRHAzZjdZaFEiLCJtYWMiOiJkOWI1NzJlNzczYjUyYzk5MjI4OTFhMDhjYmE3OWNhYzZjYTlhOWZhMTRjNDQ2NDFlOTQ4ZGMyMWU3N2E0YmRiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9xa2lSeVNleFdITU1lZ0lFTHFVYmc9PSIsInZhbHVlIjoiYk80SlRVakpaVUUwRVAwRnZwTk0zb210K3hYQkhlanpVbUpDYWVnYnVLVGh5b2Y2Mm0vY3c1d2x6cEFkblo0c2g4UWVDcFg5dGFxblF2Z25rQnQwRU16Q1Z1S282ZHlkVVp4bWFVS2t6RE5JL3RoQXNqckZKNjlmSVRwWVJ4d3U5UnpTMnp5OGk3b082QVhuenJWNWs0OUNSRWtYVm90MDFuWnZCUkk1SFlQWGRudk5NKzg5aXBYU3FGcVgwSldtRVdUYnM5REFXNGJDVnFFWnFHUHlOVVRjSStUK1o0U3dJa2pxSGt5UDdRMFRPc2E3QXUxeGdMNmh1WE1jZWphUit4L3h6RWlHVmdZSnpwajJUcmlsUDRpUXhnMkVHYmJ0S3dvekdiMlYyUGNtOHM2WFQvc1U5eDV0cHErSjJOZVN2bEh5VWkyanhEQWdORktra21rSS9ha3VUMFQ1dTRBaUdFc20rcEh4S0FWR2Zsdm9JMmpsdmxIaHBnRGVidU5pbkhQSVZIYmZmQWJHTlJ6Nnp1bjZwOVZCOGtUOHJCYnNGaXRxakhhRWcyUFJtbDg4ZnROTG1nSXBpeWVHVVlwc2kwS1V0Wmc0YkhyTi9iUjAxM1AvRUtXNUM1SFUydWMrVFk3NzNMdDVkTlkxakcweWVPVkpGdHUwY24xNHBWQngiLCJtYWMiOiIwYmEyY2Y3YWQxNGFlZjU2NjQ1ZWQ0MmU3NjExYjJmMjIxYTJiNWEyNTNlNzExMjVjZTViNmUxNDE0NDU4Zjc1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVaTlJrWFlORlJtcDkvc2pEaDlTQVE9PSIsInZhbHVlIjoiRFJnbVg3akFmazNDZWc1TkNvRURRVk03Y09aZDRQZkRUQ1NwZDhBSU1GY0JVZWw5ZGMydGh3aHBMUHAyZ2NEVzhvQlErYkl3M3Z6WWhmR056UnJWYk5rR21lZ3pTQWd1MjdMTmxHd0VHY2dWd2x3cDIvcXdiUkorczBrZ1QwMmg0eTdXZ1pOU2pSNHdQOTFGMUN4TnN5em1xUmx0R09ERnJaTzNxbnQvTWNsMUdKdUNCQTFqZWR4MlZ6N2hONVZLaHpDdmFrTXpneFNWUmhrVzd2M01SYkQ0aXFmOG5UYk51Y0VTbWdiY0YvdndhcEhqQ0ZMVU5MRU5KWFQxeFBQU2ZHSytoNWZIQmdwbCswR0xNWXcvZFUvdE9wYk1XMXU0MVlVTE55UmFkU1BWQXZsbjBYZzFoU3lFeFlEbDRsclBDNFR1S29vVkFhOU9MRU8yelVqbnFqcElucHdwNG9lY3JGZExNUjFLTEVZaFN2Qm9ZaFRDRFZOL0p5Zk5HTWtKYmttV0NXNUk1WkFWYmZoejk3U0hFWVhScVlENmp5WmQvSEZybjFYMVhud2R0OGtwUjVVLytjdGw2ZXdGL1JiYysvZ1JnUXVUYTJET0syODc0aXRnSWIvN3lYSzRtQitiT0l3aUx4Rm84UjArclhwS3hqZndxWG1WRHAzZjdZaFEiLCJtYWMiOiJkOWI1NzJlNzczYjUyYzk5MjI4OTFhMDhjYmE3OWNhYzZjYTlhOWZhMTRjNDQ2NDFlOTQ4ZGMyMWU3N2E0YmRiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173228206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-59726773 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2143</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card200</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">230.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>230.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2143</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59726773\", {\"maxDepth\":0})</script>\n"}}