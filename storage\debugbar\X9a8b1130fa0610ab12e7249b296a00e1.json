{"__meta": {"id": "X9a8b1130fa0610ab12e7249b296a00e1", "datetime": "2025-06-30 18:08:27", "utime": **********.764153, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.359949, "end": **********.764165, "duration": 0.40421581268310547, "duration_str": "404ms", "measures": [{"label": "Booting", "start": **********.359949, "relative_start": 0, "end": **********.7231, "relative_end": **********.7231, "duration": 0.36315083503723145, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.723109, "relative_start": 0.3631598949432373, "end": **********.764166, "relative_end": 1.1920928955078125e-06, "duration": 0.04105710983276367, "duration_str": "41.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43886280, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00362, "accumulated_duration_str": "3.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7519052, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.331}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.758186, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 90.331, "width_percent": 9.669}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1921088984 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921088984\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1194979685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1194979685\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1400269533 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im0zY3BvTmt5ZlRkc3NpSXpleWFMMlE9PSIsInZhbHVlIjoiQ1JzWS8vaXM0UXpNU0tuR3F4c1paazVwbXlPUG5oRDdLUTdtbXBPOTNOb3BrcEdEd3FueHF4NTRuSi9IcUFxUWNuL0ZHa1ZlT2lkTkVTa0pvM1RIVVUrblhKZkoxeGJtWTRzTzduVVRDYVBnSmxqWUhYU3JHT0dxblV3VVVXMHhEUzR6T3FEQllVc2lWZFdqN292Y1Z5amhMc1BWRXcyaDdTNEdLYUwwYTlmQllDYUI2cTV6N29wTnR5QW94WlllN0NvQzJHb2tsckk3c0h3OU4wUFhJdHV2ZHpyUGhSMVFMTk1rOFpwbFFPODNQeDRQN2EvZ08rNDMwYVoyT0VCQzErWW1KazE3OGY5djJTS00xM1RlaWhhMlEvbFp3YzhyWUY3c1dsVUFUdXVkbldDMS83N3g3eU5qdkp0Z1pVcjAzRHBIUDgrVDVjMmRiVG1SWU9Hd3hjNjBNMFJVd0JJS2NFZnZSOGNHSkNyb2MyRk1NNzFvNlloandIK0l5Z3k1NUEzcE5BSGhjU09jOGF6bmMvWjZ1OHVMVEJKZTkvejR3RmlTZWUxdmJuQjNDcXlsL3VVRm5mRHBzRnhVeDdHTUpLbnhWL0dydEpNSWtVczJFZE1vdy9VZXJtdVJpMXlZSC9JV3VlVXJrTGsyQXgyZHpSUmphVEdjRkRIQXhZNmwiLCJtYWMiOiJjODY2NjcxZGRhZjExN2Q4ZTAwODNlNDE2ZGE5MmZjODQzNDI0ZjY3M2NlMjAyMGQ1YTg2YWI4MWFhZDBmMjE0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpBK0NUNUxEVWVKa25qVjVQSEtCcEE9PSIsInZhbHVlIjoiVlo1OHRiVHcyRWdrcXl0NGx1ak80c3pnRDhnZ3pnOHpvZGVzUUZ1SldSOEJvcC82U3luN09uaFFFdzR1YlZGR2JGOTUxMlhaVVNHd1ZEbWNmeEs5TVYzbnpLY05vUWI3MTBaUTR6R1RocndEZm5HSm5DaHBmcjU3a2NLWHFOTjdGaC9GOWEySXhYWGRwcGdhREJWd28zdDNJZUs5QlBFaXFHaGN0YjE5TnNGVkJUb2l4K3EzWlBkTkFZMmlvOFhQUlVGYVN3ZlFvbWliY29FTktFZ01CZzdrVW1IOWFYaXVEZm95T0RRN2FLbmpzTjVLenNIM3NBbzJHU29vNkI1ZHBrWTdHdmVQcDV3a05BdEh0NmdndjRJS1Q4MU9EeXZqV0pzaitHU0hOYnAxTFNIcGx6M1hUbTNyQnZQQWlSbkRaV1ZTRTdDRC9YREZlU0NHeHIvcFVmTWVCTzBoaDhEU0NSaCtDU25INkVyYXg0QjM2Wm1McWduK3dudjZkQzNuZnpVWDNXdnFmU1JRWlhVMDlVcngyQTEwci81ZG9Id1Y0MEVQYkwxcTA3b25qVU5veVNQL21Ga1NveWdlaTEvbm1PNXl0UGpPMThXN1FQSkJGUTZSa2pqZDhmOVhzQlo1YnNmRzhkTUNLMHRCaklPWUIwMndkZm9rRjVLektyRXoiLCJtYWMiOiI2NTM1ZjliZTExYjA4M2JmYzNlODRkNDIxMmY3MGE4ZjE3MTg3YjA0ODg0ODdmMTdjY2ZjMjM0ZGExOWQzOTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400269533\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-98499326 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98499326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1244320139 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:08:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhRWFNlQW9HWENyMFJ6QkZvRmxuelE9PSIsInZhbHVlIjoiUXo5bDNMU1gxN0NZOTY5SktzOHBNR2RtaXNPY0hUSXFrVnpWWHIzdml3VDFwZjl3emlIakpYMGJ1ZGhXVEd5WlJ4RlA5dzdqNkE2Ym5OMlR0VlUrZFRjb01KMjUvSWlDQjV2VEhwcVM4Snp3UGV1KzJjYVZRQi9JdjlST0dNYXk3M2p2eWQ4eExFQ2ZIY3ZFMFRqTG9IR0gyY2RsNWFlRzZkR1g3bVJHVzBRR21sR1ZKNGwxUW1PbGdvcyt4N0dmSlVUTlg3Zm9jdWk1cE1oWEdnUDFadWpTdGQxKzk0azBheHB1dWNjTm5hanlMNmFONnorSHM5Zi80cjAxTkpzWEp0dUZvMm80eVNicWhWMGVtQTRYbzlHeWJXQ3JCSHk2a2Q3cXpUdFlZNFRaM2sxRk43eW9pLzQ0RC9nc3RwSGJKZ0VDd0lmVnF0ZGE4bG9wcTR4eWdWcFZkYzlXNVJIaXBDeTg1N2JqV3JzS25lZjlsWVRPTUg4RzhJaEhDWHlZbVBEQ2xnK1d0VXE4YUsxMnZuSTlXZDY3RU1DVDNpWVlBaVpwbVRVbzN3aENjQ0pxU0lsMWpiblQybjlXV0hRQ0tXcEdhZ2VIdnpyOXVFeGc4QzVhZVNSVTU1em1HTkNaOVRDczlUeU00ZDI5cmZkMlJHa2Y5TlpxTktDUEVsUDMiLCJtYWMiOiIzMDJjYTA2MWNkNjA5ZjcwYTYyOWU3YjI1Mzk0OGYwMzQ2MGMxYTFiYTk5NzM5ZTY2ZGNiMWI2YThmNTY0YjRiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldrNEoybklxUWlyZW5yZWRsWW1NZUE9PSIsInZhbHVlIjoiQnVaT0RNMDRqSEVhcUUybThPa1cyM05VSnA2cDVoaTdDUndyc09uWWFiR09KcTdqNENBVWVoR2F3YzhvdGlkdDA5SWplM0lIbjBTZDFhcndQUGRqQmMvbldEa2tjZ2xSbUdNOGVpQkZDYjh1V1pTWHFZcFNlSXZTUUhaSm12NlhmOE1OSFdEdXpZSGZROTNnR2E1OWlnM1owY1hreWhqV0hvWVlUMSt6ZnEwQkMwYTVNc0Q4RGNEVE5PRndwYXE0d0lWN25ZcjloK1NmMnhITm5pc1hUZXBERXI3dXQrWFVjOGQwcHJuNE56bE1jNyt3ODB3N0ovR1c1aTYwM2FMUjI4RWwzRVh1c2VvV082Z00zcXpFQjF2MnlvMGkrL2trejY4NDJ1Q1p4WkhTZEhiOXVyNHZTOHd1Zys3SEtMZ2pRWHFrcm5Hb2FYOStyd2ZvdVc5cGlOWHBRbU5XZDEvakVGdjFmVW10WFpXWnE2WitpWjJlclM1RTlxTk44ZG9UR3UwbWN5a0pHVkNwblB4a1hjNk5wenVVbDhQM2FBYTJwbUN2Vi9sQ0l5OUozNVZBVWRhY1UrbHFHYVFzZCszUGpwd0NLNXBHVERLLzhPWXhDL0lscDYxOHVIRUxaeE1wTjgvc010dlJVK2huN3l6QnNpbmxsckprbVdRYjdkMDUiLCJtYWMiOiJmMGQwMTk4NjdmMThlYzgxNWJlODBjYTlmZDU0MzU1NjkxNDM3OTkzMGEyYWUxNGYxYzAzOTZkZmNjYzgzNDAxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhRWFNlQW9HWENyMFJ6QkZvRmxuelE9PSIsInZhbHVlIjoiUXo5bDNMU1gxN0NZOTY5SktzOHBNR2RtaXNPY0hUSXFrVnpWWHIzdml3VDFwZjl3emlIakpYMGJ1ZGhXVEd5WlJ4RlA5dzdqNkE2Ym5OMlR0VlUrZFRjb01KMjUvSWlDQjV2VEhwcVM4Snp3UGV1KzJjYVZRQi9JdjlST0dNYXk3M2p2eWQ4eExFQ2ZIY3ZFMFRqTG9IR0gyY2RsNWFlRzZkR1g3bVJHVzBRR21sR1ZKNGwxUW1PbGdvcyt4N0dmSlVUTlg3Zm9jdWk1cE1oWEdnUDFadWpTdGQxKzk0azBheHB1dWNjTm5hanlMNmFONnorSHM5Zi80cjAxTkpzWEp0dUZvMm80eVNicWhWMGVtQTRYbzlHeWJXQ3JCSHk2a2Q3cXpUdFlZNFRaM2sxRk43eW9pLzQ0RC9nc3RwSGJKZ0VDd0lmVnF0ZGE4bG9wcTR4eWdWcFZkYzlXNVJIaXBDeTg1N2JqV3JzS25lZjlsWVRPTUg4RzhJaEhDWHlZbVBEQ2xnK1d0VXE4YUsxMnZuSTlXZDY3RU1DVDNpWVlBaVpwbVRVbzN3aENjQ0pxU0lsMWpiblQybjlXV0hRQ0tXcEdhZ2VIdnpyOXVFeGc4QzVhZVNSVTU1em1HTkNaOVRDczlUeU00ZDI5cmZkMlJHa2Y5TlpxTktDUEVsUDMiLCJtYWMiOiIzMDJjYTA2MWNkNjA5ZjcwYTYyOWU3YjI1Mzk0OGYwMzQ2MGMxYTFiYTk5NzM5ZTY2ZGNiMWI2YThmNTY0YjRiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldrNEoybklxUWlyZW5yZWRsWW1NZUE9PSIsInZhbHVlIjoiQnVaT0RNMDRqSEVhcUUybThPa1cyM05VSnA2cDVoaTdDUndyc09uWWFiR09KcTdqNENBVWVoR2F3YzhvdGlkdDA5SWplM0lIbjBTZDFhcndQUGRqQmMvbldEa2tjZ2xSbUdNOGVpQkZDYjh1V1pTWHFZcFNlSXZTUUhaSm12NlhmOE1OSFdEdXpZSGZROTNnR2E1OWlnM1owY1hreWhqV0hvWVlUMSt6ZnEwQkMwYTVNc0Q4RGNEVE5PRndwYXE0d0lWN25ZcjloK1NmMnhITm5pc1hUZXBERXI3dXQrWFVjOGQwcHJuNE56bE1jNyt3ODB3N0ovR1c1aTYwM2FMUjI4RWwzRVh1c2VvV082Z00zcXpFQjF2MnlvMGkrL2trejY4NDJ1Q1p4WkhTZEhiOXVyNHZTOHd1Zys3SEtMZ2pRWHFrcm5Hb2FYOStyd2ZvdVc5cGlOWHBRbU5XZDEvakVGdjFmVW10WFpXWnE2WitpWjJlclM1RTlxTk44ZG9UR3UwbWN5a0pHVkNwblB4a1hjNk5wenVVbDhQM2FBYTJwbUN2Vi9sQ0l5OUozNVZBVWRhY1UrbHFHYVFzZCszUGpwd0NLNXBHVERLLzhPWXhDL0lscDYxOHVIRUxaeE1wTjgvc010dlJVK2huN3l6QnNpbmxsckprbVdRYjdkMDUiLCJtYWMiOiJmMGQwMTk4NjdmMThlYzgxNWJlODBjYTlmZDU0MzU1NjkxNDM3OTkzMGEyYWUxNGYxYzAzOTZkZmNjYzgzNDAxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244320139\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1665057691 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665057691\", {\"maxDepth\":0})</script>\n"}}