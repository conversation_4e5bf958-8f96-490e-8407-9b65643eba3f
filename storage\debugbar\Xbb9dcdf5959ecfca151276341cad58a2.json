{"__meta": {"id": "Xbb9dcdf5959ecfca151276341cad58a2", "datetime": "2025-06-30 18:08:34", "utime": **********.481592, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.04471, "end": **********.481606, "duration": 0.43689608573913574, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.04471, "relative_start": 0, "end": **********.398689, "relative_end": **********.398689, "duration": 0.35397911071777344, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.398699, "relative_start": 0.3539891242980957, "end": **********.481607, "relative_end": 9.5367431640625e-07, "duration": 0.08290791511535645, "duration_str": "82.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184440, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00736, "accumulated_duration_str": "7.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.431684, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.087}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4460409, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.087, "width_percent": 7.337}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.461761, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.424, "width_percent": 7.337}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.463778, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 40.761, "width_percent": 4.348}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.468199, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 45.109, "width_percent": 37.092}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4732578, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.201, "width_percent": 17.799}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-224773100 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224773100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.467208, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1705487948 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1705487948\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1239257009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1239257009\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-400205240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-400205240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1648854951 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpxdndNQVBHSVgrK2JHMzVqQkFtRGc9PSIsInZhbHVlIjoiMjN5UUFRVVRxdDN0ejN1V1k5b0M2bkVhOWtqanVKOXZSZUFISyt6Ui9hOFFLU0liZ1lwekNod2h5SnBmZjJoYzUvR0dlWlJ5NHgrdmJNNll4ZTdKRkQvU2ovQkdwKzU4VS9pbEx0UDR4RGsycUdLeXNDSWVLQlhTaG1RZFRRTlFhQzFTK1hoTnJYeWFLeWZRNXM3dGRsa0dWdWQ5eWRhRzNuZWdBRi9Xc1c0aktRaDV2MHpsSHQ4Skc1UnFoQzRxY0g3TThTanQ1VGsxSXc3VXNIWENma1U2REY0cldRMW9vQjI5d2hvY2lEY2lKMWNlQ1pQWUpHTkRQcjVvaFZ3NXViclhzaHhjSDFzbUVWaXlyb1M2V2s1amV1UlVnVkp3QTZveGFGN0pmTXNSWjU0UVp2U2tSL3l2Y1JWOHowbTc5YUdhNzhmWjNoTGk2VTRHNVpHV0FqcENJRWNGbmtGZU5mTmxtcHNiYkdNWnRMc0ZFMExEcGtBQ3YwRjhXUVNtYnA2ekhVdGk4NGVRdnc5d1dVZDVQNWR3SWwrOFEwNXpVZ0tPRTR4WUlmOFB4NXhxUTdHem9aZUdJREJYOFNWWlZGWnhIU1NuOXp2Vm9jOXVsaDFlODNYaTlVbXJ3T1Y2L2llZkpRcWw1ZUo2MCtLMHF6WDh3Zkpkb0RtY3lTYlIiLCJtYWMiOiJhMzQyYmE0YTJlOTllZGYxYjlmYmE1YmQ0NGJjMGZiNzY0NjdmOWVmYzdiNDI0Mjc0MGY5MDc4MjA0ODczOGM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlExbm9MWXM4bzlaUnVDSFNXcDBHYmc9PSIsInZhbHVlIjoiam1UTnZiTnFCblVVVGV1MTJBYjZCdjFHV09lVUhLU1NaQlpPQmdNbGdoNXE2ZHR5cXBUa1Y1U2xEa0ZXUDZCdHAyb1g3VTNvUDBnYzh0KzZ6eUV4cjFjb0cvcmxaa0M1WExBNFZHcy9TN0tlbWl2a1BNZ1pCbkdlU3FpVUI1KzhsbFc0WWIrY3Vma0Z1Y1BsMTB6YS9TVVhxdk53a2dBYUJjWXFnL3Rmc1FTZWN6V243cmVsZDJWdGNQNXNFNHZWVGg4TW54bnErVDFxaml5bi9tV3hrcnlETW1zdjVzMGsreXBiZElOZ2t3OGFlZndabjN0emtDM1hLV0UrTFNqSFFJQXFFZml4WmtwTnlHMkNkSit4d3RUbHdyRmFKSERGQlloWCtqTnNuNXVPY2dXSjNYaDhUbk1OcW5QSmdQNXE5WlF6eU9xK1FrZEJNZGlEbDc5UWFoN29lRzZCSFBvUVpiOURVSWFJR3JWMi96NjlsQ0xOTXlDVk1mOGxBQi93bk5hRFJ0ZTg5cVVhVmkycnNzbHNQWVdURHBFOXNUWUt4YUQwWnBNcDZOd040d3Jma0JYVDhQemhIRUc5L2sxTTRQVHBvQUUrR09rOGw4RnBid1c0L1NlZUNZckZVZlhWaEVwMHJkSVUrdlE4Q1F2clV0QWZKUFh6c1VQWkwzbDEiLCJtYWMiOiIyOWJjYjMzNDE3OGUwMTEyMzcwZjU0ZjA5NTEyNjYyZDg2Y2ZlMGQyNTczMjRkZmIzYjBlMjQ3NjZjMDQ5NWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648854951\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1587094180 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587094180\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-40740647 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:08:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlowTGlta1pTUFl5TVk4SmNhWDRzamc9PSIsInZhbHVlIjoiL09ZemNnNmNKdi9mdHdpTDhPOXN5cEtCQllHVE9kMXE5d1ZiaXdoa2VYYmVrUURIV0dXaXZpYnlIdW5qam93NGU2N0VkODlvMFlyTEJPR2QyL3NwL1hwVTNRQVJPSTE0SHVDcVpkdFdyZ3Z1elltd25od0QyMnNnaHIwdTR0VHMrUWQ2bFVUNXZGQkxHRjRQTHpjZlphZWxsaVdzU2pNODFRRnFEUkQvVnZma1RYTWhSSkFyZzB0K3NtdWxNNFlJZUNEQms1VDZtWjBTekU0Z0lyVUdLa2hyZFJxaldoSFJLUTM3WDIrUGhRT3htcXNSMVVraVM2K2R4VDRqa3FQRmVmOFZQSkhVdS9QcFZ6ME9KVjl4ODBuSktmbTRDcTBjZCtMa3ZGTkk1b2w1bG0wTW5YaVc5V1dPT2RvYWhVdU1wck9ZT2Y5NjRjNEh4VTZpUWszVWJybHRXblkzR25Mb29Uc0tURUJsNnZpNkhCV2N1TG13SHRtc1hqNjdSMzRsdlN1VWJUNjZoNVB4MFBKM2V1RHBQSERScStUazAzM3ZPM29OQS9FNUZ4Q1kyTzJ5WjZPYjRXSWNXekxMczVWSFFuVXpKdDd5MlUwc0N2UFJVKzNPYzMrMlhoUEhNSGVudmY2djgwYUxJMnA4cVRsUE1nMjlZZTYrMStESUdCYlEiLCJtYWMiOiJmMzk5NWMzYzVjYjIyOGIyMDgxMDU4YmNmMjFiYTg5NjZhNGVhYmFiODAxYmRiMjNlMjM2ODEwM2JhY2RlM2RiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZvN0ZBZTYycVZic0NwcTlFdWE5N1E9PSIsInZhbHVlIjoiR0ZqeWl4NGsxS2laMEg4bytuRUxsVnMwN3RsdHM2Y05nYktkLzFKWVUxcjdYTndHaWFTNFJDekZEbVZYazA3L3dNU0lBclRXYXc1SGorOHpmUjVIcmFGdm0ydFp3UFZNNUNmVFRhY09ZTXp2S2lkSWhLWlBNRUxuWURaUlJFcHVzeE5QUVZiK1FxLzN4ZHluL1dYT2JnYWNTenF1TUEzWjd3ZnNMTFlrWnZobllsMVhCbEs1WkZjallkZzFDaVBMZjY1QUU2SUk0Nk8zb3VIYm5TV2pJMkZ0dFVTUHJtT3FLOThKNWxFWEFyYnJaUEhyK0YzazRML1MwdmhVa2l6ck5sc1AvSEMvQ0dIbmlhUW4ydU5zWWZtZWpWSFZlLzZuWGI4OTJ4b2t5L0xzL1pPZmhLVlhEV1RUbnYrZ1RxWEswRzIxWXVMNkx6ODMzTzZrMWNQekNrQisxcllFb1RmZm9pbVVxZzFLMUIrOFNmY3dISjJZcTFlMkt3WUhLQyt1NWkvbW5tTUZzd3p6ZHNONExTZGdxR242cWcvcC95M1lMUDc3d25lZHBOQWgya3NUWHFsSmp2SGVMdEY4dnNzdC9UOStyQVY0T2FDWDlxdlNGTzlpaU1PMUFsL2FscUtkYm5hR0pBbjhKeGpnSmYvRTh2dGI3K1pkYzcxMzhDaFYiLCJtYWMiOiIyM2IyMzhmNTBlYjliODQ3N2UyYTdhZWFjNDkyY2ZiOTEzNGMzZjVkZjEyYWVmNTI0YzRhMDM0NTYyNTExOWI1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlowTGlta1pTUFl5TVk4SmNhWDRzamc9PSIsInZhbHVlIjoiL09ZemNnNmNKdi9mdHdpTDhPOXN5cEtCQllHVE9kMXE5d1ZiaXdoa2VYYmVrUURIV0dXaXZpYnlIdW5qam93NGU2N0VkODlvMFlyTEJPR2QyL3NwL1hwVTNRQVJPSTE0SHVDcVpkdFdyZ3Z1elltd25od0QyMnNnaHIwdTR0VHMrUWQ2bFVUNXZGQkxHRjRQTHpjZlphZWxsaVdzU2pNODFRRnFEUkQvVnZma1RYTWhSSkFyZzB0K3NtdWxNNFlJZUNEQms1VDZtWjBTekU0Z0lyVUdLa2hyZFJxaldoSFJLUTM3WDIrUGhRT3htcXNSMVVraVM2K2R4VDRqa3FQRmVmOFZQSkhVdS9QcFZ6ME9KVjl4ODBuSktmbTRDcTBjZCtMa3ZGTkk1b2w1bG0wTW5YaVc5V1dPT2RvYWhVdU1wck9ZT2Y5NjRjNEh4VTZpUWszVWJybHRXblkzR25Mb29Uc0tURUJsNnZpNkhCV2N1TG13SHRtc1hqNjdSMzRsdlN1VWJUNjZoNVB4MFBKM2V1RHBQSERScStUazAzM3ZPM29OQS9FNUZ4Q1kyTzJ5WjZPYjRXSWNXekxMczVWSFFuVXpKdDd5MlUwc0N2UFJVKzNPYzMrMlhoUEhNSGVudmY2djgwYUxJMnA4cVRsUE1nMjlZZTYrMStESUdCYlEiLCJtYWMiOiJmMzk5NWMzYzVjYjIyOGIyMDgxMDU4YmNmMjFiYTg5NjZhNGVhYmFiODAxYmRiMjNlMjM2ODEwM2JhY2RlM2RiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZvN0ZBZTYycVZic0NwcTlFdWE5N1E9PSIsInZhbHVlIjoiR0ZqeWl4NGsxS2laMEg4bytuRUxsVnMwN3RsdHM2Y05nYktkLzFKWVUxcjdYTndHaWFTNFJDekZEbVZYazA3L3dNU0lBclRXYXc1SGorOHpmUjVIcmFGdm0ydFp3UFZNNUNmVFRhY09ZTXp2S2lkSWhLWlBNRUxuWURaUlJFcHVzeE5QUVZiK1FxLzN4ZHluL1dYT2JnYWNTenF1TUEzWjd3ZnNMTFlrWnZobllsMVhCbEs1WkZjallkZzFDaVBMZjY1QUU2SUk0Nk8zb3VIYm5TV2pJMkZ0dFVTUHJtT3FLOThKNWxFWEFyYnJaUEhyK0YzazRML1MwdmhVa2l6ck5sc1AvSEMvQ0dIbmlhUW4ydU5zWWZtZWpWSFZlLzZuWGI4OTJ4b2t5L0xzL1pPZmhLVlhEV1RUbnYrZ1RxWEswRzIxWXVMNkx6ODMzTzZrMWNQekNrQisxcllFb1RmZm9pbVVxZzFLMUIrOFNmY3dISjJZcTFlMkt3WUhLQyt1NWkvbW5tTUZzd3p6ZHNONExTZGdxR242cWcvcC95M1lMUDc3d25lZHBOQWgya3NUWHFsSmp2SGVMdEY4dnNzdC9UOStyQVY0T2FDWDlxdlNGTzlpaU1PMUFsL2FscUtkYm5hR0pBbjhKeGpnSmYvRTh2dGI3K1pkYzcxMzhDaFYiLCJtYWMiOiIyM2IyMzhmNTBlYjliODQ3N2UyYTdhZWFjNDkyY2ZiOTEzNGMzZjVkZjEyYWVmNTI0YzRhMDM0NTYyNTExOWI1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40740647\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-59369201 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59369201\", {\"maxDepth\":0})</script>\n"}}