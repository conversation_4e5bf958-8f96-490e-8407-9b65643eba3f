{"__meta": {"id": "X595206b028b3dc03ea7696172871c592", "datetime": "2025-06-30 17:59:38", "utime": **********.825237, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.423535, "end": **********.825248, "duration": 0.40171289443969727, "duration_str": "402ms", "measures": [{"label": "Booting", "start": **********.423535, "relative_start": 0, "end": **********.780332, "relative_end": **********.780332, "duration": 0.3567969799041748, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.78034, "relative_start": 0.35680484771728516, "end": **********.825249, "relative_end": 9.5367431640625e-07, "duration": 0.044909000396728516, "duration_str": "44.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43886280, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00311, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.812881, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.566}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.818658, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 84.566, "width_percent": 15.434}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 1\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 57.5\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1982978476 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1982978476\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2098696015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2098696015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1105804688 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRaSHc3ZXBjTW9SRnRyc3ozdWQxOEE9PSIsInZhbHVlIjoiU1I5MzdkdDJQN2JRbngyblI3YVAwZUk1aTJpUStJTHdMeStQb1VxT0hMODBBR2tacE1IQ1lzR0RmelRyd0x4YUw5WFpaOEx2S01kQW10ZUF0Q2xDMXNWQW4zL0gxQndVV3UvakVGa3BQb3E0OWF1UnJNM1dxVHFaL1ZaZzJNOTdNb0NwUFU4QlErYi8rWXZKd1l1OUhuQXpHZ1BnUkFsb25kdmc2akZ6cDFvclVyVGNUZzg1OGtDSGdVaFRIVVo1M1d3NlQvdUd6QTZEMDFyaHBKT1o4RDkvcXVoTGZzQUhkcnRBYVpJZ3VqRkZOaHRGbWkySG5qd1U5ZkpNc0xsMWRQMmtWZGt5blYySEdNSjVnUGR6M2tmYnkrQ1JxL3ZxK2ZqanV1aDh1SzFnODhZY3g2RUlURDdPQnRJWFVqNVpudTNtbmJIaDF6S1pGSWx2c0RSN0t5U1gwMUlvUjZtRUZNc1J4TTgyWS9HeHNYeWhLSWZyekR0WEhxWWlGQ1JJNmI5b3RaMjFJRWpUMVNuQ1BRcURnQy9NRVc1S0hSWnR0NHFKSm81WFFjRHFkMUNOaHRtWnNaRTNUTUtySWsrOGVHWkI1VGRDTmc2a3RGbWw5OTFmSUFMbkV3WGxOSlovWWxUaGsvWVFQYldwMjNRSVBOYzJndG4zdnR1UUhWalUiLCJtYWMiOiI3Y2Q2YWRkYzFkZmIwN2U5Y2ZmYTMwZjE0OWU5MTg2ZjZiNTk2Y2FiYzg4NDQ5MjNjNzUwZDk1OTAwNDZlOTE0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InU1dnpVYUQ4Q1JsWmxlbmFqTndNdEE9PSIsInZhbHVlIjoieGlYOVZndGo0TXJrM0oxZXBKcFNaR1p4ZDZ2T1dzRHIrcG9HQ1ZrWmUvT2lTUWRNQ2FJSGJGWllmUVZNNmxEamRhVXczd2dTRnJFS2syV3BGRjRwK3hmTCtteFB4K0VTS2w5QXV3R0hIMC9PT256czUzamJISU5aSG5hV2p6eXJVcEdpTWFTVHpwZHdKYjh3MGRzc3ZGOU1KSGNuYnhvTng2bmVjK2NkV2pReFNDMytvbFlUVzRUMDVsbmNqb1BvbE9jcGdxbW9GNjdxbm9NUGs5akZUYkV4TW1UODlzcURDUWVJdEdVZE83MkRpUkJLK2VUUHdkbVRRZWRob2pVQSt0SnViMENTdDJ2Z2kxSEZWU2pST2JVUFV6OXk2K1FFUEJPUU9KckMxRmMvQUpvU3BjK0hXR2JMTUVHaDRsQThWWlJ4NTZnd3NpVmRiMExWbHZIRXNyYi9hMThXUGxsWkJNdzY4U1hIYzhKaWc4c3VvVXd2TkdpTzFnTUJsWTJ5Z3lWMENiL1ByMnNOUkphSVlYYVNsdmVUVnZpbWJsRmh5ekhQMWlkRjlaVHJHaEJJWk5hMzFtT0x3b0c3MUkwcDluekF2b2ZXQi8zM0xFaSttNlR0SC94ckp0Z0RDS2tISWlIc0xhbnU1dVQrWHdRRWNGV2tGNEZWY3ovenBKQVIiLCJtYWMiOiI4YzUxYzY0MTdmNWJmNTU2NTBmZmNiZTdlOGI5MmYzOTYzYjViYzQzMTdiZmY3Mjg1ODNkYzE5YzhlZDgzNjhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105804688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1012770471 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012770471\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1734255943 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:59:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1hUDlheVlyamFsOUpvT240RFVWNFE9PSIsInZhbHVlIjoiMTBXR0xUUFkyR0VqbCtDSTJjdFVYTFp4WHZkcHlEQmhnMnVrZ0NCckdULzFlTGVobXRaVzlNYitRa3hrRmRBd1BONzJVbnNRM0NoNHJYTFBPN1lESkg3MzAwR1Iyd25veHQwWGxBQ20zNHFIU2FjQ2lJUCtudENoSzFybFVpTi9xRmxJdmh0RWM0V2kwbGxyUkZST0x6ejVGZmtjZkZuNHBiWjhTZlYyaDNuZk9mQXdBanc1U1Vxa01rRzUweUdoOGFHTGEwOThOZjlDOTk1YloxeE9nRTJuRWdQb2t4QTB1Zk16Yk9wWitFVjJSUmVNUXZ3eEpnVUdHVWNaRkFDZnRsSmZxOG5ETENJVzdoM3ZWaUh1SUVuVnlSYXI2eFRuU1o4SS85S0JMNDV6djV6eE95LzU4Nit6Qnk0aGkzbThPU0E1Q3g5ZFhhVk9kMi9KdDdTc2VhVGFnNjZzTGV1NENnZW1iU1lleFZZdS9heVhBenlrSXJzNGplaUNLclhVU2Qvcnc1blJkbVJxbzRJQXoyV0tKeGhKcE9Ib3QrQTB5YmVyQUlNbU5HUzl1WnNZaDljK1pwbkhrQzU4QVA0YUcxM1VoSTFTQ3JJUCtiR3VJcGtURlR5ZTdmOUZJMW9vUk1CTjVvdWZGVEsxTEdSdktHcHRDUmNBZGZjcFZxREUiLCJtYWMiOiIyMzM4Y2JjOTYzMzk4ZmRhMGU5ZGU0ZjgwMzc1OGJiMmJlYTZlNzU4ZGZiZjNhZTg3NjVhZGZiYTkxY2M5YmJhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:59:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImN4OWtNK1V6VWdvczVSZm9JWHlrYVE9PSIsInZhbHVlIjoieTlmVWRRSGp6QzduS1RSMXlXUEJNUy92VldBdlcvZWRoM21weG1GTjBQOWx6RmErSCtEMHo0OUYxVmZZNWtRdlZhTGgyLzhtR2VYRXVKYWovY2QxRXFZQnVNSElGby8zbnAyYWZrcldKSHVkamZ5amRSZlhQcC9NZFZaTGx3ZFpXbllaT1FOVFA3WTZjRmdwdC90dDJrZXI4dG9HSVlDY2tta1lad0hHcTRwMFZnb3V2N0szVTRFZWsrU3VXQ3VwbzVUM3I4OVkrbVNVZFluTjRDNlArYkhzRi9NaUxGU1NHWXdDamZiVEE3TU1KclFDbERVRGVBVUgwbHd1L1BJeG9VaU8zYXRXajNwcjQvZUx4d0txLzVJalhhMTR4MGp5NkRUUHBjeFJUMmh1bHgxbXBPQnQvMWFiMlBPY0M3dnVtNUladHc4eS9rYTFCbnIvSWhYY24wR3dTSWpVYnFwbHo4cjhIcjBvSkZzZkNBOTJnb2ZseWU5VXFIVmF4d2g3eERSTnJ0RVB3NmhlejRUeENpdXFPRGF3RHcrcEVMQmdNR3oyQ2xBQVI0TjRvanVweVNiVFBISkx5TXBNSGpXNFdpRHNFNTB3cDRtS1prZVY0bzhrOUliNEZITmUrVlMreFFuS1Q0S2kzdGFVaVVOVUFqMUMrbjcxVVJBTkIzREUiLCJtYWMiOiJkOWRiYWE2M2EwZmYyZjdkN2RmM2M2ZGQ1NzVmMGQzZDdiMDdhNzUxN2FhOWJhYTNmNDlkNWYyMWI4ZDA3YTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:59:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1hUDlheVlyamFsOUpvT240RFVWNFE9PSIsInZhbHVlIjoiMTBXR0xUUFkyR0VqbCtDSTJjdFVYTFp4WHZkcHlEQmhnMnVrZ0NCckdULzFlTGVobXRaVzlNYitRa3hrRmRBd1BONzJVbnNRM0NoNHJYTFBPN1lESkg3MzAwR1Iyd25veHQwWGxBQ20zNHFIU2FjQ2lJUCtudENoSzFybFVpTi9xRmxJdmh0RWM0V2kwbGxyUkZST0x6ejVGZmtjZkZuNHBiWjhTZlYyaDNuZk9mQXdBanc1U1Vxa01rRzUweUdoOGFHTGEwOThOZjlDOTk1YloxeE9nRTJuRWdQb2t4QTB1Zk16Yk9wWitFVjJSUmVNUXZ3eEpnVUdHVWNaRkFDZnRsSmZxOG5ETENJVzdoM3ZWaUh1SUVuVnlSYXI2eFRuU1o4SS85S0JMNDV6djV6eE95LzU4Nit6Qnk0aGkzbThPU0E1Q3g5ZFhhVk9kMi9KdDdTc2VhVGFnNjZzTGV1NENnZW1iU1lleFZZdS9heVhBenlrSXJzNGplaUNLclhVU2Qvcnc1blJkbVJxbzRJQXoyV0tKeGhKcE9Ib3QrQTB5YmVyQUlNbU5HUzl1WnNZaDljK1pwbkhrQzU4QVA0YUcxM1VoSTFTQ3JJUCtiR3VJcGtURlR5ZTdmOUZJMW9vUk1CTjVvdWZGVEsxTEdSdktHcHRDUmNBZGZjcFZxREUiLCJtYWMiOiIyMzM4Y2JjOTYzMzk4ZmRhMGU5ZGU0ZjgwMzc1OGJiMmJlYTZlNzU4ZGZiZjNhZTg3NjVhZGZiYTkxY2M5YmJhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:59:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImN4OWtNK1V6VWdvczVSZm9JWHlrYVE9PSIsInZhbHVlIjoieTlmVWRRSGp6QzduS1RSMXlXUEJNUy92VldBdlcvZWRoM21weG1GTjBQOWx6RmErSCtEMHo0OUYxVmZZNWtRdlZhTGgyLzhtR2VYRXVKYWovY2QxRXFZQnVNSElGby8zbnAyYWZrcldKSHVkamZ5amRSZlhQcC9NZFZaTGx3ZFpXbllaT1FOVFA3WTZjRmdwdC90dDJrZXI4dG9HSVlDY2tta1lad0hHcTRwMFZnb3V2N0szVTRFZWsrU3VXQ3VwbzVUM3I4OVkrbVNVZFluTjRDNlArYkhzRi9NaUxGU1NHWXdDamZiVEE3TU1KclFDbERVRGVBVUgwbHd1L1BJeG9VaU8zYXRXajNwcjQvZUx4d0txLzVJalhhMTR4MGp5NkRUUHBjeFJUMmh1bHgxbXBPQnQvMWFiMlBPY0M3dnVtNUladHc4eS9rYTFCbnIvSWhYY24wR3dTSWpVYnFwbHo4cjhIcjBvSkZzZkNBOTJnb2ZseWU5VXFIVmF4d2g3eERSTnJ0RVB3NmhlejRUeENpdXFPRGF3RHcrcEVMQmdNR3oyQ2xBQVI0TjRvanVweVNiVFBISkx5TXBNSGpXNFdpRHNFNTB3cDRtS1prZVY0bzhrOUliNEZITmUrVlMreFFuS1Q0S2kzdGFVaVVOVUFqMUMrbjcxVVJBTkIzREUiLCJtYWMiOiJkOWRiYWE2M2EwZmYyZjdkN2RmM2M2ZGQ1NzVmMGQzZDdiMDdhNzUxN2FhOWJhYTNmNDlkNWYyMWI4ZDA3YTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:59:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734255943\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>57.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}