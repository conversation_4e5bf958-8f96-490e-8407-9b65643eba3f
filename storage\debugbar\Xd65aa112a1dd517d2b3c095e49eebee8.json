{"__meta": {"id": "Xd65aa112a1dd517d2b3c095e49eebee8", "datetime": "2025-06-30 16:07:22", "utime": **********.54822, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.088324, "end": **********.548241, "duration": 0.4599168300628662, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.088324, "relative_start": 0, "end": **********.484168, "relative_end": **********.484168, "duration": 0.3958439826965332, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.484177, "relative_start": 0.39585304260253906, "end": **********.548243, "relative_end": 2.1457672119140625e-06, "duration": 0.06406593322753906, "duration_str": "64.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45570864, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0035, "accumulated_duration_str": "3.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5159159, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.714}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.527831, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.714, "width_percent": 14.857}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.533726, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.571, "width_percent": 17.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-984936781 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-984936781\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1532262332 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532262332\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1501113416 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501113416\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1645641016 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299634011%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw1dmNVanYrVDZ2eU15ZVVwSkViOEE9PSIsInZhbHVlIjoiRjJpNmdCRDdpbjdhaTJnOVJWb29sOVFCSlNhd0RPQTdRQ0NsamhTSEd1SlBFMERXOHpMazVVaFZiclBvcFJIZ3pKa0JFUGNneEptK3BabG5PNkRPTENhM3lRdmNvOWhReXUzWm9oclMyRWh2cHlEZXg4bTlacXlrUXdDV0xRRWFQZCtPNWgzb2RBZ2FYeDhkQk80dlBvRzQ0ekhRTDlnYW1wcE5zRldiOGUwUjM3K1I0ZGx4ZnQ5NVZtU3ZpOHVSaXk3SzJpTHMyUVA0ZEtPeC9SdE5oY09OVm5NdVZ2c0sxd3FrMTZEbHovNkVNZGVYNmVMaTlPWDVnZy9hTFVMeis1cWlYVkRuOThwZWlUSDFjSGJRRTQ3R2RpZHAwMHE4MHVxa1hIaWZlekY2dUxqMU90S3FJbWJ0WDFTTmYxQ1F4d1Z2azJjdFNvaU9NUDk0dDk1eW1nMTNWY1l1Tk5uTVR4SzE2Y1hHY3pQN3d3T3ZmK0JaVGFNWGcwZXFBdE0yMDhlcUVYWEh6Nm5wZVNZdURucXowY3hGU0xFYmdJc0diK3M3blpWdkI2UXB2RUlDSy8zOGR1V1llRHBwYXdvTEdWVHM1R3RBZVpKeEg3L1FYSUxFalk3WXk2bmhvTnpaRjNhRWtKL2Y4TXJIYzJqOXRTOWt2N0d6ZkpEMEI3YVQiLCJtYWMiOiIyN2E2Njg5MWEwN2RmMzc3YTRlY2NiZmIwYTI4MzkxYWZlZTY2OGNmNzkxMGJkNGI0ZWMwMzcwYzBkMzAzOGMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRxa2xkZ1ZaUWIyZWk4OExzVHRpcXc9PSIsInZhbHVlIjoieXN1eUdZTU9xQjZ0d2o5UDYxUDZnYXlpekU4aUtWM1hUYmhUNFZ3anJsUEFydDBENGJSTHpQOHhlN1pnOHFkU2xKR1czLzhpMDlPTTZKNktpQXV0Qy9EOGhiQUFMeCsyanpHWWVORCtYUWJjc1ZEOHRJMXRXMmxna0NsQzV3dEFYaFZSVW01SkJBMG9hM0V1TVFoaHNwRmpacTJnTzI0Lzc0cEQyZGJzbTRaemFjRWUxeE0zZmgwRGxVWEtsN1FaQlJDeVdNd2p1djZkNlQxTEY1emJXa2xsaFNWT3FjVlBuOWV6U09WaXpabHVITUliVVZqVzNGOWZYYWtHWHdXRS9OOWZuTGFIUGNSYXN2eHQxL0ZiTDhpVzFDVWNHYmVjWTQ1Q0g0QmlRSzhzY3dwbHRUK0RuZ1dyT1BFK0NIY0NyUWx2Y3NRbTNsZnBablFhUWJPMUNaZDZpRDMvR2YrcGh2WEdYcFNBQ0NCWVdzNTFHQm8xUWxpUFhKV2crVThjMCtlNlZRcFlUZGREK1cwazB3dnhDQzJBREk0a2lwZDh5bm5tcUVHVENOTzVjVTdjWUhLVjYrNGlwdTQ5bkM5TnpWeVQ0MG5BMmcxVXAwcUh6bzRKZUY5MkQxOUpLUmtOQTRubkdBakcxQnR1b1NENUhZZWVqVnpsbFV3VmU2ZEQiLCJtYWMiOiI4NzFhODZiNzdmNDVlZDQzYTI2MjQzNTc4N2I1ZDcxOTZiNjg4ZTBlOWY3YTA0MDM1MWY0NWYxMTlhYjU0MGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645641016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876770346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktKbHZ2aXdhay9NS1NwTm1xZ2xmTFE9PSIsInZhbHVlIjoiQ2IzVlUxWGhwREJ2NVIvU0dzb0NMMCtLYkgxK0lLWHNnVUpaMjdENWp3d3pmaWNGazBsMENlS1VJWXhqb3ZDOThEQW9PNVJQRCtydlpQU0E1blBLcUJyTldoQ3FFeUs5eGk5U1RLMFlhTTI1azFZalJhUjN3RHhMS2hkejRSTWNiMmtGQWplMVlqelVMaXp2WHh1TnhVYW5xTUNYZGtCbUowbm1BWWllcFJIcmhPNWpsMUZLam1NNjVDMWI5UGNBQnhLZDJzdTE1MjlFVUdiL2VOSGJoakYvOGxGV3lBSWkrSEU4cmhiNXhQMTZaN1h1R3JZWEx6ZWpna3UvUlR0TmJaUEwvRldkU1FVbVVPcUFBRmg1bElQVDZwZmZkNjRzYWNaclJyTms1dkUxeWlGcXJGbnJSUXhaVmhmYkhwTkZ3MmN0ZGRsU0ZvVlBsdUlkRHZWdnFMUlIxRWZKU3UzNjVuMXpyeG95OEVUdWJQV25UcW5QaytxQUMzSks1NW1LbjZ5bnp0KzN1bFZRdGNKMklzNHJQT016WGNRa29sa01NbE50QlNRYVEwRWNWazF2TGdGZTFtcldFc0F2QWRqaS9UMVphNy9kZTNaSjNQUTVFdnZnNVJQTktRNFNNZFl3akxZUHk1STdUWVB2Y1AxbC8wNUxuY2s2Z1ZnbW5ob3MiLCJtYWMiOiJkMThiMDQxMDkxY2E5Mjg4YWI0ZWM2NzFmOGZiOTUyODNmM2ExZDZlNzdkMjdiODg0Nzk4NDY0YzJkY2VhNDQ2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0vZnBhYnVvTUQrdXFwWW93UlBsY1E9PSIsInZhbHVlIjoiMzVVdUZEaXIzY2lzUTdTRU1PdnpCZWkxOS8xdkNVbjRqVXI1U1ZDUmFXYkYxYThHUE41dEJFejJUV01lSk9XL1BNSk1Hc1JicThjZERaSnVrVE9EazZDM05SWWx4d1J2UVE4aVJpemVTbDdWSFRnMVlPTFlGWmM1di95Z01GTWFxVWhMV0NnUUlOWnBURTJOVm16U3NYWXB6NmFCQllldXFtTk5QcWNqb3lLSkhsL29ZVkMyanBkZ1NKUFJnTE05Yk5lT3ZZOGFtVWlBMFMrU2hralcvQkJQbnVlWUxHNTdQQ29oSFhwWCt2OHRCV2VHTloxM0ZKeTZaOEFya05yRGNwa0c0WC9ydlVJZCs0N2hsaDA2NUxySHhUeVBmUXVacFprNFQxSE5FWk1STm5WWGdyaEl1anQ0b1BLaklMVVpkcnBiSjVIbG9XeEc1Vk00Rm8xaHJUN1FWSkhmR1Z1RjhjK25hVkwydTBOTVRuSmxjb1k0VDlWbEo3eUo3QXlkQnVVcCtJSmFocUpYWEl0SStQY2lNUWp6aUpNeWN3NW9CQXR1QlFWbTUvQjA0cHExMms4dEEwSXQwTDhwdElYNDRTUmxpU092SjBGT25VNlJHVjhMVkVpRTlHbTNFdkRUSUZEcEc4OFZmNFlBbk5sdkhkSXVVMitWYlBvSGNRUE0iLCJtYWMiOiI1YTcwMzRiZWMxMTljNmExMzEwMmM3MGRlMTAyNjExZTA5ODY4NmIwNDcyNGY0ZGE4ZjFmYjRlNmU2MWUxNmFmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktKbHZ2aXdhay9NS1NwTm1xZ2xmTFE9PSIsInZhbHVlIjoiQ2IzVlUxWGhwREJ2NVIvU0dzb0NMMCtLYkgxK0lLWHNnVUpaMjdENWp3d3pmaWNGazBsMENlS1VJWXhqb3ZDOThEQW9PNVJQRCtydlpQU0E1blBLcUJyTldoQ3FFeUs5eGk5U1RLMFlhTTI1azFZalJhUjN3RHhMS2hkejRSTWNiMmtGQWplMVlqelVMaXp2WHh1TnhVYW5xTUNYZGtCbUowbm1BWWllcFJIcmhPNWpsMUZLam1NNjVDMWI5UGNBQnhLZDJzdTE1MjlFVUdiL2VOSGJoakYvOGxGV3lBSWkrSEU4cmhiNXhQMTZaN1h1R3JZWEx6ZWpna3UvUlR0TmJaUEwvRldkU1FVbVVPcUFBRmg1bElQVDZwZmZkNjRzYWNaclJyTms1dkUxeWlGcXJGbnJSUXhaVmhmYkhwTkZ3MmN0ZGRsU0ZvVlBsdUlkRHZWdnFMUlIxRWZKU3UzNjVuMXpyeG95OEVUdWJQV25UcW5QaytxQUMzSks1NW1LbjZ5bnp0KzN1bFZRdGNKMklzNHJQT016WGNRa29sa01NbE50QlNRYVEwRWNWazF2TGdGZTFtcldFc0F2QWRqaS9UMVphNy9kZTNaSjNQUTVFdnZnNVJQTktRNFNNZFl3akxZUHk1STdUWVB2Y1AxbC8wNUxuY2s2Z1ZnbW5ob3MiLCJtYWMiOiJkMThiMDQxMDkxY2E5Mjg4YWI0ZWM2NzFmOGZiOTUyODNmM2ExZDZlNzdkMjdiODg0Nzk4NDY0YzJkY2VhNDQ2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0vZnBhYnVvTUQrdXFwWW93UlBsY1E9PSIsInZhbHVlIjoiMzVVdUZEaXIzY2lzUTdTRU1PdnpCZWkxOS8xdkNVbjRqVXI1U1ZDUmFXYkYxYThHUE41dEJFejJUV01lSk9XL1BNSk1Hc1JicThjZERaSnVrVE9EazZDM05SWWx4d1J2UVE4aVJpemVTbDdWSFRnMVlPTFlGWmM1di95Z01GTWFxVWhMV0NnUUlOWnBURTJOVm16U3NYWXB6NmFCQllldXFtTk5QcWNqb3lLSkhsL29ZVkMyanBkZ1NKUFJnTE05Yk5lT3ZZOGFtVWlBMFMrU2hralcvQkJQbnVlWUxHNTdQQ29oSFhwWCt2OHRCV2VHTloxM0ZKeTZaOEFya05yRGNwa0c0WC9ydlVJZCs0N2hsaDA2NUxySHhUeVBmUXVacFprNFQxSE5FWk1STm5WWGdyaEl1anQ0b1BLaklMVVpkcnBiSjVIbG9XeEc1Vk00Rm8xaHJUN1FWSkhmR1Z1RjhjK25hVkwydTBOTVRuSmxjb1k0VDlWbEo3eUo3QXlkQnVVcCtJSmFocUpYWEl0SStQY2lNUWp6aUpNeWN3NW9CQXR1QlFWbTUvQjA0cHExMms4dEEwSXQwTDhwdElYNDRTUmxpU092SjBGT25VNlJHVjhMVkVpRTlHbTNFdkRUSUZEcEc4OFZmNFlBbk5sdkhkSXVVMitWYlBvSGNRUE0iLCJtYWMiOiI1YTcwMzRiZWMxMTljNmExMzEwMmM3MGRlMTAyNjExZTA5ODY4NmIwNDcyNGY0ZGE4ZjFmYjRlNmU2MWUxNmFmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876770346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1164851032 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164851032\", {\"maxDepth\":0})</script>\n"}}