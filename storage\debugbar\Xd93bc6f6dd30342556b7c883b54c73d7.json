{"__meta": {"id": "Xd93bc6f6dd30342556b7c883b54c73d7", "datetime": "2025-06-30 16:06:35", "utime": **********.725283, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.286522, "end": **********.725299, "duration": 0.43877696990966797, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.286522, "relative_start": 0, "end": **********.670618, "relative_end": **********.670618, "duration": 0.3840961456298828, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670628, "relative_start": 0.3841061592102051, "end": **********.725301, "relative_end": 2.1457672119140625e-06, "duration": 0.054672956466674805, "duration_str": "54.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7010322, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.231}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.712725, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.231, "width_percent": 14.17}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7182739, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.401, "width_percent": 16.599}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/58\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-72427166 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-72427166\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-494108902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-494108902\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-502370699 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502370699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-176898615 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299580153%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkYrc0tmZklMOEdWYThRZEhCODAxVGc9PSIsInZhbHVlIjoiSnE0WHdSeU83dWpmNUVKbUZwaS9BRXdyeHJjZUF3eDBPTXk2RU5UNkZ3TWQ0RjFZbTd0aTZ4UHp4WG9CUjFQeHM4bkZLUFJKT0VkSVdWNzJiRTMzMml3d3dkVUFaNWdBWE1CSHZaMTZCT3ZnQXNUMUtpOFNtWjJ0bmxkV3Q0bm8rTVo1czExdTQ1c0gyNGlxQkM0aGI0VnZoS3BoY3k3dG1qY0ZWT2Nuc1pjUTZCSW9FOXNRU0lMcXM5OVJlYUptVCtOTEIrblFaSmJPeWtBL082RE0zcHZzb3FkZ3RDTGV2N1FhNFluZkZEc3d1SFNMRlQxd1VlSjg0ZFIwRVUyeEd4NW01bVpES2plU0M3cEpTWTVSdS9Fd3lub210c3BabFltU0h2cDk4bFpWbytpOTRYSmtRS290Uk02ZDExNXg1am9vTkhjMUU5L3M5VThOK003NWpVNHBnM1ZMVXVlQUdTVWpnSGlSSkx6SjhPZjgvM0tEa0JPUmxWc3YweXFrL2pCbXI0MU9wY2lEajA2RU5jZHptMjVWWUoya2luaGM1WjJaTGgzMDZEcEE5MXRUUVM3OXFIZXNQejZVdG5CamZlZ2s5Wk5HRXVCK1c0eW9UcXptRXJvUGRLczVwUFg4SW5rMWw2Smo3M3d4Qk4zWnJSY1JWNGlkb0NKSzdiQkciLCJtYWMiOiIzZDM1N2Q3OTU2ZGViMDIxZjQzM2RhYTBjYmMyZDU1NGNjZDMwMjhlNGU0OTg2YTMzY2Y2M2MxYTVhNzhmNzNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZDcFdiOERkOHY3ekRnazA5akFhSkE9PSIsInZhbHVlIjoiOEI3QWF6QTF5V3JldUF6NEVHS1FyNWRrTG9hU3BIaG11aGRXckZPMnhlbWdOazJ3ckkyVmorRmpMRFBQS2VvczBxZjdUY3puZ2NrR2NyTXRkaE5LWnFmRTI0eHlwL0FhSEhMN2JBL2hRWmQzdk12V3VIbmhSUjhWRkw1aHRSSmJkbnlESjFBUjBERVN0TG95a0tBNXV5SjVDb1JXcHNVWTZ4MFl3TUgzTUtrVDJUUjYvcC9UUDBuSG1zcHcxNmFYQ2xmTUM4MkJERnVCQVFsb1hnaURwci9hQkVVTFpPZllpQmZzWEZ6OWVOV1NOVUtRd3IxUWJCMkF1dENlMjNoRzlXSVVwTE5CdjhxeG9jMkdyK1lpNUE0SXZDaGViS0FsZlFXOGdJWDZzQ2JUeUNCQWZGQiswUXhzV0loSC9tenlIM3dvMkdpUFhzZEdqazZqMDgxMXBnbzJpRFZaRDhHSDJ4dWpGVDlwS1MzdENSeWFFNGxQU2d0L2R1VDg4bTdsL0lFaVloM3ZLVHFjdmY3Nld2ZHBrQU1vSFBqcnVPYitPTFh0d2VBdXc1d2tuWmJlSUFmaG04SzA2Zkw3YVZveWZJanVGOTd0NTN3cFZDUWE4dWY2SjdIazZmaEthNGVaaWh6dUUzdDMySnYwZUtEbExlZFNnV0M1MTdzazBEaGkiLCJtYWMiOiJkOGI0ZDZkY2VjOTk0Yzg4OTlmY2Y2MzczZjhmNDQ5YWEyZDFjZDNhNDE2YzJiNTBkMTI0ZGExNGM5MzJlMGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176898615\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2086531339 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086531339\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-244821483 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhnQk5lUTUxL0I0NWpzcWlOUUdDK3c9PSIsInZhbHVlIjoicXErMHFCRkJDSHJlaG0vTjMrdkJacnM1YndxbTBtOUZmcFdSbU00N3dBbm1CeGhGR0d4em8wMkpVZENkUkxVTWxnbXY2KzhDakw4SDBSanBpRVJGTDdWQWlVM05hNk9EUE43L1EvMWFONkRWdmxadDJCai9KYVBQS1ovZlhnK0p3MGdTYnZSOVZHbytMV3BYOHl1elhoaDUvUUdnRVhtTUplSWlNamhDaGdSRktCQkx3YjhCWWRiaHN5UWhvUVRSd1djbFI1MEdUOSs4Y2E0TXg1RjZud3ZybXdIdTYrUHFVTkFRWmJHSHcyR0Y5ZGpnTDdvWTlSR044dFdzMXdSVmlnam1hTnNSTnB5WnI4YU8zeTlHSStQbkp5NFI4T1Q0anN6QWZLMmdHbExGaC83UlJzRnI1MHBreGwwZ2pMbHd3c2xmbmpONlRlMUZQdUNUdTZPazRUWmpyUzF0WVR0alZhV1MxZUhiUWd6MG9wN0FEdmhZT044MWpZV2R4SlRSOXV4ZUFIYlhLRVFJNWNyYzFob0ZKVjQ2WEdaTEM4WkVMMmRzVi84Mmw4WWFvcXVxYjlZUERZYis2SUY4SW9sdzlJa3dBaEVXbmUwTE54eEZvUlNnRFZLRURVQXVjbUl6L3RhUjFzOFBGQmNhdmJadnlKNStyNVIwTW9lZUlBei8iLCJtYWMiOiJlNWUxYzUxNzRiZTQ4ZDNjY2VlMmY3NDdlNjgzYzE0ODRlYmJiNjE0ZjljODYwMzQ1YmRmM2EyMmM3ZWZiM2QyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhJbE82bjU4bVdrb082em1PWm45TlE9PSIsInZhbHVlIjoiK2h6NDBHOHArTXNMaEhIdm1YZlpOQ3hHMUNwditpZ2J1eXBNem1raS9jYm5xS3l1QmZ5ajd3UnkvTjl3UGlNUmJMeHQ5NWZJTmlvYUtxcWwvZGsySkxUZDZUWWVKWXlWdlN0Q0N1TEVJdUN4QW5iV2FIWHVZUU1FZzdSMGNxVEhiMGYwR3VhZlBwV1hPTFhLRWlZWjBWS0RsZUsyNlVScVc2VGhIRmdicGduVCs5KzlYWWdJQXZFcHpCZ0VYN2NETW5qYS9tRnUramRqZHRBcmZmZmRMdmI4ZjRseGFycTR1OFBFNitWUmcwOGJDRG1udzNHeEFGV3FNU0Y0cnR3UmxMR2krcEEwMkhWSTRSNG1sRTJhb0VxWE50cnJIVFJOSkhLZVB6ZnVmU0dNa3FJUkFlKzREeXAwQk9MZkQ2UjNjWGRxd1dyWTJFZzIwUmlDN3BNeUtkY1dxVi9Qbk53NUVQdHp4eTU3OTBIMW90WlJuTEVzYWpEakYyQ05Gc3JLMnloQzJocFNnUWcxMHpjRm5lbGV3OWF1cXRQdGkxRzgzNmUyQktpdmxpaEQzdTcvSmlHbnJUZmlvWkszRFJRTjB6NGpvWmxEZDJBV0swbFNRUjQ5bTlNdTkraEJ6SUliV3FkYm9Ic3pZeHFLRmdjVEpGeWVzb3hrRUo4Q3M2a28iLCJtYWMiOiIxNTY5ZTU0OTE1YzYwYjg2MjJmYmIyYTVlMTY1MjZhNzI0MTE4ZDI5ZGVhZTg3MTY5YWRlOWZkNzg4MWQ3MGU0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhnQk5lUTUxL0I0NWpzcWlOUUdDK3c9PSIsInZhbHVlIjoicXErMHFCRkJDSHJlaG0vTjMrdkJacnM1YndxbTBtOUZmcFdSbU00N3dBbm1CeGhGR0d4em8wMkpVZENkUkxVTWxnbXY2KzhDakw4SDBSanBpRVJGTDdWQWlVM05hNk9EUE43L1EvMWFONkRWdmxadDJCai9KYVBQS1ovZlhnK0p3MGdTYnZSOVZHbytMV3BYOHl1elhoaDUvUUdnRVhtTUplSWlNamhDaGdSRktCQkx3YjhCWWRiaHN5UWhvUVRSd1djbFI1MEdUOSs4Y2E0TXg1RjZud3ZybXdIdTYrUHFVTkFRWmJHSHcyR0Y5ZGpnTDdvWTlSR044dFdzMXdSVmlnam1hTnNSTnB5WnI4YU8zeTlHSStQbkp5NFI4T1Q0anN6QWZLMmdHbExGaC83UlJzRnI1MHBreGwwZ2pMbHd3c2xmbmpONlRlMUZQdUNUdTZPazRUWmpyUzF0WVR0alZhV1MxZUhiUWd6MG9wN0FEdmhZT044MWpZV2R4SlRSOXV4ZUFIYlhLRVFJNWNyYzFob0ZKVjQ2WEdaTEM4WkVMMmRzVi84Mmw4WWFvcXVxYjlZUERZYis2SUY4SW9sdzlJa3dBaEVXbmUwTE54eEZvUlNnRFZLRURVQXVjbUl6L3RhUjFzOFBGQmNhdmJadnlKNStyNVIwTW9lZUlBei8iLCJtYWMiOiJlNWUxYzUxNzRiZTQ4ZDNjY2VlMmY3NDdlNjgzYzE0ODRlYmJiNjE0ZjljODYwMzQ1YmRmM2EyMmM3ZWZiM2QyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhJbE82bjU4bVdrb082em1PWm45TlE9PSIsInZhbHVlIjoiK2h6NDBHOHArTXNMaEhIdm1YZlpOQ3hHMUNwditpZ2J1eXBNem1raS9jYm5xS3l1QmZ5ajd3UnkvTjl3UGlNUmJMeHQ5NWZJTmlvYUtxcWwvZGsySkxUZDZUWWVKWXlWdlN0Q0N1TEVJdUN4QW5iV2FIWHVZUU1FZzdSMGNxVEhiMGYwR3VhZlBwV1hPTFhLRWlZWjBWS0RsZUsyNlVScVc2VGhIRmdicGduVCs5KzlYWWdJQXZFcHpCZ0VYN2NETW5qYS9tRnUramRqZHRBcmZmZmRMdmI4ZjRseGFycTR1OFBFNitWUmcwOGJDRG1udzNHeEFGV3FNU0Y0cnR3UmxMR2krcEEwMkhWSTRSNG1sRTJhb0VxWE50cnJIVFJOSkhLZVB6ZnVmU0dNa3FJUkFlKzREeXAwQk9MZkQ2UjNjWGRxd1dyWTJFZzIwUmlDN3BNeUtkY1dxVi9Qbk53NUVQdHp4eTU3OTBIMW90WlJuTEVzYWpEakYyQ05Gc3JLMnloQzJocFNnUWcxMHpjRm5lbGV3OWF1cXRQdGkxRzgzNmUyQktpdmxpaEQzdTcvSmlHbnJUZmlvWkszRFJRTjB6NGpvWmxEZDJBV0swbFNRUjQ5bTlNdTkraEJ6SUliV3FkYm9Ic3pZeHFLRmdjVEpGeWVzb3hrRUo4Q3M2a28iLCJtYWMiOiIxNTY5ZTU0OTE1YzYwYjg2MjJmYmIyYTVlMTY1MjZhNzI0MTE4ZDI5ZGVhZTg3MTY5YWRlOWZkNzg4MWQ3MGU0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244821483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1814567394 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814567394\", {\"maxDepth\":0})</script>\n"}}