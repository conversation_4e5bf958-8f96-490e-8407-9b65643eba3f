{"__meta": {"id": "X5e6a7725c3d8f29e8c019572400695d0", "datetime": "2025-06-30 16:21:59", "utime": **********.616335, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.163343, "end": **********.616348, "duration": 0.4530050754547119, "duration_str": "453ms", "measures": [{"label": "Booting", "start": **********.163343, "relative_start": 0, "end": **********.571928, "relative_end": **********.571928, "duration": 0.4085850715637207, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.571937, "relative_start": 0.40859413146972656, "end": **********.616349, "relative_end": 9.5367431640625e-07, "duration": 0.04441189765930176, "duration_str": "44.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43873800, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00273, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.603317, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.418}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.60927, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 82.418, "width_percent": 17.582}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 2\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 21.98\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-47109185 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-47109185\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2065184088 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065184088\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im95OXk0cldSQ01VeGhLTlUwMFg2VWc9PSIsInZhbHVlIjoiSm4xaUF5V3FPeHFwRkNJUnZIQTN1d0s1SjRWNmZmaFUrcjZaR3NuWjlEcXd4ZGZtckJVbTlQRHZyVlJycDBFOXlCTlR6b28wVCtYSFA5dDcvRTZwQWptTnk3VHp3akowVzV2YUZ4MjlwSmVzMGxKTG13S0VRbFRWeW9BUXRQMjJuMDhlUjRSZjhibzI4NDljclNoMHo0cFVSajI3L0k5T3h5aGl3UzR3eTRvRXFxVWlrQU85SEoxTEx2eXZmckthRnpmNEFGTUhaZjFKZG95OVVsVGhOYkxUQTUxZ2g1RTlUd1ZlWVR3eFdaRFFlcHU1SHh6T3U5anY1Ym5obEhBR0NpZGFhU0s4d1NPMkRtSDFINVB1OTJLZ3gwN1NrZ0tDcElBVC9SRHNEVlVNcHVvL0dvRGxGdlNnZGFqblRkdWVTMWJEZGlYVWR0NW95UC9pRmdzdmlNcmlaMTZNck9RbVQvNmpudlNNSTB1WnhWUVMxb25uZk1iRDZFMGFmVE9Lb0REME4xZEdPYjFDeG9oSzhrRWVJM2tXRnBKaU5FNFNuQjlZRFBoUnRhNkZyNkl6a3NYeng5VDA2SXVDSFM3bXZxdGN1SWF0czMwWng1cFp5NXRaa0I4K3I3eFpKeXEwc3FkSUpaN1prNEd6OXUyakIvaTNXWGpSbmUwSEV3WW0iLCJtYWMiOiI5NzE0ZTJkMDg5OGQ3NTBjMjgzODUwODNhMDAxZWY1NzM4M2YwY2M3NWMxNjQ5NDlmNzg2NjM3NTFiZDViY2E1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAzdXp3eVpGcitNQTlnTjQ2d1k4UWc9PSIsInZhbHVlIjoiU1ZsMlJnVW1RMnp1Yi9TdTNINHMzYnJiVEVZL0JXa1VuUTUrcWYzSkphbVpEL0lBdEo0YXAyeng2b0srelRTZTFES1pialB4N09JWG84cVVlQVlma1dyckxSNURsY1lTWTlsbG5aa0pvYVZNd2IvTUZ3NDRobkZOVW5sYzZESXFvQmJXY3FtQlkzVkNndkZDTEoraHhQQVhzNlRYVmp0OHJBUVVFV1Q4VmVUaGM0dlVSK0Ftd3N3N1Zya1dYQ1YvdlZXRExML3lMZDM0WmxNRDFMa29IN20xVk1oMWdsM2dHR1dscVlUNnMzS05HTlJ4MzZNTmNEeGo0SHFTeFVYdmk5Rkl3ZklRVm0rYURSWEtqSVZmaFd2bFZoZENwOG9hejY0YTVUQnhzRzl5V2dpNStXeEZiMEFYRHkwNWl3b0tBUTJGRDlwdEtudVBsdHJYL3RNRkRhek5VRlpDTDQ2MXpvY3JaaDBDQW84MlFNM1A1cUYvbmFwNGNFZ3dNcktRTmdhT3luK0ZQTlM1QXZUN2tZVlBoam5aUDhyT0Rkc3pRVW5DZkNkbTFnQTJnMDhzamJyVmNJam51RDMyZ2FFTkRtRDduckxvTXI1SExYb3I0Z21nVkxJR3M5OUNyclJjRTdJUkFEd3hwWWt1TDljK3pwMkVwdjBUc3dBd1Q4MUkiLCJtYWMiOiI2YjQ0ODRkYWVlZTNmMjZhODVjYjk0NzI0M2ViN2FkNGFhMzM3MmM4ZTI1NjA5MDdlZDAzZDYwMDMwYjYzZjJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1380838703 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:21:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJETnA0MDg1UWUxbFBLQS9GVXBBeWc9PSIsInZhbHVlIjoiM2MyWklZTWp4OFJBN2VhcGNlSWlKakVORXQvTXRsNFUzTy80UUp2VGd0cVlscnBLM1B6VVl4RlhvK2cxeEZ0M1JxTWVBSTVnd3NXNVNuaGZOczAwRWg1dnhiRktjTVIxVTlhSTFVZDFHV2l2eDFpMUFYWm5odDdiQ29RcTk5VFl6VThBb0ZkV2dIamRCdkkwb0c1ZXEraGVTOE5kZ245VGo3OGlZWTdHZTVzMjRxcHBkNUpqZUlvVE1CeXlISXJhcFZsRE81UlFxaXdtaEVvWktKaHJXaDRFMzBBbm5VRGt2SlpYeXBUdWtjN21oS2pobVVZVTlWSjBuczVyS0o0bjQ2MjY2R0xrZGlLZnRXRWdocGdKdmtRSitCeVRYSTZFcXZ0WDZoZlI3c0hlSmYweW0zL0MxSDlmbGU2aUlDMlpsUEk1NVUwN25PanlSWGFMSWZYNE1Td0FGZFZramVjbXdleUUxd2YvMm9YZG9FKzF0OTJrN2k2L2s1dGd1YU54ZU4rZ25LMXRoVFhocXpyTTlZSGQzSlVrakZlZmc5WUhaY0hFSjVLdURnbURyYUluekpnd2hvQmhtVlFNbE1BMDdxTXhONjdSZUN0V0NQQTVhWUFCeEdMR2F1SC95QWMwaWlJWXRwZHZxMGxsQVlWNVkwS1BxU3g2MzV3TEN3dDciLCJtYWMiOiJlN2M4MmI2NTQzOWUwZWI4MGMwNGM0ZDBhMGE3YjA2YWJjYzBmZWY0MTI0OTZiYzhlMzA2MjU2NDM1NjY3OWEyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:21:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFTSGdMZ2hyenQwQ0ZvUHdDcjNLWlE9PSIsInZhbHVlIjoidFRhTnpqT0Zjd1VyMlY4VEtxZ1VScFRDSThnb2ZJYjBWT1lzL3lwYTh6T3AvaXNZTXRkSTFnY0taK2Z2bWJMV21sVldYRHZtejZVZ0wzU2JURFlSTEthenBJTnpwdERza1VrWlpLVG5kN0N6S0F2YmhqaEM5Wm43NDlOOUFEQ2FqZzhqTXpkZTNpL093aUltcWgzSEVQRThGVFYrdWxXdlhUSWZvczNBTWxNQ2wwZVdLeEF0UkJOUm1mS3pncllUUjR6U0JEMVJaejlrMmZaT0I4UW92ZEZXcElYdkNFc2tiVXJDVGR1RGFLWkpnajZFMEFYQTVHbmJ6akhkcXRxRFN5bGtPaXpGNk41bUlnK2h1WWsvc29IVkdyWGZaUHBraDNuMDR4cWluYmJZbFdTUyt4TlYxT0xyWE02NmRPZytvQUcxQVozdTZIMFBGK3QvcUw5b0pPSDNDL2pCaHRSM1VlZ3ZVK2RoUVpJS3lUYktERGlIeHZkTlIrRkhxOGNjYjA1SWQ3WUtrWGFrNUdldlFTbE9TSTBkM25zZjB5b2ZEbVlDdkN1WUdNaENSTHk1Sk41UjFvMGxaUnJ2SEVBR2lBemFtYzE0REp3OExsVmw3NnhlU1dpTnV2OGxUWFZYUGs0YkJ0Zzc0ZHVhb2NabTJpTXVjdkc5bDFHODlRTjciLCJtYWMiOiI2ZWI1NTRmZWM2YmMzNzAxY2NjMjQ2ZjBmYzAyMWVkOThkMjlmZDdiZmUyNzc4NjZlNzVhZDZkYWNjNDI0MmYyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:21:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJETnA0MDg1UWUxbFBLQS9GVXBBeWc9PSIsInZhbHVlIjoiM2MyWklZTWp4OFJBN2VhcGNlSWlKakVORXQvTXRsNFUzTy80UUp2VGd0cVlscnBLM1B6VVl4RlhvK2cxeEZ0M1JxTWVBSTVnd3NXNVNuaGZOczAwRWg1dnhiRktjTVIxVTlhSTFVZDFHV2l2eDFpMUFYWm5odDdiQ29RcTk5VFl6VThBb0ZkV2dIamRCdkkwb0c1ZXEraGVTOE5kZ245VGo3OGlZWTdHZTVzMjRxcHBkNUpqZUlvVE1CeXlISXJhcFZsRE81UlFxaXdtaEVvWktKaHJXaDRFMzBBbm5VRGt2SlpYeXBUdWtjN21oS2pobVVZVTlWSjBuczVyS0o0bjQ2MjY2R0xrZGlLZnRXRWdocGdKdmtRSitCeVRYSTZFcXZ0WDZoZlI3c0hlSmYweW0zL0MxSDlmbGU2aUlDMlpsUEk1NVUwN25PanlSWGFMSWZYNE1Td0FGZFZramVjbXdleUUxd2YvMm9YZG9FKzF0OTJrN2k2L2s1dGd1YU54ZU4rZ25LMXRoVFhocXpyTTlZSGQzSlVrakZlZmc5WUhaY0hFSjVLdURnbURyYUluekpnd2hvQmhtVlFNbE1BMDdxTXhONjdSZUN0V0NQQTVhWUFCeEdMR2F1SC95QWMwaWlJWXRwZHZxMGxsQVlWNVkwS1BxU3g2MzV3TEN3dDciLCJtYWMiOiJlN2M4MmI2NTQzOWUwZWI4MGMwNGM0ZDBhMGE3YjA2YWJjYzBmZWY0MTI0OTZiYzhlMzA2MjU2NDM1NjY3OWEyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:21:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFTSGdMZ2hyenQwQ0ZvUHdDcjNLWlE9PSIsInZhbHVlIjoidFRhTnpqT0Zjd1VyMlY4VEtxZ1VScFRDSThnb2ZJYjBWT1lzL3lwYTh6T3AvaXNZTXRkSTFnY0taK2Z2bWJMV21sVldYRHZtejZVZ0wzU2JURFlSTEthenBJTnpwdERza1VrWlpLVG5kN0N6S0F2YmhqaEM5Wm43NDlOOUFEQ2FqZzhqTXpkZTNpL093aUltcWgzSEVQRThGVFYrdWxXdlhUSWZvczNBTWxNQ2wwZVdLeEF0UkJOUm1mS3pncllUUjR6U0JEMVJaejlrMmZaT0I4UW92ZEZXcElYdkNFc2tiVXJDVGR1RGFLWkpnajZFMEFYQTVHbmJ6akhkcXRxRFN5bGtPaXpGNk41bUlnK2h1WWsvc29IVkdyWGZaUHBraDNuMDR4cWluYmJZbFdTUyt4TlYxT0xyWE02NmRPZytvQUcxQVozdTZIMFBGK3QvcUw5b0pPSDNDL2pCaHRSM1VlZ3ZVK2RoUVpJS3lUYktERGlIeHZkTlIrRkhxOGNjYjA1SWQ3WUtrWGFrNUdldlFTbE9TSTBkM25zZjB5b2ZEbVlDdkN1WUdNaENSTHk1Sk41UjFvMGxaUnJ2SEVBR2lBemFtYzE0REp3OExsVmw3NnhlU1dpTnV2OGxUWFZYUGs0YkJ0Zzc0ZHVhb2NabTJpTXVjdkc5bDFHODlRTjciLCJtYWMiOiI2ZWI1NTRmZWM2YmMzNzAxY2NjMjQ2ZjBmYzAyMWVkOThkMjlmZDdiZmUyNzc4NjZlNzVhZDZkYWNjNDI0MmYyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:21:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380838703\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>21.98</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}