{"__meta": {"id": "X3ba2fe4fac80e1f375976991fb213338", "datetime": "2025-06-30 18:11:25", "utime": **********.344276, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307084.919331, "end": **********.344294, "duration": 0.42496299743652344, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1751307084.919331, "relative_start": 0, "end": **********.2638, "relative_end": **********.2638, "duration": 0.3444688320159912, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.263807, "relative_start": 0.34447598457336426, "end": **********.344296, "relative_end": 1.9073486328125e-06, "duration": 0.08048892021179199, "duration_str": "80.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48652904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00621, "accumulated_duration_str": "6.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2978358, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.087}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.307395, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.087, "width_percent": 4.992}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.320298, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.079, "width_percent": 8.374}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.322098, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.452, "width_percent": 5.797}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.326132, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 45.25, "width_percent": 5.636}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.330158, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 50.886, "width_percent": 42.995}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.334261, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.881, "width_percent": 6.119}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-851462208 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851462208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.325373, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 6\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 345.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-2083831585 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2083831585\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlovRkJ1eFpZV3JjM2crMTIwaFlvQUE9PSIsInZhbHVlIjoidis3MVBaUC9rSWxoNUsvQ3YwSysrTURHZ1Vob3Zya1ZYVk4wakZ1U20ydGNrUkxoY2JlQ1creER0cWREMnJlQUlNellvTVdmRnpkSllaNWpta0QxWVE2cjFjTWEzeG1CcFAydGxHS0p5R1JCQnpYWTNrSzV1YzFKOFA1aUhFVFJ1aXdxNkNiUDZrbkhDZGpMczU1ZHhGeHFmUFE3UmxzcFFMalVuc2Z0ZTBCbnk1ekdIREV6N05ZZDhkNDUyU2ZhNFdFRTNSSjhLN3pyR1ZCNmZEcWpuYm1oM2NBQUszdEdUWC9XR0pUNWIwRGw1Q0FqMzFWdjk2MWx4L1RHb1k2OG9QS0RSck93Y1VDbG5qRzNlNWR3K0pDcVZVdzk1djhkM2s4YmdUSG9HZ214Rmd2RmtwWG9LWnJYRW1tRDdMNU1KTmd5bDVhbTd5N1JyYzNDOUtRaVMvQUx5V0NIdkdQOGFLcmRDb3l4b3lZOFRZbXRXR0Q5RTdwTzhqdjQ3bi9lRy9vVE9ENXBFNDJGRjhhVWRENC9WVVpGa0kxVDYyTXZUcElPWHUvRlh5Nml6UkVhU2Q2emdDbXZHeVVWYzhqQlRFU1FGd2pvc0JxaEVNK0lVNm1HSHF5Njg2RnN4eFpLVWdKRzhFK0M3cEppcFJQUmppdGxtdmlMaFVxZS9uRjkiLCJtYWMiOiJkNTI0MzE0YzkzYjE5ZGJkZWRlNmU2Y2I4N2NjZTMzMzViMWM0M2VlZTlmMDkzMDAzNzU2OTE1MTY2Y2QwNzVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJiTWp3am8wd016TjBuNzh2VUNDb3c9PSIsInZhbHVlIjoiZ1QveE1jUTlTMEhMU0dsbkhmQmlUeHBwUWxINGY5aExGWnQvTW82KzdlUmx0TUNaSnJESllxc0gwTTFZYU42T2QyUlNmNmdvUmlONkZOZXhnUDBpU0pnaUYrWVpFanIyRGEzaWtiN3B1RXlRaUpqdFYvZmo3SHlaWXRrOWtIbTRsNFVTZXF4V1dMM1Joek9IVCtLdUJ2OHBBeXIzenN2TWpERnFPMEVRU0ZaN3Z0WHBnazJvcEdrQmMxbSs2U3NGcFh3NGFVOEpnTTI0cGFvVkVDbHdqNmNHTzlJN2xEKzZubnBqV3JhalNMRDZaRFE0ZVMwZURBNnB2SnlUbEh3RlVWa2tLVU1uRnVCYnZuMEVVQjhVWDlBT0k4UEZ0QjYrN09QeVNRaGNEaFpYSDFhN05wZHp2STNsMnBnNGx2Q25hWjJsZjl4TCt4bkxOUnFvNTNkN3VqdlpGTXovTURjeng3ekVQdDQ5QjlEaVBlTXdKU0JNTmpnL0VNejNuQzA0OWUzY2hSUTF0TnFsK0p3T3BPcWFYVlpHV01qQnVVcTdJTzRJYWU2aTM1NGs5WWpERS9ETlF1cXpHM09POWVUbXcxSEtmb2VpMy9QYnFHZE1Ia1ozeFF4OGpIZ2FvQ3hRbTBhSGthRyt0MzdZdjNPRlNyWXdBSlVQSU1nYTJwMlkiLCJtYWMiOiIyMWUwNTNjZmRlN2IyMTgyYThhYTQ5ZmQyYTEyODY4YjgwMDE0YzllYWE2M2ZlZjNkNDZjM2IxOWYwYWZlZTAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1102627122 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102627122\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-348854443 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNVZ1crWTlrRG5YbG53bXFJUG9DOUE9PSIsInZhbHVlIjoiZFhaanFJeXplZzVwK1cweEQxandoZFk5YlFFZXV6YlhyTWlKZzEyY3VUU29qalRtR3V4VkNsVGRMVkx4bVZVcTdibU02cHM4dHM1WDQ3NTA5ZDExZm9pMzZCazlKRlVTODlaZHJrM3kyTW5HZGlhUXBQUkdOOU1Hdm40YTJzUUMvTlNtTGR5QW1jTURBZi9sK1VJT2J2eEtwMmRZT2ZIRUpza1RSVmJFeW9YWCtmZVpGY1p1emdEQmVrSXRFRVNoaUZibTlWbGlIL1pjWkNSdkh6WTR2Wk9HaUJQQmdIRlNPRk9WS3p0azNSS2xQWEVPWTJVaWRMQ252MEQ2Qm0wNkdJMldCVjhyTXZtQVNVdEdQazlBSmF6MkxnclFnNkE1cG9RTU9IcUJQTmx3Y2hlZWI5VjVKV2Z4TUdSOUNTdndETURGQUJTQ2lWSTAvS2xOVDYvYlpoR3FxSGJSeVBLTDNOMWRuWkZNRDcwbWJFUEJ1WWRNNVRoQ2l4RzQ0NWFGZDZQa2Q1ZmZmQWc1cVVaemloVUFVTTl3QTBkNFQwd0ZiUW5LVktpdHJGN0ZiQjlBWkNOSHF6WXkvWkoxM1BSRm1YZkNRSEhhenZhcHJxelRxK0dXUk1FV1FjdjQ2cVE5bHU5SjJ4MFUzRUdYVnIzOU43aDYzWlRuam1xaDJ6OVkiLCJtYWMiOiI3MDRlZjVlNTAxNDI1ZDBjMzEwMzNkMGQzOWQ2M2M4ODU5YTkwNjA5MjczNjczZDEwM2VmOGQ1ZTcyZWU4OWUzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZBczVFaCtHU0RKUTRld1I1dmhQWHc9PSIsInZhbHVlIjoiRUFLekluOXhxd2UyZGJrTUlEZVpnUkdINngzR2ZOL0czVmdEOGdHcnFNa3dwVnBkSHcxdDloVCsrMEtsck05aDdwT0VLRFNud3I2dWtTdEdONzd3bWlHbVJ5MnFoU2g0RFQzdjFNZ2g0SmpjbW1lbUtLOUd5a2dJMmdoSHRsOWhobUxPeFgxdkhBWXl3SlUvc2IvaFdnWFFYNnFMZG1Vd0dWdWFuZ05DQjVLdFl5a1pYeVVRa1hlUG5nREZKbUhBa0daOHpib29FZkJPdTZ5NnFrdWpvUWRoY0hTRUNWOHJyZTZxdnFhYXM4bWVwKzlpQXlUb21mT2Q3eWIxMXF5Tm44VHJ3Q1JpdUJ6Wnk5V3g0TVRYamorZW02TFppbGV1OUJiemNVR2R2YXJwQnU0ZVZacjhUeDlLQUJQRmYvdU5tQnZQUnJ4amRSN3l3Z0ZVZ1VuMmMxQkNGeG5ZSzlMRUk0WFNnSzBCK2J0OUxBbWQ3dTBYKytzN0dESjB6RVgvZDBWQlBqbUdQQlFXbm9ZRzRISEtuWkd6Ni93cms1YUNETjdiVGxkdHFWTWt5aGlBRGY1eitiRHhSYi90QXJTL2czcWt3dFNxR0JFanNhdEJQYkZ3MTlTN2svUG9UYmFMWkxzdXkyVEQ3MmZyOGVoa1Ayc3Ftazh2ZW5oRldUQlAiLCJtYWMiOiIwMzQ4OGMzNTMyMjc3N2ZlNmVkMTNmOTE0M2M3NjlhYTUzOWIxYjYxOTQ3YzIzYzgzZDU5MjQxNGIzNjJjZmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNVZ1crWTlrRG5YbG53bXFJUG9DOUE9PSIsInZhbHVlIjoiZFhaanFJeXplZzVwK1cweEQxandoZFk5YlFFZXV6YlhyTWlKZzEyY3VUU29qalRtR3V4VkNsVGRMVkx4bVZVcTdibU02cHM4dHM1WDQ3NTA5ZDExZm9pMzZCazlKRlVTODlaZHJrM3kyTW5HZGlhUXBQUkdOOU1Hdm40YTJzUUMvTlNtTGR5QW1jTURBZi9sK1VJT2J2eEtwMmRZT2ZIRUpza1RSVmJFeW9YWCtmZVpGY1p1emdEQmVrSXRFRVNoaUZibTlWbGlIL1pjWkNSdkh6WTR2Wk9HaUJQQmdIRlNPRk9WS3p0azNSS2xQWEVPWTJVaWRMQ252MEQ2Qm0wNkdJMldCVjhyTXZtQVNVdEdQazlBSmF6MkxnclFnNkE1cG9RTU9IcUJQTmx3Y2hlZWI5VjVKV2Z4TUdSOUNTdndETURGQUJTQ2lWSTAvS2xOVDYvYlpoR3FxSGJSeVBLTDNOMWRuWkZNRDcwbWJFUEJ1WWRNNVRoQ2l4RzQ0NWFGZDZQa2Q1ZmZmQWc1cVVaemloVUFVTTl3QTBkNFQwd0ZiUW5LVktpdHJGN0ZiQjlBWkNOSHF6WXkvWkoxM1BSRm1YZkNRSEhhenZhcHJxelRxK0dXUk1FV1FjdjQ2cVE5bHU5SjJ4MFUzRUdYVnIzOU43aDYzWlRuam1xaDJ6OVkiLCJtYWMiOiI3MDRlZjVlNTAxNDI1ZDBjMzEwMzNkMGQzOWQ2M2M4ODU5YTkwNjA5MjczNjczZDEwM2VmOGQ1ZTcyZWU4OWUzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZBczVFaCtHU0RKUTRld1I1dmhQWHc9PSIsInZhbHVlIjoiRUFLekluOXhxd2UyZGJrTUlEZVpnUkdINngzR2ZOL0czVmdEOGdHcnFNa3dwVnBkSHcxdDloVCsrMEtsck05aDdwT0VLRFNud3I2dWtTdEdONzd3bWlHbVJ5MnFoU2g0RFQzdjFNZ2g0SmpjbW1lbUtLOUd5a2dJMmdoSHRsOWhobUxPeFgxdkhBWXl3SlUvc2IvaFdnWFFYNnFMZG1Vd0dWdWFuZ05DQjVLdFl5a1pYeVVRa1hlUG5nREZKbUhBa0daOHpib29FZkJPdTZ5NnFrdWpvUWRoY0hTRUNWOHJyZTZxdnFhYXM4bWVwKzlpQXlUb21mT2Q3eWIxMXF5Tm44VHJ3Q1JpdUJ6Wnk5V3g0TVRYamorZW02TFppbGV1OUJiemNVR2R2YXJwQnU0ZVZacjhUeDlLQUJQRmYvdU5tQnZQUnJ4amRSN3l3Z0ZVZ1VuMmMxQkNGeG5ZSzlMRUk0WFNnSzBCK2J0OUxBbWQ3dTBYKytzN0dESjB6RVgvZDBWQlBqbUdQQlFXbm9ZRzRISEtuWkd6Ni93cms1YUNETjdiVGxkdHFWTWt5aGlBRGY1eitiRHhSYi90QXJTL2czcWt3dFNxR0JFanNhdEJQYkZ3MTlTN2svUG9UYmFMWkxzdXkyVEQ3MmZyOGVoa1Ayc3Ftazh2ZW5oRldUQlAiLCJtYWMiOiIwMzQ4OGMzNTMyMjc3N2ZlNmVkMTNmOTE0M2M3NjlhYTUzOWIxYjYxOTQ3YzIzYzgzZDU5MjQxNGIzNjJjZmUwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348854443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>345.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}