{"__meta": {"id": "Xf17310eabe4bf46108ad39f8783adb73", "datetime": "2025-06-30 16:06:44", "utime": **********.145131, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299603.686746, "end": **********.145147, "duration": 0.4584012031555176, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1751299603.686746, "relative_start": 0, "end": **********.089475, "relative_end": **********.089475, "duration": 0.4027290344238281, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.089485, "relative_start": 0.4027390480041504, "end": **********.14515, "relative_end": 2.86102294921875e-06, "duration": 0.055665016174316406, "duration_str": "55.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00302, "accumulated_duration_str": "3.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.120485, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.921}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1318529, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.921, "width_percent": 18.874}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.13773, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.795, "width_percent": 19.205}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/58\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-937026533 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-937026533\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1335478731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1335478731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-673543878 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673543878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-722868339 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299595611%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN1K1F0VWdEcnRDVUdBWGhmTHIxSmc9PSIsInZhbHVlIjoiMDY5amVFNlRLUjZvOHNXcHlhaWpiWGNUVGkxdTV3TkJXWngwYUl2cXdWYzE0WExZQk04MlNCbzZQbmJLVHExVjRxTnZKQlVJaWNSeEM0enNBSVdjak9LVEhEVThtK1VoUmRLQ2w0aVhrcXpJL3dwYzZZL1l3aldValRxTWd3NGFsR1VIMEhCc0lsVVZuWEhYZTJxZXJCa3JaMVJBdmNhZmpSM2s2V2dDZVMvMW5wajcyNEFQd0xwOC8yY0plb2pQWDN4NitvTm5pRVRueEZWWW9NN1hQM3haaXduNHk0TVZkUkcxei9JVFZ2NllsSjJITi80SGEyc2tOZGRrZkJUWUltdzkrQ3g1WjBCRmlHemJYYnJKVStKcWNBZDUwRGZvM3J2clkvWFJmVHNvdGxCaG9HbDJXWGJtOWxlK1pqeXEwTXREQlJUb0E1azY1QWNmOEE5L08xaXZUWUIyK1FKdEVYelQ2SEdmV3k3QUpwbDZrMlBTWG1lTDJuRURQS1ByeXl4MFU3bno5V2JFMDdIalMvc3RYVUtBN1ZiY01XVnFtWGZ1SW1seDk2bzJXbldzUVBpRTI5YXgxSFVGRlEzYlQ3UWNCdVB4SnY4WmE3eVk1azNMZVhEUFVaZmNhamVNMS9EVVA0RzFxT3ZXQ25NSUhMbmVlOSt0dDZ4OHBIdWkiLCJtYWMiOiI5YmQ1OTg4NTAwMTlmYThmM2M3Yjk4NjcyMzAwMDdjMTNmOWY2YjY0MjNjYjYwNGFlYjgyMmQ2MjljYmQ2YzEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im90MG9tT2hFMmNocTFDa2ZOWlgzU3c9PSIsInZhbHVlIjoiMExTcUkyWWJoRW53NExlT3pQOEpUWXdha1RabS95MHJiZUg4WUZvQSsxbjN1RXVQYnM1bFQxVEtXSkhqZGhMTmdybFhPRmllN09sMTJReCtXN3kxRUdjYzZDcXNiWFdVS3MvUldsM1dTaGJ3cHE3bnJaN2NzbjQ2czEwZnVZYmdQSzVNVmlzYnZmUDZ2dTFqSy8vREpJVFlTQzhzYlg5SzBSZkNQZUlUcnAwZXA4dEJsQlYwaEhLQ2czZ0M1Yzc4Q0RLVkpNTitvMkN5YytxS0tWdWtlNUV3a3hFNnRhaVo5ZzRFQUNRRXV2THVMWjVqdVB5VGVaSnI1ZzgvdDljb2FjYlBha1Z2TUNnWFp4TUlHQWdCZ285c2RLMnRsSmFqU0tEK21wVWNIQVFYVjR2Tjg5TitkVHByL3orRUFvZEh2YThZVmovcEFsd3NuamVnWTlYRGcxVzVUb09XeTJTdXJNaDhFc053RHAwTXA4UmNidWY3Nk1SbUtsQlQzZ1VWTFoxb1A0SnVGTENZbDdrWkRnYnkxUWpad0s1U29qdmt6aHR5c016dzVSWUJGL1lURVRycHBJcWErR0NmaS9zaEkwSGZqaWlBYW95aFB1ZWdqWGJXYVVHa0YzK2tqVk4xV1YxaWgvOHVQUHZIM0hKUy9icVNLdDAvTi8vdTVnaDAiLCJtYWMiOiIyMGY1M2Y5MWNmMzBlZDY1MDI1MmRhMjc0ZjVmNzJiNWFlMjViYWI3NjkwNWE5MjJkNWU5YWZmMjM4YmMxM2MxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722868339\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889097118 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889097118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1335400997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg2cjhBbVlsdXNqM2pzOHFmM0hQd1E9PSIsInZhbHVlIjoiT1hXcXNpeklieGN1VUwycCt5YWtHeWFPQlBja2pnSjJuWGtIdXRFNW4zK2V5dyszWkdXRlUyS1h0ZTdhLzFlNDk4ZDZkOGl4enV4TkJ6eWl2K2hMMTc4M2syZXhCaGNXUDNOUUZ5b3BjN2d5ZmRRcm9CT1Q1NmlXYjVBUWgzMkVYNW9PNEltV3h6YStocmVCRFdkTWdBNDcwekp3MFJuTnZoWThrZnpJVGhWNGNxRUxvUm5vdzhFaUt6MDZQbTMwaFUxM0Zycy81eU9hajkwajdhamtxMjNLSVR5NDRTL3IyWE1qMTFmSjhTZ1Y5Ny8vMDN2YjMwL2tRTVF3eHFtbC9pVktxMXZYR29yVk9HTjg3dDNpNXFnYXZlcTE5WHFQOHp3UjY2eHBlYWhJZmRZdytrMzk0L2pUZkZNaFkyaVdweDJOSmhxMXJaTUpuNXByd1BxQ3BjMUJHWU9kZW44Yks2dXdMY2U1RjBTMVB0UHAyalB3Z3BpL2RTNWNMS0duYjZ2b2c2SDZpaEh6aU1RNkRrUFhzMnROeE1FMDZRdWlqVVpHREIzSlZhSVZwOFZ6bERqcTlSOGN3WjNTYmFValUwVVNFbWNoQXYzZzBrS0JyczVGWm5icU1meEpyQ2RwVE4rSk9yblZQME90eXNLcXZWL2RNWWRmR2VEM1A1WVkiLCJtYWMiOiIwMTc4MmEwMTQyOWQ1MDdiM2ZmOTBmZWQ2NWY2MWI0ZWE1OWU4ZWU0YzQ2NmEwM2U3YmRhMmI2MDg5MTM3NjQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVYeGRjTlJRaW41MDErWjFnQXRvY3c9PSIsInZhbHVlIjoiVTZCUFBLYWJiRTQzZXNVNDFVdUdQUllyK1hXZFBBK25ubGNnSWt0dXI4WHZRUTFVZDNkS1lvcFBKMk5aeDVuSjdaR1I0QUFBTEdRbzNOWG96WU9BUkY0Q0lDUWhlcTQ5R3l5OElQSnhOd25WU1AwYlEzaDMwRFJyMXVweHdZR0laSDRtRkZUQWhmRTJnYWtqV1l4K3p1cERsOVcwOVJkbTVmZ1NENXNpcUR5aTNKU1FXKzVKUVFoYUpjMTNsM3ozRitDZHlibE4vZU9qeVY4OUpLK3R2eHdqNGdocXNKT2FXUkVwSzhGWGMrNFNYSnVsdUxYVlJOM1VHSlNjWUZmOTU5VnFzM3NjVnFtanRhU1ZpTFhtSmoreHZ3OHRUd0REakpnVFliL3BJNXgyT1pFamhXWU5zQUVUbHJMMDB3SmJSQklIZHdsTit0cWJ3S2Y2VFAvUG9BUDRlM3RZbVZpT3dCRzAxSnE1TzhOejBpOEwwY2o0SnFGWmNJYVU5OHRGV21BdDBGcE1hUVY4NE1SeUlyMXMzOG83S2tvSHZ5RFlHZm9GZFI1N3FZV252cm9ZMHpTdDRPNU96dVkxZVRWakFXVEYydlJsdFBhbmR2aDNqYkRBU3kvemFQaC95QjJhTjV0MWNLRysvNVdvZVJzeUhiSWRidGF0Wm5WWVh0YVYiLCJtYWMiOiJjNWRlY2Q2NTFlNTI0MzY3ZThlODM2OGE5OTU5ODcwMzRiZDViZDg0OGM1MGY5ZDEzNjFjNjFmZTIwYWJhNTNkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg2cjhBbVlsdXNqM2pzOHFmM0hQd1E9PSIsInZhbHVlIjoiT1hXcXNpeklieGN1VUwycCt5YWtHeWFPQlBja2pnSjJuWGtIdXRFNW4zK2V5dyszWkdXRlUyS1h0ZTdhLzFlNDk4ZDZkOGl4enV4TkJ6eWl2K2hMMTc4M2syZXhCaGNXUDNOUUZ5b3BjN2d5ZmRRcm9CT1Q1NmlXYjVBUWgzMkVYNW9PNEltV3h6YStocmVCRFdkTWdBNDcwekp3MFJuTnZoWThrZnpJVGhWNGNxRUxvUm5vdzhFaUt6MDZQbTMwaFUxM0Zycy81eU9hajkwajdhamtxMjNLSVR5NDRTL3IyWE1qMTFmSjhTZ1Y5Ny8vMDN2YjMwL2tRTVF3eHFtbC9pVktxMXZYR29yVk9HTjg3dDNpNXFnYXZlcTE5WHFQOHp3UjY2eHBlYWhJZmRZdytrMzk0L2pUZkZNaFkyaVdweDJOSmhxMXJaTUpuNXByd1BxQ3BjMUJHWU9kZW44Yks2dXdMY2U1RjBTMVB0UHAyalB3Z3BpL2RTNWNMS0duYjZ2b2c2SDZpaEh6aU1RNkRrUFhzMnROeE1FMDZRdWlqVVpHREIzSlZhSVZwOFZ6bERqcTlSOGN3WjNTYmFValUwVVNFbWNoQXYzZzBrS0JyczVGWm5icU1meEpyQ2RwVE4rSk9yblZQME90eXNLcXZWL2RNWWRmR2VEM1A1WVkiLCJtYWMiOiIwMTc4MmEwMTQyOWQ1MDdiM2ZmOTBmZWQ2NWY2MWI0ZWE1OWU4ZWU0YzQ2NmEwM2U3YmRhMmI2MDg5MTM3NjQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVYeGRjTlJRaW41MDErWjFnQXRvY3c9PSIsInZhbHVlIjoiVTZCUFBLYWJiRTQzZXNVNDFVdUdQUllyK1hXZFBBK25ubGNnSWt0dXI4WHZRUTFVZDNkS1lvcFBKMk5aeDVuSjdaR1I0QUFBTEdRbzNOWG96WU9BUkY0Q0lDUWhlcTQ5R3l5OElQSnhOd25WU1AwYlEzaDMwRFJyMXVweHdZR0laSDRtRkZUQWhmRTJnYWtqV1l4K3p1cERsOVcwOVJkbTVmZ1NENXNpcUR5aTNKU1FXKzVKUVFoYUpjMTNsM3ozRitDZHlibE4vZU9qeVY4OUpLK3R2eHdqNGdocXNKT2FXUkVwSzhGWGMrNFNYSnVsdUxYVlJOM1VHSlNjWUZmOTU5VnFzM3NjVnFtanRhU1ZpTFhtSmoreHZ3OHRUd0REakpnVFliL3BJNXgyT1pFamhXWU5zQUVUbHJMMDB3SmJSQklIZHdsTit0cWJ3S2Y2VFAvUG9BUDRlM3RZbVZpT3dCRzAxSnE1TzhOejBpOEwwY2o0SnFGWmNJYVU5OHRGV21BdDBGcE1hUVY4NE1SeUlyMXMzOG83S2tvSHZ5RFlHZm9GZFI1N3FZV252cm9ZMHpTdDRPNU96dVkxZVRWakFXVEYydlJsdFBhbmR2aDNqYkRBU3kvemFQaC95QjJhTjV0MWNLRysvNVdvZVJzeUhiSWRidGF0Wm5WWVh0YVYiLCJtYWMiOiJjNWRlY2Q2NTFlNTI0MzY3ZThlODM2OGE5OTU5ODcwMzRiZDViZDg0OGM1MGY5ZDEzNjFjNjFmZTIwYWJhNTNkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335400997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-208031596 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208031596\", {\"maxDepth\":0})</script>\n"}}