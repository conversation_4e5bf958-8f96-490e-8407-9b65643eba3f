{"__meta": {"id": "X4712b776c4b785fbc5efec4594a5b0d9", "datetime": "2025-06-30 16:22:26", "utime": **********.859396, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.411697, "end": **********.859411, "duration": 0.44771409034729004, "duration_str": "448ms", "measures": [{"label": "Booting", "start": **********.411697, "relative_start": 0, "end": **********.806948, "relative_end": **********.806948, "duration": 0.3952510356903076, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.806957, "relative_start": 0.3952600955963135, "end": **********.859413, "relative_end": 1.9073486328125e-06, "duration": 0.052455902099609375, "duration_str": "52.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027099999999999997, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8339312, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.624}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.843488, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.624, "width_percent": 12.177}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.849516, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 73.801, "width_percent": 26.199}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1709902220 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1709902220\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-343354606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-343354606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1147755952 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147755952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2142034113 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300543798%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktJa1I2UUk4V3J3aTBKYmk1dTNFcFE9PSIsInZhbHVlIjoiU1pGQmltVlVWSktqNi9hSDNGMHFvbW8xVmZPWnlLeStMdFR1U05HS0R0QmNRZlBUY0JmL0loSTAxUVRieGFuQTJlWC9qZWRyak91N1JyZlBuUmFxQlpaSFpib1NmSFB6WjNnZHVLWHZKbVhrc0NRbFp0Mml4MjJUblFka2liZDI4S2tIRmo0WkpiUjJURlUyNGpGTnEzWTlRam1yYzlXK0FJZW5tYVJ0Q3FmajZ0cnFYR3RjWm1ud0xDazhWNm5hR2Z4OGNKZjcrc0FnTEw0VFJpeERUbjkvYUd4UThSOEtHZW1mTTk5R1BGbVhTOGdvTUFJTEQ5VzMyR3JTa3plUGNLYWt1NjU2Q1RFMmgzbDExTW9nK3pJSU92MGZZeUJnNUZucXA3UGo0cXlxWkpmNmJtY0YxVFZwVTJvZlZqVGhCSW9kYWxndDJMZ1VGZXlIRGJYS1FDcUZuS3J3ZkRBeFd4Y0NXdXpRVS8rZXkvL1VlandEN3pQdVo5aUZ3MkZBUFBuTW0zb0EwRjU5TVVaRUlERmJlWHJoUXZZOFc3bjE3enlGYlVxRENFVDVLcjhmbGpwZW56dlkvVW0zVUtydkwvUm5vY3ZFeG1sWUNTbllYcVZYWGU5REdOOEtFeTZkRjdxZE9KQ1R0eGo2NUJHV2YwQXFRTGZtNTVNcnRXWGUiLCJtYWMiOiJlZTJhMjQ4ZTE0Y2ZiOGUxZmE0Zjc1ODI5ZjVkYzQ4ODdkYzc1N2ExMGY4M2ZkYmU2NDc1NmIyM2U4YTdmMDI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBsdFo1N1VJa2ZCaElaNW9ING9hNGc9PSIsInZhbHVlIjoiT1dzd3I0UjBoWlBwd2N1KytXdnRvZVNqdVdjOWhMY3dBMTdSbVBGcmtuU0hqM2FLYzZWQWM4U1hISVFudEJQKzRtd0pta0IxZS9PQ3UzTzNwaDVISytjN0hSUk9EY0Q1aUlWY0kxbzF3eWNDODZFUWlNeHFSek1qZE1Scm5Oa1lEUitGdWk5VDBrTDN2ZGozTVl4U296WGdxbWtveHBKVC83cUtMc2RQc2Jac2NXcGx0dkFnU0FJNnJnS202ZCtSUXpkS1hZQVNySXVnRmx4SXRTWkYvcmhBU2wra2g2MGw3MHBIYUVNY0VHbkxneWJUcHdmQlBkNWFYcmJidWxEaHBuM2IrZGVvTndmZlF2bzVDZ2JNRFNPSXp4c0FGUGJGNUhtQUZ1S0czRU50SWdoM0FuN05vL2lGbk5DVVVENlk2SWNJRkI1U2ZSNHp3RkhCa3AzaHI5V240MEp4KzMrblgzWU9MNmkyNi9WUGdGWEdSYkZEQjhKTjJOYk0rQUVXVUFFQk9lUnMrUTFnV0VLdUdXcHpoNFlUNGVscjFJbm02L1Y3M1EyVytuMmx3bUcxbGlZYnFLV3drMHYxU2lMTjF2ak9qVndDMzZvMGtsbnJxMmpjd21OdFdCak1DdjdZUGNPV2ZOVGtXUmRFaWpUM3Y5d0QzcnhyejR4ZjVBMTYiLCJtYWMiOiJiOWNiZDQ1OGU4ZGQwODQ0MjM2ZGU5NTI3MTVlOWJjNmExOTA4OTAzZGY3MjljYWJjMzdlNjk5N2Q1MTExNThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142034113\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2140081119 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140081119\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikd1YUpuYS9ZV1cwRStYTFVFc0svNHc9PSIsInZhbHVlIjoiemtzYW54MkxrS0RmM1E5Nzd0alJqOStsNHFrcHhVcnFGR2RHckFNMHNsOEg4aytqdnFEQitTcUNma0haQ0xZYTZCRU52NndUU1BJVkErMjhVMjFoRWhEM3pDanhkRE1KZHpHRjJEUDViSUtiSUQ3ck9KWGRoZ0J4VGxQNFJxVnlmY0luVVI4MUxvQXpzdGVNMnhXWkl0elcrSkU2aldvcGJ2Wk5mZm5rb0FRdEtOZi9UcDVEVGFGRW1pYml2UzZaOVNwNWhJdERIb0pjeW84cDVCZXRBbTFuZTZyd1h0UjRjVVk5WE5wR0pSSzlXMVJKenUwelovdk16OGZSZXQ0bjlpUGtHSFUyMXdVdFR5Wkh5a0hpVSs2M2FEdlltNFhablVhSmFIQzlMNXdZZ3NKQXIvc2U5VWFoZDV0TGxMTDRpam45WVI0ejNEaXpmNTg0RFMxbkdNRXNsdEJFQjhEMFdHWkJMaDMzSEhuRGJCdFZPcVpPVDhlSGtlSHFWcEUza1YvQkJlaSttY043ZHpac214ZTZzWWdrS2d0bVBjazR1d0FUNUNlM25kNFpJdHVtY3o0bmQ3L0J1SEE2ejlqM2Rkcld1a2Z3T2xIY0xPZ21JWVZhaDBGSHlFc2prbVVpZm8zbWU5YVN3RnVMUEpWYW5vSzZ0ZlcvTmxlK2pTeGIiLCJtYWMiOiJhZTk5M2NjMjczNTAxMzQ2MmIwYTllZjJmNzM4MWY3ZGE5MWRiYzE0NTA2NDUyZmE3ZWM4MjUzNjRjODNkM2EzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlzMnA5U0l5b3pCYkZITHRXQUFPeEE9PSIsInZhbHVlIjoiSHJMN00wTmpMZXVJaTIwaGJBRGdqN1ZscG1GZFhCdG5tRFVaenJxVTJ3YnNLWGdHbUthdElZM1hpY0tNSytxYkc3VHAxUi8yQTNpQ0dQUTgrcWY3T1l5R1MwcTNMUUphZHFza1pDR0hKUzNDL1F4TUZ6aUVNV0I4dlF3MU0xY1RzZTVpRVFBa2xHNHVTejhWbk5FMGdsZ1c1T2J1a0NFUSs1K0RUT09xTTQ0QzkxbWRRVVprZUdOeDJNZ2ZJVnhYQ0lvNTFyOEdtNWY2OFNOeU5TVlc3eklCZmNxL2NHSytBbjJYNFIxZkMrQkgwdUtVaVhGRjNXNDVkcE5NWFlOcjJHVllteEJqSWNHQ2tWWkhhb0wxdGluNnR6a1dYeU54QUpxNXlwSG9tbVNUOThQZEE4V0MrWG1FLy9aVkVVOE42bWoxRSthZWJNNkVLVFBSeHhrUU45Z1kxZXA5akZTaEZWUHR0QnpyTS9BWDc1MGluRmNYMlROZUVkL054ZFdQczcrblBuVDVIM1h4OTd6UGwvVnBodVBPNWNLeFUvTkpMY0pZS2FxV0RwbHVJQllwZXZOR0tGbjJlQi9RcU4wNTdxQkZGaVRPbEFLY0JvRG9UUUZRbWdxRzQ3Q3JxWHRIK0x3R0pIWEhlVjJTVWN5YU1oVzVLTHdab29RMVFQdFciLCJtYWMiOiJlZDlhZDZkMjM1NDQ1NjMwNmM4YmM2YTcxMWQ4ZmU5ODBhNjQ5ZTFmYzRjNGE3YWE4M2E3YzVjODY3YmEwZTNlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikd1YUpuYS9ZV1cwRStYTFVFc0svNHc9PSIsInZhbHVlIjoiemtzYW54MkxrS0RmM1E5Nzd0alJqOStsNHFrcHhVcnFGR2RHckFNMHNsOEg4aytqdnFEQitTcUNma0haQ0xZYTZCRU52NndUU1BJVkErMjhVMjFoRWhEM3pDanhkRE1KZHpHRjJEUDViSUtiSUQ3ck9KWGRoZ0J4VGxQNFJxVnlmY0luVVI4MUxvQXpzdGVNMnhXWkl0elcrSkU2aldvcGJ2Wk5mZm5rb0FRdEtOZi9UcDVEVGFGRW1pYml2UzZaOVNwNWhJdERIb0pjeW84cDVCZXRBbTFuZTZyd1h0UjRjVVk5WE5wR0pSSzlXMVJKenUwelovdk16OGZSZXQ0bjlpUGtHSFUyMXdVdFR5Wkh5a0hpVSs2M2FEdlltNFhablVhSmFIQzlMNXdZZ3NKQXIvc2U5VWFoZDV0TGxMTDRpam45WVI0ejNEaXpmNTg0RFMxbkdNRXNsdEJFQjhEMFdHWkJMaDMzSEhuRGJCdFZPcVpPVDhlSGtlSHFWcEUza1YvQkJlaSttY043ZHpac214ZTZzWWdrS2d0bVBjazR1d0FUNUNlM25kNFpJdHVtY3o0bmQ3L0J1SEE2ejlqM2Rkcld1a2Z3T2xIY0xPZ21JWVZhaDBGSHlFc2prbVVpZm8zbWU5YVN3RnVMUEpWYW5vSzZ0ZlcvTmxlK2pTeGIiLCJtYWMiOiJhZTk5M2NjMjczNTAxMzQ2MmIwYTllZjJmNzM4MWY3ZGE5MWRiYzE0NTA2NDUyZmE3ZWM4MjUzNjRjODNkM2EzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlzMnA5U0l5b3pCYkZITHRXQUFPeEE9PSIsInZhbHVlIjoiSHJMN00wTmpMZXVJaTIwaGJBRGdqN1ZscG1GZFhCdG5tRFVaenJxVTJ3YnNLWGdHbUthdElZM1hpY0tNSytxYkc3VHAxUi8yQTNpQ0dQUTgrcWY3T1l5R1MwcTNMUUphZHFza1pDR0hKUzNDL1F4TUZ6aUVNV0I4dlF3MU0xY1RzZTVpRVFBa2xHNHVTejhWbk5FMGdsZ1c1T2J1a0NFUSs1K0RUT09xTTQ0QzkxbWRRVVprZUdOeDJNZ2ZJVnhYQ0lvNTFyOEdtNWY2OFNOeU5TVlc3eklCZmNxL2NHSytBbjJYNFIxZkMrQkgwdUtVaVhGRjNXNDVkcE5NWFlOcjJHVllteEJqSWNHQ2tWWkhhb0wxdGluNnR6a1dYeU54QUpxNXlwSG9tbVNUOThQZEE4V0MrWG1FLy9aVkVVOE42bWoxRSthZWJNNkVLVFBSeHhrUU45Z1kxZXA5akZTaEZWUHR0QnpyTS9BWDc1MGluRmNYMlROZUVkL054ZFdQczcrblBuVDVIM1h4OTd6UGwvVnBodVBPNWNLeFUvTkpMY0pZS2FxV0RwbHVJQllwZXZOR0tGbjJlQi9RcU4wNTdxQkZGaVRPbEFLY0JvRG9UUUZRbWdxRzQ3Q3JxWHRIK0x3R0pIWEhlVjJTVWN5YU1oVzVLTHdab29RMVFQdFciLCJtYWMiOiJlZDlhZDZkMjM1NDQ1NjMwNmM4YmM2YTcxMWQ4ZmU5ODBhNjQ5ZTFmYzRjNGE3YWE4M2E3YzVjODY3YmEwZTNlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}