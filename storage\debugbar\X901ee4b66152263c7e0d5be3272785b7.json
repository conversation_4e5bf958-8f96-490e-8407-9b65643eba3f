{"__meta": {"id": "X901ee4b66152263c7e0d5be3272785b7", "datetime": "2025-06-30 18:09:00", "utime": **********.675971, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.251582, "end": **********.675984, "duration": 0.42440199851989746, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.251582, "relative_start": 0, "end": **********.609977, "relative_end": **********.609977, "duration": 0.3583950996398926, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.60999, "relative_start": 0.35840797424316406, "end": **********.675986, "relative_end": 2.1457672119140625e-06, "duration": 0.06599617004394531, "duration_str": "66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47518712, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1570\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1570-1633</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.64191, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.089}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.651478, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.089, "width_percent": 15.564}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.66412, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 76.654, "width_percent": 13.23}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.665761, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.883, "width_percent": 10.117}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-532969427 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532969427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669458, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => \"2\"\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1788751052 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1788751052\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1725585363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725585363\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1353047585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353047585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1018024319 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZid1plTVdVaDZpbTJLejdUaldRNWc9PSIsInZhbHVlIjoic2J3a2lxN3ZzbXZUYUNUOGJvK2JJRlIzVjdUemIvZndXQWxwL0hOMXU5THlSQkkxV2Rpd2I5ZmphRUE3cWdKY0NQQkdxZnlCaUp1eWU4L0hvRnpaTGlrV1BJLzJ3OUtORS9tNUMrVXUwczRjTFlvaXVMZmFPNDN0UWZReVVuU0xtNTB4dHp5Y2NRU0xndmIyU0ZYTHBCSHRVU1gvYUFST3FpdDVMVmFibzZVbTlNVkVhejYrOEZMcTdmcHZ1aTg4UVVwUk5vV3Y4SXN3SWdiTUQvRWEwZi94bjZia1F2b2FIV2svVHhBYzVIa3FUa3M0a1BXNE9vSVo1R0xrT21MNzVIKzNUK3lDbHVDWXN1TDJ6ZUVZT1VUeGhYci90MFhwWHdMTkxHVDQ3Qm1ZSHgyTGkzK0F3dmxVUVBVQUFQT3B2eFRNamF6d3o2RCtWbGdtcjZCa0tiVjc0eFBwQTFzZ080a3dtbXArOCtHa0ZBQkx1YXNYT3A3b3FVZU9IbjEyVFd1aW1uNEpqNVVzNkVDYXZoR2Z6U0hRQWd3a2p4Y3ZWU3BMNU1DV3pXYit6U1VKS3ROK2VnbmJySjlFMzk4SkdVeGZ6RklyNGE4MERyVXYyRjNPT2oyRG5weUh5enVVTC8xV3RtUS9uMlR5T2hVSDVMcDgxVDE2OXhDb1MxTHAiLCJtYWMiOiIyMDVkODdiNTdlNTViMzhkYTY0M2VkZDRiNTBmMjUzMzJhZTA1OWY1M2JkNDMwMWQ0MjFmYzAwODQxMjE0YTQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkMxYTY5OFlyOWNqTVhoUi9kQWZNYXc9PSIsInZhbHVlIjoiOHlOSVJERDN6SDlpMzhCTUVCZHhNeGZBZGRXT0xnTTRUSVpvT2lXTlk4dmUxTWRBL1VrQjVaS2dCREZ6d2YzdGdLRmZueVlaL25UWVZwcnh4bmc2NnRIMEhFaGl2dVkvK0x3YjdydTQyNjhFYkZLQVYvalRrYzZpR21LRkV4SElyMk1zS0lLb0F1d3dhdG5zTUd2TnhjcHRqVytwVVVhWEh2R2FmR1p4c0lBRkRGVTdOMUxiNXZ0cExuTTRuNW5aMHRFbmJ6SkRLL3owaTh1ckwyZ1RvWTUrNzZnRjFPUFBLaVYwb3QxUm5PK0NnUDJXaUdobU5PaDNxMGgyNmpQazd0djZpQzAzSUF1aU15V1RmK1VJc3FhOGYreU9kQUlvR1dIcG5tVk9FUVhQenBCdXorU2o4djZCRFR3VTJWQURhZDNGcW9UbDI5TFZxaTVYNmJ1WHZmWjhRODlaam1mdGZBRzdTYXJVL1RzMTFleW41a0dkQ3RueUtwdmpLWk0yWUllTTg4UkpDSU5XUDFNd3FNOVpPVU9IUTVUbi9wTnBBVy9uMDBBUTMrVHN5cUtuV2tNNm1JMjBvSlpSUWFUcGNXSk9sTUdCZlRUR1RnY1JWZEVROWJnZkg1Y0piK2ZsN09QMUh3ZjZBOVZFUkNXWGhkSVREalFkMFRwZnV0Y04iLCJtYWMiOiI0YWQ0NThjNDVhZDQwZGZhN2YzZjRlM2I2Yzc1YWIxZmZjM2MxMWMxYTNhMWFkMzExODU2MjU3ZGRkZWJmMzBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018024319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1297464410 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297464410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1186808351 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdyalV3S2pPTDNqYW9qYis1aE1SRlE9PSIsInZhbHVlIjoiOEpqNVQrckhBL1hVSjJLbkNjZTAzODFrQnljU1k3dzRMblBmeldvMy9ySUE5NitUa3Y4bkd4U0VxRlAvam90RHhtazM4d3hQcXlIWm15bzBKdTc5RjBacGFMcjEwcDVyQ2VwTnhxdEpwKzI4ZWhNNmRLeGRoTFhXdVlPSHNtM3ZtSFQxbmhTcWkrQ1FtRWplaDZ5aVpjTVNSTFNpbDd4ZGcwMDB4eGpHTHNMc29pM2w2di9VNm40ZDJWYkhtMStJTjlKWjhsYi91b2I5ZE1YYXU0ZVJDNU5JRzIvRURWbHgrR0hHd29YSUQ1dWVxV2dCeU9za1VTcDhqakxJdU1rMGpjaWtUdnl1UjJMbFUxRGQyZ0Y3c0d5TXN1ZTFTNjZDa2t4MWh2ZUpyMTdxOHlyQm1HQzNUQ1pzZ0QzNDk0MExUV0NSbjdIc2hpbzRSWXVxNW5BaUJDOGlFOHFLVzFVRUtraGVrUTBjc1lzZmNpeWZHaUNIb2l3ektzZDhXYW1CY3JhNlV0MXZONnFheDB3TGI1YjJ3RGUxRDNTM0taYlhDRmNhYWw0TlVSNkd3eDREZ01zZFNNclJ3WmVBY3FjUnRrU1VLN0VFbVpETmYzQWNHL2JWL2tLRUxyNjB6NVBzWEt2UWoyM3l6VzNWVUQ1S3dZKzZXeGlTbE1WSzlJdS8iLCJtYWMiOiI2MGU5M2Q4Yzc1MjI2ZTU5NDFmM2I0MWI5OWNlZjIxMGJlNDU2NzcxN2QyNDU1ZTc4NTEzMmNkZDJhMTMyNzdjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik14M3RQcHFyeDJsMWJ5d1RpMWtHTFE9PSIsInZhbHVlIjoiNVd0VDcybnZyQndtOUlwQ091K3lIWmJ6MGlhMERERFRjWElOWFF3enRISzYrUjRWZER4NmpPaDJMNnlFVXh0alVNa3VjUTQxenJyd3JiV254ekZOaWlQM3htb1NqaHZiNkJBVVpRV0VMei8vSFF2YXhNemNFcnM1eXFkd2RJbkp3U2hIVTBSZDhhOWFEb3FSTzREVVI3L2RLWUxycU81V3Fjcm0wS0tUdVFLRlI5dElIeVZYMkhsTDBrZHJHZkp4SmMvaFVmSlU5dDBDZ1VSc2tKc3dyTmE3UkpQWkc3NmFsek1KeWZmUFZxb1BRYnBGQ3l0WTc4QTVrWXVoR3F2SnFDN204cnN1Z1lXRnpTUzloWTVMV0YxNVIvMEY1TjlJc1N1RjV3UzJTeHdmY3lMV3ZoMDA4VEJodHdDQUxCVFRpdXR5VVAyT2lheXUyUUZIaWNpblg3eHZrQkNZS0dSUStZb2ovN2todVdhbnFVWkwzYjZsVzFTanBJZWNCY0lTQVpaaDBEMTdUcC9RRThta2JXRWpQUm41SGpBQ0xBbnBFUENTYU1RVk5kOVhiT1I3NGZDbVdwWFdyQkI1dkIvM2tPS1daVGl0bFFNc0ViTmFCbWZyaTVGQzJsNTFFVGJJWHE1S3l0d0cxY1NQc3hMdkxqdDllZ0ZZbU01bnBnMHMiLCJtYWMiOiI2YTVhZGUxMTZhOGY3MTExYzMzNWY4NzE3YWQ0N2ExZmMwYmFiM2M4Mzk5NzYxOWE4MjYyZDc2Y2U5ZjJmMzU4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdyalV3S2pPTDNqYW9qYis1aE1SRlE9PSIsInZhbHVlIjoiOEpqNVQrckhBL1hVSjJLbkNjZTAzODFrQnljU1k3dzRMblBmeldvMy9ySUE5NitUa3Y4bkd4U0VxRlAvam90RHhtazM4d3hQcXlIWm15bzBKdTc5RjBacGFMcjEwcDVyQ2VwTnhxdEpwKzI4ZWhNNmRLeGRoTFhXdVlPSHNtM3ZtSFQxbmhTcWkrQ1FtRWplaDZ5aVpjTVNSTFNpbDd4ZGcwMDB4eGpHTHNMc29pM2w2di9VNm40ZDJWYkhtMStJTjlKWjhsYi91b2I5ZE1YYXU0ZVJDNU5JRzIvRURWbHgrR0hHd29YSUQ1dWVxV2dCeU9za1VTcDhqakxJdU1rMGpjaWtUdnl1UjJMbFUxRGQyZ0Y3c0d5TXN1ZTFTNjZDa2t4MWh2ZUpyMTdxOHlyQm1HQzNUQ1pzZ0QzNDk0MExUV0NSbjdIc2hpbzRSWXVxNW5BaUJDOGlFOHFLVzFVRUtraGVrUTBjc1lzZmNpeWZHaUNIb2l3ektzZDhXYW1CY3JhNlV0MXZONnFheDB3TGI1YjJ3RGUxRDNTM0taYlhDRmNhYWw0TlVSNkd3eDREZ01zZFNNclJ3WmVBY3FjUnRrU1VLN0VFbVpETmYzQWNHL2JWL2tLRUxyNjB6NVBzWEt2UWoyM3l6VzNWVUQ1S3dZKzZXeGlTbE1WSzlJdS8iLCJtYWMiOiI2MGU5M2Q4Yzc1MjI2ZTU5NDFmM2I0MWI5OWNlZjIxMGJlNDU2NzcxN2QyNDU1ZTc4NTEzMmNkZDJhMTMyNzdjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik14M3RQcHFyeDJsMWJ5d1RpMWtHTFE9PSIsInZhbHVlIjoiNVd0VDcybnZyQndtOUlwQ091K3lIWmJ6MGlhMERERFRjWElOWFF3enRISzYrUjRWZER4NmpPaDJMNnlFVXh0alVNa3VjUTQxenJyd3JiV254ekZOaWlQM3htb1NqaHZiNkJBVVpRV0VMei8vSFF2YXhNemNFcnM1eXFkd2RJbkp3U2hIVTBSZDhhOWFEb3FSTzREVVI3L2RLWUxycU81V3Fjcm0wS0tUdVFLRlI5dElIeVZYMkhsTDBrZHJHZkp4SmMvaFVmSlU5dDBDZ1VSc2tKc3dyTmE3UkpQWkc3NmFsek1KeWZmUFZxb1BRYnBGQ3l0WTc4QTVrWXVoR3F2SnFDN204cnN1Z1lXRnpTUzloWTVMV0YxNVIvMEY1TjlJc1N1RjV3UzJTeHdmY3lMV3ZoMDA4VEJodHdDQUxCVFRpdXR5VVAyT2lheXUyUUZIaWNpblg3eHZrQkNZS0dSUStZb2ovN2todVdhbnFVWkwzYjZsVzFTanBJZWNCY0lTQVpaaDBEMTdUcC9RRThta2JXRWpQUm41SGpBQ0xBbnBFUENTYU1RVk5kOVhiT1I3NGZDbVdwWFdyQkI1dkIvM2tPS1daVGl0bFFNc0ViTmFCbWZyaTVGQzJsNTFFVGJJWHE1S3l0d0cxY1NQc3hMdkxqdDllZ0ZZbU01bnBnMHMiLCJtYWMiOiI2YTVhZGUxMTZhOGY3MTExYzMzNWY4NzE3YWQ0N2ExZmMwYmFiM2M4Mzk5NzYxOWE4MjYyZDc2Y2U5ZjJmMzU4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186808351\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1149527782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149527782\", {\"maxDepth\":0})</script>\n"}}