{"__meta": {"id": "X9e5dcb7bfa5061f93c9766f8bcc40774", "datetime": "2025-06-30 18:58:02", "utime": **********.532226, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.062696, "end": **********.532243, "duration": 0.4695470333099365, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.062696, "relative_start": 0, "end": **********.44179, "relative_end": **********.44179, "duration": 0.37909412384033203, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.441799, "relative_start": 0.3791029453277588, "end": **********.532245, "relative_end": 1.9073486328125e-06, "duration": 0.09044599533081055, "duration_str": "90.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.021879999999999997, "accumulated_duration_str": "21.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.474686, "duration": 0.01623, "duration_str": "16.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.177}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.499032, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.177, "width_percent": 2.057}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.51249, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 76.234, "width_percent": 2.514}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.514341, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.748, "width_percent": 2.011}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.518614, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 80.759, "width_percent": 12.614}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.523846, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 93.373, "width_percent": 6.627}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-500966316 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500966316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.517689, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1543675029 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1543675029\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-476749601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-476749601\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-678854564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-678854564\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-666039903 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6IkZGSlkxSnlvazlOS20vSy82TWRreHc9PSIsInZhbHVlIjoiektwZDNmT1JTRTl0Z3A5R2ZZeEF5ZmFyTHZrZjhDbXFvUlRMeEtvUHZOWkJxNEFHaDZMK2hvY3puZ1hNMUJ4S0hzaVZXOEZCQXRsU083cFBiNVQ0V2dLM0FURE5acU96Mlh5N2xBUCs3Qy9VbG9HZVMrVTFZSVd5UDhMYWJ6SXo5aU5ML1htZDFpYUkrU3Qxb0MzMGxncGhoTDg5bHlFSUpmNFBqSXpZbkpCYWxBdHJzdDJncnA3cWJqRFZxektJUkM2NG5mYkNSV29xbjByZGRpdGhlUGF3R1h4dDAva3RjalA5L2lmKzVUZ29mYlVqNzdDYndQaW5HTkFsWU1YbGhRZWxvS0ljZUVoNHdxU0prSlhuelZlUGhSR0RHMk5jUnBreVFVRkR1a3pyNXd4SWxtUzdGUXFrRG5iQmVPRFBDa2FIRUdLRWM0a1VGcExFM1dMcVVFUGJhVG9hd2djcDdmT0M3eUJvUW5Ham43L1BNM01TMlI5ajhlaStJYVZySHRIbXdMdFR3VFpEdng1TVZSM2ZOT3lPc2hjeDA2dHVUUmZXcVpPRFN2TWNoZE1GaWp6UWRWQjB0ZFU2UXhqWnorWFdUNG5UL1FVbk5CV2Nuemt3RTFnZVNFT0ZGT0pFelRNV1RDVVJUK2xNaEhCMGc5Vm1LY2g1OEdTOFlZbmYiLCJtYWMiOiI0NDE4ODIwNDAzZGE3NDdmZDMyN2ViMTc3NGU2ODVmNmM1OTRkZmY2MDJiM2U3MjdiZTAwMGI2ODQ4NWMwMzZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVYUHZTOGpYdUthbmlTcWdIai95VVE9PSIsInZhbHVlIjoiUHhIbzNITVQxZG1oN2pFczlCeDA4SHFPNnJjTktmVHdqbFJlTSsvT3Y3ZW5sdURsNGJDZnl1RHh4NEJMVHZzYVBYcEZ6SHhBTjFkRVdWcW5wMENGMUFNVWd4MGRIK3pZbUloc1YvSjlsa2pYd013UXpGQk0zZUxTcGliczdzbThjeFFSaW9pV2M1dzVNLzVkOUVOZlhkMmVqNng2NklDWDJYYnZ0V3E0YXBVbzNTb3AvTTdxNCtHazVmcXVUUHVSSVQ5RkQ1bUVoMWNvWDhCWHVaL3BNbWMyaXpuckZ1R2E1M0FDck84MzgwRlJWdm5TNFpyUTlCa1RmOHZwQnJwK1RtUjlremdKR2tMazlnOEJ3TkJYbEM5cDBVdkswampLcnFzTUlmTUp3byt6RW9wQ2NIc1BIY3QraFMzSkJiMndXYW9mUm1pMzk5K0pPN25Cc28xblJmKytiUnBhVDFZOXB4c1JWaVNDUW44MHRVbUVCTHQzRk5hay8wb1l4L24vVWtXc1UzR3lkbGtDLzc3SER3WlhWRFl4azA5dkRqZy9lOXVSbUlwTHJvTFA5ZkZyQ0FKV3MyVkxkNGx5YUs4TmlQQXBTWDUzeUtzKzlZTTkxZUc2V0g5a0VqcnFNSEF0SXkzU3JHUlBVcjhBSVNlWHlqdVZGU1BUN1NCTkZJcFciLCJtYWMiOiJkYmJhODcwMWY2YmJmNGQyODcxYzBhYjg1YjA5ZjM5YjgyMWIxMWRhNmU5ZWJjMzZhOGQwZWYwZTFlY2E1YTYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666039903\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-313436874 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313436874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-388672307 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:58:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldacGxucmloSXRtbW1CUDRoVU5aOXc9PSIsInZhbHVlIjoiakJ3UnlqL2Ezc0VwNGVSV0lqby9neVp6ODhYeXpFejZ2SlJRWC9OcE9qa3pIUnlEaTE4VnBidVQ0bkZvVDArWXNNS0JESDJ1U0xWaHNna05RMmhDTEgzYVc2RkowNDVVRDNhcUprbWlKQk51eGVmZXl3K3ZpTE5EdXgrR1VkL0JZQW96d1JGT1A3NlBkbVJnMDB2MXd4NDVlU2t3MkFndzJlcFdUQXNRc2puZHUxTXBqeEhkczl6V04rcm9lZzY3cExFUlZ5cGVuZi92TWoya2dlT2xxdUh4aEZxSGxPYnM2YW5qRVF0Y1JWR3hyT1hxWjBkRkp3U1pwVTFvOHA4SXBGd0M3eVhCUHRGamovMlVmSXVnMWYwU2wyNVZ5bUpPL2NEakNCeW9Gc25ZSDlOamRDbnlvNnV1TjlZbXRIWW1YcUdsTXljeVZkZ2hiaHYyTWxReUEzV0tPOEd3MDFzcWlhQ2IyZUs2VFQxT3RJVTNhVTc5SnAwN1dkazFUQklqeC9mbnF4cXFYNUdlWldsd1B5QmJwem9yMFpwVXVYclltbWxNbWNDVGd1NmMxUXdaM1RPS3BFT05sd1JVclUrYXpWcWhIMlZKR3FSTDFZVnhLVzZEbmFWWnJNSDBjbkdkZnl4Yk1DbklweGg1ZklWVGN2TUNVZVJNTEV0RElDMVciLCJtYWMiOiI4MWE3OWI5ZTNhMzQ5NGYzY2RmNWU2MGY3OTczOTE4OWZlYWJiMGEyYmJlMGY4NWU1YzFkMWM2MmVhYzE0OGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:58:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZ1dU8yZWVBanNJcXo2U0xNNFRpM2c9PSIsInZhbHVlIjoiWVpSSldCbnNwWHZqUzI1M3h2NEtGL1ltVFlUSXF1cVJ2Qi8yVnh4dlZDa2I4QjdsZlBQM05xTTZxNzBpdmlIanBsL3NwYWJZNHZGVDYyU1FxWnVqS21pcGJDcC85VG95Ukl1WERjd1RPZUZXaFNUUUpzUmpLajZNU3o2bGFFdWpwbHpZQlZqekJjaUpKZ1BzaDFwMjR0dDUrZng3L3lleWY4SEplSmxiSXp1YlgxUXN0OFhiTDNYaU5ianVqQmRMNWZyTnF6OFQyb1E2a21zMUE3RC9lTlpDclJqYmhBd3B3QloybGxkN0w4aE5OeU9XeUNmdWJXTEliSlJobk5oVWtTb00wd0NaOEh4Y2lxZVQvaGFtMy8vRDdUL0wzSTVRdFg2ejdBbGJsVytQcEpDb2RyRjBIbDg3dHVQMXZ5bGFDZEJ0YldRdzhuZnF3VDUxam9SUUw3dnV5cXRKY1g4S2liNld4SDE2QkhmY0UvZ2J4UnhmKzJPaDhtclNFTXBIQ1Z0dm1WZnN6dVFacjBBRk1xSG5xZCtZakdyV1VuU09mRTZ1RDNqckNnVXpaUXQ5bXRJMlZGY2lNVWtJTmxSM05WeTFBMlNsZzkvdUZmTWMraURIVGw5ZEtyU1JEaitpUm9FWDFEd1BIMCtGQmgybys2Z1ErWjlkQkdWZmswUjQiLCJtYWMiOiJkMGViYzdkMmYwODIzOWIxZGVmNzNkYjQ4YjhmMWY1OTViODNmNzM0OTRiY2I1MWVjYjY2ZGY2NGViNzk5M2VmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:58:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldacGxucmloSXRtbW1CUDRoVU5aOXc9PSIsInZhbHVlIjoiakJ3UnlqL2Ezc0VwNGVSV0lqby9neVp6ODhYeXpFejZ2SlJRWC9OcE9qa3pIUnlEaTE4VnBidVQ0bkZvVDArWXNNS0JESDJ1U0xWaHNna05RMmhDTEgzYVc2RkowNDVVRDNhcUprbWlKQk51eGVmZXl3K3ZpTE5EdXgrR1VkL0JZQW96d1JGT1A3NlBkbVJnMDB2MXd4NDVlU2t3MkFndzJlcFdUQXNRc2puZHUxTXBqeEhkczl6V04rcm9lZzY3cExFUlZ5cGVuZi92TWoya2dlT2xxdUh4aEZxSGxPYnM2YW5qRVF0Y1JWR3hyT1hxWjBkRkp3U1pwVTFvOHA4SXBGd0M3eVhCUHRGamovMlVmSXVnMWYwU2wyNVZ5bUpPL2NEakNCeW9Gc25ZSDlOamRDbnlvNnV1TjlZbXRIWW1YcUdsTXljeVZkZ2hiaHYyTWxReUEzV0tPOEd3MDFzcWlhQ2IyZUs2VFQxT3RJVTNhVTc5SnAwN1dkazFUQklqeC9mbnF4cXFYNUdlWldsd1B5QmJwem9yMFpwVXVYclltbWxNbWNDVGd1NmMxUXdaM1RPS3BFT05sd1JVclUrYXpWcWhIMlZKR3FSTDFZVnhLVzZEbmFWWnJNSDBjbkdkZnl4Yk1DbklweGg1ZklWVGN2TUNVZVJNTEV0RElDMVciLCJtYWMiOiI4MWE3OWI5ZTNhMzQ5NGYzY2RmNWU2MGY3OTczOTE4OWZlYWJiMGEyYmJlMGY4NWU1YzFkMWM2MmVhYzE0OGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:58:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZ1dU8yZWVBanNJcXo2U0xNNFRpM2c9PSIsInZhbHVlIjoiWVpSSldCbnNwWHZqUzI1M3h2NEtGL1ltVFlUSXF1cVJ2Qi8yVnh4dlZDa2I4QjdsZlBQM05xTTZxNzBpdmlIanBsL3NwYWJZNHZGVDYyU1FxWnVqS21pcGJDcC85VG95Ukl1WERjd1RPZUZXaFNUUUpzUmpLajZNU3o2bGFFdWpwbHpZQlZqekJjaUpKZ1BzaDFwMjR0dDUrZng3L3lleWY4SEplSmxiSXp1YlgxUXN0OFhiTDNYaU5ianVqQmRMNWZyTnF6OFQyb1E2a21zMUE3RC9lTlpDclJqYmhBd3B3QloybGxkN0w4aE5OeU9XeUNmdWJXTEliSlJobk5oVWtTb00wd0NaOEh4Y2lxZVQvaGFtMy8vRDdUL0wzSTVRdFg2ejdBbGJsVytQcEpDb2RyRjBIbDg3dHVQMXZ5bGFDZEJ0YldRdzhuZnF3VDUxam9SUUw3dnV5cXRKY1g4S2liNld4SDE2QkhmY0UvZ2J4UnhmKzJPaDhtclNFTXBIQ1Z0dm1WZnN6dVFacjBBRk1xSG5xZCtZakdyV1VuU09mRTZ1RDNqckNnVXpaUXQ5bXRJMlZGY2lNVWtJTmxSM05WeTFBMlNsZzkvdUZmTWMraURIVGw5ZEtyU1JEaitpUm9FWDFEd1BIMCtGQmgybys2Z1ErWjlkQkdWZmswUjQiLCJtYWMiOiJkMGViYzdkMmYwODIzOWIxZGVmNzNkYjQ4YjhmMWY1OTViODNmNzM0OTRiY2I1MWVjYjY2ZGY2NGViNzk5M2VmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:58:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388672307\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-459142449 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459142449\", {\"maxDepth\":0})</script>\n"}}