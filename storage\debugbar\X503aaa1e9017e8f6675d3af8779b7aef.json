{"__meta": {"id": "X503aaa1e9017e8f6675d3af8779b7aef", "datetime": "2025-06-30 18:09:06", "utime": **********.884895, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.437089, "end": **********.884912, "duration": 0.44782304763793945, "duration_str": "448ms", "measures": [{"label": "Booting", "start": **********.437089, "relative_start": 0, "end": **********.769646, "relative_end": **********.769646, "duration": 0.33255696296691895, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.769658, "relative_start": 0.3325691223144531, "end": **********.884915, "relative_end": 3.0994415283203125e-06, "duration": 0.11525702476501465, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53424712, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.880837, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1416\" onclick=\"\">app/Http/Controllers/PosController.php:1416-1524</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.021370000000000004, "accumulated_duration_str": "21.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.804367, "duration": 0.01863, "duration_str": "18.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.178}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.83086, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.178, "width_percent": 1.638}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.845371, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 88.816, "width_percent": 2.012}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8471549, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.828, "width_percent": 1.217}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1427}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.851594, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1427", "source": "app/Http/Controllers/PosController.php:1427", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1427", "ajax": false, "filename": "PosController.php", "line": "1427"}, "connection": "kdmkjkqknb", "start_percent": 92.045, "width_percent": 2.012}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1428}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.853934, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1428", "source": "app/Http/Controllers/PosController.php:1428", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1428", "ajax": false, "filename": "PosController.php", "line": "1428"}, "connection": "kdmkjkqknb", "start_percent": 94.057, "width_percent": 1.404}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 523}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1432}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8574212, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:523", "source": "app/Http/Controllers/PosController.php:523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=523", "ajax": false, "filename": "PosController.php", "line": "523"}, "connection": "kdmkjkqknb", "start_percent": 95.461, "width_percent": 2.199}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1511}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.872261, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1511", "source": "app/Http/Controllers/PosController.php:1511", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1511", "ajax": false, "filename": "PosController.php", "line": "1511"}, "connection": "kdmkjkqknb", "start_percent": 97.66, "width_percent": 2.34}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1860245031 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860245031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.85031, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2031431756 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031431756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856745, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => \"2\"\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1902565382 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1902565382\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-819206743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-819206743\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-754237861 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRUbW1OOE8vcEhtZU1LK2owZTM3aFE9PSIsInZhbHVlIjoiWkhzL2tvUUQ1NnVVeVBnNnE0YXkxc2lmbWd3NlY2RGRQeGRlUDhjZ1phNHdRQlpPa1pLYTc2Nmh0cVJSRVdKM1JueUNldm5EaWg5cG5SdUE5elNRbGNQU3dBS2lGR0xMQUNMK1B6TjVjRDl1ZzMvQ1hoMmh3cGlxWmZkb3NwN0ZRbjBSYys4OWpvV0dXd2QzU1JGempiWjJiY1pJa1RUYUNHamFkRUFtVXZRMUxhSUFNRy9kQnIwMmUyVlhaQjR0ejJETGVjWkVXTU9OYk9wR1VLVFBYQ2lVdmtkUW9nWTRpKzJhL1NXZnM5NEJUckxndmxhMWRSYitBQStha3J1RXRCTk9FcGs0K3pzQXlZeHJTK3oyZFkzMlI5VVNia1RzNElSSUMwN2ZZWUNUbG9UYnFrVzJNNGRpZm9EMkhBYXU2LzMrK2tEOHhTYXJGSGtXS3p2aDdvVEN5RUxMOEFYMGM4Qk9yK2pGbFhtR25OdDRrS25EYUU0MS9pazJpY1ZDTnRzQkZVbDdPbWsrbThPQ05vaHd6RHRmNTMzZjZ2ZnJwSmpuWG1xM0FrQWF2emJDR1pvTDJzUVJBaWxmYzFXdXM1RzVwekF0WURlZmVlUmt1YjJmWVpDR3JZZ0JoNXRvRE5KZHFoWkRxTVNJanRwTVFWMWxOSTVGOUFSWE9xZlkiLCJtYWMiOiIzZjJmYzE5ODBkOTY2MDVkM2MzNWVmN2UxZDUzYjQ0YzMzOTc3NmM2ZDE0NjE3YTdiYzcxYzg1NzUyM2I4MDI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldMUXlpRFRRN3BRNHJWSDJYc09sK3c9PSIsInZhbHVlIjoiOHlLVGlrelFaUFE2WUlMTFhRSmZVa2Y1aEYvbFpTdkcyMFlGZUhIVmRZckYzMXpZRW9ZdXFtbUhYaFBJekNYcE9qRGdUM2tDT3pCdmo0aGhtWCtPVXcxMCtUaXZ1Sk9wWXAwSzRHU0liMmE1MW04eUZYMDhBa1NYM0tRSG5za1lQdlp3WXhJNEJCdnZQRUV1V0hrSE9INDJKWjlKYlRpYzBpY0lCdmtoT1VJdjlGK21URURiWkJ4K1NGY3QxUmE2aDJMLzk2Y25IOUU3dHpYdTZHZWxrZXViMUF3TzFWSVg3cDRndWw5WWE4YlpZMXRYcE5NSkhoeFl4RGhaRjJzUG4wTFdta2d0NlNLYnU5a01EWXZOdjVIak9wczlCamI2THpXNkdjazdkaDZGVHMwUzloTXhjL2NnWDR0ZDM3UnM5Y1EvUDhCQll3eHhMVmJZUU03ZEpwZjZnbS96Y1FSWVJSMmNnbmdlSnVDeVVlT0ZZY1BtS0ZWQ3ErcWptWlFGOUYxZVBadjJqbXJPSUZQNmRXejVxQXBOMzBSTHJXMmRmZkJ2ZzgwRldjUGZzanJ1T1UrMXV0K0dSc2hGc1NOa3d2UmZsc0RYdHpSRDRtVUwvUFdyZ0p6amgveEp5VWMvNmt2akY4OE5kMjViMjI3bTZHRHVidFZCM253VWdyWUMiLCJtYWMiOiJjMjZmZDVmZDg2NmJmYzkwNGY3Y2E5MGExZjdkNDExMTY0MDJmMTUwMDE2MjA0MzNhYjczMzQxMzA1NGFiM2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-754237861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-74883011 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74883011\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1615983125 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJZMEhHbVdJdHQ0dWU4VzNZcytRZkE9PSIsInZhbHVlIjoiU3BwbmxkQjk5cmlqdTUxcE1vbVc2QTlvM2h4SUF6TmJ2STd1aXo5OUIrUGdtb2ZrRDJpc1JDMC9yMGMvc29OdU9aaGtLY0FLbjJnbWZhWDN2NE1nL0VCQm9UVFlEemVqUzNMWWhhNjhXSW15VTFIUnFhMmtmd0crelBtQlB3TG1rSFdPcHJ4WmVqRDJhS2h4bERJOFg4SXRQdXBRRTdIbCtRcjlXWWtDNnk3bXc0bHEzVG43Si9lRGZjOXBjd0tqOTJiREFlcFYyaFhlS2RpRE5MU0xUUjkwcW9yeE9zTnlGQTVRVk9GMGJKR3E0S1dqRTJFcXZaRy9qWjhTUk1rS1RSM2dSWWlWS1hKNWhJVEhlNU5EQUNlYzNiOEU1ZkcxZFQ3WnpSSkFiNGZnUlgxajNoSFY1OFMzamxickxkM0sxN1VZL2djUkxMbytnTjJoUWxIWWorc21vcFhYSUIzZVZRWExQOGR2Y3oyc0hGUWdjVWxuSXJNNnhaMmpVRksxVXpXV2RoS3cyUmtHNEhjc0VlRi9acUk1N3lYbFhrUzdkK05PSHJuYUlCK01WeFNoeE1iMVY4Vk5xZVhCQWZkYUY3Q3NIaGcrWllzS0JCY25hT2x4K1FRTEJFVEN5ZUsrTGNwUm5JM0xaTlpuSGorNlJ6azF5ZlgxVDY0UmpONm0iLCJtYWMiOiI5OTgyZDM3NzE1N2MyYTRhMTMzMmIxZWM0ODI0MjA0NWFhMDM2MWUwY2ZkZGVmYjQwNzEwOWIwNTE5NmEwNjliIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imp3amh2bC9sZkJwd2RScmVGZFBRVmc9PSIsInZhbHVlIjoiMmpLZHNEQ1p6ckhTQU1JN0FGaEZndkNXdG50ekZPeVJtVkJuOWZkQUFTbmVybno3dHc0a3RkT0w4U2hkSXFCYVA1N1hCMUkvbG5weXZQM1ArVDlIQ3JMQk1MWjNHVUcvLzdIUGtQcUNzNDJIMFVWR2lZVEp3NG9valVxbTBLazB3aDVDcGk3RVhuYzBPWUlrVFNnaFJodDNoelVCa1ZzZWlMSEFJY293VUpQSHVIUzlXcFNLc3MwZzRkTkJEb1hnN05zSlEveGYvTVZFZVI2eU1DSGxWcXlCZ0V0UHlxcmppaDZkdVlXTVlURVB3bEwyVndSanFmaFl2akdHc1BzOC8yYmxoQkVvdmxJYmxTOGZxU0ZlMmlOa29XSlF4bWZHZm9jYlNzTWcwTGdxMGVIdXJ3WmtmdDNLQUQ1R3plTGFjNlNoczkzMUlwUmNRWXpBbW1ETkEzYWlWQk1na0tjbDduRTdwQ0dRZXVKTldDNnM4bURDbFBzUCtrMDd5UGNMQjk0L0R4cXVZTVRrN1U4eXN0ZmNXZVk3eU81THRhemYvZjZTSEVjdUhrVkJiTnBzcVh4OFg2WjN2OTJCMThpeFg5N2NXd0xQTUxuSjFvT3VJNnhxSy9hdk1lSW5SdUk2TDhSb29UeGt4dXRpbnFHOGpUdm1hdTJRK2h2Zkx1MXgiLCJtYWMiOiIwMDlhMTgxMDEwMDNkMDkyOWY1YjVkNjM5MzdmZWM0MDhlZTQxOWUzOGQ2OGQ1NjYxMTMxN2QyYjEwMWQyMmI0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJZMEhHbVdJdHQ0dWU4VzNZcytRZkE9PSIsInZhbHVlIjoiU3BwbmxkQjk5cmlqdTUxcE1vbVc2QTlvM2h4SUF6TmJ2STd1aXo5OUIrUGdtb2ZrRDJpc1JDMC9yMGMvc29OdU9aaGtLY0FLbjJnbWZhWDN2NE1nL0VCQm9UVFlEemVqUzNMWWhhNjhXSW15VTFIUnFhMmtmd0crelBtQlB3TG1rSFdPcHJ4WmVqRDJhS2h4bERJOFg4SXRQdXBRRTdIbCtRcjlXWWtDNnk3bXc0bHEzVG43Si9lRGZjOXBjd0tqOTJiREFlcFYyaFhlS2RpRE5MU0xUUjkwcW9yeE9zTnlGQTVRVk9GMGJKR3E0S1dqRTJFcXZaRy9qWjhTUk1rS1RSM2dSWWlWS1hKNWhJVEhlNU5EQUNlYzNiOEU1ZkcxZFQ3WnpSSkFiNGZnUlgxajNoSFY1OFMzamxickxkM0sxN1VZL2djUkxMbytnTjJoUWxIWWorc21vcFhYSUIzZVZRWExQOGR2Y3oyc0hGUWdjVWxuSXJNNnhaMmpVRksxVXpXV2RoS3cyUmtHNEhjc0VlRi9acUk1N3lYbFhrUzdkK05PSHJuYUlCK01WeFNoeE1iMVY4Vk5xZVhCQWZkYUY3Q3NIaGcrWllzS0JCY25hT2x4K1FRTEJFVEN5ZUsrTGNwUm5JM0xaTlpuSGorNlJ6azF5ZlgxVDY0UmpONm0iLCJtYWMiOiI5OTgyZDM3NzE1N2MyYTRhMTMzMmIxZWM0ODI0MjA0NWFhMDM2MWUwY2ZkZGVmYjQwNzEwOWIwNTE5NmEwNjliIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imp3amh2bC9sZkJwd2RScmVGZFBRVmc9PSIsInZhbHVlIjoiMmpLZHNEQ1p6ckhTQU1JN0FGaEZndkNXdG50ekZPeVJtVkJuOWZkQUFTbmVybno3dHc0a3RkT0w4U2hkSXFCYVA1N1hCMUkvbG5weXZQM1ArVDlIQ3JMQk1MWjNHVUcvLzdIUGtQcUNzNDJIMFVWR2lZVEp3NG9valVxbTBLazB3aDVDcGk3RVhuYzBPWUlrVFNnaFJodDNoelVCa1ZzZWlMSEFJY293VUpQSHVIUzlXcFNLc3MwZzRkTkJEb1hnN05zSlEveGYvTVZFZVI2eU1DSGxWcXlCZ0V0UHlxcmppaDZkdVlXTVlURVB3bEwyVndSanFmaFl2akdHc1BzOC8yYmxoQkVvdmxJYmxTOGZxU0ZlMmlOa29XSlF4bWZHZm9jYlNzTWcwTGdxMGVIdXJ3WmtmdDNLQUQ1R3plTGFjNlNoczkzMUlwUmNRWXpBbW1ETkEzYWlWQk1na0tjbDduRTdwQ0dRZXVKTldDNnM4bURDbFBzUCtrMDd5UGNMQjk0L0R4cXVZTVRrN1U4eXN0ZmNXZVk3eU81THRhemYvZjZTSEVjdUhrVkJiTnBzcVh4OFg2WjN2OTJCMThpeFg5N2NXd0xQTUxuSjFvT3VJNnhxSy9hdk1lSW5SdUk2TDhSb29UeGt4dXRpbnFHOGpUdm1hdTJRK2h2Zkx1MXgiLCJtYWMiOiIwMDlhMTgxMDEwMDNkMDkyOWY1YjVkNjM5MzdmZWM0MDhlZTQxOWUzOGQ2OGQ1NjYxMTMxN2QyYjEwMWQyMmI0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615983125\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}