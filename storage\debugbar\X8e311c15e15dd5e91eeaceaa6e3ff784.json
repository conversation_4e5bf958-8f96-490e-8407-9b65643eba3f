{"__meta": {"id": "X8e311c15e15dd5e91eeaceaa6e3ff784", "datetime": "2025-06-30 18:04:59", "utime": **********.966855, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.447753, "end": **********.966869, "duration": 0.5191161632537842, "duration_str": "519ms", "measures": [{"label": "Booting", "start": **********.447753, "relative_start": 0, "end": **********.864045, "relative_end": **********.864045, "duration": 0.4162919521331787, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.864054, "relative_start": 0.41630101203918457, "end": **********.96687, "relative_end": 9.5367431640625e-07, "duration": 0.10281610488891602, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48169832, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.025500000000000002, "accumulated_duration_str": "25.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.897334, "duration": 0.01984, "duration_str": "19.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.804}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9275582, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.804, "width_percent": 1.804}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.942678, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 79.608, "width_percent": 1.451}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.944667, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.059, "width_percent": 1.176}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.951008, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 82.235, "width_percent": 12.902}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.957555, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 95.137, "width_percent": 4.863}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2053570661 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053570661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949588, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1610356580 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1610356580\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-4190578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-4190578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1284282655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1284282655\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1593462027 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZBSXIxY1VocjBic2xNQ1JMVlVaOFE9PSIsInZhbHVlIjoicFFUSGdQSEx3clZ2YzBzSkE5QkZFVHRBZUUrWkRPSXRnNUJBZFE0TEs5bjR2SjVrZlY3ZW1IZXQvZWNtckxoZzZRNURDS2djak9BdDNiYVlrUnFGKzQ2bGhkSXRGTjI5SDZXZGJBZk51eStHeWFSeUhoV2JrTktvTExyRkM0cEpYaDgyaVhKdkZFRXdLajFINU5jQkxkVk9hY3BiaGt6THdUaG9mNzlwdzVBbTMrVFJ5bnBweGFxSitHdGFBWVJoLzVwYUZHU1c1MkpoeGZidEZhdEhsSXdlL1VOTUg1YlZ2SW9lSnYycjJHYXRMeVE3SEJlMGFncXZXMmxQVU5udC9yaEQ0SzFKUkk0OGxtSm1XUlhiTHV1WDFkTUJRMFhHd2ZsZjVBWlQzdjlTZyt5M0V0TkpUN2R2RE0xeEt2Q2IzQ1BWcjZUQXlvMnhxeFRYVVNBMmIrRDJTWUxNNnZQQmt3dDh1N1FEeGFuZDVBTklCb2tUT2JXVG92L2h5VTJVL2syRkZ4UzE0bzF4dEJ0Nk5zMzFLSUcvaHplVzNXTzRtd1JKb0U2em5obElXRkUrMStvajg0YWxSR2tDV3FDVng5NXlodzFyK0JzbHJEdWhBbnViYnorZDk5aldlRDF1TC9ZOElHU2JvMGU1dm5Qb0wrSlZuamNQMFROM3dLQjkiLCJtYWMiOiJmM2UzMmE4OGIyMDkwMjBmYzU1NDQ5OGE3MGEzYThkMDY2YTVjNjgwNDk2MmI5M2UwOTI3NDBmMTU5ZjhiOGU2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtLb0luNmpLL2JLZElZL2xIZ0FIUmc9PSIsInZhbHVlIjoiZFZ2WkhrTG1FVXRDejJiM0phbkl5M1daNHdRbGFWRHhPV1lRYjUwTW5WZXI4dDJtdFZ0TVp5OXJQaDZXTGZqNW1vWkJpV3hKV3ZmRnVaWnBTT1lPbXhNOTRMMU44cWhwSVkyZkFiWDRmM2d0WWpYakxBaHFRUThBU3hCUVdxRThCK1UxdFdrNi9GdkJZN3VZTkQ1OGFxRVpEUytheEFaK2x1Z2JRRzMwMklMMGdSVm9IU2lMa2hyRkRDQzJSNS8zNTZyWjNuT0RzVnNoUGlSdTZ1SGp3VE9NOTdxaFJKdldTc0xWalR1anVSMEltWEZxV0ZORjc2Nk5SYnhZcVI2a1ZmemxGZEdabXpnQm0yWmIvUFBRUUJELzhVZldyMjk5MUU2UWt2WFplU25xak9pQU1DTzlMb0VVQXkyOTNEUWxLTzVtTXIwdmJTNkVTTzUrVStmSmdVK2tmS2ZDcGp2ZFZManFSaFJuMnMzZ1hrUGxGVldhWERYTGp4K3cweThFSkRZSmZwc0o4Q0VMa3IrMVlDaUpZSXh2R0RRV01ESTJSZGtSY21rSlM1dGc2cnQ0a2JQUUZSSDFLaEp2YUFzVVRsSjduQ0R0T0UvT3ZvaUdKOXpWL3ZKcmdZZUNXdEl4QVdyTTVQZDZFY2k1bFZ4Wk9YV2o5OFdsTDVyaVh4NS8iLCJtYWMiOiI1ZWRlNDQ0YTJmZDM0MjBkYTczZjQ3MzdhYjIyNDJjNGQ1MjBkYjQ1MGY5NTFlOWM3NWZhMDNkMmE0NmVjNDc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593462027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-266535029 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266535029\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-521321599 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:04:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtZRkVvOENVOTVxSXVhQnZNUTlpUWc9PSIsInZhbHVlIjoiSlNGcDVnaHloRStpbkovUjBwWmNjd2syNG5lOVhHcmdwM0JHZkR4a2RaQS9UaWora2txZUxmbVdwYy83Tzl4eHZUclFEQmMzb05lRVArUERtQUQ1VkhKcG52TTAwd0FSRGFCb0Mzb2hTQTNFeVBlWk51dXk2blY5VVhhdmtRcWl0R2lXZ0JzSUNvR2tNYWM1Mk5ZcmNBb1JvL3pwenF1S0RQRElsdnpuRmIydldmVHFXQjlhcVV5QUY4TmNQSFF1M0RvQ2JKYjlseE4xaFNpR05GMXFEdUx4VDJudjhybm9oR1FBRFVvT3pNM2QyVk1TVHZramFVdmRnejV4cWNZR2xKTXh6OUJXZWJmT1BUQTZWT1lUV0pUbksyYkQwQlFjL0VPWEw0YU5HNUYySlgzcXpMOEM0UGRLM0RmbGN3QmdVcUpSZ05oYW9xSEg4N1UwSTBsbEQvbEx3WFRCVjIwbFJWUGxPOGwwbU5JSmpkMVZ6aW4ram5vL2NhTlJVSmxnVFpwVkE2Mjh5TEZObnh5UnBxNFEyaWEyaXpwZDhEcm9rVkNPUlhuRzJiVER1Q09LSVJ2N2ZEZzdReDR5dFVaNytwbW13dDFUOTE4Rk85L2ZqOVJsR25qWnp1MU10UHlsSWZCc1JvNjY5cXpDVTZCaHJvdTE4NnU3WEFyeElZcmEiLCJtYWMiOiJjNDAxZGVmYmY5MWI3NWE4NjE4MWYyOWY0NTVhNmVjNmY5ZDU1YTg1M2UyMjRmZTIyOTM4NmM4NGExOGY2ZTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:04:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlGMHVJQjFDUERtaWdqT05NTk1MZmc9PSIsInZhbHVlIjoiUmtMbFZVRnVraWZwcmFTd3ZvU0lJNHVOdkkvaEF3SVRZZXdZcWtEaG5ubEJ5cWNVSzZ2cDFVallnL3Z1UjI4Vm5oY3FoTENTMkhRYnpZYi91Zkh6QXZpSmFJTEZlVXlaS2FXTDh2cTREcEZmTW1tZ2t6QXlHMUJlRVlSQVd2MlZlWU9ucE1IbHk4d2FLc3VtWEowcTI4QTl6NmpjaXhMdzJoRnpKN3pqWEs5SWNKMCtzaElSbmNYaWoxTTdIbVgzVHAwK1lBcUJ1TXR5RTZJSTBXeFlQOXZNdTdPQWQ2WUgrSGkzMHBFUmhpSnFIVHpzUXBEU1hWL0FxOFFwL05vWFNyTTZKbjNyYzNHNUdqQnJpd3Zhb25KSmZYQUYrc0NGYk9SSlNuRzVKaFUvL1NTZnNXY2NIYk9UM1JCQ2lYbCtDN2JzZ0svNlBaTmZvNWlzYkNwaUNscCtyclVTKzJRc3BiZFM5M05WMnQvNkkxRkdMT2VuSW5kd1drcFFjaVNnU3Bkb3AvSHMwZy9rYnhRT1dRMlVsU2Y1WlhReDQxVGRPSm9vajlOOXorWUtGcDFjMUNPYkdNTGVqRnh0TUlEcGd4NTFuZzJuc095N2ZSNmY5bm1SVFVtcWg3ZDBoSU8xTXVqYWdXNnFIQkJsQzJwN1hXd0ZSdXZVNHptUWVxKzQiLCJtYWMiOiI3OGFmOGFiZWE3ZmVmOWZjNTdhODVmMmM4NjQxMGEzYWVhMGViNmM5ZWE1YmZlZmQ4NWI4ZDVmNGMwNjg3ZDU1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:04:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtZRkVvOENVOTVxSXVhQnZNUTlpUWc9PSIsInZhbHVlIjoiSlNGcDVnaHloRStpbkovUjBwWmNjd2syNG5lOVhHcmdwM0JHZkR4a2RaQS9UaWora2txZUxmbVdwYy83Tzl4eHZUclFEQmMzb05lRVArUERtQUQ1VkhKcG52TTAwd0FSRGFCb0Mzb2hTQTNFeVBlWk51dXk2blY5VVhhdmtRcWl0R2lXZ0JzSUNvR2tNYWM1Mk5ZcmNBb1JvL3pwenF1S0RQRElsdnpuRmIydldmVHFXQjlhcVV5QUY4TmNQSFF1M0RvQ2JKYjlseE4xaFNpR05GMXFEdUx4VDJudjhybm9oR1FBRFVvT3pNM2QyVk1TVHZramFVdmRnejV4cWNZR2xKTXh6OUJXZWJmT1BUQTZWT1lUV0pUbksyYkQwQlFjL0VPWEw0YU5HNUYySlgzcXpMOEM0UGRLM0RmbGN3QmdVcUpSZ05oYW9xSEg4N1UwSTBsbEQvbEx3WFRCVjIwbFJWUGxPOGwwbU5JSmpkMVZ6aW4ram5vL2NhTlJVSmxnVFpwVkE2Mjh5TEZObnh5UnBxNFEyaWEyaXpwZDhEcm9rVkNPUlhuRzJiVER1Q09LSVJ2N2ZEZzdReDR5dFVaNytwbW13dDFUOTE4Rk85L2ZqOVJsR25qWnp1MU10UHlsSWZCc1JvNjY5cXpDVTZCaHJvdTE4NnU3WEFyeElZcmEiLCJtYWMiOiJjNDAxZGVmYmY5MWI3NWE4NjE4MWYyOWY0NTVhNmVjNmY5ZDU1YTg1M2UyMjRmZTIyOTM4NmM4NGExOGY2ZTU5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:04:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlGMHVJQjFDUERtaWdqT05NTk1MZmc9PSIsInZhbHVlIjoiUmtMbFZVRnVraWZwcmFTd3ZvU0lJNHVOdkkvaEF3SVRZZXdZcWtEaG5ubEJ5cWNVSzZ2cDFVallnL3Z1UjI4Vm5oY3FoTENTMkhRYnpZYi91Zkh6QXZpSmFJTEZlVXlaS2FXTDh2cTREcEZmTW1tZ2t6QXlHMUJlRVlSQVd2MlZlWU9ucE1IbHk4d2FLc3VtWEowcTI4QTl6NmpjaXhMdzJoRnpKN3pqWEs5SWNKMCtzaElSbmNYaWoxTTdIbVgzVHAwK1lBcUJ1TXR5RTZJSTBXeFlQOXZNdTdPQWQ2WUgrSGkzMHBFUmhpSnFIVHpzUXBEU1hWL0FxOFFwL05vWFNyTTZKbjNyYzNHNUdqQnJpd3Zhb25KSmZYQUYrc0NGYk9SSlNuRzVKaFUvL1NTZnNXY2NIYk9UM1JCQ2lYbCtDN2JzZ0svNlBaTmZvNWlzYkNwaUNscCtyclVTKzJRc3BiZFM5M05WMnQvNkkxRkdMT2VuSW5kd1drcFFjaVNnU3Bkb3AvSHMwZy9rYnhRT1dRMlVsU2Y1WlhReDQxVGRPSm9vajlOOXorWUtGcDFjMUNPYkdNTGVqRnh0TUlEcGd4NTFuZzJuc095N2ZSNmY5bm1SVFVtcWg3ZDBoSU8xTXVqYWdXNnFIQkJsQzJwN1hXd0ZSdXZVNHptUWVxKzQiLCJtYWMiOiI3OGFmOGFiZWE3ZmVmOWZjNTdhODVmMmM4NjQxMGEzYWVhMGViNmM5ZWE1YmZlZmQ4NWI4ZDVmNGMwNjg3ZDU1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:04:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521321599\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-48611934 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48611934\", {\"maxDepth\":0})</script>\n"}}