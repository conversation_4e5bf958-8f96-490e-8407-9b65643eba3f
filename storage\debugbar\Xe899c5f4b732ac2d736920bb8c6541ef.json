{"__meta": {"id": "Xe899c5f4b732ac2d736920bb8c6541ef", "datetime": "2025-06-30 16:05:53", "utime": **********.14253, "method": "GET", "uri": "/receipt-order", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299552.607228, "end": **********.142549, "duration": 0.5353209972381592, "duration_str": "535ms", "measures": [{"label": "Booting", "start": 1751299552.607228, "relative_start": 0, "end": 1751299552.984955, "relative_end": 1751299552.984955, "duration": 0.37772703170776367, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751299552.984965, "relative_start": 0.37773704528808594, "end": **********.142551, "relative_end": 1.9073486328125e-06, "duration": 0.15758585929870605, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48093544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET receipt-order", "middleware": "web, verified, auth, XSS, revalidate", "as": "receipt-order.index", "controller": "App\\Http\\Controllers\\ReceiptOrderController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=23\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:23-75</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.05790999999999999, "accumulated_duration_str": "57.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.035293, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.246}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0466988, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.246, "width_percent": 1.002}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0541701, "duration": 0.05436, "duration_str": "54.36ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 4.248, "width_percent": 93.87}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.123748, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 98.118, "width_percent": 1.157}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.126045, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 99.275, "width_percent": 0.725}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage warehouse, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1585228801 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585228801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.131009, "xdebug_link": null}, {"message": "[ability => show warehouse, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-944131866 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944131866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.132212, "xdebug_link": null}]}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-order", "status_code": "<pre class=sf-dump id=sf-dump-2070445681 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2070445681\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1757984928 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1757984928\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1402317580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402317580\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-210065306 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5aYzRZbVA2TG9Eb1kwM202Vm1pcUE9PSIsInZhbHVlIjoieWVrdW05Ymw1dlkvbks2V0VaaTRmM1J0NDlLcFY5QVBQclcrdlloSmtaNEpITTJWZWhBb011S0ZxMDhGdFk5a0dZSHZiL1lKWlNXNDVQczlMNTY2Q0N6VHJ5TGJrWThxd1MycVNNSEpoR1B2WTYrRSt0U29hVGpDQVhvdXVVK0ZFSUtybENZN1ZMeE05d0cydFU3c1FLNENMcHhwWTlRaDhxTVFIR210VDJYN24rRXhRaW1GQU9aanpOeWZCcHFQOW9WL2NIRTFPY1lFcUZQSEZ5T2JJOGZRWGtFcDZTUXZ3bFoxeDF4dnhTcHRSeHhXbjdVNWt5blpNNHMxdTFaUGhlZzFnUVZ5K0hWYjBzTWdqUlRjNnNNV2F5d1JWS1lhalhMU282WHZyZDBUa2Y1MC9mbm51MDl5SWM3dnByZVgvL05La2lJOUNKVzdDZGlsWElPSUI2U0ltQ0Z4UWt5S21BYXZuNUJqZ2lFd2tOU1c5R1JkRThFT21TN0JVbzEvSDk2TUl1OXRSbzg4MXRGUytySzhoYnNCY2ZRUHRINHROYmtIR240dVplRHhKeW52eU5rZ0lGTzdIYzVxZjdya3p5K2ZGMHRZSWNrcWpVRVM0aXJ1OXZLaGJYME5IY3RQQ2MyejFaelpxTWxYQ2VCWTdpM3RTeG1mN0c5Ty9JbFciLCJtYWMiOiI1NmExNDA4YWUyNzE1ZGQ4Y2Q5YWY0ZjYxZDVkOGQ2OGI4YmFmZDE3NjgzNmJhYjg5ZDBjNWM5YTljNmJmYzMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNSVkJ5a2hrelBGYmcyK0htaEtRYWc9PSIsInZhbHVlIjoiaS9rRWNFQ2NOVUs0QlRDWWZQbnA1V1VwcW9scE9UdUcvYVJvRjFqd2JsUG9Pa2VMamVRelBrS1lUL3M5d0FweDF1bklSOXVRNGdqemluRkJwM1VZR01LKzdYSTh6SkZ4SEtKcVJ6eFRBWi9HMU5YS0o4bGxxa20zbFMwRUVGbjQwVkRDY2o2dWovUC9MWXdKSmdxNE5MMkRBSXM5c1pFcVFFRmdsV3RydUkwWDBQUTRrK0VNVTRCRFNzSVRJL0dtTWFLNER6Z2NueEtqZ0JjZGgrU243czREQnlBTTBqV2orMlRrVnNZTHJxK1JNc2FEVlAxcENzNDhNWjZrMVdpSkZuUWNjSmtJWVlNa2NtMnlUTlVBY2xQUm12a0hhSHM0anc0WEFOeVM3NEVVUEdIYmpRRGhoSkozeURYbXZLL1pLNHU5cWlXQ09FQ2h5R244YVg1ajBUUlhmRlI1eUo4VUdoRmp3dzJVSHBuVnJDZGpnSzVKa1RUQmhJQVAyUVMwc0FuVlZoOERpdndLRmpGVFk2cUF4cksvQ0FRTDVMblBLeTBLakF3V1FCSXFhYzJJb1BvUWk1RmRBZG9YS3pmMlRZSFNqNjcranYyV0xHZmtUNGdjWmJiUk9DZGYxUzJtQjR5SDVWQnJpQ2Z6SUhCYyt2dWo3ZkhsWU9ZbEtONnIiLCJtYWMiOiI3YmU0NTExNDc2N2MzNjY0ZDhkM2Q3OTk4NDZhZmUyN2U5ZTZmZDE0YTg3ZTljNDRiNGZjMTU1YzViZGY3YTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210065306\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1804504805 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804504805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-215537341 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5YWllyb0Z4blovR2JNa2RNUWQxaUE9PSIsInZhbHVlIjoienBwQmdCZjZ3L0JTV2tQMllTVVRKT3Btcy9vRkxnVUpVZGdNQ09WV0lOL0pyaU9kUUZFakFqMUk5a0U3T2FDQkk5bHhJeW5oWjh0MFJBaStJOHR3Q3YybDBuSTlnYzRuLzk2OGdKTnppZWJVdGJjeW5tUVpVbjJJYVhoOFEyZGpDWkd5M1RPclc4Z0lkZFRKODhtS1NRRFFaZXlSWHVQL0l1eXF2R1VGTDlYcUVxV1BjR0daS0dDMkhOV1FldDNpU0pJeGMzRlU5U0FCWUlockVmZEYyZTlCZHVHaXg1ZUF5dUpOODVyK3lDSDBpck92dnE1RllTRWJhWlBnOGFkSHlUOWYxNjNIdk02YlRjL2s5YUhJZEdWK29JOEs5a1NNZjl4c0YwdHgwc1hTMGNUUUtKYWlEWDRjNUd6SmtqV1B2Qmg1bi95MTNGTDRNRjRjckl5Uzh0NEdBZURoUFZXd0ttWU53dGFjV2h0Tzl2WTliSC9IMThLdnB0dXZkN0RxM2tabmNiQ2N5OWtoS0J4QXloSDl2aVdlcDNqTVpBSGQ3TXB5VSt5VEI3VlAreEFrQms3QVRWQ0NXbm0xVG1mMFppWElFd3dYZ0p4ZE5DRy91V0NzMnNxdThCYTNBVmJEckQweStMRmVvMjlSbkFqSUxJcTloSTRzc1J5UkJWRGQiLCJtYWMiOiIwOWU4MmE2ZDg3NmIxZWI5M2QyNjMwNzIwYTY2ODI5YzE5NTI0MDkwYjRkZDYzYWZhOTIxOWRmY2ZjODE4ODkzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBNRFVxM1NYTURSbEl0Zlo4UzBUaGc9PSIsInZhbHVlIjoiWGh4c1JTVnZ2bVdIVG9iRVVqOU51enNRWEgrMDlWeUhUeS9FU3k4YUwxUTZ6aWRUVk9GaVc4ZGVCRS9PTk45UXFYaEVzaHN5RTFKZ3F0eGU5ZE1FR0xNcm5WZFJYWndSbmU3Z1labCtiSDFHUGJycXRKL0FJK2JCUEp6cnFkQnRIU1Fkb0dObEVMVW5Id3JqSnAweEY5QTRNckNjYzU2bDFEMzFyaFdpYUg4NFgrODR4akd2eEoyWnJ4VW1lL09KQ1ZkYmRaandMbDJDdEtoU0hEVjQyMjExTU83R1ZUS2FEUUlUQlpFK00wRm1XVHNOdjBNOU9XUHo1V2tnS1pZQm5aNXRvMWhyQ2J4SE5Edm1USWtnMVY0YmJoOVE1a0svNXRaQVhMUzQxQlhZSGNBb0drbzNyRFd5clZMbmRuVHN2ZkhwYXZqdm1MMGFnZE9HMC93QnNnWWFIYTk1UnVJOEFZeHFENUFTNUZhVzViZzRkTlV2WUF5M0ZDc3dTaDZCTktSRGdySG1nQURJV1Y5S20ycFIwd0VSdDUwbTN6dERmaCtIaHdUSTV5OVBZNERTdmhLbjdBaytJM0ZBN2VucEVRNzI1dFlkL2NoYzJMZHMvZ1QrbUlaMGJZNUVNdXl4MWVtbHZ4TW95YXZVNVYwNmd0bXBlK3gyRmZQUTI5NzciLCJtYWMiOiIxNDFlZWEyZWRhNGE4ZGM0MTNjNmNjMjc0ZTU1YWJlZGI4ODFmNDZiMmI5NDgxOGFmNDk4YTQ1ZmI1YmM2OGY2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5YWllyb0Z4blovR2JNa2RNUWQxaUE9PSIsInZhbHVlIjoienBwQmdCZjZ3L0JTV2tQMllTVVRKT3Btcy9vRkxnVUpVZGdNQ09WV0lOL0pyaU9kUUZFakFqMUk5a0U3T2FDQkk5bHhJeW5oWjh0MFJBaStJOHR3Q3YybDBuSTlnYzRuLzk2OGdKTnppZWJVdGJjeW5tUVpVbjJJYVhoOFEyZGpDWkd5M1RPclc4Z0lkZFRKODhtS1NRRFFaZXlSWHVQL0l1eXF2R1VGTDlYcUVxV1BjR0daS0dDMkhOV1FldDNpU0pJeGMzRlU5U0FCWUlockVmZEYyZTlCZHVHaXg1ZUF5dUpOODVyK3lDSDBpck92dnE1RllTRWJhWlBnOGFkSHlUOWYxNjNIdk02YlRjL2s5YUhJZEdWK29JOEs5a1NNZjl4c0YwdHgwc1hTMGNUUUtKYWlEWDRjNUd6SmtqV1B2Qmg1bi95MTNGTDRNRjRjckl5Uzh0NEdBZURoUFZXd0ttWU53dGFjV2h0Tzl2WTliSC9IMThLdnB0dXZkN0RxM2tabmNiQ2N5OWtoS0J4QXloSDl2aVdlcDNqTVpBSGQ3TXB5VSt5VEI3VlAreEFrQms3QVRWQ0NXbm0xVG1mMFppWElFd3dYZ0p4ZE5DRy91V0NzMnNxdThCYTNBVmJEckQweStMRmVvMjlSbkFqSUxJcTloSTRzc1J5UkJWRGQiLCJtYWMiOiIwOWU4MmE2ZDg3NmIxZWI5M2QyNjMwNzIwYTY2ODI5YzE5NTI0MDkwYjRkZDYzYWZhOTIxOWRmY2ZjODE4ODkzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBNRFVxM1NYTURSbEl0Zlo4UzBUaGc9PSIsInZhbHVlIjoiWGh4c1JTVnZ2bVdIVG9iRVVqOU51enNRWEgrMDlWeUhUeS9FU3k4YUwxUTZ6aWRUVk9GaVc4ZGVCRS9PTk45UXFYaEVzaHN5RTFKZ3F0eGU5ZE1FR0xNcm5WZFJYWndSbmU3Z1labCtiSDFHUGJycXRKL0FJK2JCUEp6cnFkQnRIU1Fkb0dObEVMVW5Id3JqSnAweEY5QTRNckNjYzU2bDFEMzFyaFdpYUg4NFgrODR4akd2eEoyWnJ4VW1lL09KQ1ZkYmRaandMbDJDdEtoU0hEVjQyMjExTU83R1ZUS2FEUUlUQlpFK00wRm1XVHNOdjBNOU9XUHo1V2tnS1pZQm5aNXRvMWhyQ2J4SE5Edm1USWtnMVY0YmJoOVE1a0svNXRaQVhMUzQxQlhZSGNBb0drbzNyRFd5clZMbmRuVHN2ZkhwYXZqdm1MMGFnZE9HMC93QnNnWWFIYTk1UnVJOEFZeHFENUFTNUZhVzViZzRkTlV2WUF5M0ZDc3dTaDZCTktSRGdySG1nQURJV1Y5S20ycFIwd0VSdDUwbTN6dERmaCtIaHdUSTV5OVBZNERTdmhLbjdBaytJM0ZBN2VucEVRNzI1dFlkL2NoYzJMZHMvZ1QrbUlaMGJZNUVNdXl4MWVtbHZ4TW95YXZVNVYwNmd0bXBlK3gyRmZQUTI5NzciLCJtYWMiOiIxNDFlZWEyZWRhNGE4ZGM0MTNjNmNjMjc0ZTU1YWJlZGI4ODFmNDZiMmI5NDgxOGFmNDk4YTQ1ZmI1YmM2OGY2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215537341\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-395828001 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395828001\", {\"maxDepth\":0})</script>\n"}}