{"__meta": {"id": "X6c88c14035711977d57d3efcf23b816a", "datetime": "2025-06-30 16:05:55", "utime": **********.857314, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.416928, "end": **********.857331, "duration": 0.****************, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.416928, "relative_start": 0, "end": **********.790948, "relative_end": **********.790948, "duration": 0.****************, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.790958, "relative_start": 0.*****************, "end": **********.857333, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "66.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01499, "accumulated_duration_str": "14.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.818911, "duration": 0.013859999999999999, "duration_str": "13.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.462}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8408618, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.462, "width_percent": 2.802}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.848695, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.264, "width_percent": 4.736}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw1cUw1cjhISmt5RXdqWCtycjZ1UXc9PSIsInZhbHVlIjoiZDdhTUdjN1lkRnlkeTNKa2VjS3pIVUxGcWlETEcxZERCcmJ1bEloMS9tajkrV2pxZFV2dGJELzZrbEh0TlhVL3kyL2QvQ0xZdGlLYWZPZkFraThRdWJFSitpNjVJUlJKU1VCTFBBcjkzYVdKZWRlNkIwRlIyWHdvSEpBcFV0ZC8xeUs3ZWpJYjcxcmRqdFd5Wkp6bjVuZm5TQUtEZFJ5WExuYisxaURXMzhOc3U2NkF4SHlQMG40VnJ0TXp2YTlMaFVNV1lJUENxUmV6alJ5R09vYjBsLzJSMi8xVHNBeGV4Q0tLUGFiRy9UOGJJZTU5Vk5ZeUxtK0NjbUo5RXE1Z2FXcXJycXZqL3poeEkwekJwR2J4cnNvN08waklTM1Z0U1orMkQxR3oxbVViOEJUMmwrby9uYzFCbmhSdXZ0Q1N6NmlGTm9IZlYwYzdBbEhuYkc1RXYyTjV2RVJrWHJNRDRvTEpPM0JGZVRuaWtaM29UbGI3QW9pU1ZleitJaGJpZVBxSkY4QTIxZXJPYytLZ2pKUHcyVktiSWxFZjhXRkprakMrVkRtR1lSbm1KTldTUlFNdU80Y0VEeGRybll3WFp5RnQyTjU3cjQxM3FOMXJET21rcjBkeEJ6Z1ZGc3krU2M2bUJ5OWVVcG5oSWtZRFFpUXZaZUhwZlJmekRFRUYiLCJtYWMiOiIyMmE5NTc3Mjc2NjU1YmYzNGUwYzhhODQ5OGQzYzMzNjYzMDlhYjNlNTU2OGY4OGYyNWI3YjRkNTA3ODRmMjk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpaK3NRMFRGM0taWEluRDEyTy93UVE9PSIsInZhbHVlIjoiemZQWmx2M1UrN3U2Z3lmOXhuVXFUWFdmMHVuRkc5a3JaZmJYMTdMRGpUUFJVdkhJQjJxaFNwYUs0ekR0amk0b0RXTjhTZVpaSWNUdXdxMTVXVURZNFVybjU0eTVQSzluRFdoV3FUYmZxWWNKUVpMcnc0ZCszemh3SzllZUZ4enFOUE9CQ2hjdkFpWStNMzNnNFQvcGY4ZG12eTljT2t5a09aUmtNUTJlUnAyYjBxVDZ1bmM4QnE1WWVKc3ZCbHQ0Q293OVdWaXpOMHR1NHQwV24vMW1ZaGdoNWhIMlZzM3FDZ3kraXc1V3MxRTR4MHJPbG1lR3pFN0dFU0ZwZlY0ajFKNW9ITXo3QTBnViszWTdjWFdFOENTbVFlV0Y4TWdaQ3BkWTFvVEppUTUvSkNhSUpBS2syZDh2clFVYW5kSWtTNzRQL091QUZoSHNGSlBmMEcrR1dJZVp1NlRMRzdHOHFydXNIUGQ5bnZnRFM4YU5ncVd1a2g3N1FnQzhDS1hkeXhKM2VGUWh2ODJDU0lsWGJrNDJudVJkWURkRmhLWlFIdGFzRlJuTnRHMDd3UGd5THFqRGtTTVltUzFqMGZ0aHJIZ3JrRHlyK0w5em4wc0dZNWJpdm5zTGVDa2ZxeXN1ZkVueS9KYzlZSXhzTG8xeDFpdUwyMmZ4bVBJRjFrTFEiLCJtYWMiOiI0MjI4MGRjZDk5MDM4MjAzNzkwMzBiZTcyN2NhMjE5ZDQ0ODA4Y2Y5MjZlYzUxNmY1NGExZTk1ZjI4NWRkOGU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-521583235 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521583235\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2110650918 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ3dWdvcmtKOFdCRU5PZXdtQzJ5M2c9PSIsInZhbHVlIjoiVW9QUmJGN2VMUlg3aG9OQTQ3N2ZFV0tXT3VtM3RxOVlxSVZ5QUxjc3M3elNBSDA5Z1UvNUJNZDFKVWxBRGR5dTBNWGswRmYvNlBnN2hmK3pKM1JrNkNWL05GOXEyVUdnVXYwUVhHYWozREZJQ1pwUVNuVVJwMDZQcXh3TnNlVm1BZXk1RlVRWjFiMURGQWlOWUNTejVYZW5zTjFid0tObmtHQm1OYjR4ZnNIemZ4SUoxbWpJMWNZUlVCYkUwc3ZtOWp6RFQ3RnhuOEM1S3d6QmNRR01STk5qZit0OGVhMDhnQnBqTCtGekc4bmQzdHdyN1drWG1ja0NtbmdLN296K0RQWmZtak8vLzBtYml6RmpaZm0zemF5clVSTDM4eUpUR1o0dFRzYWJ4NzRBQXVzNFo5Y3dBdzFIT1ZWZVF5WWo2eHo1enRxWGFLN2VhOHFxUXBhVTJFbis4bi9ZMnJIdml3T3F6RjVReFF0N0RLWTNCdDBoWGJ0WGxqcEpIZ3dlMU4vRGxXTVFZbmJMR05OM1R2UkJrdGNDMU8yV0VTOHpMK0JrcTNOZkFZZHp1amViVjRYcjR6SEJuUkpKUG1aeWsva3hMVjBXTERMM01zNW1oNU5GeVlEelM3WUt5SzFQLy9yS2tHQ2VPZS93K3MzQUtPL2JWUWE4enNGTm1oRkoiLCJtYWMiOiJmZWQ1ZWIyOGY2Y2NiNmI2ZTI0ZWM2YTNhYTVjNjkzZjZkMzQwYjhhNDdjOWIwNmY5ODc5Njk4OGQwOWUxOWQxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlXcVh5YWtrMUs1d1FJV2FlbzM1U0E9PSIsInZhbHVlIjoiWjh6TkJoWUo1WHRvUHUzYW5ES0ljNkRiT2VCWEJOSlJyTDdzM2xjVXhnb3pvamMyNnU0c202dHlKNFlWWkhLOEF0MVFENE91a2J0NlVqM2VVRXFLQmxVUzgvUVN1SGVkSkZJZ3UxN1dwbXo3cVZORzVFZWRJQ2hzYWV4L0o3Q3BlcTFucWd0MzN5WnM0eEFkT0J5SUszeGdFZ2dWcW5sOG4rRkFjVTEzd09RbzRwVnE3cEJyRjFkcndvQjBnVDZ2NUg0Q21ISy9oYzBXc3FLeDZ5Zmxqd2haMHZrTXQ2NGFndGdyVUtuTHFXelJLS0t6Y2ZzbUFvcVdudlpHOFZCNTdrNDJuWk0vVjFNb1d5L3F3L2ZKYlZTL0NjNDRBUy9yenFVUUF4VVZGd3pVSmM5UHN0Ni9BYmZHa1l2YXUrTWtWd01lMU44VnVpbXQzc0hTNktESGxRRGg4UDNqVVNsTDY4bGVyd2tqeGo3dUdZZHdHRjlLc3FMZ3ByUnpvaTUwaVJWV3BNUko1aUFnVno3ZUpyTms1OXdrYzNIbjZMMFZYWVpzNW9yWDlhYWlzSXJJNE5Ydk1vMGExWlRRd20zS21wOWUya1NJT25CRzU4L3JQbXlmaGg2aVlZcGM1V2RDM20rczFSYjVFcWZxeDhpcWFaN1RkQVM4TzVLcVZPZS8iLCJtYWMiOiI0NDY4MTc4NmFmOWVlMWQ2MDc1MjdhMGVkMjYxZjU1OTgxZmRiYWFmNjYyY2M5NGYwM2JjN2M1YTlmMDQ2MzBiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ3dWdvcmtKOFdCRU5PZXdtQzJ5M2c9PSIsInZhbHVlIjoiVW9QUmJGN2VMUlg3aG9OQTQ3N2ZFV0tXT3VtM3RxOVlxSVZ5QUxjc3M3elNBSDA5Z1UvNUJNZDFKVWxBRGR5dTBNWGswRmYvNlBnN2hmK3pKM1JrNkNWL05GOXEyVUdnVXYwUVhHYWozREZJQ1pwUVNuVVJwMDZQcXh3TnNlVm1BZXk1RlVRWjFiMURGQWlOWUNTejVYZW5zTjFid0tObmtHQm1OYjR4ZnNIemZ4SUoxbWpJMWNZUlVCYkUwc3ZtOWp6RFQ3RnhuOEM1S3d6QmNRR01STk5qZit0OGVhMDhnQnBqTCtGekc4bmQzdHdyN1drWG1ja0NtbmdLN296K0RQWmZtak8vLzBtYml6RmpaZm0zemF5clVSTDM4eUpUR1o0dFRzYWJ4NzRBQXVzNFo5Y3dBdzFIT1ZWZVF5WWo2eHo1enRxWGFLN2VhOHFxUXBhVTJFbis4bi9ZMnJIdml3T3F6RjVReFF0N0RLWTNCdDBoWGJ0WGxqcEpIZ3dlMU4vRGxXTVFZbmJMR05OM1R2UkJrdGNDMU8yV0VTOHpMK0JrcTNOZkFZZHp1amViVjRYcjR6SEJuUkpKUG1aeWsva3hMVjBXTERMM01zNW1oNU5GeVlEelM3WUt5SzFQLy9yS2tHQ2VPZS93K3MzQUtPL2JWUWE4enNGTm1oRkoiLCJtYWMiOiJmZWQ1ZWIyOGY2Y2NiNmI2ZTI0ZWM2YTNhYTVjNjkzZjZkMzQwYjhhNDdjOWIwNmY5ODc5Njk4OGQwOWUxOWQxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlXcVh5YWtrMUs1d1FJV2FlbzM1U0E9PSIsInZhbHVlIjoiWjh6TkJoWUo1WHRvUHUzYW5ES0ljNkRiT2VCWEJOSlJyTDdzM2xjVXhnb3pvamMyNnU0c202dHlKNFlWWkhLOEF0MVFENE91a2J0NlVqM2VVRXFLQmxVUzgvUVN1SGVkSkZJZ3UxN1dwbXo3cVZORzVFZWRJQ2hzYWV4L0o3Q3BlcTFucWd0MzN5WnM0eEFkT0J5SUszeGdFZ2dWcW5sOG4rRkFjVTEzd09RbzRwVnE3cEJyRjFkcndvQjBnVDZ2NUg0Q21ISy9oYzBXc3FLeDZ5Zmxqd2haMHZrTXQ2NGFndGdyVUtuTHFXelJLS0t6Y2ZzbUFvcVdudlpHOFZCNTdrNDJuWk0vVjFNb1d5L3F3L2ZKYlZTL0NjNDRBUy9yenFVUUF4VVZGd3pVSmM5UHN0Ni9BYmZHa1l2YXUrTWtWd01lMU44VnVpbXQzc0hTNktESGxRRGg4UDNqVVNsTDY4bGVyd2tqeGo3dUdZZHdHRjlLc3FMZ3ByUnpvaTUwaVJWV3BNUko1aUFnVno3ZUpyTms1OXdrYzNIbjZMMFZYWVpzNW9yWDlhYWlzSXJJNE5Ydk1vMGExWlRRd20zS21wOWUya1NJT25CRzU4L3JQbXlmaGg2aVlZcGM1V2RDM20rczFSYjVFcWZxeDhpcWFaN1RkQVM4TzVLcVZPZS8iLCJtYWMiOiI0NDY4MTc4NmFmOWVlMWQ2MDc1MjdhMGVkMjYxZjU1OTgxZmRiYWFmNjYyY2M5NGYwM2JjN2M1YTlmMDQ2MzBiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110650918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1184625030 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184625030\", {\"maxDepth\":0})</script>\n"}}