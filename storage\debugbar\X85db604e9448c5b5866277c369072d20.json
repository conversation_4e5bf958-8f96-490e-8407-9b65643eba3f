{"__meta": {"id": "X85db604e9448c5b5866277c369072d20", "datetime": "2025-06-30 18:06:07", "utime": **********.818283, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.389476, "end": **********.818296, "duration": 0.4288198947906494, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.389476, "relative_start": 0, "end": **********.743541, "relative_end": **********.743541, "duration": 0.35406494140625, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.743551, "relative_start": 0.35407495498657227, "end": **********.818298, "relative_end": 2.1457672119140625e-06, "duration": 0.07474708557128906, "duration_str": "74.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00763, "accumulated_duration_str": "7.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.775065, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.392}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.784902, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.392, "width_percent": 4.325}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.798587, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.717, "width_percent": 4.325}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8002539, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 36.042, "width_percent": 5.374}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.804575, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 41.415, "width_percent": 30.537}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809141, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 71.953, "width_percent": 28.047}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-124912437 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124912437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.803688, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1474491054 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1474491054\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1906768666 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1906768666\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1571895717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1571895717\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-776262099 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVyN09SNXNJMXhEd3hUMTFOeUpzOFE9PSIsInZhbHVlIjoibnA3OWZqL2NHQmRzVCtENnN2alhFUU9EaHBDSHFKVWtFT0N2azZwSVJQY2ZHdHJTTERCRFBUOHZva1RhNkJJdW16akxYKzJ0OWlxdXNycnZBVzJNRGxVRDBBQm9QYis4MmN6N1BuZmU3VGNUWDVScEgyRjYvRnZzcy9JTGRVY3NMK0h0RnRBSzlxb0MyVmE1TlI0Q2ZrOVFlbnpjU1lQc3FRV0swY29TeGtRNkVoZ2g3MXlFenZsQXFSanBTMVlKMkpEd3R5dXJEc3BJdVBFbjdHMjdTVlkwcCtDdlE2REFUVjRMYnJ6Vkk5dDdDdDloZVY5TmVPaHFTU1hhMTlMMUdBNHl0RmhyYUpnYitpMURRRWQ5SmlURGxSQmJJZ0lFVm5PUGhMOWs3c1EwSWRwVG9aVG82QlpEeEpUbVU5VWcrZ0JUVzRzZmpWNlhNTkhMMXUwelZKL1hoR25NNVhpOWdhL3oxdUxHTXBDVk1VMmsza0R3V3RJbDM3Q3dmQ0NZYmc1MU5LNmdVU3NRa08rZW9ySWV0c1RNS0VvUmF1RnF4VUNPcWNqMk0rbXloNDl0bUZEa09FenFvZ2VKaUYxUDZ3SDJ3NTArUStURWZPZnZOUWxKbmtYVTJ5amlaMHFZMXhiTmkvSmZoYjhWcTVPRWM2elZtTHpoc1VLUjZZY1EiLCJtYWMiOiJhM2YyMzAwNjllMzFhOTg1NjI4ZTcxYThmOTU3YjIxMjQ2YmI0ZWQ5NWZiNmU1MzFkYWE5M2EzZGU1NjQ0ZDUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhSTGRVTTRPNzM5cnA2V3BYekY0Mnc9PSIsInZhbHVlIjoiczVsRWdmSlRTUkNxMk16UDVieUFxT013SStRN1VUUklZUFc2MDNERlMycW42RkpMMyt5S1RERkVYVDh0MHpFblZTRG5DWTZZTHJVajhTM3VCM05MN0lnbEhRVWdPTzkwSVZhMm1kSEVFSk0rWHEyUDd3TTE1UUliNkVYM1Y2MUpLUXJJOFdUV08vb0ltZHZuQnR3Z3YyQVRjc1h1cUFrWUdnNThyVG5mRWhSL3lEcUNZejZGTUkveDRCM29OaXQ2S3NzeDVrckVRQU51OUdBNzJieFlTZ1J3WmlTUldIbUQ1Q0Z1TVJFaW9QbFhzd01ITWRrL3ljdmx2TktGQUtWd2FvRUlsVVZjRXN2K1JSbzQzc3NlSFpIeWNmQ2IvT1BlaEpQV2xwWkJDcFBpUjlXNCtJQkt2Qmc2U1dqaUExeDNMRHVxWHhXVks2aE5LUUhmdUFYVUR2Z2M2WE05bWNnMVZCaVFqUldFMkpLV2tpRXY3bEJ1RGw4cGpjeVcrT1pEVkl5dUZ2VDBnOHp5c3dmMjhCa0xWQzVva1FIeFZma3EyV1FKZVIvNjNZYTAvYVZBVVBWN3p4SE1qdmVvejcyQVVFRU5ZTUpOWCtZenFHRXBSc3dVTytVR0JyV29QcW5pQjJqTXp1Yy8wOWtWK0Q2UXNGQ2Fqbis0L3dwdmViRGIiLCJtYWMiOiJkNzMyMjZmNjhhNDE4ODMyMzg2N2MxNmI4YTFkNmFhMDZhYjcwOWQzY2YyYzBlMWM3YjA2Y2U3YTJmZDc3NzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776262099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-241545808 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241545808\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1559486158 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:06:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJOVm4yRDd1VkMzMm80TkVkNkFmVFE9PSIsInZhbHVlIjoiUXRHUTBKNWE5aC9SM1pTWUJoNFZZVU9qUzJ5VzFQS0t2eTlDazVseDl0b0dLcXhFaWpicjF2aVp2OFZ5MGJJZlB0MmZNWGh5bUQwMXFUdHhyaUpkc09KdFRkUjdING5jNnZPcmx5bE9WZzhsZ1c5c1FmY1JUcFFyWDNCQTBjUzlSb1RWdVluZEFPSEtqN1ozcUp5NG43VGN6QVY1WE9GYUJHd2pVOFkvbFZWWEljUUtRU1JGcHZaU001L0orTWY1U1hVR1VTc1gwTWdtYVE1ZGxkbGhPOENUSUxzVnhEcWN3UXJvWUJPcEV5TFE1Wm9mdHM0dVJWWHVZRy9BOGxwSGY2Njk2WUYxK2ZHS1NEbi9FS2E0MFptdGhNU1llRXE1OEdSNzVtUkFLSGt4SUNXWS91YTR2NVZqNkJlMVRCSjlLemNEV3M4RnZteGg4N3BqQkhKTDJEYUhudHU1b2dBWHdlYytVTFZ0eHFtNFNqa3RyaVEzaWtLelA4aVJucW16V1NxYXVIUG9LdkRLdW1zdVhHRFkvcVZmOWdZS05GcFg1RU15UjRvN09SYTRhMW5ZTVJMUGJVbXkyQ2xVaFp4OUNiK3d6UXhhQTN5bHJtMzRsa3pyVlQ1RUZWazk4eWZxVlJvRG1xT1hneW4ya2RMeEtlWXROK0c3TlhvSDF1RmoiLCJtYWMiOiI1ODhkMDhkNzUyNzdkZDUxZDBkNjUxZmNhZDFjZDgyZDRkNDg1ZmY1ZjZiMGM3N2NjODA4ZTY5NGI1ODkwMGE1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:06:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNMSzA1VStZRE42TVJTMXcxZTJwQnc9PSIsInZhbHVlIjoiMFlpUE1KS2RiQlh1cEFZeWZSMGFraGFJVzR0MkVlQk42WUxvOVdNY0VTcnNtUGt5ODJIWS9uMVY3aXpjcWZ2Y3VyV3BsYjM4K1o3MVB3ZFUrTzMyblJSN2dZZFRZSHpxRmZORjduWFMwZHFtME1wMG5ReEczZjBXUWU5dnZQc2dueFJidnkyNEtsYzNKVjhvOHZNYzB0czMrMEVZU1c1eG5wT3RwOXRKL3hHbVBVb01ickVqMytSaGprNHRXOHRlUXgzK2JNVUZtcUlJdXJ3dHp0S0hOMVh6M1Y4bzJoV3c1TzNXU2hLMi9sRHgxWnMzOUdqenQ0aHJqVEJWV24yZFZnS0E3ZytPeWJSbkZuTEsyRjE2RlpsN1J5RDJVY2xmTGlVVUhxWEhEcmwva2VkUGFsS3NMZEJvbmI4aGdtcVlmUTBVV2gxVG93V1MzV21YOVZBaFNjdnRoTzRXcG5ZNiswQ1VPZzlTQlM3SE1GK1F4RVp0V1VPa0xRVG1jVmxIeVZJYkFxakFIVEFiZjVVZmtaUldSZHd1cXp0dVR3V1NBYk5CMmRDRWZMOElxUjJlbUFMVTZydGhOanVCUmFhZmxqSWF3VnV1aVB2cGIrMVZvSzQzci8vVUViQ3JNSlVhVUYzRnU1Wm5FWS9EVTlheVVWVlVOQjRaRmpKT2MvdlciLCJtYWMiOiIzZjNkNmU1NmNmYjkzMzcwNTJhN2EyZDIwZjVmZWFmMmZlODM5NzZhNDI1NmUxNmI3ZTg5YTRlNWY5ZDdmOTU0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:06:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJOVm4yRDd1VkMzMm80TkVkNkFmVFE9PSIsInZhbHVlIjoiUXRHUTBKNWE5aC9SM1pTWUJoNFZZVU9qUzJ5VzFQS0t2eTlDazVseDl0b0dLcXhFaWpicjF2aVp2OFZ5MGJJZlB0MmZNWGh5bUQwMXFUdHhyaUpkc09KdFRkUjdING5jNnZPcmx5bE9WZzhsZ1c5c1FmY1JUcFFyWDNCQTBjUzlSb1RWdVluZEFPSEtqN1ozcUp5NG43VGN6QVY1WE9GYUJHd2pVOFkvbFZWWEljUUtRU1JGcHZaU001L0orTWY1U1hVR1VTc1gwTWdtYVE1ZGxkbGhPOENUSUxzVnhEcWN3UXJvWUJPcEV5TFE1Wm9mdHM0dVJWWHVZRy9BOGxwSGY2Njk2WUYxK2ZHS1NEbi9FS2E0MFptdGhNU1llRXE1OEdSNzVtUkFLSGt4SUNXWS91YTR2NVZqNkJlMVRCSjlLemNEV3M4RnZteGg4N3BqQkhKTDJEYUhudHU1b2dBWHdlYytVTFZ0eHFtNFNqa3RyaVEzaWtLelA4aVJucW16V1NxYXVIUG9LdkRLdW1zdVhHRFkvcVZmOWdZS05GcFg1RU15UjRvN09SYTRhMW5ZTVJMUGJVbXkyQ2xVaFp4OUNiK3d6UXhhQTN5bHJtMzRsa3pyVlQ1RUZWazk4eWZxVlJvRG1xT1hneW4ya2RMeEtlWXROK0c3TlhvSDF1RmoiLCJtYWMiOiI1ODhkMDhkNzUyNzdkZDUxZDBkNjUxZmNhZDFjZDgyZDRkNDg1ZmY1ZjZiMGM3N2NjODA4ZTY5NGI1ODkwMGE1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:06:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNMSzA1VStZRE42TVJTMXcxZTJwQnc9PSIsInZhbHVlIjoiMFlpUE1KS2RiQlh1cEFZeWZSMGFraGFJVzR0MkVlQk42WUxvOVdNY0VTcnNtUGt5ODJIWS9uMVY3aXpjcWZ2Y3VyV3BsYjM4K1o3MVB3ZFUrTzMyblJSN2dZZFRZSHpxRmZORjduWFMwZHFtME1wMG5ReEczZjBXUWU5dnZQc2dueFJidnkyNEtsYzNKVjhvOHZNYzB0czMrMEVZU1c1eG5wT3RwOXRKL3hHbVBVb01ickVqMytSaGprNHRXOHRlUXgzK2JNVUZtcUlJdXJ3dHp0S0hOMVh6M1Y4bzJoV3c1TzNXU2hLMi9sRHgxWnMzOUdqenQ0aHJqVEJWV24yZFZnS0E3ZytPeWJSbkZuTEsyRjE2RlpsN1J5RDJVY2xmTGlVVUhxWEhEcmwva2VkUGFsS3NMZEJvbmI4aGdtcVlmUTBVV2gxVG93V1MzV21YOVZBaFNjdnRoTzRXcG5ZNiswQ1VPZzlTQlM3SE1GK1F4RVp0V1VPa0xRVG1jVmxIeVZJYkFxakFIVEFiZjVVZmtaUldSZHd1cXp0dVR3V1NBYk5CMmRDRWZMOElxUjJlbUFMVTZydGhOanVCUmFhZmxqSWF3VnV1aVB2cGIrMVZvSzQzci8vVUViQ3JNSlVhVUYzRnU1Wm5FWS9EVTlheVVWVlVOQjRaRmpKT2MvdlciLCJtYWMiOiIzZjNkNmU1NmNmYjkzMzcwNTJhN2EyZDIwZjVmZWFmMmZlODM5NzZhNDI1NmUxNmI3ZTg5YTRlNWY5ZDdmOTU0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:06:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559486158\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-624207032 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624207032\", {\"maxDepth\":0})</script>\n"}}