{"__meta": {"id": "X03e854811f8ef69023da570fa010512d", "datetime": "2025-06-30 16:10:03", "utime": **********.319976, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299802.878584, "end": **********.319992, "duration": 0.4414081573486328, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1751299802.878584, "relative_start": 0, "end": **********.238778, "relative_end": **********.238778, "duration": 0.36019420623779297, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238793, "relative_start": 0.36020898818969727, "end": **********.319994, "relative_end": 1.9073486328125e-06, "duration": 0.08120107650756836, "duration_str": "81.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00799, "accumulated_duration_str": "7.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.275582, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.025}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.284886, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.025, "width_percent": 5.006}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.298451, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 25.031, "width_percent": 8.26}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.300403, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 33.292, "width_percent": 6.008}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.305712, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 39.299, "width_percent": 38.048}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3109689, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 77.347, "width_percent": 22.653}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1403905837 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403905837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304347, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1887981057 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1887981057\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-505836581 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-505836581\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-969687159 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-969687159\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1852929333 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299801140%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhKWWR1NERtbW80MW81a0k1eGVOU2c9PSIsInZhbHVlIjoiRHV2NnZpTHVIYkFhVlVtVzdzM3hSemNxdWNJTzJ3MU55VW14aW5wYU5mS1hkZ0hFa3JEZldYTlUwTlVueUduTHBpakVHN1dSeXBiZ0FmaEcxZWhYWXB6aDlscGdqSmlQUzJ2Q3pyVkJBZkl2SGhhTGhjd2txRFNqNmRWcDkxNGJZWnVhNytIMDVkZWgzazl0S056K01tNGI5eGd4L0I1YmNXY3Y4S09laTFTNU96bDRCN01JTUVGbVZJT1oyMlNmREZIeVFEVkx5aE5WRTVKM2JHd1RmUDdWYXNTdmVEcExmZm1JeUJiZmdXYmdLQjNtWlIzOU9QemU5aUFUNFRkczM0VDIzOU9pU1ZtRWcvRGJDbTc5UkVnOGhaS0JMd2ZtaXZTbkwxVC9EWTRKVCs0NFh6YWNxdWZpenVkVGllTXdjb3ZBZHZHb2ZlaXQzc1lRdnVYMUsyT0FhbDEwS0RzNzlJQkhTdDdBMFRiN0tZUisyNWRPQ3g4aklPc0dSbzdYSmhhMGMwc052enlLci9yR3VMeW84MW5CQVhteVlrcFJBeGNMVlhha0R5UkZxdTRhS1l6UjExL0l4R3VPbkYvbXAzcjZhL2N3SE5sNWp6YkgvKzBQeFR0QmRjT3FYdHFMNHhJaytQMFdiT0N4TmRYNlNmazhnMFpHWWpFS1pwTG4iLCJtYWMiOiIxMzk3NTMwMzkyZjY2NzY3YzhiZjI3NGYzMjQ1NWUwZDFiOTk3NDU1NDdlM2E1ZDBhNzA1NWJjYzhmNjA2MzE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRaTlg0Mi80anBWTkJacjN6aVlQUmc9PSIsInZhbHVlIjoiWElrUUJlbmV2TTM4L3FrcTZ2c293QStOREo1MUptWnozNGxZWVFxRGRDV1R0ckdGcGRWWFcrR2o3Zzl0TlM1dTV5cnRNRVFhM2hNN1BkeEg2aWp0dXQvRXVqRUpzUlR5SE8rZStYL280WVRHTDk1R3lXS0RENnlNYWcrSFNZd1pVTTVyUnE4ZklERXcvektGVmswZFh3c1QvUjd5N2xnZkZyQnVpMUMrQ3lQenNVMzNOYzRLVC9WSGRVQVBSQS8xZlpMY0NqR3N6Y3ovNGRObXBpMVBYTnBITFhNZ0RRQVVJYTZoUEc4UE1HL3RDbWgyYWEweXNrdHdYbW5Hc0RVUHgzbFlZOHRhM2hWTVpqbXgxNUJQd3ZRNGhUNTVSOFlDWjB0UDhCTkRxSWh3M3pqRzdueitqTkgxWDFZbjNPalhnSlhJQXNxNmZ6b3BQZTZFSFdLZVNDQ0FCYnUwbi9xVlp1eVA3NjltKzlIZkpMWFBuTlg3RGZRN1hyUEhrYzBTbm8wSDJXamw3K1kwbjJ5SjMyalQyWDlSSHpWV2tPOXhweE5WeGFDN0ZZSGMyNEhGTzdub2FJeHJoU1JyZjBadFhrUTN4WFhhcHpnSE1oV1JwSXJIL0lVTDJOTlh3TzVwdlZ1YytvTldROHJFRkdKZ0ZwVzNtaisvTVVQM3NDNWEiLCJtYWMiOiJhNjgwOWM1OTkwMjZiZjY4ZmM5YmJkMzMzMDExMjljOTRlNWYwZmJiMzZlMTVkM2Q1MWQ4YmZmMjE5ZmRiZDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852929333\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1346117856 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346117856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-794723221 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRwcEFoUDM3SGkyTkw4NFJ2bkluNWc9PSIsInZhbHVlIjoiSGRhb1htcnQwRmVWTlFzZGtyRFFPdlFEK1QxbEZpblUwRWFVSEFWN283bmlwNmJIbWowdmJRNzNxQldhdVhROW0rTTRhZWhvZ1NVdEZUQmw2UHYrekkvQkxSRWpmUFQ2M0FaSlI0eHpLUURDZUFpQjJGbFl4OUNLSWRYM2JpMFJSbURVeWN1RksxbWxlT2tuTGpVcCtNZHAwUjJYTlI5Mk5CdHlsUmUybVp6OE5zZ2IyaGd4TEIyd05vK2pPWTU4QUFCOFJvVVMyNURoeUdUQ2dVL25uUGl1aXd2a0F4Q3NMOFZlTkZPNWJydGphTTVOUFNoYTNWQ0NSalAreS85OFYvOEZGTGw1aUs1enNDRWhOMVJiSnBtZXN4MUlEMCtMZXBOZ3IyL0dkWFRSWWVpL0ljYUY3eWo1QkpYbVZvRldnZzkvN0lpMkNoSTNZR2tYWXNxUlNyNGJqQUlQNGxYbSt5VjNBVDc4eklBOXR6Ny9FK2E3OW9sWkxYVHNodFZZWEx6ZEhxS296ampYMFlwRk1zMHM5Z3dValk2ZXN5YnVTMEg0b2JNSWFGcnRqK05yek5UT3NNRDVnZ2hyUnp6Y1ovdzcvSDRZLzhSaGk1VUNORjZxMEhqMGZicytXNi9oTkZmZC90Q3R1eUFCdUlTRFV0SVdZUFlBdFA0WnZ3OEoiLCJtYWMiOiIxYTRlMjVkN2U2MTVjMTRkNDEyYjkyNTE1MWM3YzI0ZDVlZjdhZGNiNWIwNGY3ODU0ZjMxZmE4NTMzNjcxMGNiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklrazU3b3FIMUQra3dVQ1JZclk1WUE9PSIsInZhbHVlIjoiRkY2UFVHUlJKT1BRdnh0d2Vnc2JYY2lvTHNzKzJUSXBLS05HeHNJc281alVLVnY1ckFyUmlFalFCWHVmQXpzOW52MXpPOW5RLzJWeHYyalhnU0trYk0rK0RxaFhqRW9Ma2RQRmFHaHBoa0NDalZlUlEzL08rd1EzUzBtMlowcWpjNTNwL1ltZGxSVFVOc0Yrd2cvWHY1K0s5eG5CWW5UUVRENEdUOEt1TWlCUUlaMlJOOVJzWGY4aHBKd0ZvMjE5R2w5cDBRYkgrZm5uZml2RGtRZ0hIQzVwM3lQS1dOU2NZV04wN1VidlVPcXNhWmFYcktmWnQxbERUS2pyTXhlMDBKd3BGWC9wWW5POUd5eGZkaFIzc0VNTVlTWnM3QlhWZ1FTSW5DVzBVS3ovdndGUitRLzVON0xzbUdYRHUvRlBsZFFNaHk0eEhJZzdwZFJWZEZ0cTB3RUp2WWQ5dWYrRWU5Q2RqSlNQemVYVFZMSFVzRHFCZVlYR21PL25ab09zbmVJSmtNQmc4VXhTMldmUFNxaUVYN0ZycGI3SzE5dnhJcWdQMlJaT251b0RUdHRRNXhuUktJYVhZS2VuMVN6TEdDZ0JNNFZEcmxtTDZaMlNBTWJsZmpKdmxOL2dEdUxNbzVGUFN0SUVJVTBRWHo3dkltdTJ1UWlkYm5YN3ByQjgiLCJtYWMiOiIwNTc3YWYxNDUyYmExYzYxMDRlMTBlYzVjNDVjMDQ1OWRiMjAwZjdmMDg0MzY3MjQ5OWU3MTRjNmFlZmI3MzA3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRwcEFoUDM3SGkyTkw4NFJ2bkluNWc9PSIsInZhbHVlIjoiSGRhb1htcnQwRmVWTlFzZGtyRFFPdlFEK1QxbEZpblUwRWFVSEFWN283bmlwNmJIbWowdmJRNzNxQldhdVhROW0rTTRhZWhvZ1NVdEZUQmw2UHYrekkvQkxSRWpmUFQ2M0FaSlI0eHpLUURDZUFpQjJGbFl4OUNLSWRYM2JpMFJSbURVeWN1RksxbWxlT2tuTGpVcCtNZHAwUjJYTlI5Mk5CdHlsUmUybVp6OE5zZ2IyaGd4TEIyd05vK2pPWTU4QUFCOFJvVVMyNURoeUdUQ2dVL25uUGl1aXd2a0F4Q3NMOFZlTkZPNWJydGphTTVOUFNoYTNWQ0NSalAreS85OFYvOEZGTGw1aUs1enNDRWhOMVJiSnBtZXN4MUlEMCtMZXBOZ3IyL0dkWFRSWWVpL0ljYUY3eWo1QkpYbVZvRldnZzkvN0lpMkNoSTNZR2tYWXNxUlNyNGJqQUlQNGxYbSt5VjNBVDc4eklBOXR6Ny9FK2E3OW9sWkxYVHNodFZZWEx6ZEhxS296ampYMFlwRk1zMHM5Z3dValk2ZXN5YnVTMEg0b2JNSWFGcnRqK05yek5UT3NNRDVnZ2hyUnp6Y1ovdzcvSDRZLzhSaGk1VUNORjZxMEhqMGZicytXNi9oTkZmZC90Q3R1eUFCdUlTRFV0SVdZUFlBdFA0WnZ3OEoiLCJtYWMiOiIxYTRlMjVkN2U2MTVjMTRkNDEyYjkyNTE1MWM3YzI0ZDVlZjdhZGNiNWIwNGY3ODU0ZjMxZmE4NTMzNjcxMGNiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklrazU3b3FIMUQra3dVQ1JZclk1WUE9PSIsInZhbHVlIjoiRkY2UFVHUlJKT1BRdnh0d2Vnc2JYY2lvTHNzKzJUSXBLS05HeHNJc281alVLVnY1ckFyUmlFalFCWHVmQXpzOW52MXpPOW5RLzJWeHYyalhnU0trYk0rK0RxaFhqRW9Ma2RQRmFHaHBoa0NDalZlUlEzL08rd1EzUzBtMlowcWpjNTNwL1ltZGxSVFVOc0Yrd2cvWHY1K0s5eG5CWW5UUVRENEdUOEt1TWlCUUlaMlJOOVJzWGY4aHBKd0ZvMjE5R2w5cDBRYkgrZm5uZml2RGtRZ0hIQzVwM3lQS1dOU2NZV04wN1VidlVPcXNhWmFYcktmWnQxbERUS2pyTXhlMDBKd3BGWC9wWW5POUd5eGZkaFIzc0VNTVlTWnM3QlhWZ1FTSW5DVzBVS3ovdndGUitRLzVON0xzbUdYRHUvRlBsZFFNaHk0eEhJZzdwZFJWZEZ0cTB3RUp2WWQ5dWYrRWU5Q2RqSlNQemVYVFZMSFVzRHFCZVlYR21PL25ab09zbmVJSmtNQmc4VXhTMldmUFNxaUVYN0ZycGI3SzE5dnhJcWdQMlJaT251b0RUdHRRNXhuUktJYVhZS2VuMVN6TEdDZ0JNNFZEcmxtTDZaMlNBTWJsZmpKdmxOL2dEdUxNbzVGUFN0SUVJVTBRWHo3dkltdTJ1UWlkYm5YN3ByQjgiLCJtYWMiOiIwNTc3YWYxNDUyYmExYzYxMDRlMTBlYzVjNDVjMDQ1OWRiMjAwZjdmMDg0MzY3MjQ5OWU3MTRjNmFlZmI3MzA3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794723221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-732778286 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732778286\", {\"maxDepth\":0})</script>\n"}}