{"__meta": {"id": "Xe9ee6bd07c4fcd952bc16d91dd347cc0", "datetime": "2025-06-30 16:07:34", "utime": **********.77566, "method": "POST", "uri": "/branch-cash-management/close-shift/60", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.25798, "end": **********.775681, "duration": 0.5177009105682373, "duration_str": "518ms", "measures": [{"label": "Booting", "start": **********.25798, "relative_start": 0, "end": **********.635002, "relative_end": **********.635002, "duration": 0.37702178955078125, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.635011, "relative_start": 0.3770308494567871, "end": **********.775683, "relative_end": 1.9073486328125e-06, "duration": 0.140671968460083, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45677512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST branch-cash-management/close-shift/{id}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\BranchCashManagementController@closeShift", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "branch.cash.management.close.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=151\" onclick=\"\">app/Http/Controllers/BranchCashManagementController.php:151-193</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02468, "accumulated_duration_str": "24.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.667011, "duration": 0.02229, "duration_str": "22.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.316}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.698272, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.316, "width_percent": 2.026}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 154}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.702506, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:154", "source": "app/Http/Controllers/BranchCashManagementController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=154", "ajax": false, "filename": "BranchCashManagementController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 92.342, "width_percent": 0}, {"sql": "select * from `shifts` where `shifts`.`id` = '60' and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["60"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 157}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.703766, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:157", "source": "app/Http/Controllers/BranchCashManagementController.php:157", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=157", "ajax": false, "filename": "BranchCashManagementController.php", "line": "157"}, "connection": "kdmkjkqknb", "start_percent": 92.342, "width_percent": 1.985}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 16:07:34', `closed_by` = 15, `shifts`.`updated_at` = '2025-06-30 16:07:34' where `id` = 60", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:07:34", "15", "2025-06-30 16:07:34", "60"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7057688, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:168", "source": "app/Http/Controllers/BranchCashManagementController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=168", "ajax": false, "filename": "BranchCashManagementController.php", "line": "168"}, "connection": "kdmkjkqknb", "start_percent": 94.327, "width_percent": 1.702}, {"sql": "select * from `users` where `users`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7076242, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:171", "source": "app/Http/Controllers/BranchCashManagementController.php:171", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=171", "ajax": false, "filename": "BranchCashManagementController.php", "line": "171"}, "connection": "kdmkjkqknb", "start_percent": 96.029, "width_percent": 1.135}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 16:07:34' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:07:34", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 174}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.709567, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:174", "source": "app/Http/Controllers/BranchCashManagementController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=174", "ajax": false, "filename": "BranchCashManagementController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 97.164, "width_percent": 2.836}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 177}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.768027, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:177", "source": "app/Http/Controllers/BranchCashManagementController.php:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=177", "ajax": false, "filename": "BranchCashManagementController.php", "line": "177"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/branch-cash-management/close-shift/60", "status_code": "<pre class=sf-dump id=sf-dump-253425008 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-253425008\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-877439354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-877439354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1342713099 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342713099\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1763561622 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjF2TkFzMkFyUXJGdE56WFRVS2R0N0E9PSIsInZhbHVlIjoicmNMbWl2ZlllaWNXWFpLSzh1VmlNak1lYkJ6bjdIcys5RzFHc2VZRVZYZmpnOStBdmZiYjVSTlhreC8rN1lTbGh4VTYxT2xjZHdNMXIvdFJTWGRpbDlYQmZMelNyMEpEOURTODlVaGV6UUxQamxYSGVYOElvM1dla2l6bEl0dU5FTlAyZXcwdXAwd3doV1lBdmt0SHp2NnVJa1hQeWdwTks2NTFUS2JRNG82VzhxOEF6eG1MUS9BeUpjM3JIS01oZEFTTEVHWkxGN0d2UmxJeFdwU0tFWnVZR3E3cWtLajkwbUE3OWlQQzUwYklCZmxIa29CSzhJZXlaaEgyZGlqM3ovYlVreVlDWFFzWXRCWFVxaDc0ekZzVVhBUEczd01CdisyMXRzVlNmQW1sbHVLbDNzb3hBTG5ZdnZqUXBzRmhOVEFkZkxZUEtQcGs2YnVpS3RSWlh5cjV6S0dVTVNYNE5lNEZvS0tRK21BY0syNG5FaU9oUC84WE1ON2pxdDFONUc1MnM3MzVxcDBLWU15OEtLR0puVTMyTU42NW82SHFOWkliZVZmMFUxZWxRVGo0SEhOT2lQUGxIUDBUU2tEZjVOcnpDQmlZdEVHTlZJSHc1UW94bk9IVGd0QzBvRjNzckovaW41K2NqN25xb1VlWFVmcUQ3NjdoMEhpVlRDNTEiLCJtYWMiOiI5NDZkYWU2NjcxMWQwNTM3YjIxNjM3YzBkNGU5ZDUwMzYyNWZmMmIyY2QzYzE0NTZkNzIzZTVmMmVjOGFjYTk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkllNDRxeXNOK2g5WE52M1RkYWY1NkE9PSIsInZhbHVlIjoiR0g0VjFXQkNwMVIzcjJxTHhrU1RqQ0phZ2x3UVVyV1hsOWZDeXNuT2VkNzRMMGEwSnVFZ0VnSmxBY083dzRrVFFtUnYxUks3U043MlFpUXh6RkY4MVhwUnp6NmdNWUZ3aHkvMG9uVXM2TzUvUWlZQkFYeEpCZk83R0JMQ1cxbU5CU3NDMGYxdEsrVnRjSHJMWlVHYURxOFA3MEZEUEswakl5SmtpN0wvZXpoNUhtdm9ac01RQ3lFbjFxTG9Za1VvV0FITkxBT2FLSERWdjFJeDF2eGg1OHJucWlHL0I0QTROUDBWeVBuaklPbWJRVXhQUnhXd2owNi91U3laTnNhaGZLc0V2Tk9aY0grcDVHNDdxNzlyMHVQdGJjUUhSS3VORlFMeTBBMDFUa2dDK09WMTU4VzVKY2VFRFR0NDJpaWpCNDF2S1NtdFFxSURBamRYakNUYnBVT0w4QXpDK1I0Y2pEd3lmMncrNm1aZ2ZIdXRPTVU4bGxGL0pLbXdoWW11OG1rWFdtRjUzOWk0WEMyNTNSTjJOWUpEaXAxZ0pJSUI5OUFWYjBKUUFiS3J5ckFYZFg3TjBHQlZISm5SUFBqMmVTUFNmYjFiSE9wSmpaNjl3OGIzdm1na2s1TFVUak5MYjJUazVCN3pLeVA2eGpqaThuYU5jbWM2NnR5OEpvUTIiLCJtYWMiOiI0OGZkNzU5ZWY1NDYyZWVlODE4MjA1NTFmNDVmMjA0ZWZhNTIzNzQyMjcxYjliYzc3N2I4NWIzNjQwYjdhZGE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763561622\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-86851231 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86851231\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1828866890 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImoxdFA5d0ZXSjNKZlFWV1VXeVlqelE9PSIsInZhbHVlIjoiUUoxTVB6Y2FGM2hxa0ZPL1o0WDN5QkpaMVAxeHJKNWltNkZaKy9pdlJHU05jM1IxQzRaWHoxWkY2T3RVWCtzd0hpMzY5bGMyeTZZaTZDUFNaZEx2Ukt2Yjh1bmJsbi9EamRSUkpTM01hVlp3NXdBQ2txdTFndC9oQ3JQS3I2eXVxSkFPQlpURzVUVDZBMkgvd091UmJBNVhqWlVDYVgxQkw1bmhNV1QyNVQyQUlUcEZ5NlpDcDNBaEpGUC9WdTE3SlBUSWFubi9Xa2UzZ2VnamRUeFFzSUJrSE1vWXVPNlhBdmc4RkpmcHdia3dBMjJIU2hJUUQ4OTJHL2NYN0V0Rkw2bTFnemE3WGczM1VDNEFIdnIrZWEyQm5RdExzTEM1ZXZ2aFpJNTV1T3FrdkVRUGE5SFpSWXJKZEpVcUozUVBrUmx2MTlnbjY3VWlxVHVTaHB1b3NGc0NCVFFQNHczcVpyUGJPa2FDR1BGMExPcDZtajZ4d3ZwVVJhRTVPZWs4TWc5OE4vMGFETFh0UGpKZVVjWSs0Q2xCcGQxai9QRVBreS8zZEFLNWtVL1RTdUtpWTZuWU1TeXJoSTRadGdvTVJtWGJXMzFROG96b1pmaVpxQnJxM2NUQlB3K0pxOWZ2OVlQN1ZuOHRNVE1MbC81NUtDNlE2Wmw5QUViN3N5KzQiLCJtYWMiOiJjMzhiNTY2MzY4NjZkZDdmNTVlNDBiOGMxNjhiZWI4NGI2ZmI0YjVjNTY5MWJmM2U4ZDFhZjQzYTliYTlhMDNiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNHRS9ESGttNGN0Zzk1S0xyQW52aWc9PSIsInZhbHVlIjoiY080UDJSYmlnSk5SUXdWSkZKYU9RbThUT1ZSdCtnS1hzVWx6czU3bDdpa1RyTlcrUXZvV2RXbHd1NXdQZllvTXNrZ25iQmZNMDhPRjFMakowMzZJeEo4Kzh4Zm5xTjVkSURDYlB2blZDVFFtSkpvajUxQkZWMis4WEtvN1JVdjk0WloxcTJBbWFZaEJHR01sdHRIL3hDVURPenlNZkFVWnpwWk5TdG9nMEVZSCtSQWowNDVkdUgyRVVyVHRNR0ZjUVJkZ1psWFpKMUI2VDhXWFRMQzZFcGRhODQ0dU4wRHlPVm1MeEVXS01zZWlYUyt2aHlKVUhIMWJqVjRwdWdaSHRtVjlKN2V1STBWWVR0QmczRmszV2dMbHpsYTNpRExIMFU2NkliRVF3ZkVUMGxBMU1xT3ZhdUV6MjZTY0hIR29FVEtBWFNOZ1lDOEM1dGhnZnZ4djh6eVRJOHFmT05aVStGWU05Qks4Yzk2alltcXVEOUh3c2I4Z0FoLzlNVEQwRW8rMzlMM2UwT0J4eURNY2M3MjBhR3R3V0lqVStxd2wxMkwvZXBEc2ZIZU92K3UvNFEzMVJJTUV0WVJEUTRiUE5wWUpYeHBnWDdWbmkreTd2R25yek9QSzAyTE9kTjBsUHhmOGtNRS9zaUo2cWxGd3Q4alBsZ2dTci9nNENUcVAiLCJtYWMiOiIzNDEwMGQ4MWJiZTQ3MDZmNTJhZjU1OWQ2YmZkYTc4M2EwOTE3ZmFlYjYyYTQ5NjgxNzFjMjZkYjY3NjRiNmEyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImoxdFA5d0ZXSjNKZlFWV1VXeVlqelE9PSIsInZhbHVlIjoiUUoxTVB6Y2FGM2hxa0ZPL1o0WDN5QkpaMVAxeHJKNWltNkZaKy9pdlJHU05jM1IxQzRaWHoxWkY2T3RVWCtzd0hpMzY5bGMyeTZZaTZDUFNaZEx2Ukt2Yjh1bmJsbi9EamRSUkpTM01hVlp3NXdBQ2txdTFndC9oQ3JQS3I2eXVxSkFPQlpURzVUVDZBMkgvd091UmJBNVhqWlVDYVgxQkw1bmhNV1QyNVQyQUlUcEZ5NlpDcDNBaEpGUC9WdTE3SlBUSWFubi9Xa2UzZ2VnamRUeFFzSUJrSE1vWXVPNlhBdmc4RkpmcHdia3dBMjJIU2hJUUQ4OTJHL2NYN0V0Rkw2bTFnemE3WGczM1VDNEFIdnIrZWEyQm5RdExzTEM1ZXZ2aFpJNTV1T3FrdkVRUGE5SFpSWXJKZEpVcUozUVBrUmx2MTlnbjY3VWlxVHVTaHB1b3NGc0NCVFFQNHczcVpyUGJPa2FDR1BGMExPcDZtajZ4d3ZwVVJhRTVPZWs4TWc5OE4vMGFETFh0UGpKZVVjWSs0Q2xCcGQxai9QRVBreS8zZEFLNWtVL1RTdUtpWTZuWU1TeXJoSTRadGdvTVJtWGJXMzFROG96b1pmaVpxQnJxM2NUQlB3K0pxOWZ2OVlQN1ZuOHRNVE1MbC81NUtDNlE2Wmw5QUViN3N5KzQiLCJtYWMiOiJjMzhiNTY2MzY4NjZkZDdmNTVlNDBiOGMxNjhiZWI4NGI2ZmI0YjVjNTY5MWJmM2U4ZDFhZjQzYTliYTlhMDNiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNHRS9ESGttNGN0Zzk1S0xyQW52aWc9PSIsInZhbHVlIjoiY080UDJSYmlnSk5SUXdWSkZKYU9RbThUT1ZSdCtnS1hzVWx6czU3bDdpa1RyTlcrUXZvV2RXbHd1NXdQZllvTXNrZ25iQmZNMDhPRjFMakowMzZJeEo4Kzh4Zm5xTjVkSURDYlB2blZDVFFtSkpvajUxQkZWMis4WEtvN1JVdjk0WloxcTJBbWFZaEJHR01sdHRIL3hDVURPenlNZkFVWnpwWk5TdG9nMEVZSCtSQWowNDVkdUgyRVVyVHRNR0ZjUVJkZ1psWFpKMUI2VDhXWFRMQzZFcGRhODQ0dU4wRHlPVm1MeEVXS01zZWlYUyt2aHlKVUhIMWJqVjRwdWdaSHRtVjlKN2V1STBWWVR0QmczRmszV2dMbHpsYTNpRExIMFU2NkliRVF3ZkVUMGxBMU1xT3ZhdUV6MjZTY0hIR29FVEtBWFNOZ1lDOEM1dGhnZnZ4djh6eVRJOHFmT05aVStGWU05Qks4Yzk2alltcXVEOUh3c2I4Z0FoLzlNVEQwRW8rMzlMM2UwT0J4eURNY2M3MjBhR3R3V0lqVStxd2wxMkwvZXBEc2ZIZU92K3UvNFEzMVJJTUV0WVJEUTRiUE5wWUpYeHBnWDdWbmkreTd2R25yek9QSzAyTE9kTjBsUHhmOGtNRS9zaUo2cWxGd3Q4alBsZ2dTci9nNENUcVAiLCJtYWMiOiIzNDEwMGQ4MWJiZTQ3MDZmNTJhZjU1OWQ2YmZkYTc4M2EwOTE3ZmFlYjYyYTQ5NjgxNzFjMjZkYjY3NjRiNmEyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828866890\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1426811022 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426811022\", {\"maxDepth\":0})</script>\n"}}