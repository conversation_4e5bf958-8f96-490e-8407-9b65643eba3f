{"__meta": {"id": "X1ab502a929b6790ff630468d11ccd06a", "datetime": "2025-06-30 18:10:49", "utime": **********.840392, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.425978, "end": **********.840408, "duration": 0.4144301414489746, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.425978, "relative_start": 0, "end": **********.780217, "relative_end": **********.780217, "duration": 0.35423898696899414, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.780225, "relative_start": 0.3542470932006836, "end": **********.84041, "relative_end": 1.9073486328125e-06, "duration": 0.06018495559692383, "duration_str": "60.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43888024, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02106, "accumulated_duration_str": "21.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8102999, "duration": 0.0208, "duration_str": "20.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.765}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.83406, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 98.765, "width_percent": 1.235}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2143 => array:8 [\n    \"name\" => \"STC calling card200\"\n    \"quantity\" => 1\n    \"price\" => \"230.00\"\n    \"tax\" => 0\n    \"subtotal\" => 230.0\n    \"id\" => \"2143\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1827078542 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1827078542\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-6868877 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9xa2lSeVNleFdITU1lZ0lFTHFVYmc9PSIsInZhbHVlIjoiYk80SlRVakpaVUUwRVAwRnZwTk0zb210K3hYQkhlanpVbUpDYWVnYnVLVGh5b2Y2Mm0vY3c1d2x6cEFkblo0c2g4UWVDcFg5dGFxblF2Z25rQnQwRU16Q1Z1S282ZHlkVVp4bWFVS2t6RE5JL3RoQXNqckZKNjlmSVRwWVJ4d3U5UnpTMnp5OGk3b082QVhuenJWNWs0OUNSRWtYVm90MDFuWnZCUkk1SFlQWGRudk5NKzg5aXBYU3FGcVgwSldtRVdUYnM5REFXNGJDVnFFWnFHUHlOVVRjSStUK1o0U3dJa2pxSGt5UDdRMFRPc2E3QXUxeGdMNmh1WE1jZWphUit4L3h6RWlHVmdZSnpwajJUcmlsUDRpUXhnMkVHYmJ0S3dvekdiMlYyUGNtOHM2WFQvc1U5eDV0cHErSjJOZVN2bEh5VWkyanhEQWdORktra21rSS9ha3VUMFQ1dTRBaUdFc20rcEh4S0FWR2Zsdm9JMmpsdmxIaHBnRGVidU5pbkhQSVZIYmZmQWJHTlJ6Nnp1bjZwOVZCOGtUOHJCYnNGaXRxakhhRWcyUFJtbDg4ZnROTG1nSXBpeWVHVVlwc2kwS1V0Wmc0YkhyTi9iUjAxM1AvRUtXNUM1SFUydWMrVFk3NzNMdDVkTlkxakcweWVPVkpGdHUwY24xNHBWQngiLCJtYWMiOiIwYmEyY2Y3YWQxNGFlZjU2NjQ1ZWQ0MmU3NjExYjJmMjIxYTJiNWEyNTNlNzExMjVjZTViNmUxNDE0NDU4Zjc1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVaTlJrWFlORlJtcDkvc2pEaDlTQVE9PSIsInZhbHVlIjoiRFJnbVg3akFmazNDZWc1TkNvRURRVk03Y09aZDRQZkRUQ1NwZDhBSU1GY0JVZWw5ZGMydGh3aHBMUHAyZ2NEVzhvQlErYkl3M3Z6WWhmR056UnJWYk5rR21lZ3pTQWd1MjdMTmxHd0VHY2dWd2x3cDIvcXdiUkorczBrZ1QwMmg0eTdXZ1pOU2pSNHdQOTFGMUN4TnN5em1xUmx0R09ERnJaTzNxbnQvTWNsMUdKdUNCQTFqZWR4MlZ6N2hONVZLaHpDdmFrTXpneFNWUmhrVzd2M01SYkQ0aXFmOG5UYk51Y0VTbWdiY0YvdndhcEhqQ0ZMVU5MRU5KWFQxeFBQU2ZHSytoNWZIQmdwbCswR0xNWXcvZFUvdE9wYk1XMXU0MVlVTE55UmFkU1BWQXZsbjBYZzFoU3lFeFlEbDRsclBDNFR1S29vVkFhOU9MRU8yelVqbnFqcElucHdwNG9lY3JGZExNUjFLTEVZaFN2Qm9ZaFRDRFZOL0p5Zk5HTWtKYmttV0NXNUk1WkFWYmZoejk3U0hFWVhScVlENmp5WmQvSEZybjFYMVhud2R0OGtwUjVVLytjdGw2ZXdGL1JiYysvZ1JnUXVUYTJET0syODc0aXRnSWIvN3lYSzRtQitiT0l3aUx4Rm84UjArclhwS3hqZndxWG1WRHAzZjdZaFEiLCJtYWMiOiJkOWI1NzJlNzczYjUyYzk5MjI4OTFhMDhjYmE3OWNhYzZjYTlhOWZhMTRjNDQ2NDFlOTQ4ZGMyMWU3N2E0YmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6868877\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-257592131 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257592131\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-931755141 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJaOGQxSkM2OXVqdHdramtvbDNIVnc9PSIsInZhbHVlIjoiWkcvOGJjY3I1UG0wNjYrSHBNNUZKOHhIamZkSVJaeTA5RWNES3BHajRGdTZYZW1iYkhPNE0xUHZWSWRsY0xleVh6ek5qOEdpYmZYM2RlOGdmRzI5enlpaEEvOEVaa2V1RGprTExRb2ZCcUFNWUQyMVFPY25jV1F4TzFXOXBzWFNMdGdTWFFOMzY0UDNEZjlSUjFXQlZJS3dZUDMzZkxJN2I4eUkyVzBJMklxYjgxUFdhL3E3VWpYZlBhc1JmWXA0ZlRHczJsVVhEbGFjNThBZWNhbFB0Nng5SzZJN1gzV3AvaDIySjFNRDVSWnd4eXlxazJrNGRxZlZ5czVRRE90QUtJTE1mMUZpejl0NnIwb0djZXgvRnJUUFkveWpNNyszaFZmZm9xYXJrOEs2RXQ0OUQ4ekUzZmRUZHJDZFRUMmdQT2NzK3hPblY3bFZ3cGxMbkhKNk8zY1ZiKzhsbVc2YTBEWkkwSGVnQlFKOGVtRVpKd2c0bGcyQk5NUW1NZUdxNGdVWkJBY1RoWCtSR0l3bm9YS3I4VFM0bXV4cnVsaFRKWTZUcnpCUU8rTzhTNVZLY01PTWEzelMxUkdpNTZ5YUpTOGFQRTZ2RVpaTk9JaFRac1lGRlRQZTZwc1o2UFJQOEVJc0JQQ0VIeFNUTWQ3VHRZR3V2Qkp0dDExb0xGQWoiLCJtYWMiOiI3ZjA5NmRiZjJhNTliMjBjNGM0Njg1NTNkMzJkZDMyODliM2I4Y2UxZTU1N2U4OTcxNmUxYWU2NjIzN2M0YWMyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii82OWpsbDZ5MmNpVkZicmU4OWROYmc9PSIsInZhbHVlIjoiWWpvR2R3TU1YL1hzRm50STFrS2U3cFlNL2VGb210TTkwOHdqeTZEV2VSTmRNNG1oZEpJSzNrT2Exb2FwODFCaW1CMFFwTHAzajRWbkhWRzJOYld6QUVqSDYvM2FwVkprbzBhUVJsRjBGWmxScE9BcnRHT3ZGUVJySmhvL1hwOVBzbUNHdGQvYng4dkl5ZnFwYXl0MDJCMndPbmhjK0d4aUtQRUNpdkdJNXhIUC9EbUN6QStHYk1tMzQ4T2F4Um9OYUtFdWFEdnhKY0NGR2VkSFJLUUhCMndFSnpPUGlPL3ppS2h4VVk2Vy9HQU5NZXlTa3R4OVZ4bzZ0L2U1R0ZkelhoNFFiOWRtME0yd1pIZHdKektOUDJYM3hvVUVYQ21xZVJGRGx2cmFDK0NZVDZmdmJPazVMUnVDdGUxS2xiakY3RVRmckRuYzB3WVI1L3JIeEoyUVBmZnJSUlk1akMxRklhajBoaW8yR25zd3E1eng1THdaNEk3VVNWaVRsWWN5eWd0aWk1ODRML3MzZ0VHQ05oT0xoSTVBRXFxMFF1NzAzeEpUemNBdTZ1Z2tKeUpveWxKZUNhVUIxTHM0VHZ3RzZ0VW5kNEFxVEtUTkRaQzRrVUMyaGpqcy9vcmtNZ2dPV1Exd1R5WXgrNFZXYXNCRjkyOGdSMk5LUEFHbkNMSUgiLCJtYWMiOiIzMjVlYjYxOTg5YWMxNTcwZTlkZTc2OTQ3YWJiYzQwNmQ4ZmVlN2E2YWVkYmFlZTIxOTY0NDExZmM1OTg1ZGZhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJaOGQxSkM2OXVqdHdramtvbDNIVnc9PSIsInZhbHVlIjoiWkcvOGJjY3I1UG0wNjYrSHBNNUZKOHhIamZkSVJaeTA5RWNES3BHajRGdTZYZW1iYkhPNE0xUHZWSWRsY0xleVh6ek5qOEdpYmZYM2RlOGdmRzI5enlpaEEvOEVaa2V1RGprTExRb2ZCcUFNWUQyMVFPY25jV1F4TzFXOXBzWFNMdGdTWFFOMzY0UDNEZjlSUjFXQlZJS3dZUDMzZkxJN2I4eUkyVzBJMklxYjgxUFdhL3E3VWpYZlBhc1JmWXA0ZlRHczJsVVhEbGFjNThBZWNhbFB0Nng5SzZJN1gzV3AvaDIySjFNRDVSWnd4eXlxazJrNGRxZlZ5czVRRE90QUtJTE1mMUZpejl0NnIwb0djZXgvRnJUUFkveWpNNyszaFZmZm9xYXJrOEs2RXQ0OUQ4ekUzZmRUZHJDZFRUMmdQT2NzK3hPblY3bFZ3cGxMbkhKNk8zY1ZiKzhsbVc2YTBEWkkwSGVnQlFKOGVtRVpKd2c0bGcyQk5NUW1NZUdxNGdVWkJBY1RoWCtSR0l3bm9YS3I4VFM0bXV4cnVsaFRKWTZUcnpCUU8rTzhTNVZLY01PTWEzelMxUkdpNTZ5YUpTOGFQRTZ2RVpaTk9JaFRac1lGRlRQZTZwc1o2UFJQOEVJc0JQQ0VIeFNUTWQ3VHRZR3V2Qkp0dDExb0xGQWoiLCJtYWMiOiI3ZjA5NmRiZjJhNTliMjBjNGM0Njg1NTNkMzJkZDMyODliM2I4Y2UxZTU1N2U4OTcxNmUxYWU2NjIzN2M0YWMyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii82OWpsbDZ5MmNpVkZicmU4OWROYmc9PSIsInZhbHVlIjoiWWpvR2R3TU1YL1hzRm50STFrS2U3cFlNL2VGb210TTkwOHdqeTZEV2VSTmRNNG1oZEpJSzNrT2Exb2FwODFCaW1CMFFwTHAzajRWbkhWRzJOYld6QUVqSDYvM2FwVkprbzBhUVJsRjBGWmxScE9BcnRHT3ZGUVJySmhvL1hwOVBzbUNHdGQvYng4dkl5ZnFwYXl0MDJCMndPbmhjK0d4aUtQRUNpdkdJNXhIUC9EbUN6QStHYk1tMzQ4T2F4Um9OYUtFdWFEdnhKY0NGR2VkSFJLUUhCMndFSnpPUGlPL3ppS2h4VVk2Vy9HQU5NZXlTa3R4OVZ4bzZ0L2U1R0ZkelhoNFFiOWRtME0yd1pIZHdKektOUDJYM3hvVUVYQ21xZVJGRGx2cmFDK0NZVDZmdmJPazVMUnVDdGUxS2xiakY3RVRmckRuYzB3WVI1L3JIeEoyUVBmZnJSUlk1akMxRklhajBoaW8yR25zd3E1eng1THdaNEk3VVNWaVRsWWN5eWd0aWk1ODRML3MzZ0VHQ05oT0xoSTVBRXFxMFF1NzAzeEpUemNBdTZ1Z2tKeUpveWxKZUNhVUIxTHM0VHZ3RzZ0VW5kNEFxVEtUTkRaQzRrVUMyaGpqcy9vcmtNZ2dPV1Exd1R5WXgrNFZXYXNCRjkyOGdSMk5LUEFHbkNMSUgiLCJtYWMiOiIzMjVlYjYxOTg5YWMxNTcwZTlkZTc2OTQ3YWJiYzQwNmQ4ZmVlN2E2YWVkYmFlZTIxOTY0NDExZmM1OTg1ZGZhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931755141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1927064689 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2143</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card200</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">230.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>230.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2143</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927064689\", {\"maxDepth\":0})</script>\n"}}