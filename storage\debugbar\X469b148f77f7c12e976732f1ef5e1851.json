{"__meta": {"id": "X469b148f77f7c12e976732f1ef5e1851", "datetime": "2025-06-30 16:09:54", "utime": **********.555987, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.015577, "end": **********.556014, "duration": 0.5404369831085205, "duration_str": "540ms", "measures": [{"label": "Booting", "start": **********.015577, "relative_start": 0, "end": **********.46509, "relative_end": **********.46509, "duration": 0.44951295852661133, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4651, "relative_start": 0.4495229721069336, "end": **********.556016, "relative_end": 1.9073486328125e-06, "duration": 0.09091591835021973, "duration_str": "90.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45553984, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02111, "accumulated_duration_str": "21.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5017269, "duration": 0.02009, "duration_str": "20.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.168}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5330532, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.168, "width_percent": 2.084}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5412889, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.252, "width_percent": 2.748}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-404183152 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-404183152\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1444287175 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1444287175\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1129950073 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129950073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299642499%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlXakJqVk0yejZKWjBCTFNXRWhCaEE9PSIsInZhbHVlIjoiSjRMN0hqakI2RDBHOE90OVNtUDBEekV6bHBuZjl0V0tEd0pYT2VXeS9JTkhDeENrNzJxNmwvdUdwMkx5WlFDMEk2dDV6QjNTRDJvRDZObFRHMzRzUnYycmdFS0l0QnF1azhLUVNaY281MDEyeS81R3oxdHFxMVcwUHVZdENSLytRdEMwYmlMbWpYZWh0d1Y0aVZrRnA1em1XR2o1bng5ZUhEQ3lGTFduNWVLMjVHK0lJeHoya05VMkxXSjVCUDVjckgrb1pkVXA2Qkgxd2pOVm4ycE1CUzIxT3dzcmQzUkoxaDY2NEVCbUV5cTVLRUJUeUV3YnBoTE4xSHNVeDc4SVQwTUV2cTR5eG13MEhEWHlPRy94dFhoR2dsellKYmsvd2FiOTVQSDlFSGRsR3JJUDlNaE1idmc5WjB0dnlJU20zcmZyVDZxR3lOKzgwMkM2MjVoWldUQ2NnODd4Y1pjK2lpNWRSUUJ4bVpUelF5bXBmUVNNb3ZVL1ZjcXU2VHdIa3drQVRsQ2twbFVFZkttU2kwQ2FXTkZWR1BlRUIwVFJQWEFiSFRPQTEyVzkydERNZUJ2bHlDVGVVTUQwbU5kYzNPbEh1OThuQWwvdDhzSzBoQnBRTFNYbUZhOW15MHZxY1I5RFp0Zk95N1ZHZUxhVmVjZ0VtOVpKaDU4K29uOWsiLCJtYWMiOiIxYmVmMTg1OGFhMGE3Nzc2ODU2NGJiMDQ3MGVlNTY0N2UzYWJiMTdlOTZmYTJjMjA0ZDllOWE1NWE3MzVhOTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1pRitFZDJOYTJpZWdwYk5Tc2tveVE9PSIsInZhbHVlIjoia1V4aGRDbms0R05RR2JlRlpSQWpzVi9WTlFzQU5hOFFmS3V2bVBOMUREZXJBQUY3M1NOMHJaUGdHcHB6SW52cGwxV2FjTFppVkR3dHBYZVFua3FmWEZEQlNWanlQYzVqOEtaRUpBYXlkL3FPbWZveCtiWTRPdGNxM2lXMU5zUmtlTDROODRLWVpXQUw5WGZpQTYzNGZnbFlRVFA5UVdlQVAxNklRMlFsbFl2czJqL3NoUlJBd1dYelhhYVZpUlpYN0kwVnZCNHd3clNxOFQ0RjdaTTFRRldjZDBKdCtYMlRPNXM5WVVYNVJWWFI3WUFSMWhvVERDTXBSaklJa09xYzY0WTdmYmF6T3RMK3FRUW9mOExqOW1wTVJkOFZYc2VPc1U4Tmp1VWpnQ09CbHNnN08rWXZTaUJrVi9ET0Nid1g0K2tlYVpIdVQ2ZXdoRmFpaStha3VjSjVRRFBJVUF5MllnY3lCK3B3MVVxTVU1bzVjNCswMEhqVVFJTlB0SHhVNzlacHhTaXB3UUVqYXRuU0NkQ3c5MU13TFREaDBYWHNtL0lDQ3g4WXZXN095VkNpUkhDMkE2M1Bld3VPdmYvakk3WHFrQkhXdW43ZmhGOC9pdTNRNWU2SFF1U3Y3dGpNaWFycFVRWjlISVkyNGFjanVCMnVRbXp4dWxJY0o2cloiLCJtYWMiOiI0OGRhYjMwYjU0Yjk0NzIzNzVjYTljYTZkM2ViYTZjZGYxZjQ3YzhkMWQwYWMwNTI4N2IzMTVjZjU5YWY4OWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1426741804 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426741804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-838101737 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZkMmQ1d3lOeWhNVDNaM1h4ZDBBSmc9PSIsInZhbHVlIjoiV3lrWDU4dnZTVk55VDlHcE15VWQvT1JYUGFxR2dwMk9RTy9wS3lhZnRycmxSY3ZsUDRNRU5nMEpSajZ3ZmxkcmlFeEhmQnQyWk5WRXFxQzBjUTJvOVNnd1poUm8yaUgreTNnZXhZWTVPOEJ2dksyRStUQVNpTURqbUtaaGlpejdVd3pEYm03S3Y2WVhDN1BVbWZUNlcyZWt5TS9Kd0ZFZ2FFSUhZbVhUUFFET0xLUTQ0Sk1TcnFCVjFtSWtVZStWVzZoUlRYa0hLM1VFak9BbkI2OUNRSWNzZHVsMXBGN2NoWk9sQm9xNkdQYXRlcW5wNExPQXBaZmNNbzdsTG02TWQ2QXdCNlpjOHl3MUI0N0ExUzZJRXRuOTF2QzBpSW4rRUJLaEVOZXNQT2Jhd2U5YUpVa0pkbkkvdTNLVGN1Q1VvV0pwbm9tK2N6ZnA1RE5QWGtKUUR0MWlmWXZ2MUpZLy9lOW00bG1OOCthb0hiVUVKSjJKVGNsS0VqTmdhWXZBQUI3cnJOOFhBTHZJYXo5eldKK1Y4dUVGZDZybkJqeFZyeFdHdGJwZGhjZDUzeHNXNm16aUlRUXplcjdGSWpIUFJWZ2ZwNTcyQ3lGSk8yaU96cG40Qm1ZQkJkVFhmekQzVG1McmNpeDNJVHRVMFR3bUNXR2FmSkY0Tm8xZi8zdmUiLCJtYWMiOiIxOThkMzM3OTlkOGY5MzlmNmI4NGFiOGQ3MGMyNmY1M2UxZmIwOTVlMmE1YjliOTUyNTg2ZjJlMWJlZjNlYzk2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNqU3dnZFYzVU5rL1k4dHFOVE5JZWc9PSIsInZhbHVlIjoiZVhJaVJrajF0Mlk3TkZsWTBwWTQyYko2YWZHNEdjcE83Mk52ajBOTW9vTWZjYmtPaEhqa2owdzB3bCttWFU0M1FMZ3o0UXZqdmpiOHpMRTlxV0laS0wzMTFWcTNidnJsaWlkM1JwYVk1RlNzUGcwR29wdnhJQmR5TFE0elBqZkw3TE1DWmx6cDNBK21xMHkwQTEvUW9hRVpva0lRa2VoMFBucVgyRGl4YW1aQWtNK1gxWjRoMkphSC81ZXJDTEsvNlRieVVRdFExRHY2dWRKNXhkWVI4d0wzc1F5T2FXcDZrL2hYZ1ZuZWJucE1YV2VJSTFOSkJwc1pHMUtRcDRjemt5elRLUW9ZUGs3QlBGMmF3dXpkS2FuTmVMWGYxb0FFVnNRSW5wY3F1VDFrd3NPVWhraS9jZ3N1VGlWYlFNWXdTN24rWlF0NlRQUGlmejFta2RkaU9iZThYQUdqSGJLa0RHWHltYlMvTzhLUXNLNXpnTFdSV2VEdWIrczd3cmk0czRVSHZhMG5RR2xuYkg1QW1NNlNLQkVlTklhSmoyYnAxS21jVVJabHkvNEd3TWU5enF2UmNJMGNqVDdSd3lMdkRXeXhBVzMzMmdjUUo0WHF0RU5jYjA2c29Na29uTjBVbytHRENaSW95Q294dXlNa1JGNThOOFZ0L2NaYWFvaGwiLCJtYWMiOiIwMzg1ZmNjMDIyNWRjOWNhMjBjMTNiNThhYzMyNGQ3ZmRhNGJmMjVkZDhkMzYxZjcyMTcwNWM2YmIwMjk4Y2MyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZkMmQ1d3lOeWhNVDNaM1h4ZDBBSmc9PSIsInZhbHVlIjoiV3lrWDU4dnZTVk55VDlHcE15VWQvT1JYUGFxR2dwMk9RTy9wS3lhZnRycmxSY3ZsUDRNRU5nMEpSajZ3ZmxkcmlFeEhmQnQyWk5WRXFxQzBjUTJvOVNnd1poUm8yaUgreTNnZXhZWTVPOEJ2dksyRStUQVNpTURqbUtaaGlpejdVd3pEYm03S3Y2WVhDN1BVbWZUNlcyZWt5TS9Kd0ZFZ2FFSUhZbVhUUFFET0xLUTQ0Sk1TcnFCVjFtSWtVZStWVzZoUlRYa0hLM1VFak9BbkI2OUNRSWNzZHVsMXBGN2NoWk9sQm9xNkdQYXRlcW5wNExPQXBaZmNNbzdsTG02TWQ2QXdCNlpjOHl3MUI0N0ExUzZJRXRuOTF2QzBpSW4rRUJLaEVOZXNQT2Jhd2U5YUpVa0pkbkkvdTNLVGN1Q1VvV0pwbm9tK2N6ZnA1RE5QWGtKUUR0MWlmWXZ2MUpZLy9lOW00bG1OOCthb0hiVUVKSjJKVGNsS0VqTmdhWXZBQUI3cnJOOFhBTHZJYXo5eldKK1Y4dUVGZDZybkJqeFZyeFdHdGJwZGhjZDUzeHNXNm16aUlRUXplcjdGSWpIUFJWZ2ZwNTcyQ3lGSk8yaU96cG40Qm1ZQkJkVFhmekQzVG1McmNpeDNJVHRVMFR3bUNXR2FmSkY0Tm8xZi8zdmUiLCJtYWMiOiIxOThkMzM3OTlkOGY5MzlmNmI4NGFiOGQ3MGMyNmY1M2UxZmIwOTVlMmE1YjliOTUyNTg2ZjJlMWJlZjNlYzk2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNqU3dnZFYzVU5rL1k4dHFOVE5JZWc9PSIsInZhbHVlIjoiZVhJaVJrajF0Mlk3TkZsWTBwWTQyYko2YWZHNEdjcE83Mk52ajBOTW9vTWZjYmtPaEhqa2owdzB3bCttWFU0M1FMZ3o0UXZqdmpiOHpMRTlxV0laS0wzMTFWcTNidnJsaWlkM1JwYVk1RlNzUGcwR29wdnhJQmR5TFE0elBqZkw3TE1DWmx6cDNBK21xMHkwQTEvUW9hRVpva0lRa2VoMFBucVgyRGl4YW1aQWtNK1gxWjRoMkphSC81ZXJDTEsvNlRieVVRdFExRHY2dWRKNXhkWVI4d0wzc1F5T2FXcDZrL2hYZ1ZuZWJucE1YV2VJSTFOSkJwc1pHMUtRcDRjemt5elRLUW9ZUGs3QlBGMmF3dXpkS2FuTmVMWGYxb0FFVnNRSW5wY3F1VDFrd3NPVWhraS9jZ3N1VGlWYlFNWXdTN24rWlF0NlRQUGlmejFta2RkaU9iZThYQUdqSGJLa0RHWHltYlMvTzhLUXNLNXpnTFdSV2VEdWIrczd3cmk0czRVSHZhMG5RR2xuYkg1QW1NNlNLQkVlTklhSmoyYnAxS21jVVJabHkvNEd3TWU5enF2UmNJMGNqVDdSd3lMdkRXeXhBVzMzMmdjUUo0WHF0RU5jYjA2c29Na29uTjBVbytHRENaSW95Q294dXlNa1JGNThOOFZ0L2NaYWFvaGwiLCJtYWMiOiIwMzg1ZmNjMDIyNWRjOWNhMjBjMTNiNThhYzMyNGQ3ZmRhNGJmMjVkZDhkMzYxZjcyMTcwNWM2YmIwMjk4Y2MyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838101737\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-368047454 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368047454\", {\"maxDepth\":0})</script>\n"}}