{"__meta": {"id": "Xd1c087b7b63037e945084b7446f23c66", "datetime": "2025-06-30 16:05:02", "utime": **********.287046, "method": "GET", "uri": "/add-to-cart/1877/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299501.823621, "end": **********.287062, "duration": 0.4634408950805664, "duration_str": "463ms", "measures": [{"label": "Booting", "start": 1751299501.823621, "relative_start": 0, "end": **********.184699, "relative_end": **********.184699, "duration": 0.36107802391052246, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.184708, "relative_start": 0.3610870838165283, "end": **********.287064, "relative_end": 2.1457672119140625e-06, "duration": 0.10235595703125, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48567104, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.020479999999999998, "accumulated_duration_str": "20.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.220343, "duration": 0.01593, "duration_str": "15.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.783}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2444472, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.783, "width_percent": 2.148}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.263936, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 79.932, "width_percent": 2.832}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.265856, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.764, "width_percent": 1.855}, {"sql": "select * from `product_services` where `product_services`.`id` = '1877' limit 1", "type": "query", "params": [], "bindings": ["1877"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.27037, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 84.619, "width_percent": 2.148}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1877 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1877", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2744431, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 86.768, "width_percent": 11.865}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.278268, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.633, "width_percent": 1.367}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-375076322 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375076322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26949, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1877/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1029541319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1029541319\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1962523544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1962523544\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI3NzIwNGdmV3RYVS90eFZGMjVUa1E9PSIsInZhbHVlIjoidlRsVTNodE5Zb0N5QnZUa0hoMUtFRzNKZ3VISzV0Z3JKTjFpTk5SaDA5aWhiRUNBVGRVSTAySGd5eXpONmE4RUVIVng0MjFndm8wUUNta1BuY3kxb3BjQkswbzdCZWY0N0lPZC9KbnU5SHowVUhiNTFsK1VKd1I2UWNWZzNYUXk5aHBnakxyZmt4bm9zQ2R2enZjd2FzOFd6WlpBWVJXVWNIbU4wUzdKUHJ6YkxoZWdEb2FVcm1GSUpTRjI4ekcyTkxzbjJmVDd5UG5Ua00yS2VuNXRZaGpYSmQvR3Vrc2syK0RZN2UveVlLSmxlZUtsM2RiVjRuR0ZwU3IwNzRNTVpFYXJOMkJIZUNmTGZPR3BXK01tMG5HZk9McmdyaXJ1NGhhUXVjcmVGQnA1S0hDVkdCOHJ4SGhXREVoNi9GNDJHSmJUa29mOTdqWjFZVzVUdkNMcGpwMHBVamNUc0tXMkNvMFdLMVQ4Z3o1QXpNRDN4T2sxdHo3aEp4dDl0N2NpRzB2cGs1R0hzbytQWVRKa29SRjJEdXFJWVhHSDMyRGFCUDZSU3BjV3BzMnZpNm1MbTZITVhPRXFobEhzNm1VUU01UW93aFlHcGltMmI4VjdFNU1YYWFCZzg2L2VveHZpc3dpdk9hZlEzNTFnNzhlYkZKT0p4NUFEYjQxZklqR2YiLCJtYWMiOiJlZWU3MzI1Yzg5YzExNDE4NDEwNmVkZTM2ZWE3NTRhOTJmMThjOWIwZmUxNWJjYTZlZGZmYWRkZmFkOGYxYjdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpaZ3ZybThNSUlEV01hRlM0bWRGN2c9PSIsInZhbHVlIjoiMmpScWxQcVFTZ2hQUVhyVFlodWVlS0o5dXR5dlB3eHdyRU5OZHNQSU9hSC8xV2FtNE9PbWFWNkxYQmVVMXZYVzVzYWd4MXNXdXV3ZTFlYmJYdDJjS3NTTW5iQVRKaFZ0eEhmendoekdZREt0UHJnLzA2SHZhYVRGZ2RTWWhVV0NRTDcvS0RGYTloWFd0VFg0SW9MNnNwa0V2ajJSWjlldndYbWF0S0VoZlI0c0FuaG0vVTNRVUlVbUlFUDFQV1RoaGRvWHNpWFREMWErcjdWS0pYSTdDL2ROSTk4OTV4WHlTb1FncGJXMTRqL2g5azlrSEd6TWRHblY1b0pNNnMvTm1IejBleXFyL0xEbTNHK3h2V2g5dmlzVjdBTXoyekkyMU1HYzYxZGhwNm1RZlE0Y09vYWpEV1lBOVdDSmxoczNMQjhEK3BXZ0Njck1CU3hhS3ViREdiTGxxL2dad1lweURDUysxSUJsSUNlMlZmTUJBaXlEYmcyWUV0VkNZamtnekY2bm1JL2JKM0J1MWpKVUNWRkRkQ1Fta3lVU2xWK08wQmZma3pqdWlSdTgvaGJZcWRTWE51NjFTMXpMM0NQTm1QVTMyUmZUNS9jY0ZzemZYOXh3QVdEdGpaaXptNkl1eHdxaTVHS3Q1K2Y4M3N2Y1llSjR0YUNZT1lXc051ODIiLCJtYWMiOiI1ZDZmZDA3OWZkZTEzNzUzZWZhYWMwY2M4Yzc1YjZhMzc0Zjk1YzFlODhiY2FlZjVhNjE0MDAzOGEyODJiMjY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-13506053 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13506053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-15631367 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRYRGdtNjhSYnlTZXYxT3p4QitsaGc9PSIsInZhbHVlIjoibThRdjh6NjB3VWVwMjBjWVdKMElBOTRxbkhqdTd5ZVlvNURXc0F3emZleEZ0LzBxb1huZWtDd3BRdE0rUVJweHlXZHFicTR5aXNWeitvTSs3Y0ljSkVoaHk2NzJ0dzBqbVF1Q2hEbG0vZzBpQUdMRFhMZE1IeEFXcmhuZUVvK2xKWXZnYXVkWVhwL3FTYnZLOGJpWm1QVERMdjI2SVhaakpESnZGT0xQc3BlNUtCZ3U5Wi9aT3BZdy9iOEdHQ2l1Y0NFdTc4cUFCa2pUdEk0cVhXcUI3VEIyWXE3MzhSclRFQ0dYaHBoOHFuVkE2Ky84TExaVythdlhMTHkrbWZYdXpZc1RSQVhucitYaENHaFF3a3NWWkE4MjdidEkwWUVNaGk2cVM4RXN6S0pyWTlQSFl5OWZLclEvb3UraEdvOWViNXBjcEI5MWRWSGVuc2FzYUxYVmZvaTM0U1l4cnFteVZrc1FROE1CZG5SWDVhQnRPV055bkg5ZEdiVHNkd01KSGZhV2Q5WjZyV3pJeFE0R05YeitDcVdEbjNRc0Q1SVhrSXlwa1pwNm9vS004bVZNSlQrN2tHOU5zUWpCWTA4Tnl4TVcwd29yMmM5ay9lZGd5RzlsQkgvYXhwalFMSm5yY1lQQ3lucFlWeDg4cTk4YlloRWZ2ZE83UTByaWQ4UWgiLCJtYWMiOiIzNzY0NTczNzY0ZDJiMTdjZmVkMGFjNzU1NDkxOTQwNjFhNzRlMzRjM2U0NGZmYTA0ZWM4OGNjNGM4NzVhMzIyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlYxZGFRcWlRK05qUlY1SlpRSlJaQUE9PSIsInZhbHVlIjoibGhTY3RLZFNZeUJCYlg4UVVmWXYxUlA3UndtY0F5dHJzNi9JenEwMnd1NEJPR2RHeTVEbjJwVkpkVlA0cVUrM2hzdDI2eG5GUEZvUDZvanYydGxZVTZLK0RxR3Fid1N2dDhpd3FvRW8xZ0dvVkZDVFBWdW93UnRYRjJMR3ZxQi96c3czZjFMcXlSS3pqNVI3U1hVRXptWVZzWWVvd1NVVTZGU2VkRlp0eUdtNGNTa1RtVjN2WWUvT1NCUE8yMzhKaUdVTHF3a2piZ0lRSGVENzVlYm5zcUEySHVPMW9tcEJjN0VHZlVaMzNuZWdQWmFrN1BFNTY5TElLMkhtVHpEMlBRUDFaWFNleG9ERUpuZnNrQ1NLTGNpQmFsc1ZBc3REaHdKVHhSa1ZzRU8rL2c1WWxjcnhHbm1kaDN5WWp3OEdFL1RVeWYxOTNwM3gxOUpJVVdJcjlCYW5ERkV0azlsaXpCeGNwYnQ3aWV6TzJFWTlmTFQ4czI5YTViWFZwMHRHZlpoRFV5K3lRTnI5TkJ5WkFlMjNrL0VQSFBiU0NvbVJGTy9RRExmaFo5djFsU3gweEprYStHaGdWUHIrcWcvcWViTkt2TTJaUW1CbUJOSFcrMGdRcUFNRElFdyt3NEJ4VzZ5dkNlcU9wM1pLSzJMSWpRUHRvSks1RkdvVTVULzMiLCJtYWMiOiI0MmE5NGIyMjg3Mzc3MzVjNmJhMTZmNWE4MTBhYzJjODc5ZDc1OTNmMzU0NmZlMzQ5ODAwNmJiY2Q0ZDI2N2U5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRYRGdtNjhSYnlTZXYxT3p4QitsaGc9PSIsInZhbHVlIjoibThRdjh6NjB3VWVwMjBjWVdKMElBOTRxbkhqdTd5ZVlvNURXc0F3emZleEZ0LzBxb1huZWtDd3BRdE0rUVJweHlXZHFicTR5aXNWeitvTSs3Y0ljSkVoaHk2NzJ0dzBqbVF1Q2hEbG0vZzBpQUdMRFhMZE1IeEFXcmhuZUVvK2xKWXZnYXVkWVhwL3FTYnZLOGJpWm1QVERMdjI2SVhaakpESnZGT0xQc3BlNUtCZ3U5Wi9aT3BZdy9iOEdHQ2l1Y0NFdTc4cUFCa2pUdEk0cVhXcUI3VEIyWXE3MzhSclRFQ0dYaHBoOHFuVkE2Ky84TExaVythdlhMTHkrbWZYdXpZc1RSQVhucitYaENHaFF3a3NWWkE4MjdidEkwWUVNaGk2cVM4RXN6S0pyWTlQSFl5OWZLclEvb3UraEdvOWViNXBjcEI5MWRWSGVuc2FzYUxYVmZvaTM0U1l4cnFteVZrc1FROE1CZG5SWDVhQnRPV055bkg5ZEdiVHNkd01KSGZhV2Q5WjZyV3pJeFE0R05YeitDcVdEbjNRc0Q1SVhrSXlwa1pwNm9vS004bVZNSlQrN2tHOU5zUWpCWTA4Tnl4TVcwd29yMmM5ay9lZGd5RzlsQkgvYXhwalFMSm5yY1lQQ3lucFlWeDg4cTk4YlloRWZ2ZE83UTByaWQ4UWgiLCJtYWMiOiIzNzY0NTczNzY0ZDJiMTdjZmVkMGFjNzU1NDkxOTQwNjFhNzRlMzRjM2U0NGZmYTA0ZWM4OGNjNGM4NzVhMzIyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlYxZGFRcWlRK05qUlY1SlpRSlJaQUE9PSIsInZhbHVlIjoibGhTY3RLZFNZeUJCYlg4UVVmWXYxUlA3UndtY0F5dHJzNi9JenEwMnd1NEJPR2RHeTVEbjJwVkpkVlA0cVUrM2hzdDI2eG5GUEZvUDZvanYydGxZVTZLK0RxR3Fid1N2dDhpd3FvRW8xZ0dvVkZDVFBWdW93UnRYRjJMR3ZxQi96c3czZjFMcXlSS3pqNVI3U1hVRXptWVZzWWVvd1NVVTZGU2VkRlp0eUdtNGNTa1RtVjN2WWUvT1NCUE8yMzhKaUdVTHF3a2piZ0lRSGVENzVlYm5zcUEySHVPMW9tcEJjN0VHZlVaMzNuZWdQWmFrN1BFNTY5TElLMkhtVHpEMlBRUDFaWFNleG9ERUpuZnNrQ1NLTGNpQmFsc1ZBc3REaHdKVHhSa1ZzRU8rL2c1WWxjcnhHbm1kaDN5WWp3OEdFL1RVeWYxOTNwM3gxOUpJVVdJcjlCYW5ERkV0azlsaXpCeGNwYnQ3aWV6TzJFWTlmTFQ4czI5YTViWFZwMHRHZlpoRFV5K3lRTnI5TkJ5WkFlMjNrL0VQSFBiU0NvbVJGTy9RRExmaFo5djFsU3gweEprYStHaGdWUHIrcWcvcWViTkt2TTJaUW1CbUJOSFcrMGdRcUFNRElFdyt3NEJ4VzZ5dkNlcU9wM1pLSzJMSWpRUHRvSks1RkdvVTVULzMiLCJtYWMiOiI0MmE5NGIyMjg3Mzc3MzVjNmJhMTZmNWE4MTBhYzJjODc5ZDc1OTNmMzU0NmZlMzQ5ODAwNmJiY2Q0ZDI2N2U5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15631367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1389672531 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389672531\", {\"maxDepth\":0})</script>\n"}}