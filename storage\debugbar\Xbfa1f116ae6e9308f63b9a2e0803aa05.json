{"__meta": {"id": "Xbfa1f116ae6e9308f63b9a2e0803aa05", "datetime": "2025-06-30 18:00:32", "utime": **********.699077, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.232508, "end": **********.699095, "duration": 0.4665870666503906, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.232508, "relative_start": 0, "end": **********.59582, "relative_end": **********.59582, "duration": 0.3633120059967041, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.595828, "relative_start": 0.36332011222839355, "end": **********.699097, "relative_end": 1.9073486328125e-06, "duration": 0.10326886177062988, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48169832, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03054, "accumulated_duration_str": "30.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6263082, "duration": 0.02309, "duration_str": "23.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.606}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.65815, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.606, "width_percent": 1.67}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.673506, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.276, "width_percent": 2.62}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.675873, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.895, "width_percent": 1.604}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6808941, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 81.5, "width_percent": 13.032}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6879382, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.532, "width_percent": 5.468}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1188552969 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188552969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.679648, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2140202016 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2140202016\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1837596323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837596323\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1558759860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1558759860\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1172400125 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB0NTNpTnAzaW50d2hyeEwxSXh0Z3c9PSIsInZhbHVlIjoiaG5SWm9XWFAyUVBjUysxZCtUcUZsWUloOVNXY2hCa2VvZmdUcFlFMUc5UUpUMnF1cXVBcnZvY1RjSnl2cU1zTXp5ZUJyem02U0h0UU5zUHBqQlAwQXpXTE8ya3YzOXVTU2lkUTRMM0Uwam5pRVF3Z01oY1NvSlYxTklNUFRhYkZTVHpGdnYyREJlVmNMWDBJcE9UTGVpNUF0dGxDQ0hvZk5pSVlNblNmZzk4cEdRTFBjY1l0SVhWc2RBL1lIMnZ4MXltck9ja1hZaHUzdmNIV2RmOFA0TUdmRUs3cGhqNUpuczkxMHJzR1BzT28zQ0hUb3hVaUdpRVd4TG5TN3prK0VENWtTR29VbU1hcVU1TDIwSzcva0xQOHNOZ3FwZHd2dkhmckRFbDZBa2RiSGlmZCtia0RxR2ROQ2I3Y3lYUkltSld3Z2xWU1QrWFZWanJlV3hnamN0QWI3TGVFeU9KVlRJT2cvekxEa01mYTBUeGkvbGJvcUUzcHNFVCtmNUhoRlArcG5CaFdyNithL2xVTFlvVWNYdHNoZ0Nsb2xUZjluTmpxQjN0L3pOY2RTQWI4Z2lJMDJnZE15clFGeU82M21iM1pueVppSEE4bzk0N0RpMnBldjBKOHRSb0g4dFhsajlzRkRMYm1NRnptcjFTenNYM2l3MnczWkgrNE91b0IiLCJtYWMiOiIxMDIxNjY1Y2QwZTZlMDlmMDQ4YzFmNWE3OWU3NWI2YjdlYWRmZTZiMjYzZGUyZjBmZDNhOTI4MDU2MzcxZGQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhCVGFNVTQveHdEQi8vNkxUWjdwcHc9PSIsInZhbHVlIjoia0xHK2ozeG1CVWlJZlUyMGlTaUpEVmJZN0pYN1NMRDdFVVhPQWlVbXhLRmp2QzhWc2JJVFJFOThkUCt5ZWliL21aOW1iUm16eWx6K2ZsQ29NN1dOcEhPbWhhMkZCVXY5RVk0cWJURVg2ZTBnWitCL2JJbEpVZFlWYU1TMUFYczBjTldZTkVTNUk2Z3F3aHNCZ1dxdDNseGFadTVKMTR2WTNISU5oZFBKZU9zNmJjR29hb2thcytLb0xWbzZHRjVrQi9GaU5PK005MFl2VTQwb1BWZGI4R2VjdVByc0UrSFEwOGZXdkpEQmNPM2hEODVYRTRoSjc2MWV3Tk85eVgwNWd0MmRObTlwWG1KWnhTMlphaktadmhQUGVVcnhraEIydEN6N2wvVTZ0SGRid0Y3TlkzaW5rODdQUnJMbmE1c0tHT0dXNDlXTzBrYUYyRHk2YVBlTWlXdUtPU3ZmNXBJVDgxcVpJemRzaVpUSTlMOG90MVNyQjlSRVdUbzR6WG9yUTlsQzlpUDcvSllpbDMwRzZKQkJKU1NpMFo3bFducUlyWVc0a1hCWXAveUNCclJwYkRoSXZWYTlTaDBZa2RtSldPYktBTXpweW1qUzArc2cvWmJ1L0dLaGFsMmczd3FHNUVrMmk3aXRBY0NzaWhTTEJlMGdmY1dOc1VpWmpWZ04iLCJtYWMiOiI5M2JkY2QzNmU4MGNiYzVkODc4OTQzMjM3MGFmY2U2NzlmMjlkNzA2YjM2OTc1ZGYzMWI2MzEwZWNjNjExMzRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172400125\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-184227876 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184227876\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1498588134 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:00:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhzdXFVTzhuRzA0Q0czVGVsOFZnV2c9PSIsInZhbHVlIjoidXo3VTFvRU01cnRqU29KU3hxYTVEbncyV3Q4RGZGQk91RVdDUHFqQkR5VGl0OUxRRVJNVkQ2MmFpK3VaWngyNmZjUDFLbXhXWjlsdFBpWERvUHE5bUNFM08yREhRVmhOUkMvVGhrRDk3bkt6N0tqWW9nVUJnSXNZb0MyL2FCWnJUa0t3OWo4V1g1QlRGbU5Fdmx1cTEyZHZvSVpsd2k2WWc1VlNHQmUwSEpBVFdtZ3IyTGcwNExlOTZzN0VUZHI0MnBXMkJtRHowQytaOVhlWkpTd0NGNENJL0U4RU5nb2IybktCZ2lTNkNsYi9NaHBDR0lxbk1INmRGZHdKOFJLWkhJUVVCMDNaS2xuUkk2VXBOV05rMm5tTXp1ZklNRkxENTY5b0FiL0FuU2wvTE5MVExkcVc2RE4wWU9qUE1EWENXWDE2OHpHVktobVVMclg5M0Fxc1doeDM3YTQ5eUl4ZHhGckV2QlhIMUVGSFJiVHlWZFlkMmxCM1FadkFnVkRyK1hNUWxhTDkyZUR4NXRVMDluZXllTHo1NWxxWnZVeFRjaEtmMC80dTFEOGZLNHg3eEFzdWl2eXUrUmNyWDZzdm5pRmIrd0FOY1RHUDdDd2xzSzM4R2ZlS3hwaThKNEtKUEZBbWlEajcxZ2FBWFdJeXZlb0YvRlNGUW5wNStlYkwiLCJtYWMiOiI2MTU4MTZiNDhmOWE4NGQ0ZThmNGQ2OGVjMjk0YTU4NDYwN2MyOWY3MmVjNTM1ZjcxNWRhMmZmZGViZDg0ODAwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:00:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBNYWd5TVBscFVpMGFGbmNLTlY1b0E9PSIsInZhbHVlIjoiYXFWbVlIZWxxSjhkaVNWOGZyNi9JMys2OG5BMVhPTytFOXFCbjVTSjRjTlZRaS85ZHR2RDJDb0tiemIvNWdXMXV5cXpPQkozWGFwVUFLTTYrRnQwdWVPeGlLSHVhZ0RGL1FmdDVRekdlRFZvRnRVcWdIK2wrYVJJbEpyYW1hdEpnajdQRFU5OTZ4WGQxcW5xS2VrWUlvOGVMVmU3NnFReWdYUDgvbTFmNVNxVGd0ZnFBTnR2Q3JJRFo5QkprUVovaUNiNTE3UlcrMG5NQzVhTExWZWJNOUVqekRZU1NXNitYUWVJT3FZMkNGMlJTYUdqVUl5UkhQaHhWMklKOHg1ZHh2Qk8zN3NjZ0hpQVlTc1UzUVF3aHI2VXlTbkl0UnFYQy9uRm9PWVpWMGtLYkk4YzBOY2V6NVFXRHlWSHBKRmQ3RjQxT2x2djJxNGhxb2phWEgzTHBiZkprSDFFMzB1ams3cldlWUNUMVVDaDRGa1J5ZXNXRTZ4MGw3ZHJNdjFVOGdMbFZkWEhEWERIS2FCV2JKVDYzR3o1SzhTVnp4M3l1YWNEc00rdk0xQnRIT085VFc2RmtZaXlCRjhHeHFROW9pa3RhWDBIdEdNTmMxQ3V3eXVuZUVtcEp1SzBQdS8yT0ZQeEp4YUoycjFRNnd6TVl3NVVyY0tFaU56SDhzQ0wiLCJtYWMiOiI4NjM1OTFmMmJmYTE5NmYzNDk3MjI0MWJmODM0MTM2MTYzM2EyZGNhMTQxNWE4YWE5MDdhNGIyZmI0MDA1MGEyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:00:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhzdXFVTzhuRzA0Q0czVGVsOFZnV2c9PSIsInZhbHVlIjoidXo3VTFvRU01cnRqU29KU3hxYTVEbncyV3Q4RGZGQk91RVdDUHFqQkR5VGl0OUxRRVJNVkQ2MmFpK3VaWngyNmZjUDFLbXhXWjlsdFBpWERvUHE5bUNFM08yREhRVmhOUkMvVGhrRDk3bkt6N0tqWW9nVUJnSXNZb0MyL2FCWnJUa0t3OWo4V1g1QlRGbU5Fdmx1cTEyZHZvSVpsd2k2WWc1VlNHQmUwSEpBVFdtZ3IyTGcwNExlOTZzN0VUZHI0MnBXMkJtRHowQytaOVhlWkpTd0NGNENJL0U4RU5nb2IybktCZ2lTNkNsYi9NaHBDR0lxbk1INmRGZHdKOFJLWkhJUVVCMDNaS2xuUkk2VXBOV05rMm5tTXp1ZklNRkxENTY5b0FiL0FuU2wvTE5MVExkcVc2RE4wWU9qUE1EWENXWDE2OHpHVktobVVMclg5M0Fxc1doeDM3YTQ5eUl4ZHhGckV2QlhIMUVGSFJiVHlWZFlkMmxCM1FadkFnVkRyK1hNUWxhTDkyZUR4NXRVMDluZXllTHo1NWxxWnZVeFRjaEtmMC80dTFEOGZLNHg3eEFzdWl2eXUrUmNyWDZzdm5pRmIrd0FOY1RHUDdDd2xzSzM4R2ZlS3hwaThKNEtKUEZBbWlEajcxZ2FBWFdJeXZlb0YvRlNGUW5wNStlYkwiLCJtYWMiOiI2MTU4MTZiNDhmOWE4NGQ0ZThmNGQ2OGVjMjk0YTU4NDYwN2MyOWY3MmVjNTM1ZjcxNWRhMmZmZGViZDg0ODAwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:00:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBNYWd5TVBscFVpMGFGbmNLTlY1b0E9PSIsInZhbHVlIjoiYXFWbVlIZWxxSjhkaVNWOGZyNi9JMys2OG5BMVhPTytFOXFCbjVTSjRjTlZRaS85ZHR2RDJDb0tiemIvNWdXMXV5cXpPQkozWGFwVUFLTTYrRnQwdWVPeGlLSHVhZ0RGL1FmdDVRekdlRFZvRnRVcWdIK2wrYVJJbEpyYW1hdEpnajdQRFU5OTZ4WGQxcW5xS2VrWUlvOGVMVmU3NnFReWdYUDgvbTFmNVNxVGd0ZnFBTnR2Q3JJRFo5QkprUVovaUNiNTE3UlcrMG5NQzVhTExWZWJNOUVqekRZU1NXNitYUWVJT3FZMkNGMlJTYUdqVUl5UkhQaHhWMklKOHg1ZHh2Qk8zN3NjZ0hpQVlTc1UzUVF3aHI2VXlTbkl0UnFYQy9uRm9PWVpWMGtLYkk4YzBOY2V6NVFXRHlWSHBKRmQ3RjQxT2x2djJxNGhxb2phWEgzTHBiZkprSDFFMzB1ams3cldlWUNUMVVDaDRGa1J5ZXNXRTZ4MGw3ZHJNdjFVOGdMbFZkWEhEWERIS2FCV2JKVDYzR3o1SzhTVnp4M3l1YWNEc00rdk0xQnRIT085VFc2RmtZaXlCRjhHeHFROW9pa3RhWDBIdEdNTmMxQ3V3eXVuZUVtcEp1SzBQdS8yT0ZQeEp4YUoycjFRNnd6TVl3NVVyY0tFaU56SDhzQ0wiLCJtYWMiOiI4NjM1OTFmMmJmYTE5NmYzNDk3MjI0MWJmODM0MTM2MTYzM2EyZGNhMTQxNWE4YWE5MDdhNGIyZmI0MDA1MGEyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:00:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498588134\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}