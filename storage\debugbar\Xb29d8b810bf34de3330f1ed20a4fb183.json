{"__meta": {"id": "Xb29d8b810bf34de3330f1ed20a4fb183", "datetime": "2025-06-30 17:58:35", "utime": **********.204621, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306314.763397, "end": **********.204635, "duration": 0.4412379264831543, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1751306314.763397, "relative_start": 0, "end": **********.146741, "relative_end": **********.146741, "duration": 0.3833439350128174, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146751, "relative_start": 0.38335394859313965, "end": **********.204637, "relative_end": 2.1457672119140625e-06, "duration": 0.05788612365722656, "duration_str": "57.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.184704, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.425}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.195107, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.425, "width_percent": 16.933}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.197908, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 76.358, "width_percent": 23.642}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-814071523 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-814071523\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-575332867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575332867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1061873161 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061873161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-517492529 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306296830%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBGczRQSk1jcWdJZVcvdjlaeitNNXc9PSIsInZhbHVlIjoiSEVZZHp1aU5vdzBPU2hERFB3RS8vMFVoTUEzUGo2cXB2dDFTTk1WdlhuSkduN2paM0ZtZVpwS2pzTkJyNkxUOXpoWkExb1VyOXQybFpid0RrcGF5cTUzdjRXVWptVW12QnZ6K2Q2aVlFZ1VDYXUxU2ZKSDFudUtIUzlRRkVqYU9WMDJSQXpINXVTN1RZYmdNUnp2YWVIZk5mbUloamR6bjRDaFFabmswQzZMQzVIQUhrR3dIOWs1c1AraHlhdFJsYmxrbGc2M0NpL2xod3BVdW8yaEEwRmJKRkdRZnlqQWYybkhnb3JsQ0tNTDZxS2xSOGZqdlB6R2FaR0FUZUNma2ZjbnVoVUg2VFV5c3FqaXl6TWNZcDJJYWlXQ25pK0RucFgrbmh0MTBxUjhxSVhUK2I3Ly9HMXJYTWVNREJHdkZuMzk1OE9PQnU4OFZESDNBVjlNbERsekt2SVZQSmVOWHNzQXRackpMdnhwTEl0aVV3ZHNOVG9ZcWdQZXdydWNXUHFBMWJEOVpmU2tITCtJL0NzdjJpRzdQMlFEd3BGNW54ejVsM1RFcXJrYmdpVHBMM1RWTytXQ2pzRjFIOCtNcGdVeklxMVVON3NWdXVKeDNpY1RzM1JKVkFTL0p4dHFSQWNUTUNoNGdicTdIU2ZRQ0cxQ3ZMVWQxdzNURnNnUUMiLCJtYWMiOiIzMDgwNmEyZjQ3MjE0ZDg0OGNhNDQxMWI4ZmFiZTczNTJkOWY4ZGNjN2IyNzQwNjNmZjE1YWUzODY5ZjhjMzEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijc5azBTWGZxbFFLMHBITmJGQWpMVEE9PSIsInZhbHVlIjoid0dFTGdaclJiRzJXSDVUTlhYUTV3bS94STBBcC9WM0QyMkRHWkt2VG8vM1p4NHliRDgzOGFTVkRMZFNUUHQ2cEtsSjlVR0dwK2JsWUdGOVdLU0NIZUQ2TmR3N2poMUl2S0p1bGd1c2lDcENxWmp3VjFGenFUYmNrRjAvc3hzTEplb0pEdTVVclVKVUN6c2s0QThmckFOdmlSUjVnalJuTjZEZFdVMTNYbm52ajlFOHBIWGhzbjFIREFheHQ2bDJWQ0UzYTQ0RmFPOW1Za0srUTV2ZHArL013K0taQko1Rm9wbXpiY2VCa1hHTDUrNjU2YVdTMDNiKzdpTnRCTjBYN3p3NFZmNUNFS2UxRUNKaW1xS2tQVjBIL2E5WWhDVFgzdXBrMTJ4cHJ5a1FHWGx5azdGU0swajI4MEJMdVNoaHM1MjFIUFJCTE5LVU1xelA3ZkE0S29rK1BMYko1aW9sZHhlYm13UzhBWnJIR2h3a09KOGlzMzlCQzhocFJ0V0RvQXI3U1BIL0xOQk9ocWJvS3Fyb05RSW5JT0N1RmsrblR0RE5TdGpqRkRwYXlhZk5pNkZLRkpNc1pUeWY3NE40d0twQmY5a2J5bmlOa0lJT01rc3hoY3I3OXA3Wm5HQkE5ZUlIYjhJK3dGbER2b0NkQ2w5RjMwbkYyMEtEOWxjNVYiLCJtYWMiOiI2MzU0ZDE2YWQyYTg1MGVhMzY1YjdkOTVhYmNlZDFhN2VkZjQ0YjkwYWEyMTk2ZTVjNWFlYjc0ZDFmNDk3YTZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517492529\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1087568081 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087568081\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1384107166 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:58:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJtd1dJTXpyVGFiN0MrZm1PQXRrY3c9PSIsInZhbHVlIjoid08rLzlpRzVUZStVV3R2QW5XbnFPMHB6OXBCOTJ0MGRoRVUxdlo2UEJULzYrSU50WjFXRU11ZlltV3J6Yk4xSnlmVWIyZ2VOZHlrZjJ1UzNwRWpQV1pEU3hsbEdIbHNhemNjT2NGS3RoU3hJT2RzUHZPaXhiczdBQVdVUGwwb09YY0pNcCtRcEQ5NnV5ODBVZDJVVXdNdzVLUzcvMTZ6S2VuSTc1K3g0Y0RHMFg4dFVrZmd2Smp5Mmpxdm9xRXY0WENNc3BJSlJJTDhSVmY1NDEwZmhSajhqWnFtb2RYMHZIY1hpTHhBNW0rOCtjOFdLNDlsNjA1SWc4NlNuakxhWGxQOTlESlpiMjVzUXJoMGlZeUpTQ2pYQ2s3cmpnQXhpQytRblFJblBQNTZ4ZXR6bmU0YXdvekFnb0NGakd0TWRLWmNiaTc5VzFjNTVwQW1ZcDA2cnIxaWJCNlA5cjdUUFoweFprczdhdm5lR1Q0R2dLaEw4SytTN1pjUXlvQlpXZnZFMXhzVEpEb2ppR3Nld1RSbUVIdzZ2R0FySndRaEpNcmNzZ1BUV3BaS0h6YXEwdVVDNmVGZy9OVDFPd24xLzVLNGltV1A4SEdiUFNoZUVpQ1IzZ0ZDdUVRSVZNSVhwNldsenUvS2U1RFAzVjEyeE4zT2huL3ZyV1laYzg2V3IiLCJtYWMiOiI1Mzc1ZjRhOGI3YmFiYWJjOGY2YzE1ZjQyNTAyOGYyZTk2NDRmMzc4NTM1ZTIyNDYzMDRjODI5ZGFkYWE5YWY3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFvaXlVTWloS3ZxOXo2Z0ErWEtQdHc9PSIsInZhbHVlIjoiaXgvOUtBMUhPZytpaDdNMDNlUDFNdytyOUR6OGs4bURneG5UTjh5STVCRGtJOWZvRlB2dkpUMmJQVUUzbkZ0ZXJWWDNkM2dxc3Q3bE5NazFXYUZTTXVXTHJVMHFnTnBBNlVIeDN2TnJ0SG5CY2ZVOTFhYVJMUjg2Nit0ZDZ4aEFrbUVFdkVtRXFjT1NLMWxoaitUTnJ1azRzeEFGQ1ZVeENFTmxTY0daQ2doZ1F0RTFwMHorbUl2Rkx5SU42YVJOMW95T3MxUWJEQml6RXN2d2t0NlVQeTZQTEc1SnFXbDduZHFuR2R2dzNQY0ZIS2sxY2loQmUzMTUxS1JFREJyQWhFRmt5TUJXRE5HRGEzWnhwbXdIeUQ1MzRCZ1BqaStGSS9WNUJWYTRaYWszNFVEOUp0ajhac3gzOTlTQWhOMVVvUnNJbFhBc2dVUk93eG9zaUNqVUNiaTFLQXhmNTJQRklNWStkZThoZ3hjTjFpZzdkbXFFTzNsTkJ0aG0vdEFQWHgzOG9sWG1QcCt6b0RhVXVSZnVxZDJ6MTEveUN6RFJ5UVlWTE1XRmNrSWxXcmNMeC9Idkt5OFdwOTB5eCtGdDhnOS9xaHFJRmFkbDUyN2R1UFU0dnRtS2lucDYxazREbGdIeXdVcExCa2xFVkxDK3dWTVBjQWNadWhKMUNPUTQiLCJtYWMiOiIxMTlhMGU4ZTJhNzgzZDRjZDQ5ZDRiOTM0ZWRmMjljNjhiYzIxY2E4NjVkMDU0YmZiMGViZDA5MDM4MmM3ZThiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJtd1dJTXpyVGFiN0MrZm1PQXRrY3c9PSIsInZhbHVlIjoid08rLzlpRzVUZStVV3R2QW5XbnFPMHB6OXBCOTJ0MGRoRVUxdlo2UEJULzYrSU50WjFXRU11ZlltV3J6Yk4xSnlmVWIyZ2VOZHlrZjJ1UzNwRWpQV1pEU3hsbEdIbHNhemNjT2NGS3RoU3hJT2RzUHZPaXhiczdBQVdVUGwwb09YY0pNcCtRcEQ5NnV5ODBVZDJVVXdNdzVLUzcvMTZ6S2VuSTc1K3g0Y0RHMFg4dFVrZmd2Smp5Mmpxdm9xRXY0WENNc3BJSlJJTDhSVmY1NDEwZmhSajhqWnFtb2RYMHZIY1hpTHhBNW0rOCtjOFdLNDlsNjA1SWc4NlNuakxhWGxQOTlESlpiMjVzUXJoMGlZeUpTQ2pYQ2s3cmpnQXhpQytRblFJblBQNTZ4ZXR6bmU0YXdvekFnb0NGakd0TWRLWmNiaTc5VzFjNTVwQW1ZcDA2cnIxaWJCNlA5cjdUUFoweFprczdhdm5lR1Q0R2dLaEw4SytTN1pjUXlvQlpXZnZFMXhzVEpEb2ppR3Nld1RSbUVIdzZ2R0FySndRaEpNcmNzZ1BUV3BaS0h6YXEwdVVDNmVGZy9OVDFPd24xLzVLNGltV1A4SEdiUFNoZUVpQ1IzZ0ZDdUVRSVZNSVhwNldsenUvS2U1RFAzVjEyeE4zT2huL3ZyV1laYzg2V3IiLCJtYWMiOiI1Mzc1ZjRhOGI3YmFiYWJjOGY2YzE1ZjQyNTAyOGYyZTk2NDRmMzc4NTM1ZTIyNDYzMDRjODI5ZGFkYWE5YWY3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFvaXlVTWloS3ZxOXo2Z0ErWEtQdHc9PSIsInZhbHVlIjoiaXgvOUtBMUhPZytpaDdNMDNlUDFNdytyOUR6OGs4bURneG5UTjh5STVCRGtJOWZvRlB2dkpUMmJQVUUzbkZ0ZXJWWDNkM2dxc3Q3bE5NazFXYUZTTXVXTHJVMHFnTnBBNlVIeDN2TnJ0SG5CY2ZVOTFhYVJMUjg2Nit0ZDZ4aEFrbUVFdkVtRXFjT1NLMWxoaitUTnJ1azRzeEFGQ1ZVeENFTmxTY0daQ2doZ1F0RTFwMHorbUl2Rkx5SU42YVJOMW95T3MxUWJEQml6RXN2d2t0NlVQeTZQTEc1SnFXbDduZHFuR2R2dzNQY0ZIS2sxY2loQmUzMTUxS1JFREJyQWhFRmt5TUJXRE5HRGEzWnhwbXdIeUQ1MzRCZ1BqaStGSS9WNUJWYTRaYWszNFVEOUp0ajhac3gzOTlTQWhOMVVvUnNJbFhBc2dVUk93eG9zaUNqVUNiaTFLQXhmNTJQRklNWStkZThoZ3hjTjFpZzdkbXFFTzNsTkJ0aG0vdEFQWHgzOG9sWG1QcCt6b0RhVXVSZnVxZDJ6MTEveUN6RFJ5UVlWTE1XRmNrSWxXcmNMeC9Idkt5OFdwOTB5eCtGdDhnOS9xaHFJRmFkbDUyN2R1UFU0dnRtS2lucDYxazREbGdIeXdVcExCa2xFVkxDK3dWTVBjQWNadWhKMUNPUTQiLCJtYWMiOiIxMTlhMGU4ZTJhNzgzZDRjZDQ5ZDRiOTM0ZWRmMjljNjhiYzIxY2E4NjVkMDU0YmZiMGViZDA5MDM4MmM3ZThiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384107166\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1528854330 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528854330\", {\"maxDepth\":0})</script>\n"}}