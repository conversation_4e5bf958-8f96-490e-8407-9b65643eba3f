{"__meta": {"id": "X6b99336484ef1fbf94181cbf02e76d09", "datetime": "2025-06-30 16:10:01", "utime": **********.180055, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299800.726444, "end": **********.180071, "duration": 0.4536271095275879, "duration_str": "454ms", "measures": [{"label": "Booting", "start": 1751299800.726444, "relative_start": 0, "end": **********.120243, "relative_end": **********.120243, "duration": 0.3937990665435791, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120253, "relative_start": 0.39380908012390137, "end": **********.180073, "relative_end": 1.9073486328125e-06, "duration": 0.059819936752319336, "duration_str": "59.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45568696, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033799999999999998, "accumulated_duration_str": "3.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.15307, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.16}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.16439, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.16, "width_percent": 14.793}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.170019, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.953, "width_percent": 18.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1739251194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1739251194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1767079830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1767079830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-535573919 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535573919\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-563641432 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299796773%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik44THFhY3pVZElvZFZ2MlVCVEs5WEE9PSIsInZhbHVlIjoiWVkzQkhRMmo5MDZpcTZjT2JwTW1FejlicTBMOTFXRnpScHdLdStVYWlialk2Wkp1YklyaUNGZWhad3hhTFBRR3o5YlpQblIrTkFGdXVML0VLbG1zR0tsMDlHMmgwSldTRzFsdWwvSXhvL1dubC9XdlZrL0VYcXlGcWdCVHZ2aWdaK3lMS25HczRFN0g0dUczUWZLa21FQnRqU0U4UTdDTFFqTjVnaDZKdUc3bjlzeUpncm9JeVNsd2lNc256Rkl2Y2JHdUVCK2tnQnc5VXovWDJHK2VaNStscmY4MGxVVGpzNFJ5TDFlZlZucXB0Vkl4MFhDeXFJR1dyR29EcXJSV3RoK3o0TTd6bERoSUNnWHpYVmVERkhIQVE1OWkzOEQzeU5lTmlxWjFpMWpaTUFaKzdhUTkrUGlaSVhvREZNZ0VreDVubmJIYnZrRkhMeDdEVENSMVhWWjFINXkyRTAyN0J0a2hweExEbGtQM1hKc3V5L2N3TGlkVWZIMUpzU3h5VkZIaWMzU29aMEk1VittSVltWnlEKzZTdEdpMWhlbXAvUXluaDd4Nnp0OXBZN0ZMemJFSXpzM2FCWFh0OEdQTCs4RFpGb2NjRlpFS296NXhrOGtYOFlpNlVkR1hnWmNoKytqNU5aQzdIZldiSjBqNHFHZUNiYmE2UmYxaXR6a2ciLCJtYWMiOiI3MDBiYzczZGE4MzU2ZWZlOGE1ODYzYjA5YTQ3MmZhZmVjNzk0ZTlmNzFkMGFhYmU3MDQyMDk2NDU5ZDAwOTcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjM2OHdXYjJTM3pXYW1VTEhQVFVlUWc9PSIsInZhbHVlIjoiazNSajdia3JnOVNvQWFkbHY5SVhWZzNVdi9EdEdRWGRjNG1CSHRnQ3BUREYxZ0wxTExoaDVFQWdJWWkrL2JrazRuM1ZQYklIMmVyRzRJeVJPbDdUM2xhazNMK1lCZ0xkdUszeThhd3M4TGtWZmhmZE8xZGM0UU1CNG0zWllUN0w2UVhmUEJCZThYYWNlb045U0FiaEtPQzVMT2UvV29sVjBYWHkxcFFXelo3a21TclNmb3k0R2Y5L2cranFJekl0VFh6aWU2QzNUNi9pSC9NQklGcWpIWVRPSDVLZzdlZ2RVcGZleDNYb2NoZlc3ZjVJck9BaWlOSVdMMUhnZHYvbGYxTUh3YW1IakpSSkNlaCt5MFJIQXpmTDRoNnBydkNHeElXT1FGbSt3UGVOaVR5OEt2bkZjU1ZTNW9OaW5ncEhadzExRVpQb0VnT2tIUC9DWEZ3YnNUUC9hRWdNYnQrS0MvQm5LaGdyRlZNQ0dGV0RDNHBBUTJqV1R5ZnF5QVJkc0ZkNkhYbXFzbGpNcWlzbk0ybnR0UmpKNWNteVJHSU5SMUZsenE2Vy8xSkdFamJvZ2h3eFBzUUNkNnVPN2xTOTExRVlNbHZmc1lTMW4ydG9FTEEydDZETUVDdUMwTktseU5uVGd2dHJxekxjRTJYMXJTWjVMeXYrS1ltZXpWeXEiLCJtYWMiOiJjMDQxMzg1NDQyMGQ1YTRhMDYyYzc0ZmY4MzZlNDE4ODZkNGU3ZDhlMGE4ZDhkNzkzMjgwZmEzMzYwZjBhOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563641432\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1173827825 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173827825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InIxMG1JNis3SDg0VGVHUVpSUDFDN1E9PSIsInZhbHVlIjoiMDB2MzFybjN4eUNPaFRvWUljYXl6QTZrS3ZqVlJ5UHdIK2RPaCtPTzdCMFM5c3VTbUtodmZSYUxDcVFhQlNDZVNIMXNHT0lOK2QrenNrUlBWMWdZU1JsYzhoVU9QL1RDL1ZCK0lkQnJpMnBMVkZRY2Q3YnR5UGZIclB0bmU2ektoeS9TTzNaU0FLL2NmeVpHaVp2bUxyMHVxTnE2UFVpWXkvUW5TVllCcEpDK1FZZG9SVngvNUV6eTdtbUV1L1pyRW0zWVdXUjBvbXl3ZTZBMHQwd3R1QUVMeldPeHhKUmYwanUzMkFTVWs0WGhXNm4yQ29tUzIzOENoYXowZHo2akhobW5UN0NNU1dqdlZOb0xPZTNGYm1CZEc3TjFCcUVJcmtpTVR1VnRBVkRmcGpXN01nQk1xbTdXMDNzNGhaaWVkeTl2Y291Y3lFYnJPYys4MFVjVTgyTHFBbllzU25ENk9rczhEaTRXWEVmR0hWUEMralFiVjRxQm93aVp3RXkyMlV1OE9STGhqd3cxVEluRkxvdXk0MVlYblJlZ29MckJtWE1NWE9yQ0MxRUdzYS9Nb01nckRXSktiQ1FpMG52NGgrdzh5Yzl3enVoR2N5clBHTG5pcThCTDFpM2x6NzNyd1NBYVU0a25WUmxuWmh1MHUrenVDMldKdUUrSVRvcisiLCJtYWMiOiJhODRlNDE1ZmQ4ZjMxYmI1ZjliZDZlYzc1ZmIwMGM0NGZkNzc2NTJlOGZhMjgxZmE5NGVjYzViYTYwNTg1NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iks4WEpYVnRPSGhuSGhieG8xNUl2M1E9PSIsInZhbHVlIjoiRVRnZ0RmY284eGxqaXloNktIWmFDS3AxcmlOL2xOdGlkN2YrTGk2alpIazhpamRld0FvMmVjNWRMOS96d3FDaktOclJHczBETDNBUUxUWkRRLzFnOVA0RStwU0xvVjAvV2x1UVI5T0ZQZktVMFNxOFlyNE5TdWxIWlVKbUU3WEdkZHZBWWtsSUF4bnJ3NXpMSkFTczA3a21RMjVQQlppN3hLTkN1T2hnN3BzR0hCd3NXcUQxS0JWMjFuUDh5L3hzOVhDeFZicEdRUnlaTVN0UWpoVjhNNkxhZS8rcGNRT2FwVHhhbFl2TnZqSTJQZmVzdGdjd3JSWlZpWENzd3B2UC9VNk9LL21OYnMrTW5xdENQNGw5K2VFSEpsQ1gvaVBaNkMxL2p5TlU2dGdTd2RIYlgvM1VvQkhEQmplaG5McXVBcHFLeHNtNUlBM21HRzJsOTM4SWdSbXRzQkxXZ1lQa1lCMjkrdGwzU2h6cllUcENEa1JEaDFocW9JQ0w0WEowbmVNYmdxRGNteDBRaW00a0RJaWIxN0IvWkhIMXU3c3E3enFtT0l0eTE0TXhIb3VBMzZDY2xobFBEWHVBSXNtZzlKSGYraFZsdFZJYjRNUjg0aGN4VTc5UUJXQWNsRVJlenIrbysvU3RMZU15dVk5bnRJbjJHYWx4TTh0M05VeU4iLCJtYWMiOiI4ZWQ0YTZlODEzZmRmOTcxZTNiZTliYWEwOWE3ZTY4NjBkY2U1Y2NlZTNjMWQ3NTNlYThhZmY2YTE2MTE4MmMwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InIxMG1JNis3SDg0VGVHUVpSUDFDN1E9PSIsInZhbHVlIjoiMDB2MzFybjN4eUNPaFRvWUljYXl6QTZrS3ZqVlJ5UHdIK2RPaCtPTzdCMFM5c3VTbUtodmZSYUxDcVFhQlNDZVNIMXNHT0lOK2QrenNrUlBWMWdZU1JsYzhoVU9QL1RDL1ZCK0lkQnJpMnBMVkZRY2Q3YnR5UGZIclB0bmU2ektoeS9TTzNaU0FLL2NmeVpHaVp2bUxyMHVxTnE2UFVpWXkvUW5TVllCcEpDK1FZZG9SVngvNUV6eTdtbUV1L1pyRW0zWVdXUjBvbXl3ZTZBMHQwd3R1QUVMeldPeHhKUmYwanUzMkFTVWs0WGhXNm4yQ29tUzIzOENoYXowZHo2akhobW5UN0NNU1dqdlZOb0xPZTNGYm1CZEc3TjFCcUVJcmtpTVR1VnRBVkRmcGpXN01nQk1xbTdXMDNzNGhaaWVkeTl2Y291Y3lFYnJPYys4MFVjVTgyTHFBbllzU25ENk9rczhEaTRXWEVmR0hWUEMralFiVjRxQm93aVp3RXkyMlV1OE9STGhqd3cxVEluRkxvdXk0MVlYblJlZ29MckJtWE1NWE9yQ0MxRUdzYS9Nb01nckRXSktiQ1FpMG52NGgrdzh5Yzl3enVoR2N5clBHTG5pcThCTDFpM2x6NzNyd1NBYVU0a25WUmxuWmh1MHUrenVDMldKdUUrSVRvcisiLCJtYWMiOiJhODRlNDE1ZmQ4ZjMxYmI1ZjliZDZlYzc1ZmIwMGM0NGZkNzc2NTJlOGZhMjgxZmE5NGVjYzViYTYwNTg1NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iks4WEpYVnRPSGhuSGhieG8xNUl2M1E9PSIsInZhbHVlIjoiRVRnZ0RmY284eGxqaXloNktIWmFDS3AxcmlOL2xOdGlkN2YrTGk2alpIazhpamRld0FvMmVjNWRMOS96d3FDaktOclJHczBETDNBUUxUWkRRLzFnOVA0RStwU0xvVjAvV2x1UVI5T0ZQZktVMFNxOFlyNE5TdWxIWlVKbUU3WEdkZHZBWWtsSUF4bnJ3NXpMSkFTczA3a21RMjVQQlppN3hLTkN1T2hnN3BzR0hCd3NXcUQxS0JWMjFuUDh5L3hzOVhDeFZicEdRUnlaTVN0UWpoVjhNNkxhZS8rcGNRT2FwVHhhbFl2TnZqSTJQZmVzdGdjd3JSWlZpWENzd3B2UC9VNk9LL21OYnMrTW5xdENQNGw5K2VFSEpsQ1gvaVBaNkMxL2p5TlU2dGdTd2RIYlgvM1VvQkhEQmplaG5McXVBcHFLeHNtNUlBM21HRzJsOTM4SWdSbXRzQkxXZ1lQa1lCMjkrdGwzU2h6cllUcENEa1JEaDFocW9JQ0w0WEowbmVNYmdxRGNteDBRaW00a0RJaWIxN0IvWkhIMXU3c3E3enFtT0l0eTE0TXhIb3VBMzZDY2xobFBEWHVBSXNtZzlKSGYraFZsdFZJYjRNUjg0aGN4VTc5UUJXQWNsRVJlenIrbysvU3RMZU15dVk5bnRJbjJHYWx4TTh0M05VeU4iLCJtYWMiOiI4ZWQ0YTZlODEzZmRmOTcxZTNiZTliYWEwOWE3ZTY4NjBkY2U1Y2NlZTNjMWQ3NTNlYThhZmY2YTE2MTE4MmMwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}