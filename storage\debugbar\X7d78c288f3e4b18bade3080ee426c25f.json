{"__meta": {"id": "X7d78c288f3e4b18bade3080ee426c25f", "datetime": "2025-06-30 16:10:01", "utime": **********.208274, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299800.727444, "end": **********.208294, "duration": 0.4808499813079834, "duration_str": "481ms", "measures": [{"label": "Booting", "start": 1751299800.727444, "relative_start": 0, "end": **********.126706, "relative_end": **********.126706, "duration": 0.3992619514465332, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.126716, "relative_start": 0.39927196502685547, "end": **********.208296, "relative_end": 2.1457672119140625e-06, "duration": 0.08158016204833984, "duration_str": "81.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45556144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01652, "accumulated_duration_str": "16.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.162077, "duration": 0.015179999999999999, "duration_str": "15.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.889}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.189104, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.889, "width_percent": 5.024}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.198109, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.913, "width_percent": 3.087}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1481491826 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1481491826\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-385818860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-385818860\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-505557315 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505557315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1379701854 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299796773%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik44THFhY3pVZElvZFZ2MlVCVEs5WEE9PSIsInZhbHVlIjoiWVkzQkhRMmo5MDZpcTZjT2JwTW1FejlicTBMOTFXRnpScHdLdStVYWlialk2Wkp1YklyaUNGZWhad3hhTFBRR3o5YlpQblIrTkFGdXVML0VLbG1zR0tsMDlHMmgwSldTRzFsdWwvSXhvL1dubC9XdlZrL0VYcXlGcWdCVHZ2aWdaK3lMS25HczRFN0g0dUczUWZLa21FQnRqU0U4UTdDTFFqTjVnaDZKdUc3bjlzeUpncm9JeVNsd2lNc256Rkl2Y2JHdUVCK2tnQnc5VXovWDJHK2VaNStscmY4MGxVVGpzNFJ5TDFlZlZucXB0Vkl4MFhDeXFJR1dyR29EcXJSV3RoK3o0TTd6bERoSUNnWHpYVmVERkhIQVE1OWkzOEQzeU5lTmlxWjFpMWpaTUFaKzdhUTkrUGlaSVhvREZNZ0VreDVubmJIYnZrRkhMeDdEVENSMVhWWjFINXkyRTAyN0J0a2hweExEbGtQM1hKc3V5L2N3TGlkVWZIMUpzU3h5VkZIaWMzU29aMEk1VittSVltWnlEKzZTdEdpMWhlbXAvUXluaDd4Nnp0OXBZN0ZMemJFSXpzM2FCWFh0OEdQTCs4RFpGb2NjRlpFS296NXhrOGtYOFlpNlVkR1hnWmNoKytqNU5aQzdIZldiSjBqNHFHZUNiYmE2UmYxaXR6a2ciLCJtYWMiOiI3MDBiYzczZGE4MzU2ZWZlOGE1ODYzYjA5YTQ3MmZhZmVjNzk0ZTlmNzFkMGFhYmU3MDQyMDk2NDU5ZDAwOTcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjM2OHdXYjJTM3pXYW1VTEhQVFVlUWc9PSIsInZhbHVlIjoiazNSajdia3JnOVNvQWFkbHY5SVhWZzNVdi9EdEdRWGRjNG1CSHRnQ3BUREYxZ0wxTExoaDVFQWdJWWkrL2JrazRuM1ZQYklIMmVyRzRJeVJPbDdUM2xhazNMK1lCZ0xkdUszeThhd3M4TGtWZmhmZE8xZGM0UU1CNG0zWllUN0w2UVhmUEJCZThYYWNlb045U0FiaEtPQzVMT2UvV29sVjBYWHkxcFFXelo3a21TclNmb3k0R2Y5L2cranFJekl0VFh6aWU2QzNUNi9pSC9NQklGcWpIWVRPSDVLZzdlZ2RVcGZleDNYb2NoZlc3ZjVJck9BaWlOSVdMMUhnZHYvbGYxTUh3YW1IakpSSkNlaCt5MFJIQXpmTDRoNnBydkNHeElXT1FGbSt3UGVOaVR5OEt2bkZjU1ZTNW9OaW5ncEhadzExRVpQb0VnT2tIUC9DWEZ3YnNUUC9hRWdNYnQrS0MvQm5LaGdyRlZNQ0dGV0RDNHBBUTJqV1R5ZnF5QVJkc0ZkNkhYbXFzbGpNcWlzbk0ybnR0UmpKNWNteVJHSU5SMUZsenE2Vy8xSkdFamJvZ2h3eFBzUUNkNnVPN2xTOTExRVlNbHZmc1lTMW4ydG9FTEEydDZETUVDdUMwTktseU5uVGd2dHJxekxjRTJYMXJTWjVMeXYrS1ltZXpWeXEiLCJtYWMiOiJjMDQxMzg1NDQyMGQ1YTRhMDYyYzc0ZmY4MzZlNDE4ODZkNGU3ZDhlMGE4ZDhkNzkzMjgwZmEzMzYwZjBhOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379701854\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1201907560 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201907560\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1168859187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im4yWWV0M0lRSXFyQkhUME1vNXlSR3c9PSIsInZhbHVlIjoiQk1MdHpRbFBUOTNEZWtlWlJNcWRETlNrY2Y3TzVML1BaWEFQSFFFSG9HSDZqNlBnbmdQeDcyZVZ4dGkyUFdQMmczYVZqSzNuZ1hEcFN4M0VyTkhXdFlvanowQXQrRTF1UWpXSVpiMFlMSGhlWTcvUWU4cVVWVEMvZlcwUGxrMnF2cGtKeEkyQ0tNci9qM3VWZk9QRWhwUlhZQUFUTDBmNnd2TE5rZ0Y1dHBIUzdFNk51Wno3dkdKanFwZHhPQ2RneUtzSWd1TkFtRUZOWFozeWNybE9jZmRIVXZTV2s5Q3lja2x1V2FzRnB4ZU5TVVZUREpjemhIZTBTUXA4QlI3SXFwbjJqWDk0V0wxVG0rSWEwOGZBNWV5RUZoL1YwVDJFeitYVHhBalBtUVNWckNiTEJFWFMxeFBnMFVkSXhiclp4a3hjTnV2MzRQWlNkb3BTWUVlbUhxUmNSVmRnNm5SS1N3VWJpRlBMOTNidTVYV0ZPOVcyd1lOUGpRWGxCTWpSWTRmaGVOa042ZzdrTk1aVy8vNGkwTGI2blJXN241NUpISnVoYy8wRXcyckFHZC85UlFtMzNpTE90YTRuTlp4TytFN0NTeFBhQWoxUmhtcDl0UHBhVDE3aHZ1c0NMUUlMUXEyMm1rcTJlL3FSWm5HUWRVeHF2WUE5U24zVGo0MzUiLCJtYWMiOiIxODY2YjVlMGM4ZDI3YmM0ZmI4ODEyMDM0NTg1OTA0MDNmODI0NWY4YWE5NDJkOTg2ZjAyYzIzYzBiY2Y2OTg4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRLUUNUN1lKVnJqclVTdW1SelF2ZXc9PSIsInZhbHVlIjoiOFJjRzRnNDJ4d0pmZ3MzZUVYNFlTeW1MdC9mbGp2RXREYlBleEN6R2xtZ2hIdVRYeDNKWXdPQVdOaDNRcGFYTnlwVllYMjhlYzVwS0RiUlp2YmFOZWxOTExVOCtRSHRnWEtWTW5ueU1sZkZuWm9EeFQ4WkRnZFM1bTRQS3kzS1JtbjlCSDZGQWM5YWpIU0lMNVh4NkMxdFAvOWJ5VTB6QTdnRGYvZU9icXl4WUZYSjllejFKeUM3TUYvSHJGLzg3dWlFVlEvV1ZJaUFMZ3MxN05UbFZKcWNCbEZUY3FTTTZmR2VVY1NpRklwTE1ERjJBWmU4ejdPa042OUZ3dWFJQVdLcFAwOS9lRDh5UUQ3TWkzcHpCVEVRVzNUNnB4czlqV1VMY3ZiZVlKdjhIOGZ4bUtybkxpdG4vZC9uKzFHdzF4VmFtcVpLUU9Zc0M2NE1ROFBTSGFVVmpTWUZ5cG01ZVVTblhGMDI2ZC9IMk1GN0l1Zmk1bFVpbVJiUDh0VDJDQ3NpRXVuWnA2dGFzbjV5VVBzdjNZcGMxcUlCTEZqRGpoaDQ4T0hWUW9wSk5IbmxyTEVTbkdRWDV4TzhmbUJ2dVl2Mm01aUFpRmxuaTRheW5hSXVEMWIzM2czVkpFcE52clVwc25wVW5zVm5sMFYyQXpLdTFPSlE4bHJHUDBNeCsiLCJtYWMiOiIzNzU3OGU0YzY5NzZjZjIyM2ZiOTQ3N2ExNDI0OGJiNDFiOGQ4ZjQ5MjRiNzU3MWEwZDgyMzZhZmMwZTk1N2JkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im4yWWV0M0lRSXFyQkhUME1vNXlSR3c9PSIsInZhbHVlIjoiQk1MdHpRbFBUOTNEZWtlWlJNcWRETlNrY2Y3TzVML1BaWEFQSFFFSG9HSDZqNlBnbmdQeDcyZVZ4dGkyUFdQMmczYVZqSzNuZ1hEcFN4M0VyTkhXdFlvanowQXQrRTF1UWpXSVpiMFlMSGhlWTcvUWU4cVVWVEMvZlcwUGxrMnF2cGtKeEkyQ0tNci9qM3VWZk9QRWhwUlhZQUFUTDBmNnd2TE5rZ0Y1dHBIUzdFNk51Wno3dkdKanFwZHhPQ2RneUtzSWd1TkFtRUZOWFozeWNybE9jZmRIVXZTV2s5Q3lja2x1V2FzRnB4ZU5TVVZUREpjemhIZTBTUXA4QlI3SXFwbjJqWDk0V0wxVG0rSWEwOGZBNWV5RUZoL1YwVDJFeitYVHhBalBtUVNWckNiTEJFWFMxeFBnMFVkSXhiclp4a3hjTnV2MzRQWlNkb3BTWUVlbUhxUmNSVmRnNm5SS1N3VWJpRlBMOTNidTVYV0ZPOVcyd1lOUGpRWGxCTWpSWTRmaGVOa042ZzdrTk1aVy8vNGkwTGI2blJXN241NUpISnVoYy8wRXcyckFHZC85UlFtMzNpTE90YTRuTlp4TytFN0NTeFBhQWoxUmhtcDl0UHBhVDE3aHZ1c0NMUUlMUXEyMm1rcTJlL3FSWm5HUWRVeHF2WUE5U24zVGo0MzUiLCJtYWMiOiIxODY2YjVlMGM4ZDI3YmM0ZmI4ODEyMDM0NTg1OTA0MDNmODI0NWY4YWE5NDJkOTg2ZjAyYzIzYzBiY2Y2OTg4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRLUUNUN1lKVnJqclVTdW1SelF2ZXc9PSIsInZhbHVlIjoiOFJjRzRnNDJ4d0pmZ3MzZUVYNFlTeW1MdC9mbGp2RXREYlBleEN6R2xtZ2hIdVRYeDNKWXdPQVdOaDNRcGFYTnlwVllYMjhlYzVwS0RiUlp2YmFOZWxOTExVOCtRSHRnWEtWTW5ueU1sZkZuWm9EeFQ4WkRnZFM1bTRQS3kzS1JtbjlCSDZGQWM5YWpIU0lMNVh4NkMxdFAvOWJ5VTB6QTdnRGYvZU9icXl4WUZYSjllejFKeUM3TUYvSHJGLzg3dWlFVlEvV1ZJaUFMZ3MxN05UbFZKcWNCbEZUY3FTTTZmR2VVY1NpRklwTE1ERjJBWmU4ejdPa042OUZ3dWFJQVdLcFAwOS9lRDh5UUQ3TWkzcHpCVEVRVzNUNnB4czlqV1VMY3ZiZVlKdjhIOGZ4bUtybkxpdG4vZC9uKzFHdzF4VmFtcVpLUU9Zc0M2NE1ROFBTSGFVVmpTWUZ5cG01ZVVTblhGMDI2ZC9IMk1GN0l1Zmk1bFVpbVJiUDh0VDJDQ3NpRXVuWnA2dGFzbjV5VVBzdjNZcGMxcUlCTEZqRGpoaDQ4T0hWUW9wSk5IbmxyTEVTbkdRWDV4TzhmbUJ2dVl2Mm01aUFpRmxuaTRheW5hSXVEMWIzM2czVkpFcE52clVwc25wVW5zVm5sMFYyQXpLdTFPSlE4bHJHUDBNeCsiLCJtYWMiOiIzNzU3OGU0YzY5NzZjZjIyM2ZiOTQ3N2ExNDI0OGJiNDFiOGQ4ZjQ5MjRiNzU3MWEwZDgyMzZhZmMwZTk1N2JkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168859187\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-297872182 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297872182\", {\"maxDepth\":0})</script>\n"}}