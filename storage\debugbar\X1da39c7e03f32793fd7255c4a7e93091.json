{"__meta": {"id": "X1da39c7e03f32793fd7255c4a7e93091", "datetime": "2025-06-30 16:06:05", "utime": **********.841138, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.341399, "end": **********.841158, "duration": 0.4997589588165283, "duration_str": "500ms", "measures": [{"label": "Booting", "start": **********.341399, "relative_start": 0, "end": **********.784277, "relative_end": **********.784277, "duration": 0.44287800788879395, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.784288, "relative_start": 0.4428889751434326, "end": **********.84116, "relative_end": 2.1457672119140625e-06, "duration": 0.05687212944030762, "duration_str": "56.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034799999999999996, "accumulated_duration_str": "3.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8140218, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.816}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.825564, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.816, "width_percent": 14.943}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.833541, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.759, "width_percent": 17.241}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1092316772 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299561091%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii90Zk1VdW1JVFNLZ0JWM0VxVVhCenc9PSIsInZhbHVlIjoiSDcyY2Y3V3U1eEVuK3doS0w0QkF1Zjg0UEJFZktSYVZ5OGFIQnFMYk9FU2NoRnF4ME5QYkRoWXcrTTZVVWhpa2lSOWNWQ21hcEJ1Y2IwM2FRM2c4dkZqSloveWRsakN3SS80SEZlMDBZMjcwd3gzc1dsVDFyeHhreGFJU1NteXVRV210MG94TzFCcmNTdFdsK0R1djRDblhMK0h2YmpNdllLdGtuclFNSGs5Smp3Umg1ZFB3YVV2SG5QWmtPeHpTMVlaZmc1UXkraEFDb1BkUkN5dlVuYXpvdVFYTlFBQllaYjFUVEcxbmxMRFViOFFjUGlNMm9MVmJHTlVhV0Vma2x2cjVmTlhlMEIxNlVodEYvaTRXMzBDaldIVEMxdHRLVTQydElQU0xuZGpYdjZDL3RWZFF4Rm83elVIQkNwK09KV1o5VC92Ri9iUk5wQkhmUUVrdjlTMURqTnB3VDBEazdzVlZlYUFPWEkvL2tBV0pFQU93NFpMTjNKcXFmNHplSEdIaUlvODdMVzVTeXpYSktNV1pkZksvSmFQTnRWUitaMjQ0ZjRCbjl2YzBwU045UUQ4OWVjdGt3NlozenRSQUZIcTJyTWdJMjBSQitEUVVOUGIrNkRpL1lGZWZ6UXg0aDZkTWpIYXAyS3lQdWhxTndZQXppM3YzZFBYMnI3amciLCJtYWMiOiI3MDg5MzFhYmE4MDRkZjFkMzcwMzFiYTVmMzI4ZjczY2U5YWMxODY4NDY1MDliN2NlYTJhMWNiZTM0Y2QxNjA0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRiUndua004TjgvWVEzQUlKY1d1UUE9PSIsInZhbHVlIjoicWV6OTRieEliU2JubzlpRnNrNER3OW9TZjJnaFBQQ1h0a2ZsWXA3cExXdi92M2ZjaUJVYUMybDRMcEFrOUpTZFlHMmdTRmhIR1dmeklZc2luV0ZaMzVBeDFkOW9tWkVBTUtmUFlqMDRJQVUvYkNQTzZrZDJyTDRRdTVKeVZPbFdoWVd3bm1EU1hoWm0yMU9rWEk3eSs3TnpxVEQ0bmJGU2lOcm9UZ2lXL0pOSUZ6WjZmeXlSMkcrVmlKa2JzeklKdGxGVjQxVDkzWU1VRXF5M1V2dEN4OStkNW9VenI2YTRBTFR2eG5kOWRFV25mYW4rM2o1dUp1RFNMUDdPN0o2aTNUU2p4cVlUZGJ3ZXRWa0Zidi9KdmFCSHVNVmpGTmsrS1hlbndiUVR5TU5xUm5EYWE5bzlKSC9pSzFxUlRZWFQwQm5xdDlYNWpQR0l5T0lIYnIvTCtnVDFPVzA1aFdZMHJPeXZpK2pobURjNWl5d1ppWkF3Z3FHaytCUTY4REdQNTc1Nm1UU2NIdTRqS2xxekpoTHlTRytnNHdoLzQxRGcxK2tCNnQyS1ZnaGcyOWpjVXZyWnBQa0ZoVzErOXlraUpwQVZmaExTbGxJRjdVNTJ6UEE0U2lpSlJuOC9iTzErNjBUN2ZuQkNMTlAxdUpnK0ZyK2FZalRkWGRWY0VBOWgiLCJtYWMiOiIwOTIzMGY4ZjVjNDljODVjZWRiOTg2NzAxYTY4MGZmMTcyMDQ3MzRjM2ZlMGFjMGE2ODA5N2Y1YTY1ZWYwOTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092316772\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1101145475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101145475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1415435810 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVIWnpraGVqbHNUVFJNMm1RdGdyM0E9PSIsInZhbHVlIjoiYWMwdUJvUGxZWThvV0gwL2F3aVQwaGd5QWhoYStkZU9MUFVLTzBzKzlaTXFpakRjZXVsL0c2YWZVWW80ZUFqZzdKanJqZkVhbnh5TFZVTlo3MzFlb1ZqQTVJYzNxOVkwNnRPVXRmTTg2bHdRejMwZ3Z0WVl4bldxRzcrRjkrNHNzRlRqU3hjczFpRDUzVmZ2ZHVLOXhHZUNmR3lMME9pMmJqWTl1M05EVVBWTzBSRjc2aDFkakdrNGJ6cnprM3pMell3VFRBR00zRHlpVk1vcncvc0FDcENmejlXcTZWUjVtUWJLNEM5bnVkN3FaRkdkS3B1NWZkbTlsQU52RXZYZkZRbkFXckUyNHNQNUNDb3dkQ0xHYjVucnpVZUIyU1FzUTRMSkJRck1jSTVqcEs4NC96SkcxVU9NalhOQmRaYlBLT2ZuY3BuME5WL0loUitsY2pBT01JQ3V2RktZdmIzQ3lVbkVsSjFKaDUvQTg3Y3BuU2k0dncxY1JRTXVzanJhUEF6NE5iY3B6K0QrWktMTHlOS3BzdVRZSXg4T2pBeitjYXhEZkNuY2taNjZ3eWhId3d5UFBVeUlld3RLcVRZVzMyNUl6SnpDbDhyWDErM3lnZ3dhM09xNVRic01GT3p5bHdIOFkwYW10ck1XMXZ3OHpvQm1uNkwvWGlqbS9oK0giLCJtYWMiOiI3NDhkODhiYmQ5M2JhOTAyNTRmNmFkNjJmOGQwZTNjNjRhNDBkM2FmMDJhMmRlZjUyMTIzZmIxZWMzOTk4Y2I5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFSMEhQL3Q1T1hTSUltbzYxbHpBQmc9PSIsInZhbHVlIjoiREp6eHRuVWUyM09uRVowNmZhNVk5ZGczeGVOOVFBSlZvZDVsOTZRZXVvRExQOFhkbmdmREs2NnR6cE01T1Zya05SVTFHZkd0WWVIZXpkclBOdEp6d2RTY2J1YW0zMXVtbDVma3N5RFJhcmhjMm1qMTRqN3lOOVJpdmNPUm9oSVNybm9RNVFvejJuenFXN0tzMlRwQmpTanphV3BIVUlNQ0djdFpzLzhuNGE0T093U1JEaW42Sjh0empCNWJRaTlQeTVDeFVhNzVhaUdiZCthUnlrKzdvdlRzL05KRGQzTUJVWW1qQWdnZFgzRXBzTW1HeDFiamtKQzA3YlVVTkdJVVk0TzlHMnhTUmMxaFo3T1FNK2pzOG5lUVRCR2doZlRxdGVqNittSkFiWmhJRnVaNFdZdlByY29pWUowNldPQmlhY2RadE42b0VwSVJ2dFlzdWthbjdYSW1kanBwY0RTd0hCR0tjQW5pQmQ4blNYMkZOa3krV3FPbjFFa3Y0OUU5dlVMRlVqdWRjVUgvU21tWUI1OXFMYWx6T1JrUEsrVnQySTdZMkpBR2h3Q29WdFdWZ0Zqa0doL0FSdWJyTlpFLzVBZVM1eTA4ZUtDLy9LdjhHdkk0WW03ci9UTENLNzM1aktQQUhBVmJON2VvMWhWSXFWKzlUL2M5dVJwOFMyNXAiLCJtYWMiOiJiYzgxODExMDg4ZDMxODNhNzA3ZTI1YWFiNzgyNGNiYmY0MTExMzdjMjlkNDc1M2QyNjRkMjAwZjA2MWJjNWIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVIWnpraGVqbHNUVFJNMm1RdGdyM0E9PSIsInZhbHVlIjoiYWMwdUJvUGxZWThvV0gwL2F3aVQwaGd5QWhoYStkZU9MUFVLTzBzKzlaTXFpakRjZXVsL0c2YWZVWW80ZUFqZzdKanJqZkVhbnh5TFZVTlo3MzFlb1ZqQTVJYzNxOVkwNnRPVXRmTTg2bHdRejMwZ3Z0WVl4bldxRzcrRjkrNHNzRlRqU3hjczFpRDUzVmZ2ZHVLOXhHZUNmR3lMME9pMmJqWTl1M05EVVBWTzBSRjc2aDFkakdrNGJ6cnprM3pMell3VFRBR00zRHlpVk1vcncvc0FDcENmejlXcTZWUjVtUWJLNEM5bnVkN3FaRkdkS3B1NWZkbTlsQU52RXZYZkZRbkFXckUyNHNQNUNDb3dkQ0xHYjVucnpVZUIyU1FzUTRMSkJRck1jSTVqcEs4NC96SkcxVU9NalhOQmRaYlBLT2ZuY3BuME5WL0loUitsY2pBT01JQ3V2RktZdmIzQ3lVbkVsSjFKaDUvQTg3Y3BuU2k0dncxY1JRTXVzanJhUEF6NE5iY3B6K0QrWktMTHlOS3BzdVRZSXg4T2pBeitjYXhEZkNuY2taNjZ3eWhId3d5UFBVeUlld3RLcVRZVzMyNUl6SnpDbDhyWDErM3lnZ3dhM09xNVRic01GT3p5bHdIOFkwYW10ck1XMXZ3OHpvQm1uNkwvWGlqbS9oK0giLCJtYWMiOiI3NDhkODhiYmQ5M2JhOTAyNTRmNmFkNjJmOGQwZTNjNjRhNDBkM2FmMDJhMmRlZjUyMTIzZmIxZWMzOTk4Y2I5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFSMEhQL3Q1T1hTSUltbzYxbHpBQmc9PSIsInZhbHVlIjoiREp6eHRuVWUyM09uRVowNmZhNVk5ZGczeGVOOVFBSlZvZDVsOTZRZXVvRExQOFhkbmdmREs2NnR6cE01T1Zya05SVTFHZkd0WWVIZXpkclBOdEp6d2RTY2J1YW0zMXVtbDVma3N5RFJhcmhjMm1qMTRqN3lOOVJpdmNPUm9oSVNybm9RNVFvejJuenFXN0tzMlRwQmpTanphV3BIVUlNQ0djdFpzLzhuNGE0T093U1JEaW42Sjh0empCNWJRaTlQeTVDeFVhNzVhaUdiZCthUnlrKzdvdlRzL05KRGQzTUJVWW1qQWdnZFgzRXBzTW1HeDFiamtKQzA3YlVVTkdJVVk0TzlHMnhTUmMxaFo3T1FNK2pzOG5lUVRCR2doZlRxdGVqNittSkFiWmhJRnVaNFdZdlByY29pWUowNldPQmlhY2RadE42b0VwSVJ2dFlzdWthbjdYSW1kanBwY0RTd0hCR0tjQW5pQmQ4blNYMkZOa3krV3FPbjFFa3Y0OUU5dlVMRlVqdWRjVUgvU21tWUI1OXFMYWx6T1JrUEsrVnQySTdZMkpBR2h3Q29WdFdWZ0Zqa0doL0FSdWJyTlpFLzVBZVM1eTA4ZUtDLy9LdjhHdkk0WW03ci9UTENLNzM1aktQQUhBVmJON2VvMWhWSXFWKzlUL2M5dVJwOFMyNXAiLCJtYWMiOiJiYzgxODExMDg4ZDMxODNhNzA3ZTI1YWFiNzgyNGNiYmY0MTExMzdjMjlkNDc1M2QyNjRkMjAwZjA2MWJjNWIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415435810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}