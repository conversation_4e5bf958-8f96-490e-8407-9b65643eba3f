{"__meta": {"id": "X72cc7550b88533c019eaa18eb531bb59", "datetime": "2025-06-30 18:09:05", "utime": **********.183728, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306944.787232, "end": **********.18374, "duration": 0.39650797843933105, "duration_str": "397ms", "measures": [{"label": "Booting", "start": 1751306944.787232, "relative_start": 0, "end": **********.144042, "relative_end": **********.144042, "duration": 0.3568100929260254, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.144049, "relative_start": 0.35681700706481934, "end": **********.183742, "relative_end": 2.1457672119140625e-06, "duration": 0.03969311714172363, "duration_str": "39.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43874208, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.172614, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.716}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.177638, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 84.716, "width_percent": 15.284}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => \"2\"\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1300409277 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1300409277\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1842268778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1842268778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-95561856 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkwrNGFHY3JJR0ltdGdIUzErZnNtRmc9PSIsInZhbHVlIjoiVXNHSFdTM0NEdXFMMWpOcEkzUmxBNlRZWEIvc2ZMWlBua0o0WW96cUg0RWZvSTV5NGpEa3dlNU52bVVJQjdFOHI1UEk4THhaU3pNTTNyWDZFdExZN0pZZDdZYWhmekVZUXoxRUd0NTlpT3ZRWUdsU3NLU1AxMjFna0d6Y2N4QjNqTHJoWS9iTWdieGsraWJJbEVVcjdRbG9vZzBHQWFHSFNmN2NVMnJ4a0J0bGd3R0FWdUlOUTNJUXVKSlZ2bVpSbk8xa1FVbmRLbm5Ca29KbVZUOENiUXAwQlZNVjN6b1h3c1Q5amdyMzY5SVl4VTUrd0tNMFV5Y2ZIYXoyeUQ3dWdqdXdaQVhvYW81Y3VJc0doS2NzTnFYVTJrVXgwVjZ5MVZKSXFEcjllSm4zVUc0WWd5cEhOZ2Zoc01aa29FMkswRXNQRGMwUTZENjFGQ21vL0U3aDZQZWI3Qk1PUjZ2ZHY1QWVHWC9wN1hTYWg4UXllQ3BiQTljUCtNMnR3eFdzRHN6YUlBcm4zb2M3MXcxVWdaYmhCUjNQc0E5cWR2Yld2YTArVjRZWTFHQVRadVV0VGp1MGFmYk5JU0lpdjRyNG94Vm4wQ3lDSlFUUWMvRFBJZ3gzZ0ZKM2Q4RC9JQysrSkJaNFFRcDlUWjkvU3B2SUJkaEpEOVVOdDg2bGd5VGgiLCJtYWMiOiI3MzQ2MTU4Y2QzNDdlMDVhOTRmMDZjNTBjZGI3OGI3YWYyN2I2OWRiNDhmZDFmODk4NWQ4NmVmZTliMjgzNmVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ims5TWVObHY4WWJNSkVFQy83K0lzYWc9PSIsInZhbHVlIjoiMVU2dngwRENISmJJc01iVnVXaDAxRVlSQTdQQ0l2QUVKQ0pMNVdkaXNLZWozSkI3VUxuek0wZDNmT1JuQjVoK2dia3NVVGFtSUk0WjJPemptRkJFWjljUm05VFMwVVhoL2JwbU5nTXBObWQ2dVlmSjNQclVZaDBFMDJsQ0FscitsVlg3TXJyVXlZbmRORVJhRTRKNmhESExrMTBuU05VamlHUi9iN0IrVGxDYUxBa3hwazYwK2RxMzZuS2VLYzIwN0VHSy8xdjZQbTNYZFMyd0piYUt5Mm9kOG1iYkxYeU9pUi9FWmtvSXVYZHZraUgzYkpJb25QUU9VRXlsU282SWx0ZWJzci9CVXhXcnhrUU9FNjRLL3RHQkNxZ1pGaFpaMVJyKzY3OGVTbVFpK042WUs5NG1sNVhGYmpnNVg4OVY0YkluaHIzWktNdFBWSk1JcFpNNTRKT1dsaW05emxkMXlZWGpHQTh6ZHVyREV6OERLZkxQdTVPbmNoUmRtcUhOYUJiNWlUdXVsOHRic05Ockp4SE1ldXI1RFNZVkpvelRiK3BBSm9sWUdvb0ZsUHMzYkhzZTNqdzkrZmZrQ2FQYkhmYVdnYU0xK3VkQ0VCZWVSaEVIMVRxS2t5ekNNQ3dyNGQzUmJHWmpHSzFKdnlVMlBaQzd2aGZjYS9nSkxTQlAiLCJtYWMiOiJmYmVkOGM5YWYyOGY2ZGYwYjAwMjQzZjNlOGY5MDE1ZDRkMjNiODY1ZDhkYTYwNTUxNDZhMzZmMmEyYmE3Y2QyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95561856\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-823190173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823190173\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-277694917 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:09:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRUbW1OOE8vcEhtZU1LK2owZTM3aFE9PSIsInZhbHVlIjoiWkhzL2tvUUQ1NnVVeVBnNnE0YXkxc2lmbWd3NlY2RGRQeGRlUDhjZ1phNHdRQlpPa1pLYTc2Nmh0cVJSRVdKM1JueUNldm5EaWg5cG5SdUE5elNRbGNQU3dBS2lGR0xMQUNMK1B6TjVjRDl1ZzMvQ1hoMmh3cGlxWmZkb3NwN0ZRbjBSYys4OWpvV0dXd2QzU1JGempiWjJiY1pJa1RUYUNHamFkRUFtVXZRMUxhSUFNRy9kQnIwMmUyVlhaQjR0ejJETGVjWkVXTU9OYk9wR1VLVFBYQ2lVdmtkUW9nWTRpKzJhL1NXZnM5NEJUckxndmxhMWRSYitBQStha3J1RXRCTk9FcGs0K3pzQXlZeHJTK3oyZFkzMlI5VVNia1RzNElSSUMwN2ZZWUNUbG9UYnFrVzJNNGRpZm9EMkhBYXU2LzMrK2tEOHhTYXJGSGtXS3p2aDdvVEN5RUxMOEFYMGM4Qk9yK2pGbFhtR25OdDRrS25EYUU0MS9pazJpY1ZDTnRzQkZVbDdPbWsrbThPQ05vaHd6RHRmNTMzZjZ2ZnJwSmpuWG1xM0FrQWF2emJDR1pvTDJzUVJBaWxmYzFXdXM1RzVwekF0WURlZmVlUmt1YjJmWVpDR3JZZ0JoNXRvRE5KZHFoWkRxTVNJanRwTVFWMWxOSTVGOUFSWE9xZlkiLCJtYWMiOiIzZjJmYzE5ODBkOTY2MDVkM2MzNWVmN2UxZDUzYjQ0YzMzOTc3NmM2ZDE0NjE3YTdiYzcxYzg1NzUyM2I4MDI2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldMUXlpRFRRN3BRNHJWSDJYc09sK3c9PSIsInZhbHVlIjoiOHlLVGlrelFaUFE2WUlMTFhRSmZVa2Y1aEYvbFpTdkcyMFlGZUhIVmRZckYzMXpZRW9ZdXFtbUhYaFBJekNYcE9qRGdUM2tDT3pCdmo0aGhtWCtPVXcxMCtUaXZ1Sk9wWXAwSzRHU0liMmE1MW04eUZYMDhBa1NYM0tRSG5za1lQdlp3WXhJNEJCdnZQRUV1V0hrSE9INDJKWjlKYlRpYzBpY0lCdmtoT1VJdjlGK21URURiWkJ4K1NGY3QxUmE2aDJMLzk2Y25IOUU3dHpYdTZHZWxrZXViMUF3TzFWSVg3cDRndWw5WWE4YlpZMXRYcE5NSkhoeFl4RGhaRjJzUG4wTFdta2d0NlNLYnU5a01EWXZOdjVIak9wczlCamI2THpXNkdjazdkaDZGVHMwUzloTXhjL2NnWDR0ZDM3UnM5Y1EvUDhCQll3eHhMVmJZUU03ZEpwZjZnbS96Y1FSWVJSMmNnbmdlSnVDeVVlT0ZZY1BtS0ZWQ3ErcWptWlFGOUYxZVBadjJqbXJPSUZQNmRXejVxQXBOMzBSTHJXMmRmZkJ2ZzgwRldjUGZzanJ1T1UrMXV0K0dSc2hGc1NOa3d2UmZsc0RYdHpSRDRtVUwvUFdyZ0p6amgveEp5VWMvNmt2akY4OE5kMjViMjI3bTZHRHVidFZCM253VWdyWUMiLCJtYWMiOiJjMjZmZDVmZDg2NmJmYzkwNGY3Y2E5MGExZjdkNDExMTY0MDJmMTUwMDE2MjA0MzNhYjczMzQxMzA1NGFiM2M1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:09:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRUbW1OOE8vcEhtZU1LK2owZTM3aFE9PSIsInZhbHVlIjoiWkhzL2tvUUQ1NnVVeVBnNnE0YXkxc2lmbWd3NlY2RGRQeGRlUDhjZ1phNHdRQlpPa1pLYTc2Nmh0cVJSRVdKM1JueUNldm5EaWg5cG5SdUE5elNRbGNQU3dBS2lGR0xMQUNMK1B6TjVjRDl1ZzMvQ1hoMmh3cGlxWmZkb3NwN0ZRbjBSYys4OWpvV0dXd2QzU1JGempiWjJiY1pJa1RUYUNHamFkRUFtVXZRMUxhSUFNRy9kQnIwMmUyVlhaQjR0ejJETGVjWkVXTU9OYk9wR1VLVFBYQ2lVdmtkUW9nWTRpKzJhL1NXZnM5NEJUckxndmxhMWRSYitBQStha3J1RXRCTk9FcGs0K3pzQXlZeHJTK3oyZFkzMlI5VVNia1RzNElSSUMwN2ZZWUNUbG9UYnFrVzJNNGRpZm9EMkhBYXU2LzMrK2tEOHhTYXJGSGtXS3p2aDdvVEN5RUxMOEFYMGM4Qk9yK2pGbFhtR25OdDRrS25EYUU0MS9pazJpY1ZDTnRzQkZVbDdPbWsrbThPQ05vaHd6RHRmNTMzZjZ2ZnJwSmpuWG1xM0FrQWF2emJDR1pvTDJzUVJBaWxmYzFXdXM1RzVwekF0WURlZmVlUmt1YjJmWVpDR3JZZ0JoNXRvRE5KZHFoWkRxTVNJanRwTVFWMWxOSTVGOUFSWE9xZlkiLCJtYWMiOiIzZjJmYzE5ODBkOTY2MDVkM2MzNWVmN2UxZDUzYjQ0YzMzOTc3NmM2ZDE0NjE3YTdiYzcxYzg1NzUyM2I4MDI2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldMUXlpRFRRN3BRNHJWSDJYc09sK3c9PSIsInZhbHVlIjoiOHlLVGlrelFaUFE2WUlMTFhRSmZVa2Y1aEYvbFpTdkcyMFlGZUhIVmRZckYzMXpZRW9ZdXFtbUhYaFBJekNYcE9qRGdUM2tDT3pCdmo0aGhtWCtPVXcxMCtUaXZ1Sk9wWXAwSzRHU0liMmE1MW04eUZYMDhBa1NYM0tRSG5za1lQdlp3WXhJNEJCdnZQRUV1V0hrSE9INDJKWjlKYlRpYzBpY0lCdmtoT1VJdjlGK21URURiWkJ4K1NGY3QxUmE2aDJMLzk2Y25IOUU3dHpYdTZHZWxrZXViMUF3TzFWSVg3cDRndWw5WWE4YlpZMXRYcE5NSkhoeFl4RGhaRjJzUG4wTFdta2d0NlNLYnU5a01EWXZOdjVIak9wczlCamI2THpXNkdjazdkaDZGVHMwUzloTXhjL2NnWDR0ZDM3UnM5Y1EvUDhCQll3eHhMVmJZUU03ZEpwZjZnbS96Y1FSWVJSMmNnbmdlSnVDeVVlT0ZZY1BtS0ZWQ3ErcWptWlFGOUYxZVBadjJqbXJPSUZQNmRXejVxQXBOMzBSTHJXMmRmZkJ2ZzgwRldjUGZzanJ1T1UrMXV0K0dSc2hGc1NOa3d2UmZsc0RYdHpSRDRtVUwvUFdyZ0p6amgveEp5VWMvNmt2akY4OE5kMjViMjI3bTZHRHVidFZCM253VWdyWUMiLCJtYWMiOiJjMjZmZDVmZDg2NmJmYzkwNGY3Y2E5MGExZjdkNDExMTY0MDJmMTUwMDE2MjA0MzNhYjczMzQxMzA1NGFiM2M1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:09:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277694917\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}