{"__meta": {"id": "X905fc23d67d1224d0aa9c3706ce25761", "datetime": "2025-06-30 18:10:09", "utime": **********.291828, "method": "GET", "uri": "/search-products?search=&cat_id=25&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:10:09] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.276423, "xdebug_link": null, "collector": "log"}, {"message": "[18:10:09] LOG.info: ✅ انتهاء searchProducts - الوقت: 11.42 مللي ثانية - المنتجات: 2", "message_html": null, "is_string": false, "label": "info", "time": **********.287626, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751307008.886346, "end": **********.291844, "duration": 0.40549778938293457, "duration_str": "405ms", "measures": [{"label": "Booting", "start": 1751307008.886346, "relative_start": 0, "end": **********.21556, "relative_end": **********.21556, "duration": 0.32921385765075684, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.215571, "relative_start": 0.3292248249053955, "end": **********.291845, "relative_end": 1.1920928955078125e-06, "duration": 0.07627415657043457, "duration_str": "76.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47777656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00857, "accumulated_duration_str": "8.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.248182, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.287}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.257806, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.287, "width_percent": 3.967}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.271059, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 26.254, "width_percent": 4.667}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.272719, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 30.922, "width_percent": 3.034}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`category_id` = '25' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.277323, "duration": 0.00505, "duration_str": "5.05ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 33.956, "width_percent": 58.926}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2838662, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 92.882, "width_percent": 3.617}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2858171, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 96.499, "width_percent": 3.501}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1065409553 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065409553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.275856, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-913978402 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-913978402\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-541825974 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541825974\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2047335455 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2047335455\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNLV2lpek5qRlVjcXRuK3dWRE92Q1E9PSIsInZhbHVlIjoiWTZucXAyRUE1WloreXpqVlUvNlpGK2I4SUNHbGw1UWprZ0dZckRRY1N0UUVPb3o4U002RG94cGpkS2VRRjN4UHRXNDA3ZDcyUHBOdVEyWFpQemlrTE1aMGtrb1YraUovQUNXdFY1N3VmUXZ0QnFoY3FKeUlJeUR6a3ZHUGxzVUxpR2djRndwUUhUa0Y5V29XU3Zxek9XMlBkS052QmkxbStyOVROUkd4dGpMbFNRSzIycUpUUmwrVkQwWCtyQ0FFbnhlQkQvRkpSSndoYldtTDY1VkxZMDZVWnFrOE1HRVpRcE1zZEdiYlM5WGsxMmp4MmJUSGJhbjZjZVRsVWl1NTVGTHovSUQwT3hXMXRZOHZLVzZSaVB0MEQrOWlEanJKUmNTek5yNWd4TGo5RWJhekQwSEVhWTR1dTgwT0pMRWdmbEJrOFo0ZUEyWFc3a290NG84V2REQjRGUEFLWVdNelNyL3psMVU0SVFHQkR4a0VMUC9SVW5jQzNpM2JvU0p4L0tkenlCdkpoWStRQnVlcEJTSUJhSXBoUDVidXovNEovRVNtYlJyMy8yak1RTGF3VlpPVDRmZkFnVGZnZWYvajJwWlVuV3Ruell4Y3IxMmhERUlvUWZXVWNSMk1nVllMMUg1SGcxanM4MkMwSEY0YjdiakhRSm1Tdmx2b1RNK2MiLCJtYWMiOiJkY2ZmYzczOWRkNzEyYmRlYzFjNmY1NjcyODgzMDZhMTI1ZmE5NGY4NTUxMzhmMjBlMTE0OTRiYzE4OGMyMmI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZ0UU90WnR3YVJuQ2tvM0VXcnZpa1E9PSIsInZhbHVlIjoiRlJtTEtnT1RPa1ZIOHorU0JkaVdyM0pYV1ZZOHNOT2JCZkpPQkJkWGxLNDdEVitla1ZPb3ZrT0p1Rlh4OHRUeDVoM0s3UGxLNXc2UERqQk9QWURTczZqYWdpaE1NSXp4VVdvWE4yNlBSZFlSZU1IN0wyd2xyenZ5RytyT1ozVDR5Y3VkN0YzWmJ5cW4vUGdOd3ZaeWlObzk3UFZCK25QUStseGw2bmR0RkIxdElkNU92QlpmZmkzb2pzT2pYb2dlSUNSUGFNaURHVnVPVS9aRk4zZUVRTXVwSlZJUGgzb1loWEZWRFdWK3BHK3VhWGtLd3dJVnJQZmo3SVIvcmljVGY2WFoyemVUZ0xmbGlWUHdCWjFHRGNCK0NEZnFGOHozMWtPT1g3S3NZL1cwZjZ2dGNPb1pScldsa3Y0NzBGaHlqUzRDSDZYMWlMYkEvYkd6Unp3ZkhGOGZFWE1JZ3ViVHBMOVNlbWRJc0RRUm5TYUJ1TWxvNmtnYURNQm9sUC85YTBobi81US85Tld6bTlqMFFwMSsvOGFQSVI1UXN5Ky94czAvbTlNUjcvbm16M2RWR25oU1BrTkkzNHA3bFZzYk5xalBGNVZ6NU9vaHZscDJPbjhWd3dzSFJxWHIzS3JkUUdBNXEyTXk4WDl2NjB3eUZJcXhlVTJ0eTFZbm1zMUMiLCJtYWMiOiJkMGFkMDk2NGFjODdiMTgxYmUzNGU2NGI3ZmFiOWZmNWI0ZjZhM2QzZjY3YTA2NDA3MDIzZWI2ZTg1NzIxYzgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-608372301 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhWbnloNkZZTFpoK0I4ejl6RzdwSFE9PSIsInZhbHVlIjoiODJMaUhGSE5La0padmtucHQ0akxXQ0tzdUJjL2xtWHEzdDhRcWkwcUZaNzlXazVZeVVTNDg0MGJ2RWk2TWU4QkRGdENlZkdRWURabUozZ2xLMGhhVzN6bEM5OWxYWUxsdVk2S2U5RjZnYkZxY3lTVTNUU3RraHJwWitJNGxhOWk0UjFzeUs3L1U3UWxuZCs5dkRSUFA3blpabU45akVrV3NFWnZhZFB5azhzNklSOFREZ1JCMzB2Ly9WQmVGSTVXQkY3RVRmakNkOXp0bWd3VmliMGxUT1pRdVJFRTNaQ25ML3lEbk5kZ0J3M0hvb0JDaWRIaDFOTFVqYlh0NWlJRGp5RTRwc2tBVVU2T0NSZVIwaS9IZmkzOHVNZ05GRitCQWtoOUJyNktQV2poMWgwRFJvSzh1Z001UHBiNUFXc3ZGT2JjUUFYZ1JnUUl6QUJ0LzhhOTByU2krNXJ1OTZDcVBnaEd0R1BjT0V5ZmxqQ084NDhFeUFqVFE2bWoyVWZpU2dBK1JwMHZ1NXpkaUNPdXNsWk1YRSsydjY1TUJjeGRmVjZTSUMrVXp3cjNXcFN6NFFUeUFqY0NPZHVJdXFaeDh0RzVrNWNMM3BSNWR2WmVVT3VpVTNUN3dwMWovYXBBczducWMwdUNtYmxVK1JYMnd1aDVFdnIrUTZOVzFkbTMiLCJtYWMiOiJhMzBiZDIwMTIzNGQ1OTNhOGVhNjQxNDU4NjBhYWYxOTg5ZTkzOGVlMzVlZjc4N2Y0NjZlZjhmMzg3NmMxMTBlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik42bm53NE5zcURnWGM2aGt6eFZMZnc9PSIsInZhbHVlIjoiZXdwYTlKMVQ5V3dlTjI4MVlZRHFVOUVnZWxKNzdJU3pIQk5vM0xaKzF1OHdrdC9SelJMa1ZDa2xwY0pnaW5xVUFyV21qQlFPM3k1Y1ZacWdlejhBR0d2TVlJY2kxczlCTko0T3Rocng0MTA0VVRML29FMXEzRThQdng0SFpMZzd5dGV4OFJ6enhaZ3ovWWVRK3VLNUJ1b2xzQmxkWFNFWi9zOGJ6ZUE3K3NhQWp2NTZobmN0K1V3d1BkN1REdFpTS0toTW9waHpONE9vRndlL1pDWktlYzJSK2ZjQ1BDWFVXeW5wTTQyYkZQZEFxdDZXQXhHM1VEUnQyWGlKMWZZSm5OS3N0UW9USjFQSEI4Zk5XcnB2Mkc5dzJkWkJTVDUvK3AyQTFjZ3I4L0dwdG5SYVFDblFBTm81b01jN0QvcDJQdkx1VlpUUkwvNGFxeklQcjlwYmpRZGUwb2NNc1piY0xwVVV2QW5sWFNhZTgxdTZISnczeTlnU3ZhZzFrNjdDMHVjWndnbkxIcmdpTHhPVngwZUhOSVg4ck1saEx1Z1ZMdC9pVWhGTVdOQ283akNMSkNyRVR1cE9PNnlSZy9FN0ZTTGJGZmtWd3BzQThiRHdXbDd0dG5aUXpUTE1DMmRvZFNEaW5wWDJTTTdDM3RqYkdMUVUzV2svOURmQUxpdm4iLCJtYWMiOiJkNjQ5NTllYjJlZGFiNTBiNTZkYWNjOWI0ZjIwZjdiY2RjNDNmOGRiMmNiYzQ0Yjg4ZmNhMmI4MWM0N2FjZmY0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhWbnloNkZZTFpoK0I4ejl6RzdwSFE9PSIsInZhbHVlIjoiODJMaUhGSE5La0padmtucHQ0akxXQ0tzdUJjL2xtWHEzdDhRcWkwcUZaNzlXazVZeVVTNDg0MGJ2RWk2TWU4QkRGdENlZkdRWURabUozZ2xLMGhhVzN6bEM5OWxYWUxsdVk2S2U5RjZnYkZxY3lTVTNUU3RraHJwWitJNGxhOWk0UjFzeUs3L1U3UWxuZCs5dkRSUFA3blpabU45akVrV3NFWnZhZFB5azhzNklSOFREZ1JCMzB2Ly9WQmVGSTVXQkY3RVRmakNkOXp0bWd3VmliMGxUT1pRdVJFRTNaQ25ML3lEbk5kZ0J3M0hvb0JDaWRIaDFOTFVqYlh0NWlJRGp5RTRwc2tBVVU2T0NSZVIwaS9IZmkzOHVNZ05GRitCQWtoOUJyNktQV2poMWgwRFJvSzh1Z001UHBiNUFXc3ZGT2JjUUFYZ1JnUUl6QUJ0LzhhOTByU2krNXJ1OTZDcVBnaEd0R1BjT0V5ZmxqQ084NDhFeUFqVFE2bWoyVWZpU2dBK1JwMHZ1NXpkaUNPdXNsWk1YRSsydjY1TUJjeGRmVjZTSUMrVXp3cjNXcFN6NFFUeUFqY0NPZHVJdXFaeDh0RzVrNWNMM3BSNWR2WmVVT3VpVTNUN3dwMWovYXBBczducWMwdUNtYmxVK1JYMnd1aDVFdnIrUTZOVzFkbTMiLCJtYWMiOiJhMzBiZDIwMTIzNGQ1OTNhOGVhNjQxNDU4NjBhYWYxOTg5ZTkzOGVlMzVlZjc4N2Y0NjZlZjhmMzg3NmMxMTBlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik42bm53NE5zcURnWGM2aGt6eFZMZnc9PSIsInZhbHVlIjoiZXdwYTlKMVQ5V3dlTjI4MVlZRHFVOUVnZWxKNzdJU3pIQk5vM0xaKzF1OHdrdC9SelJMa1ZDa2xwY0pnaW5xVUFyV21qQlFPM3k1Y1ZacWdlejhBR0d2TVlJY2kxczlCTko0T3Rocng0MTA0VVRML29FMXEzRThQdng0SFpMZzd5dGV4OFJ6enhaZ3ovWWVRK3VLNUJ1b2xzQmxkWFNFWi9zOGJ6ZUE3K3NhQWp2NTZobmN0K1V3d1BkN1REdFpTS0toTW9waHpONE9vRndlL1pDWktlYzJSK2ZjQ1BDWFVXeW5wTTQyYkZQZEFxdDZXQXhHM1VEUnQyWGlKMWZZSm5OS3N0UW9USjFQSEI4Zk5XcnB2Mkc5dzJkWkJTVDUvK3AyQTFjZ3I4L0dwdG5SYVFDblFBTm81b01jN0QvcDJQdkx1VlpUUkwvNGFxeklQcjlwYmpRZGUwb2NNc1piY0xwVVV2QW5sWFNhZTgxdTZISnczeTlnU3ZhZzFrNjdDMHVjWndnbkxIcmdpTHhPVngwZUhOSVg4ck1saEx1Z1ZMdC9pVWhGTVdOQ283akNMSkNyRVR1cE9PNnlSZy9FN0ZTTGJGZmtWd3BzQThiRHdXbDd0dG5aUXpUTE1DMmRvZFNEaW5wWDJTTTdDM3RqYkdMUVUzV2svOURmQUxpdm4iLCJtYWMiOiJkNjQ5NTllYjJlZGFiNTBiNTZkYWNjOWI0ZjIwZjdiY2RjNDNmOGRiMmNiYzQ0Yjg4ZmNhMmI4MWM0N2FjZmY0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608372301\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-551405067 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551405067\", {\"maxDepth\":0})</script>\n"}}