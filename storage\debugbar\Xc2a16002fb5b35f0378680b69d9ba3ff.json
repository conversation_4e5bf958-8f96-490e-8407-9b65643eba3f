{"__meta": {"id": "Xc2a16002fb5b35f0378680b69d9ba3ff", "datetime": "2025-06-30 16:22:20", "utime": **********.4954, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.01966, "end": **********.495416, "duration": 0.4757559299468994, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.01966, "relative_start": 0, "end": **********.412541, "relative_end": **********.412541, "duration": 0.392880916595459, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.412552, "relative_start": 0.39289212226867676, "end": **********.495418, "relative_end": 2.1457672119140625e-06, "duration": 0.08286595344543457, "duration_str": "82.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721880, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01149, "accumulated_duration_str": "11.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4423559, "duration": 0.01072, "duration_str": "10.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.299}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4611661, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.299, "width_percent": 2.872}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4667141, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.171, "width_percent": 3.829}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1021280004 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1021280004\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-735261183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-735261183\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-394051809 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394051809\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300536063%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZNWEdzTmIyNFpJM3cwZ2krTytPQXc9PSIsInZhbHVlIjoiT2V6cGhtSjkzZU9yUUpNN3dYOEFuTUFEbCtnZ0RsMnFzaXQxK1JoVVc4WmlQQnpHNlRTcUtoLzloek9pQWU1U3AyYlArQ1BmSStKWVZLdHpVaXdlRzFrS1J1SENOd0VhR3VDUE9HdkcvK3RzbW9ZNGVFb0FPSGR3V1ZzckhHaGF2a3cxMVNYTTZuWStlbkhWdFZUeVZQWW9BYkhZdzQvdFN5TDZYd1hia1UxejNWMWpWdXd4UjhONWkvMkdBdzNhcWVnemtPODh1ZFRXd056eitaUS9RY0JMZWMxeUFJUXlEcTQ3cnNOVjVkZCtlMkVNV0VPaU83Rkhmb0V3Qkt0ZnBydWphbkJTdUF5SnBBaHRrV0R3amRweVBvVzFNNzVRYlQ4OTdjczMrMnE4UlJiUDhQZVZNYzgzblFnbFpyME1MUHhmUmtyamc3ZVdEZGpBeSszQlcrT0RDUTNwWG9PL0wvaFJJT1pRdUNkL3RBcWJvZTZlSXZmeWR6UVcvOEZhR2RnTXkxZFhSQ0VucUg1ZWFhVWV5Z0NJNmRUTDVsbllVN3ZJRW5NL1RFWS9CeFY0WDFLdnc3bDN0VUZaSEpPMk90ODlLY0E4V1JxUnliVDUvY3kwZjJ2YkRhYlpEdWlzQldWdjNuRm9iYVV2d1Z1dUorcDI4QWNSRHU2dXI3Sm4iLCJtYWMiOiI0MDYzNmZlY2VjYjhjZDBjZGY0ZmUxODc3MDAxMzNkNWIwOTA1ZTRjODA5ZWZmZDk1ZDhjZWMxZGQ1ODhlZDdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZyWGJUZURINExmclhGM3Eydk5laUE9PSIsInZhbHVlIjoiczRvKzdQOW9nR3M3VzYvK1huUWpSK3RYa2ljYm0zclV5TjJtZE95Y0ppRXFrUFdzYmtlZjRwcXo0a3RYemI0L2VBNlVycktYUE5XUjdQL2JOVHFiQUNDUGZybHVXcmFNa1NHbEppVW56RjFuSkNCS2hYN1RtVExXbHdlemZ0c2Jub29KRjZiTlZIdEpGRnRvSTQrQmdDbWQ3NG1rVWxaN3FwaU50Vm5rZEtlZkpNR2MwV3ZPcHpncUJzSEdNNFZnSTFnbTJzWXB4Tm5BMUhOeDZxZHl4M29DaHJFUVN4ZjdpUHZMeDdTVUo0VzN6OE45Kzgxc0xDR01maVp1QjZUVEFMMVJGUkFMalI0MkM2UDRZYVhVMlp6Vkg2ZGs5WEVtUTJzVFdqSUg3Nk9xZnNzUGZKdExibzc1clNEU3Q1VVZDNjN6Z1o4OWVhUHlWdm5PNmwwcjBjMmxPVStnWU9OemlpYUoybkNuYXpPUG5WMCtTRmZQYWUrZUxnd1lQRnhyTEZhNEVaNzdlSWRPUmkxRk5yL0NDNUNQWDgwcFpvU3V1TUZFNVVVelAzMkVjUDZQTGx1bDNuU3FTUTI2aWd1RVBvYXJUZ2hyOHNBZU0yVFFZSXBSZkJROHltUkpQMlUxckkvMVo2QjBLV1F0OWZEYWh3TGNYcjlzWmwxOSsydWkiLCJtYWMiOiIwOTZkYmRhYjZmZWUyOWQzMjAyYWI2MGRkNjZjMTQ5ZWJiNWFkOGQ5NjM4YzE3YTEzYjBmMGNjZTBjZWI0MTBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-98793052 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98793052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-867783740 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpsUWYxS3UrMStodzRpeDlRYmlxQ3c9PSIsInZhbHVlIjoiSWxVUStBaHFpVUFlQjdTUGRESUVBam8zd1ZRcUQ4SHcwcXhMZjZUT3pVcnVISkRkWVdVQmlmOWhmVWpoODBJWm55NDQvZ09MdjMxZWRPMmJsUWdEOXNPalhGZTRvNUhLdmVjQkU3S1V4YzJjb2p0KzNuMUVhNExDZk9FVzFWZDZoN0lyQ1FNWmh2L3hiNklzcVhqdU1DSy8xL0E2Vm0yRXVFNmtKVzhIUjJvVEg4ZncvUkhYRWFjcUxnQXIrd0tGcDBDWi9KbVM0cDhTY1hRWFhTYWY3SDNLWkRQVG9ac2U3dG9ta2xKYUY2SjJJQllHUUtZTXVGMmlCakwwK0ZDaHpmU09hK2xOS1NtWk8xU2tDMDk3bG9oNm1sdS9GS1RiSnY0N3J4dlMwY3d5VXVrbjhrNmxQQWU2ejl4aWpNcmxaNVpXWkxTRXBuSHFMRXdkQ2t5YlZ5a2k1QkhMUWlSRXN3Sm5nSnBaTFJ3cWZtNm80YnJteDNiUTQwYlZoazRvTUdCM0hCTlEvV0xjSnFlNEVYVVNNTmZHc2szcSs1ODVRaTNGRmtGd1ZIdkN0RWJSN1lvU1ZLY0RudzZZVzVUditoVVpZc0w1RDFDUjRKOEs1b0I3a0h0eUZ2U2JPRWV4ZHBOZThRUDAxbEZoUWRwMFUwV1FwNG5iQjdKb0lEVjgiLCJtYWMiOiJhYmEyNWYyNjc1MGE4MWRhN2U1MmU3ZjVkYzQ4YTc5MDc5ODhhODY2Y2YzMDEyM2FlZjFhMDNjNTY5N2JlMGYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBmVGxLV0wxOHVhNzN3c1pETWMzZ1E9PSIsInZhbHVlIjoiWHhJN0dubUlpWTUxZktVZnNIampqenpQNUxJL3UwdTZlSk1rWmp2UzJsMThQVDlKVWd0UUFuMGVKV2I3TFBTam5CWWpKTzlnWmZSNkZWQTJYN2NQdmE4L2JQODRYTHR2NDZaMzhQZ243SSt4RC9NQ3BOM0o0L0dMV1dEYUhLZ3lUdnU5eTJhdGFZZzJLS1FYWkNHaHF3VXZFOHJTVXR1WStZY2dNUS9MREtCeU4xMGc3WTZkSHYrZmFCcWV3Q3FrWC9ldUxJOFB5bHVOUnFBQUhXMXJtQXV2VXJ6VDZzeFZNNktlL3BhU0R0d3hPMzJhYWpnRU56SGtDVXN0TEw1TTBVb1FHT2toWXVvUldjYUl4ZDYyZ05EOUU5Z292T1hqS2YwM2dQZjhuUi9uTlhFWWlrZjlpTmVOYy9PU1hidWc4djZGT1VJYXFzV0pYZkQrZGVaS3RTQWZlV3Y2MURLeFRtY1h4aXVZRmVlQUhxK08ySUNZeEQ0dktKWlVIaDhOT1RsZzQ4dUtJZmtBRXJobm1OT3BLeVdVeGR3WkVIUmV6alhyUTlWK25aV2JVWXNvTWN1MG50Mi9obDVQUmJJRTI3d0FCUzNaOW40OXZrdUw5N3ZrbVZrSGZ4cWcySUl1RldaZmNORlFGbUpsSmR0YllGcmEwS3ZmY3diSG1EZVkiLCJtYWMiOiJhNTBmZGIwMmJkODRhZjhlOWU1NWFmNDkzOTE4ZGU1YTdhMDA2YWJlMGE0ZTc0NDkwYzg5ODRjNzBmZjMxYjBmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpsUWYxS3UrMStodzRpeDlRYmlxQ3c9PSIsInZhbHVlIjoiSWxVUStBaHFpVUFlQjdTUGRESUVBam8zd1ZRcUQ4SHcwcXhMZjZUT3pVcnVISkRkWVdVQmlmOWhmVWpoODBJWm55NDQvZ09MdjMxZWRPMmJsUWdEOXNPalhGZTRvNUhLdmVjQkU3S1V4YzJjb2p0KzNuMUVhNExDZk9FVzFWZDZoN0lyQ1FNWmh2L3hiNklzcVhqdU1DSy8xL0E2Vm0yRXVFNmtKVzhIUjJvVEg4ZncvUkhYRWFjcUxnQXIrd0tGcDBDWi9KbVM0cDhTY1hRWFhTYWY3SDNLWkRQVG9ac2U3dG9ta2xKYUY2SjJJQllHUUtZTXVGMmlCakwwK0ZDaHpmU09hK2xOS1NtWk8xU2tDMDk3bG9oNm1sdS9GS1RiSnY0N3J4dlMwY3d5VXVrbjhrNmxQQWU2ejl4aWpNcmxaNVpXWkxTRXBuSHFMRXdkQ2t5YlZ5a2k1QkhMUWlSRXN3Sm5nSnBaTFJ3cWZtNm80YnJteDNiUTQwYlZoazRvTUdCM0hCTlEvV0xjSnFlNEVYVVNNTmZHc2szcSs1ODVRaTNGRmtGd1ZIdkN0RWJSN1lvU1ZLY0RudzZZVzVUditoVVpZc0w1RDFDUjRKOEs1b0I3a0h0eUZ2U2JPRWV4ZHBOZThRUDAxbEZoUWRwMFUwV1FwNG5iQjdKb0lEVjgiLCJtYWMiOiJhYmEyNWYyNjc1MGE4MWRhN2U1MmU3ZjVkYzQ4YTc5MDc5ODhhODY2Y2YzMDEyM2FlZjFhMDNjNTY5N2JlMGYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBmVGxLV0wxOHVhNzN3c1pETWMzZ1E9PSIsInZhbHVlIjoiWHhJN0dubUlpWTUxZktVZnNIampqenpQNUxJL3UwdTZlSk1rWmp2UzJsMThQVDlKVWd0UUFuMGVKV2I3TFBTam5CWWpKTzlnWmZSNkZWQTJYN2NQdmE4L2JQODRYTHR2NDZaMzhQZ243SSt4RC9NQ3BOM0o0L0dMV1dEYUhLZ3lUdnU5eTJhdGFZZzJLS1FYWkNHaHF3VXZFOHJTVXR1WStZY2dNUS9MREtCeU4xMGc3WTZkSHYrZmFCcWV3Q3FrWC9ldUxJOFB5bHVOUnFBQUhXMXJtQXV2VXJ6VDZzeFZNNktlL3BhU0R0d3hPMzJhYWpnRU56SGtDVXN0TEw1TTBVb1FHT2toWXVvUldjYUl4ZDYyZ05EOUU5Z292T1hqS2YwM2dQZjhuUi9uTlhFWWlrZjlpTmVOYy9PU1hidWc4djZGT1VJYXFzV0pYZkQrZGVaS3RTQWZlV3Y2MURLeFRtY1h4aXVZRmVlQUhxK08ySUNZeEQ0dktKWlVIaDhOT1RsZzQ4dUtJZmtBRXJobm1OT3BLeVdVeGR3WkVIUmV6alhyUTlWK25aV2JVWXNvTWN1MG50Mi9obDVQUmJJRTI3d0FCUzNaOW40OXZrdUw5N3ZrbVZrSGZ4cWcySUl1RldaZmNORlFGbUpsSmR0YllGcmEwS3ZmY3diSG1EZVkiLCJtYWMiOiJhNTBmZGIwMmJkODRhZjhlOWU1NWFmNDkzOTE4ZGU1YTdhMDA2YWJlMGE0ZTc0NDkwYzg5ODRjNzBmZjMxYjBmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867783740\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1493613456 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493613456\", {\"maxDepth\":0})</script>\n"}}