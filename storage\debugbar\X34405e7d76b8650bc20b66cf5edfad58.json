{"__meta": {"id": "X34405e7d76b8650bc20b66cf5edfad58", "datetime": "2025-06-30 18:08:27", "utime": **********.352726, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306906.953002, "end": **********.352746, "duration": 0.39974403381347656, "duration_str": "400ms", "measures": [{"label": "Booting", "start": 1751306906.953002, "relative_start": 0, "end": **********.286646, "relative_end": **********.286646, "duration": 0.33364391326904297, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286669, "relative_start": 0.3336670398712158, "end": **********.352749, "relative_end": 3.0994415283203125e-06, "duration": 0.06608009338378906, "duration_str": "66.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020340000000000004, "accumulated_duration_str": "20.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3146598, "duration": 0.019530000000000002, "duration_str": "19.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.018}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.343286, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.018, "width_percent": 2.36}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.346221, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.378, "width_percent": 1.622}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1894841269 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1894841269\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2037315358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2037315358\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1657193883 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklmMUhNR3FOYWJFRDdjQk5hN3BCVWc9PSIsInZhbHVlIjoiK3g5N2UrYkVhYWhRYkFXODZ4VVVUWk5tNmtBNDlMZlRWSGIxaWo3cC9kcEFQWWdiTk5ubkFidW9QTDBXQnkxbENpOVdyMWF3T3k3MktmOE1DM2JDQXZKdnlGY1NiVnBkbmxTSzNXVUhMNFpWQjRPQncyOG9yZllMTDByRFl2T0dBNmJSZjl5NTZqVStwMVYwWXpZQnpUZzJHeTdTZnlnUzJXRjMxeGNpWi9qK3E3RW8yeGViLzhlZThqLzB3S0FNOGI5SC85MHh4MVhpMHhJV2xYbWw1aWVQeWVBOXdTMk1HNGU0QVB6ck9ON0tMVi9ReFFTbDFyK3JPdjNuT3ozVnQ2TWtzNW56Vlpuei83L1VEY1AzK0tma3ZMOWtDT1JNWjRMK1NCUjRiTEVXenFML3l2RmV6UC9sZzUwbDIvdTVGVGpjUHk2YlhiOSsva0RQazhaemxVUC9SMlhpbU9sVG5uTm1BQW8vQnZqNmpGK0M4WjVXdWRmKzRxamxXZUUwU2R0UklQSVFrSnFYTUZDOGUzMUNuM0ozNFZydU5kQWxzT0pzZ0JkNDh4VmFnZ2lVKzRFN09wbUpUS21ReHhGSjJoMlkzNkJGejRIOTRYYXNjZlNKekUvTjlidkt5bm1pYUtCSDV6cVFmRnFpdnF3UHBOL25XYWZaNkhPUFovMVoiLCJtYWMiOiJhZDJiNmY3YjQwNzNkMjRhYTMyMjFjYWIyMjM2YmRlYjJkNmJkZTljNTIyMTExOTRkODFhNWU0MzM3YTY3N2UyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlplSXlFWnVhZnZRSURpMjYxSWQzbWc9PSIsInZhbHVlIjoiTXVrNzFySk9ycldLeFNyNHhkdVhLMzZDTXZXZVY0S29kZHM5dWU3ZnFNd0hmOHEzcndYY2pDRU1GdzVSZ25sY2ZzdEtQSVN4ZlJGQlRKWFJpckFLczNFY1BIUUE2YUJLR3RuNVpOdWJpY1JJV0thS1BGMFh5VXFxSmFJUVdMOGFjSmcvbTVvVEZEYlllaHJ4MmpGdElMUE1JVHl0L2hiWWptb2I5R0FOK3VHZEZVWVJqeEZLZ1N6NjEvRmxCZVV1RnFmQjh5SEdZVTl3OEZCNVFyWS9WOEFURUd4anlqY1hhc0EvZjY3ZEdZZ1BBRmZ0UllwbS8rcVhIdHMxYWZKSEM0dEZEcHU3M1dITlB0T1ZBeXZJdzlQUTVXQ0JlR3k0WEc5L0FmM3ZNMlZTT3Jqa09nK0JEc25PTG84NkNyUEhCU0kyWUQzak0rUDZOVTd0cGpNaXhqckNjdXBvNlh4RXREUDFRbTdjM2o1S0pSNFJpZ1QvaEVTTkhSWDRpNUdzYkdEZ2RuNVROWCs0RGpIRGNZSm16MUl4Sm9lSS9QS3J4ZFNjZ1VnV2lPclpSdktTaWI3c0l3aysxR0p2YzY3OVJYNjJLM08veTRDNnFRNVdsMElEcTF4K2gwT0sxOTJYVjFxbThzZ2ZtR210UjlDL0VxN3A3R1Jmdk1SdFNJR04iLCJtYWMiOiJmOTg1MWY0NWRjNTI4NGNjMTg5ZGE0ZDA5ZDBlY2E5MGE4OGYzMDc2MTA2NzQ0Y2I3M2E0YjQ4ODRjNDVhNzljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657193883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1722911285 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722911285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1143834301 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:08:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im0zY3BvTmt5ZlRkc3NpSXpleWFMMlE9PSIsInZhbHVlIjoiQ1JzWS8vaXM0UXpNU0tuR3F4c1paazVwbXlPUG5oRDdLUTdtbXBPOTNOb3BrcEdEd3FueHF4NTRuSi9IcUFxUWNuL0ZHa1ZlT2lkTkVTa0pvM1RIVVUrblhKZkoxeGJtWTRzTzduVVRDYVBnSmxqWUhYU3JHT0dxblV3VVVXMHhEUzR6T3FEQllVc2lWZFdqN292Y1Z5amhMc1BWRXcyaDdTNEdLYUwwYTlmQllDYUI2cTV6N29wTnR5QW94WlllN0NvQzJHb2tsckk3c0h3OU4wUFhJdHV2ZHpyUGhSMVFMTk1rOFpwbFFPODNQeDRQN2EvZ08rNDMwYVoyT0VCQzErWW1KazE3OGY5djJTS00xM1RlaWhhMlEvbFp3YzhyWUY3c1dsVUFUdXVkbldDMS83N3g3eU5qdkp0Z1pVcjAzRHBIUDgrVDVjMmRiVG1SWU9Hd3hjNjBNMFJVd0JJS2NFZnZSOGNHSkNyb2MyRk1NNzFvNlloandIK0l5Z3k1NUEzcE5BSGhjU09jOGF6bmMvWjZ1OHVMVEJKZTkvejR3RmlTZWUxdmJuQjNDcXlsL3VVRm5mRHBzRnhVeDdHTUpLbnhWL0dydEpNSWtVczJFZE1vdy9VZXJtdVJpMXlZSC9JV3VlVXJrTGsyQXgyZHpSUmphVEdjRkRIQXhZNmwiLCJtYWMiOiJjODY2NjcxZGRhZjExN2Q4ZTAwODNlNDE2ZGE5MmZjODQzNDI0ZjY3M2NlMjAyMGQ1YTg2YWI4MWFhZDBmMjE0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpBK0NUNUxEVWVKa25qVjVQSEtCcEE9PSIsInZhbHVlIjoiVlo1OHRiVHcyRWdrcXl0NGx1ak80c3pnRDhnZ3pnOHpvZGVzUUZ1SldSOEJvcC82U3luN09uaFFFdzR1YlZGR2JGOTUxMlhaVVNHd1ZEbWNmeEs5TVYzbnpLY05vUWI3MTBaUTR6R1RocndEZm5HSm5DaHBmcjU3a2NLWHFOTjdGaC9GOWEySXhYWGRwcGdhREJWd28zdDNJZUs5QlBFaXFHaGN0YjE5TnNGVkJUb2l4K3EzWlBkTkFZMmlvOFhQUlVGYVN3ZlFvbWliY29FTktFZ01CZzdrVW1IOWFYaXVEZm95T0RRN2FLbmpzTjVLenNIM3NBbzJHU29vNkI1ZHBrWTdHdmVQcDV3a05BdEh0NmdndjRJS1Q4MU9EeXZqV0pzaitHU0hOYnAxTFNIcGx6M1hUbTNyQnZQQWlSbkRaV1ZTRTdDRC9YREZlU0NHeHIvcFVmTWVCTzBoaDhEU0NSaCtDU25INkVyYXg0QjM2Wm1McWduK3dudjZkQzNuZnpVWDNXdnFmU1JRWlhVMDlVcngyQTEwci81ZG9Id1Y0MEVQYkwxcTA3b25qVU5veVNQL21Ga1NveWdlaTEvbm1PNXl0UGpPMThXN1FQSkJGUTZSa2pqZDhmOVhzQlo1YnNmRzhkTUNLMHRCaklPWUIwMndkZm9rRjVLektyRXoiLCJtYWMiOiI2NTM1ZjliZTExYjA4M2JmYzNlODRkNDIxMmY3MGE4ZjE3MTg3YjA0ODg0ODdmMTdjY2ZjMjM0ZGExOWQzOTRmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:08:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im0zY3BvTmt5ZlRkc3NpSXpleWFMMlE9PSIsInZhbHVlIjoiQ1JzWS8vaXM0UXpNU0tuR3F4c1paazVwbXlPUG5oRDdLUTdtbXBPOTNOb3BrcEdEd3FueHF4NTRuSi9IcUFxUWNuL0ZHa1ZlT2lkTkVTa0pvM1RIVVUrblhKZkoxeGJtWTRzTzduVVRDYVBnSmxqWUhYU3JHT0dxblV3VVVXMHhEUzR6T3FEQllVc2lWZFdqN292Y1Z5amhMc1BWRXcyaDdTNEdLYUwwYTlmQllDYUI2cTV6N29wTnR5QW94WlllN0NvQzJHb2tsckk3c0h3OU4wUFhJdHV2ZHpyUGhSMVFMTk1rOFpwbFFPODNQeDRQN2EvZ08rNDMwYVoyT0VCQzErWW1KazE3OGY5djJTS00xM1RlaWhhMlEvbFp3YzhyWUY3c1dsVUFUdXVkbldDMS83N3g3eU5qdkp0Z1pVcjAzRHBIUDgrVDVjMmRiVG1SWU9Hd3hjNjBNMFJVd0JJS2NFZnZSOGNHSkNyb2MyRk1NNzFvNlloandIK0l5Z3k1NUEzcE5BSGhjU09jOGF6bmMvWjZ1OHVMVEJKZTkvejR3RmlTZWUxdmJuQjNDcXlsL3VVRm5mRHBzRnhVeDdHTUpLbnhWL0dydEpNSWtVczJFZE1vdy9VZXJtdVJpMXlZSC9JV3VlVXJrTGsyQXgyZHpSUmphVEdjRkRIQXhZNmwiLCJtYWMiOiJjODY2NjcxZGRhZjExN2Q4ZTAwODNlNDE2ZGE5MmZjODQzNDI0ZjY3M2NlMjAyMGQ1YTg2YWI4MWFhZDBmMjE0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpBK0NUNUxEVWVKa25qVjVQSEtCcEE9PSIsInZhbHVlIjoiVlo1OHRiVHcyRWdrcXl0NGx1ak80c3pnRDhnZ3pnOHpvZGVzUUZ1SldSOEJvcC82U3luN09uaFFFdzR1YlZGR2JGOTUxMlhaVVNHd1ZEbWNmeEs5TVYzbnpLY05vUWI3MTBaUTR6R1RocndEZm5HSm5DaHBmcjU3a2NLWHFOTjdGaC9GOWEySXhYWGRwcGdhREJWd28zdDNJZUs5QlBFaXFHaGN0YjE5TnNGVkJUb2l4K3EzWlBkTkFZMmlvOFhQUlVGYVN3ZlFvbWliY29FTktFZ01CZzdrVW1IOWFYaXVEZm95T0RRN2FLbmpzTjVLenNIM3NBbzJHU29vNkI1ZHBrWTdHdmVQcDV3a05BdEh0NmdndjRJS1Q4MU9EeXZqV0pzaitHU0hOYnAxTFNIcGx6M1hUbTNyQnZQQWlSbkRaV1ZTRTdDRC9YREZlU0NHeHIvcFVmTWVCTzBoaDhEU0NSaCtDU25INkVyYXg0QjM2Wm1McWduK3dudjZkQzNuZnpVWDNXdnFmU1JRWlhVMDlVcngyQTEwci81ZG9Id1Y0MEVQYkwxcTA3b25qVU5veVNQL21Ga1NveWdlaTEvbm1PNXl0UGpPMThXN1FQSkJGUTZSa2pqZDhmOVhzQlo1YnNmRzhkTUNLMHRCaklPWUIwMndkZm9rRjVLektyRXoiLCJtYWMiOiI2NTM1ZjliZTExYjA4M2JmYzNlODRkNDIxMmY3MGE4ZjE3MTg3YjA0ODg0ODdmMTdjY2ZjMjM0ZGExOWQzOTRmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:08:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143834301\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1846870299 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846870299\", {\"maxDepth\":0})</script>\n"}}