{"__meta": {"id": "Xa9916a206cfd547f53f0ad4049a3189d", "datetime": "2025-06-30 16:50:35", "utime": **********.355437, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751302234.857469, "end": **********.35545, "duration": 0.49798083305358887, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751302234.857469, "relative_start": 0, "end": **********.242083, "relative_end": **********.242083, "duration": 0.3846139907836914, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.242091, "relative_start": 0.38462185859680176, "end": **********.355451, "relative_end": 1.1920928955078125e-06, "duration": 0.11336016654968262, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03198, "accumulated_duration_str": "31.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2851892, "duration": 0.02578, "duration_str": "25.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.613}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3196461, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.613, "width_percent": 1.376}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.333734, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.989, "width_percent": 1.376}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3355842, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.365, "width_percent": 1.47}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.340188, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 84.834, "width_percent": 7.849}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3454728, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.683, "width_percent": 7.317}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1555331999 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555331999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.339183, "xdebug_link": null}]}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1138930295 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1138930295\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1293415317 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293415317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-605030423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-605030423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-164673313 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751301269373%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpJdzVSSmdUYytSb0xhVGNrc3lnUnc9PSIsInZhbHVlIjoiVDZNTzJmYWV4bks0ekhaeGRubElqQVlFRm5ZZ0dDMlhaL0JQYzFkSjNoUVRPMFc3T3VxWFdieHdweU1XWERKTkZVSGg5ZHg5T09XRUF5dnJVMW9JbFN0VWR4MjZHYldVSUR5MnBIZm9NVkJib0FSenJleHJiMnd0cktmZ25sZUZPWGJwSWZLZE5mUTVzWVJtbGRMSDRlSnNjaktTQ2k5WThXQzhCdnJoWXVCZ1BGUWRJTDN0cituMnpRL2V1RkdyRG1DVjEyWDJ6dWpEdC82TUJwZW43WHRWUmltNHVuemJXOTlGVWl4REFtQ3IrbDRhWFJxZzdCWVROWVUrK2lGMFMrbzFVbytTSm9RWGhudUh4SU5nQkNDNEtUaVY4QWRtT3VBUjh6SEg0MHhMa3RoRUNpMGdkYXd6QWQxeG9ybkxOblhmaks4ckpqNG5oa2ZDM05MbGhqQzAvaUM3bzlpN2xGUjVJVVdvZmdNUGJyTG8vS1Q0S0RhUld2YlRNdlRwNklUSk11SWFGWGtGYXNrSmhGVmVaZUVvT3RMTnFrRktLZnkvNWhvZ29tcGoyVk5lckZRZUYvbUU1MTJacE4wNHZ3dDVNTzRZd1dzSzNveU9Tb1VMQ0lzUWhtblNoc3NGSTRUMnpQSTk5a0lIU0JRNmZjVVFhS3B5dVhEZUlxRHkiLCJtYWMiOiI2MzFjOTg5ZjU0ZWIyMDJiMWU0YzQwNzQxYmUxZjY0NDVmODg1Zjg5Y2NjM2NjNTk1ZGQwY2JjMWY2NjFjNDkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJ6anFVeVJjYXlpNUoxMHhLb1dIUHc9PSIsInZhbHVlIjoiOEc2UjlLRlRvYi8ySk1ObXFSOHpwTTJkdnpvRU1TVGI4a2t1Q2NYUTF4ODJFSzdzOGM3N2dVN2RuQlFvYWhoZUp3TUFpTE1nU00yUlpzNjU5TUpVVGU5dUxDeGZpUTNCTXM0TGlhakZjdzkxYTV4WmFNK0pDRXhZM1pTNzJPRkdjNGhoYWhtOVloQzJXdHAzLzBicFNQMW1SZFFNSUhhL1IyZDhkSVJvblBHMng2aktpRlJQNVEwSmJaUXo1SExmVGtYYjRqK2M2NHhGeVJYR0xvYjFBblgwT1gvTndLd1kzZ3Zib3NjYWlISTkxOFQ2cEFlWnlQalo0b1B2K3JwKzV5dnQvMFowKzM2eTd6NHdTWDJCOHhLMmFRK0VoTHdZc1MxL2FaM3RpZlpiZHUrT0NweGk2eTQrclNlU1o1L1ZWcnZDTG1Cbk1oQjdRK3hVNVdFREplVXFRMUFTdXJDbWdET2w0YllBdEJOYWViUWI2Z3BPS1R4ampySzI1OTVlajcwcFd4SjVkSFhraWFWdVNab0pwMjVxandVUkNXaDArUWFzUUlPV3dlTHBidVUweVkwNWhrU1J1OGFDQmlSaThqZmU1M21XcmdiQmplc2xuMXhnbmVjTjQxRnRhZjVDOTNXN3lEQUFxa2pKdWJTcjMwd3lrTjd1bC9neU9mU0kiLCJtYWMiOiI1MGYyYzQ1ZmRiYjg4ODU5MmU1NDk0ZGY1ZWFkMWU4NjVmNGIwZTU1OGE1MjBmYmFkNTRlZWE1MDMwN2UzODRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164673313\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1154314177 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154314177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1988035405 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:50:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ0b2tZbnpkWWVUbENoNnFwc1JreWc9PSIsInZhbHVlIjoiYnZRNGdHbXRwTnBsa0RKbmF5aFE0VktTblVRTGx1anVBKzB2TFNwLzlybnd0Nlo3VnVHZEpDcnBWZWFJZFNyL2FQb01EVXhNdjdVd3NjSmdHWmMzRGN2NVVRbmloLzhSRmZUQi9iQ0FwZCtYZ3daeHlHdFJNclZmUE8rUTBYZnlsVUc5VFl2dEpGVHJNRVFJdHg1RmxQTU9jcnVjMjQra0hTWkV2VjBUVXBseTJHcVF2d3RHUyt4RUQ0LzhSdEdlS1pSeDhQNUgzL2REUERGdVJQVmRLQlVOZGVNb0JIT1hNRHVRN1p3VGxCNksvaWxOdmxzekE3d2lpMktySG1sODZSRUszVTkyWUhUMm5oaU5yb0pWZnJPK3FBNW5vRVhTRm9jUXV5VjNVanpaVkRtd3V0ODN5Z1AxdWtmUmdET1RkbVVCbTBnMFM1REcxM0drOFVaSTBpZGpGYyt0TURhL1oyTDNLYlJvWXlkS2lGVi93TGQ5dnh6MW9LSkRkTlBLMGJ6d0JPUnMra1NJc2tGUHNwbTNTWWRxRktoSzdzd3gwSG5BSlBCdEZkMDVkTGd5TWFGc1M0Y1NyOWJ4ZTRBMGlOVU82YUk0cE9ST0JXNmRvbWJMRi9zNS91dllqekNZSXFrK1ZmS0VCRE9MNDlrKzBMSVorbHdaWThQM1ZyMkMiLCJtYWMiOiJkYTE4NDRiNWM1NTk1M2NhZDgyZGY2OWVjYzAxZWU4OTkyZWJmOTgxYWE3ZjJkNzc4MWU0ODEzYmZhMmVlMTIxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:50:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxVTk1DWFBVNklBQzhWT1l5UVdFREE9PSIsInZhbHVlIjoicytMZ2R5T0RYbCsvZlN0dTFueXBYMldPanFRVFhJV1U2dEZGaWtGcFN2TDF4ZXFOWDlIbVprMnlpcE5CMDhsZFlsNnpwTDNGb3V4UEEreUx1S1liOENyMlhCdkdFUzR2MjJqa0w5d3psaVdPWTlOdUJadDJzTzJlQ1hFZW0wVlNUcTBSZmVibHVzdHdDWUZSRjFOTDBvbVF5VitaMGxJSHEwTWdudlRneHo5azhjby90QVRUbUE1TUJMaURCVUV5MTlFNjBlVXlxbGpSMUh4U05KMEN6UExQWjNmL25vWitTaWZpTERUSElJRHRuK2FzcnQwSElmd0ZqT203a0RjWVhwR1RHNGZEdnk1MzBCUTNtekcrYm8vNEJvdUhnM205Z2ZlVkVKbkxBeTU4T09LSkdoTDAvWHB5cDZLa1Q4bHZaK2xvRm1JQThkNmUzYUs1anA3MjZUT2liWGRpWlRZTjlPbWZLVnZIdnRrSDMvQ1VxRGozeXdnVFdzbmxLQXpGeWNBWldsSWRyVVhVdkg2U2Q0dVczTTZjYUFteEhTV2lNNmt3RS9OMWNpblJGaWFjNThKZnYzbjRGVzVkOHBZUEx2cVJkVUFCWGV2cHlvNlc2UU5aZE1ZdTAxSDNGVXdRdjhnUWlpcmg3YnRRTi9qUHdWdTJqQW1YTWU4R0Y1OXAiLCJtYWMiOiI1YjIyNjUyMTY3YWMxYmNmNmIwNWYzZTEwYWI4MzUzNTBiY2ZkMDFjYjlmODA3OTc1MGFmMzY5Yjk5MzcyNTQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:50:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ0b2tZbnpkWWVUbENoNnFwc1JreWc9PSIsInZhbHVlIjoiYnZRNGdHbXRwTnBsa0RKbmF5aFE0VktTblVRTGx1anVBKzB2TFNwLzlybnd0Nlo3VnVHZEpDcnBWZWFJZFNyL2FQb01EVXhNdjdVd3NjSmdHWmMzRGN2NVVRbmloLzhSRmZUQi9iQ0FwZCtYZ3daeHlHdFJNclZmUE8rUTBYZnlsVUc5VFl2dEpGVHJNRVFJdHg1RmxQTU9jcnVjMjQra0hTWkV2VjBUVXBseTJHcVF2d3RHUyt4RUQ0LzhSdEdlS1pSeDhQNUgzL2REUERGdVJQVmRLQlVOZGVNb0JIT1hNRHVRN1p3VGxCNksvaWxOdmxzekE3d2lpMktySG1sODZSRUszVTkyWUhUMm5oaU5yb0pWZnJPK3FBNW5vRVhTRm9jUXV5VjNVanpaVkRtd3V0ODN5Z1AxdWtmUmdET1RkbVVCbTBnMFM1REcxM0drOFVaSTBpZGpGYyt0TURhL1oyTDNLYlJvWXlkS2lGVi93TGQ5dnh6MW9LSkRkTlBLMGJ6d0JPUnMra1NJc2tGUHNwbTNTWWRxRktoSzdzd3gwSG5BSlBCdEZkMDVkTGd5TWFGc1M0Y1NyOWJ4ZTRBMGlOVU82YUk0cE9ST0JXNmRvbWJMRi9zNS91dllqekNZSXFrK1ZmS0VCRE9MNDlrKzBMSVorbHdaWThQM1ZyMkMiLCJtYWMiOiJkYTE4NDRiNWM1NTk1M2NhZDgyZGY2OWVjYzAxZWU4OTkyZWJmOTgxYWE3ZjJkNzc4MWU0ODEzYmZhMmVlMTIxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:50:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxVTk1DWFBVNklBQzhWT1l5UVdFREE9PSIsInZhbHVlIjoicytMZ2R5T0RYbCsvZlN0dTFueXBYMldPanFRVFhJV1U2dEZGaWtGcFN2TDF4ZXFOWDlIbVprMnlpcE5CMDhsZFlsNnpwTDNGb3V4UEEreUx1S1liOENyMlhCdkdFUzR2MjJqa0w5d3psaVdPWTlOdUJadDJzTzJlQ1hFZW0wVlNUcTBSZmVibHVzdHdDWUZSRjFOTDBvbVF5VitaMGxJSHEwTWdudlRneHo5azhjby90QVRUbUE1TUJMaURCVUV5MTlFNjBlVXlxbGpSMUh4U05KMEN6UExQWjNmL25vWitTaWZpTERUSElJRHRuK2FzcnQwSElmd0ZqT203a0RjWVhwR1RHNGZEdnk1MzBCUTNtekcrYm8vNEJvdUhnM205Z2ZlVkVKbkxBeTU4T09LSkdoTDAvWHB5cDZLa1Q4bHZaK2xvRm1JQThkNmUzYUs1anA3MjZUT2liWGRpWlRZTjlPbWZLVnZIdnRrSDMvQ1VxRGozeXdnVFdzbmxLQXpGeWNBWldsSWRyVVhVdkg2U2Q0dVczTTZjYUFteEhTV2lNNmt3RS9OMWNpblJGaWFjNThKZnYzbjRGVzVkOHBZUEx2cVJkVUFCWGV2cHlvNlc2UU5aZE1ZdTAxSDNGVXdRdjhnUWlpcmg3YnRRTi9qUHdWdTJqQW1YTWU4R0Y1OXAiLCJtYWMiOiI1YjIyNjUyMTY3YWMxYmNmNmIwNWYzZTEwYWI4MzUzNTBiY2ZkMDFjYjlmODA3OTc1MGFmMzY5Yjk5MzcyNTQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:50:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988035405\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1235321561 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235321561\", {\"maxDepth\":0})</script>\n"}}