{"__meta": {"id": "X44269143ff141210283c488c6e7fd51a", "datetime": "2025-06-30 16:03:38", "utime": **********.643252, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.059821, "end": **********.643266, "duration": 0.5834450721740723, "duration_str": "583ms", "measures": [{"label": "Booting", "start": **********.059821, "relative_start": 0, "end": **********.516753, "relative_end": **********.516753, "duration": 0.45693206787109375, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516767, "relative_start": 0.45694613456726074, "end": **********.643268, "relative_end": 2.1457672119140625e-06, "duration": 0.12650108337402344, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348112, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00333, "accumulated_duration_str": "3.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.576954, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.76}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.588838, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.76, "width_percent": 19.82}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.591893, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 79.58, "width_percent": 20.42}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-478056305 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-478056305\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1152341242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1152341242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1997402418 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997402418\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1812094422 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299403618%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndVd1ErOSsvRWhVMjU3a1ZkS25FbWc9PSIsInZhbHVlIjoiekhqekRIeWVZS3NvejZlZWFpY211ZEhtWGVxdkt5aUVZayt3UlRQV3VCUk56cm8rMW0yY2FBaytFM2xqbHB4NFBtZGtna09iYjRnWlBDcjdYMHpwTUllQ0J6OTFQckRzMFdZQXU1UFJWVlJ2NW90a1pkeVBxMkk3NmgzNlR4bkNjYk51b0RMcFdhbGZBQUYwWmJoV0Nrd0d0Y3d0Slc1RkR6NnJReTJHUm1kQVAxbENtTzFPSWUxRXhpZnViM3FmbUhKdDRBWDNjM1M3Tm1nZGFwaTdCSUtCNExYQkMwOWNWeDlJWjF2c01GSnBLaFc4NWhLSUd2Z2xqYWkvYjFtYlU0Z0FFM010WVRmazdMMUxQMXpna00vVnJHMlM1WHVZa3hTN3NESFdTT3YrZEN2V2pVRGFOTUs3c1YyY284cSs0ZW5xdWp3Y1piUEhhWVNqSzhJNnhTZXdTcWJLbGxaOGp1WVdxUFlZLzIrQ3RIYllqQ1VwbEpMYkw2NTVwU01SSWNOZnRzOCsxckljMXZBaG1GSkR5aWsrSmNxVThTcjFZbm5JWm4xa2tySEpTYTc1KzBDSVFtbnYvTGx3QzRneDFqUGtwcVF2VjNSS3Z0dEtjcUZpTTBQbGFsc25wQXByTm82YWRLSmVWUXZlYS9RR0pQMS9JVVJuZzl5eXAyNkMiLCJtYWMiOiI1NWYyNjc3OWUwM2IwYWU5ZmVjMzgxYzM4OGE5ZWE5NGM2NjI4NzFiN2M0NDBkNDVmY2U3MTY0M2YxZjQzNWQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZHcGlEQ0QvRVRiTVk4dDlZOWxsSUE9PSIsInZhbHVlIjoiZ3NxUWFWWmVFVnZsZTRPbTkrWDM4MDlyVFBGUm9zSnNTY1JRZ0tsTFM3Qklmc0syOVo0eTE2Q2xVa0xPOWZKSlRuN2dDUkYrMTVGWGYrKzdpY2VOUzVZUmxINUVvbS9pZi9oWWYxODIrVHc5eDBHWEVQWDNpbS8wY0hLc2szMm1McWtoWlY2bDNaODBDTmZpcnRSS3RNZXNxcUJNVlZSWDFmZXJNUWdiNkhhUjgrVEIrRFRRd2ZRQ1J1QkpQMFBvenl6SitucGI3QkVCQW4vK3A0M3RzZnByNUZMQlMyM0M1ZUR4VmtESWZESDZQdXpDZ3dTcDd4L096RkdVc0VMbG80bW9WdzhDVjFLMG5EbFdialVKYnluVWNVQkdoUlp3R2swa1BySE5TRERTQVkyVEdaOFlYRklnUGV6Uk51amc0ZFc2dzdDbEFPSXVMYkJzaHB4QVI0YzJ2TzNaQUhYc0FnaUNXNk45ckVuMWhoYVROS1pxaGVzSkxlMTF0NWUxM3F2WTlxOVVVNUZtS2pxZUVaZDdKc2ZMblJrNERkcjFaT29jOFpvR2Q3SkEvblBFWHpkZ05BakpiSG01V3dJMkcyanlSV3BURHZRUUd6QzRnZEJVV1ZrQWJyU2tYbUduMmhLT2NOYW9kMzFNZEpyMWQrYkJERU0wK2FxMjc3MWciLCJtYWMiOiIxM2U0M2MxZmRkYjc0YzZkZjc4ZWM5ZDI1YjAwMzBiMmFlZmJhMzVjMzY3NWY3OGIyZGE0NDkxYWRkZGE2Nzg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812094422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2014332093 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014332093\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1657596421 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:03:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlMOTNhd0hDSFJLQkpCVmN6U1RYNlE9PSIsInZhbHVlIjoiTzRsa3RqelJmOVkzOTVjUGtqdFpKei9LclBXemF4TTNyRlFYOEZmVGxwSVdoeEJnWlVQd2JhOHl0Mi9yeEhoMHFoU01YdHNFRHQ0cFRIVktiV25aMVJyS1VDL3k5Y0ljdjlNUnd2eEptSVJLV3ROc3JHcFpKaUZrSzZ4VVNKSklHU04rYWNFWDJxTGU1SzhQMnp4dVhRMXBQd3ltVkRwVXV0M3dJeDdZaE1hc21aQWJCTk05eUdld3hOVlk5d2hxYW5heXdKRE5WbERwK0R3QUdiTHJlVEJWWmlja1hxdmhWZHNjclBVcUlTNnlFVTJHSGpMVlhPZmJFQ2NzUXFJOFNJTTF6TTJPcmo1Rlh1YVlDWVRZVFF3YVdWdWtTdERZM0tvZ0tWOG9YTVNGeTJUOHMyN3lFVENuU2tJdlVKWmtQMUg1NzBEMk9kWDNEaENVZ2plM0NxWk0ycmsyZnB2Wk9NNGQwU0c1Qm1QUnUrSjQ3UHA0a2NmZUJ3VDVWTTgrY2FhdmVZdndOQ2l5cDhFcWFxK3lqdEpTa2FBMlhydVM3REN4YWZpZy9VUHZ3d0Q4Nm81R2lWR1FsOCtQdkZoYy9MV0IvdDJ5NVpZWWY4MzZQbG9JbTNDTGlBYmJKdHI1R0pndDZFSVR0aU00ejFNWDdWSzI1N2szOEJtYmlkRTciLCJtYWMiOiIzYzg4YjNlYWQ5ZTcxZjAyZTRlMDEzOTMzY2I0ZWM1ZTNkODgyNjQyYWQ3YmEyYzFlYTU4ZDcyMTk1YTVkNjdmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijk5SGFlTmQxZGt5aU5lRHhsekV4QUE9PSIsInZhbHVlIjoiUXlaUDIrR2s2cHF2UUNubUlmUVk3OWtmc3JVZzdoZ3QxSG5JLzZVMy9XZDhPUkwzN3RWVklybUp2eFlFU1BNa3UrVmZ5REoyR3MrOElreHlFbFlrbEJwTVhSakQyaGtCaGJKT0FqZFFoK2htOVVHSkhjQkJwclRzOHEvTFlicHk0c3hSaW5UK214Zmdabm5Yelc2OFFmTlhKOHBubS80c2tSSUxlS2R5cWN1alVPVEJER3ZveDJzN0Qzam9lOG5uWm83SHBtQnM1d2pCR3NMQVQ4azBOMU9nRzZnK0paVkwwZ2lnTUpyVENqSG1xYmVMMTFSdXU4TkdtY0dpQ2VsbndpUVUydXNzcWJrOGZOa2JCdXZ4djZXbjVoREt2T2UybjhFY055WXhvYUVQZHkreFlmOXEzNy9zZGI1czVWblRHRGVmalVWZ25ISVdBSllXY2Q3eHk0dlRaQ09sdEhIZW1nUGIyMGdwb21uamNpTFcvODFST0VQcTlRTmVXdjA3b2hvVFIvU1RtaEp0UGJGU0VWakx0dkxFUmMwMy9SWHo1azhkdDJPbVNZV09PcU1GOXlJb2JOZTVUZ2syQmJJSmJMTlI0WHc2QzI4cHA1Vkt5dHE1Rm1zaWFmeDdOV3RqODNlZFJZd282NjdLSVEzZVk4K1Y1VXFPUWVxSzlxVnEiLCJtYWMiOiJmODliNjVhMjI3MzE2Y2Y4NzRmMjc4NzY5MzJmOTg3YzFlNGYzZDVlZGFiODAwNzRkOTlhYWM0YzRjY2M2MDk3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlMOTNhd0hDSFJLQkpCVmN6U1RYNlE9PSIsInZhbHVlIjoiTzRsa3RqelJmOVkzOTVjUGtqdFpKei9LclBXemF4TTNyRlFYOEZmVGxwSVdoeEJnWlVQd2JhOHl0Mi9yeEhoMHFoU01YdHNFRHQ0cFRIVktiV25aMVJyS1VDL3k5Y0ljdjlNUnd2eEptSVJLV3ROc3JHcFpKaUZrSzZ4VVNKSklHU04rYWNFWDJxTGU1SzhQMnp4dVhRMXBQd3ltVkRwVXV0M3dJeDdZaE1hc21aQWJCTk05eUdld3hOVlk5d2hxYW5heXdKRE5WbERwK0R3QUdiTHJlVEJWWmlja1hxdmhWZHNjclBVcUlTNnlFVTJHSGpMVlhPZmJFQ2NzUXFJOFNJTTF6TTJPcmo1Rlh1YVlDWVRZVFF3YVdWdWtTdERZM0tvZ0tWOG9YTVNGeTJUOHMyN3lFVENuU2tJdlVKWmtQMUg1NzBEMk9kWDNEaENVZ2plM0NxWk0ycmsyZnB2Wk9NNGQwU0c1Qm1QUnUrSjQ3UHA0a2NmZUJ3VDVWTTgrY2FhdmVZdndOQ2l5cDhFcWFxK3lqdEpTa2FBMlhydVM3REN4YWZpZy9VUHZ3d0Q4Nm81R2lWR1FsOCtQdkZoYy9MV0IvdDJ5NVpZWWY4MzZQbG9JbTNDTGlBYmJKdHI1R0pndDZFSVR0aU00ejFNWDdWSzI1N2szOEJtYmlkRTciLCJtYWMiOiIzYzg4YjNlYWQ5ZTcxZjAyZTRlMDEzOTMzY2I0ZWM1ZTNkODgyNjQyYWQ3YmEyYzFlYTU4ZDcyMTk1YTVkNjdmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijk5SGFlTmQxZGt5aU5lRHhsekV4QUE9PSIsInZhbHVlIjoiUXlaUDIrR2s2cHF2UUNubUlmUVk3OWtmc3JVZzdoZ3QxSG5JLzZVMy9XZDhPUkwzN3RWVklybUp2eFlFU1BNa3UrVmZ5REoyR3MrOElreHlFbFlrbEJwTVhSakQyaGtCaGJKT0FqZFFoK2htOVVHSkhjQkJwclRzOHEvTFlicHk0c3hSaW5UK214Zmdabm5Yelc2OFFmTlhKOHBubS80c2tSSUxlS2R5cWN1alVPVEJER3ZveDJzN0Qzam9lOG5uWm83SHBtQnM1d2pCR3NMQVQ4azBOMU9nRzZnK0paVkwwZ2lnTUpyVENqSG1xYmVMMTFSdXU4TkdtY0dpQ2VsbndpUVUydXNzcWJrOGZOa2JCdXZ4djZXbjVoREt2T2UybjhFY055WXhvYUVQZHkreFlmOXEzNy9zZGI1czVWblRHRGVmalVWZ25ISVdBSllXY2Q3eHk0dlRaQ09sdEhIZW1nUGIyMGdwb21uamNpTFcvODFST0VQcTlRTmVXdjA3b2hvVFIvU1RtaEp0UGJGU0VWakx0dkxFUmMwMy9SWHo1azhkdDJPbVNZV09PcU1GOXlJb2JOZTVUZ2syQmJJSmJMTlI0WHc2QzI4cHA1Vkt5dHE1Rm1zaWFmeDdOV3RqODNlZFJZd282NjdLSVEzZVk4K1Y1VXFPUWVxSzlxVnEiLCJtYWMiOiJmODliNjVhMjI3MzE2Y2Y4NzRmMjc4NzY5MzJmOTg3YzFlNGYzZDVlZGFiODAwNzRkOTlhYWM0YzRjY2M2MDk3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657596421\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1634373381 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634373381\", {\"maxDepth\":0})</script>\n"}}