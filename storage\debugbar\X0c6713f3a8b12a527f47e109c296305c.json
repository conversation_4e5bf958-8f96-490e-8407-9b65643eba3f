{"__meta": {"id": "X0c6713f3a8b12a527f47e109c296305c", "datetime": "2025-06-30 16:10:11", "utime": **********.880741, "method": "GET", "uri": "/add-to-cart/1545/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.413367, "end": **********.880756, "duration": 0.4673888683319092, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.413367, "relative_start": 0, "end": **********.788062, "relative_end": **********.788062, "duration": 0.3746950626373291, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.788074, "relative_start": 0.3747069835662842, "end": **********.880757, "relative_end": 1.1920928955078125e-06, "duration": 0.09268307685852051, "duration_str": "92.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48567832, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0063300000000000006, "accumulated_duration_str": "6.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8319838, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.908}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8423662, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.908, "width_percent": 7.109}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.856566, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.017, "width_percent": 8.847}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.858466, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 41.864, "width_percent": 5.845}, {"sql": "select * from `product_services` where `product_services`.`id` = '1545' limit 1", "type": "query", "params": [], "bindings": ["1545"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.863021, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 47.709, "width_percent": 6.003}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1545 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1545", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.867245, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 53.712, "width_percent": 37.441}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.871276, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 91.153, "width_percent": 8.847}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2100122107 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100122107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862039, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1545 => array:8 [\n    \"name\" => \"غور\"\n    \"quantity\" => 1\n    \"price\" => \"1.00\"\n    \"tax\" => 0\n    \"subtotal\" => 1.0\n    \"id\" => \"1545\"\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1545/pos", "status_code": "<pre class=sf-dump id=sf-dump-1505373173 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1505373173\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1587063697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1587063697\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1028572717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1028572717\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2017119660 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299801140%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhVUTRHN2d2SElnZDN4WkNiN0pHQ2c9PSIsInZhbHVlIjoiYklWVnFMNDNpamxBVHNKMWFtZXowM0F4VStReWUzMXRNdEExcTZrTHpDZEhRaXR6eDF4bFN2MUMveDdNQ3d4Q0xvUlh5cTkwUlNzejE5eUhpOG5JaENNMm5Ba1daUVJ3SDBYOFJwZHlIUVVBeHdta1hIOEtYdktXWTNOZmc3RXM4RkgwSWxFOEsza3RPTDh3MkZraFRrWEhXc3VGVStrQmZHcU5idU95Y0RockptRUJ0MU9QK1hjeHpQMjlzNy9KME5ITjBsM0s2UTlrWDdXN0FzRlhiRmtBUFZwMFppaDhJa0ExYXA0T3JiWjEvUUJvTmEvNG5KMTdSVVd2SHcwcDZhcklUZCszcE8vYUZZczkybkFHOGFnbEVOcFZSMktSamVYZ04zbER0cDJ6MWZvVDUyWE9rN21XUTBuckszR004dTQzN2dKQzNSUXE4dGxqeWNzcGxMWjdrUGdWMlhCTkl2R2MzNHlFaWZvMGZISjZwMEo1SW1laXFqQ0VOL05UMUdVNnVlZXVodk0zVDduSXp6ZU9GMzUwaTE3aE5QeC8vMWZ4SUpBNEQ1VWs5eUxQd2VkeEp0ZVlUWVdDS09kL24yaGFUZFV3Z0psRThDRCsyMjZRd3JiVE5iYnozSW56WTR0Ung1N0dmY3EzUW53ZlNTd0FnODN4amUvYUM4SEQiLCJtYWMiOiJjNDMxYTZiOWMwZDJiM2NjZDUyYWY3MWEzN2RmMWE4OTA1ZWQ0N2EzMTE2NjM2ZjZjYzI2ODY3ZmMwMDY4M2Q0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpSTzI4OGZNVmg1OVJTMGJOK1BTQWc9PSIsInZhbHVlIjoiK0NiR2lGN0YraXY0d0hWTm5ZNmpTZS9GTjAyK2lYWWQ5RC9qeTdvaGU3cjQ3cHZzYVlLbkpZWGZUWC9CckZmOGdPQ0VjTVYxQnYwUU9USzB1ZEhHZ1lpUUFwbFlTdW53cytaQms5b0llbXZkVVJIWDFoblB4cUpJck5QSGtoVEk5NXlnY0hIQjZUTnVaR21JZGlUMTdBVFU4eThRZ29OeDlKSWc1aXFJczZ1alNPUUZycUZFQ3d2NjAwYWFzWGFSZUdramYyUlhNZXFndm00YWRua2xSUGFacnRZbWhWTStlK0RVdCtoOFpGMGphTDZNSk0wOG5EbGpVODk0akd1empSdEpNc0FXbDhuMk43RFdOUGV2U2ZOeUFoSmhGK2p5R1hpcWJ0VC9GMElySkIrVFNJemwrRUV4UDNLQzVMVzd6cjB2UHlDMC9zUmcwSDZhYW82d2srMVJiMmNCNVlINGl0eHMrcnJDWjBFV3BkWkRVSGVad1dFRDZwNXlmMDQydEI0WkRTdnk1UlQzdnRUYXJ6WDRWNVVCeC9GNUFFWDBsQURnZTU0VUxtSHh5cS9DcjAxcEN2QS8xOHF0WVI0WUo2S0FsNVlLei9kaHAvZkNmYytlVWFvUnZnSVQ0ZzBoczFQUFNLVjlKbWVaV0FkaHhCTWNhb1lObGV2Q3RuWXMiLCJtYWMiOiI2MGZjOTY0YjA0OWIzNWIzZmJkMmQzZjUxODdhNTFkOGQzMjU5YzcwMzBkYjI4Njc2MWJkZGQ3Yjg4M2NiMGFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017119660\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-65910444 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65910444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353402172 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRjWmdlbjBSdENNYVUzeUVaRzR6NkE9PSIsInZhbHVlIjoiUTJjcFptZ1dSdXRKd2toMTdOYnZpMjNCcFJteU5wUUt1NktkdzJLaks5YTBUbUwxbTRtckpaSXMvaUNrQlc3d3hTdjBnS1VIbmd0eEdtVkNOcGRDN291c3A5U1kvOG1tbUlVcDFiMmR1Q21KcmZkTGJ5QnppUHppcTRsTHE3aHh2TVlFaFc4VTNUNGZtZzdITmxxZjcyTVNyZjJHYjhWYmxSdzhuRXBoZHo1WSt1VTdtdzZzdE9Mc21nMGRPRzdKc3o5NFpoLzl3WDZsTGZEZi9adzZFanNYSDBTczczRisxV21YRHAzMjJpKzBtWFF4TlpwZGgzNHVpaFRwRW9EaGF0V0VGNlNBWnU5REQzZlcxWGFUcmZlbHlpdTl3Y0YvTmg2bGNxVVRmNUpvZ2VSdFFsZU1QSUlPN3JXcnR1UVZOemdyazRqd2I3ajNSR2MzWFdPQkN5TzdjblBMRjNwVlZUR3RieWE1cnIwN3U3UzVyRjF3dDlDcTQxaU1mcFpyTzRFMGtBclIwYWpieTZibitxTDF2MlZOTjNqN1Z1M1pSdk1XZmdFRFJlckdOVys0b3MzZnFDKzFiL1JPemdyOVdEWDYwTDdRUDFGNkhsN0ZDMlIrQzRsYXBPTkZyRFpZL0VxcGFFd2lFaGhsSWJjWUUvWFJnV01FQ3BaUDY5WmIiLCJtYWMiOiJkZWVmNGE4NDRiNDJmZjQxZDZjYzkwYjNhY2E5ZjMxZDZhNmNjNTA5NmZhZTVhZjVhY2FlNDU5OTYzMmY4OGMyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJxVnZaQXZxUi9LOG1OL0FrbFRsSFE9PSIsInZhbHVlIjoieTBBUkdEY1ZYRjFVQ2pRekkvYzd4cHdEOGNjOStEM0pUeHg4ZW95ODBpWForRXVib3ZUVC9EZFZ6d0FiNjNJQVhRODJSUXpUSWZvczk3bW0wb0JNSy9PYVg3WCtWR3FrVXRDRmpiekozMUhwR0JHVzhwZjlQNllCOTBhWFM4Z0dPdXI5U2ZrZUxNL2xTTWNPWVpySWxiYWJuRXlaRzlqUFF5SDhicVpJS0ZMWVUxNnNzM2NqR25EbmRjYU5td1hSb2RkWGdTbFArekVHWTl3NHRoelRHMWVjam9pbnlnK3JUMWgyRkFrcmUvSW4rQWNYY1BGNUVFUldNNHNrVlhSbm51WEpZNG1ER3BVa053SkQzNG0vY3RreUxQSk1zUUUySVR5N2NZY0hiM21IT3ZQcXEwZCt6dzN4a0NCY0Q3V2gvTGwxTy9KYVloRUhzOFZxYzlmSlRPbzhOcE04ZmcxUUZjV3V5eXlNeU9hR0ZMY2hLdEZxTlpqRUVIU0taaXB2Z3JXSHd5bUxxQjJYZTZQZFA0OVpxdjBUaTBjV2ZxZWxXZXorUHliSWJ5SFdtaENrRVJlb3FoZUFwMDVzRHlFbHZrRUpueXQ4QnRCd0JSWHJETm5XZ0lwVm5rVnNCY1dGeWxwZU9DVGx0aEo1cVZTOWlTbkl5MjFQWXVuQncxQjgiLCJtYWMiOiI5MTRlZWUwYmNmZGI1ZGI5NWQzOWU2MDNjMjY4NTU4OTg2ZWYxZThkZjZlNjdiM2QyNjlhM2RmZjkxMGI5MmExIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRjWmdlbjBSdENNYVUzeUVaRzR6NkE9PSIsInZhbHVlIjoiUTJjcFptZ1dSdXRKd2toMTdOYnZpMjNCcFJteU5wUUt1NktkdzJLaks5YTBUbUwxbTRtckpaSXMvaUNrQlc3d3hTdjBnS1VIbmd0eEdtVkNOcGRDN291c3A5U1kvOG1tbUlVcDFiMmR1Q21KcmZkTGJ5QnppUHppcTRsTHE3aHh2TVlFaFc4VTNUNGZtZzdITmxxZjcyTVNyZjJHYjhWYmxSdzhuRXBoZHo1WSt1VTdtdzZzdE9Mc21nMGRPRzdKc3o5NFpoLzl3WDZsTGZEZi9adzZFanNYSDBTczczRisxV21YRHAzMjJpKzBtWFF4TlpwZGgzNHVpaFRwRW9EaGF0V0VGNlNBWnU5REQzZlcxWGFUcmZlbHlpdTl3Y0YvTmg2bGNxVVRmNUpvZ2VSdFFsZU1QSUlPN3JXcnR1UVZOemdyazRqd2I3ajNSR2MzWFdPQkN5TzdjblBMRjNwVlZUR3RieWE1cnIwN3U3UzVyRjF3dDlDcTQxaU1mcFpyTzRFMGtBclIwYWpieTZibitxTDF2MlZOTjNqN1Z1M1pSdk1XZmdFRFJlckdOVys0b3MzZnFDKzFiL1JPemdyOVdEWDYwTDdRUDFGNkhsN0ZDMlIrQzRsYXBPTkZyRFpZL0VxcGFFd2lFaGhsSWJjWUUvWFJnV01FQ3BaUDY5WmIiLCJtYWMiOiJkZWVmNGE4NDRiNDJmZjQxZDZjYzkwYjNhY2E5ZjMxZDZhNmNjNTA5NmZhZTVhZjVhY2FlNDU5OTYzMmY4OGMyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJxVnZaQXZxUi9LOG1OL0FrbFRsSFE9PSIsInZhbHVlIjoieTBBUkdEY1ZYRjFVQ2pRekkvYzd4cHdEOGNjOStEM0pUeHg4ZW95ODBpWForRXVib3ZUVC9EZFZ6d0FiNjNJQVhRODJSUXpUSWZvczk3bW0wb0JNSy9PYVg3WCtWR3FrVXRDRmpiekozMUhwR0JHVzhwZjlQNllCOTBhWFM4Z0dPdXI5U2ZrZUxNL2xTTWNPWVpySWxiYWJuRXlaRzlqUFF5SDhicVpJS0ZMWVUxNnNzM2NqR25EbmRjYU5td1hSb2RkWGdTbFArekVHWTl3NHRoelRHMWVjam9pbnlnK3JUMWgyRkFrcmUvSW4rQWNYY1BGNUVFUldNNHNrVlhSbm51WEpZNG1ER3BVa053SkQzNG0vY3RreUxQSk1zUUUySVR5N2NZY0hiM21IT3ZQcXEwZCt6dzN4a0NCY0Q3V2gvTGwxTy9KYVloRUhzOFZxYzlmSlRPbzhOcE04ZmcxUUZjV3V5eXlNeU9hR0ZMY2hLdEZxTlpqRUVIU0taaXB2Z3JXSHd5bUxxQjJYZTZQZFA0OVpxdjBUaTBjV2ZxZWxXZXorUHliSWJ5SFdtaENrRVJlb3FoZUFwMDVzRHlFbHZrRUpueXQ4QnRCd0JSWHJETm5XZ0lwVm5rVnNCY1dGeWxwZU9DVGx0aEo1cVZTOWlTbkl5MjFQWXVuQncxQjgiLCJtYWMiOiI5MTRlZWUwYmNmZGI1ZGI5NWQzOWU2MDNjMjY4NTU4OTg2ZWYxZThkZjZlNjdiM2QyNjlhM2RmZjkxMGI5MmExIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353402172\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1545</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1594;&#1608;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1545</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}