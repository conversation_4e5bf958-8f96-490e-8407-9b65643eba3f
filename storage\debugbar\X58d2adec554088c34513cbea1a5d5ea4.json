{"__meta": {"id": "X58d2adec554088c34513cbea1a5d5ea4", "datetime": "2025-06-30 16:07:37", "utime": **********.285468, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299656.834492, "end": **********.285485, "duration": 0.45099306106567383, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1751299656.834492, "relative_start": 0, "end": **********.219962, "relative_end": **********.219962, "duration": 0.385469913482666, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219974, "relative_start": 0.3854820728302002, "end": **********.285487, "relative_end": 1.9073486328125e-06, "duration": 0.06551289558410645, "duration_str": "65.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00386, "accumulated_duration_str": "3.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.258633, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.43}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.269665, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.43, "width_percent": 13.212}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.275693, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.642, "width_percent": 17.358}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1854461088 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1854461088\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-395010774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-395010774\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1473951205 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473951205\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1898628907 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6ImZIYnZRcTVmN0NRZHFuMHkyUjlCYXc9PSIsInZhbHVlIjoiMXBoellpR2Q2NGFsUTFNczQ2RmtCaGZoK05Ua2ZVZ3lYRkhtdTQ3OUo1ZkRMU1piWEE2UFR5OHBpK00rd0pHSVp5ZVIwT01JcVQwbVNGQmxtVFZBMmxnRG9vYW95Zmd5bXZEQVpxQmFCaWtIY1U0dHk1cTdBQS84OEREbnBDbk9tQTdDek9zRHdzYXEyUXRxTTR6WWdDcWtFa2R5NFZtK09nRktwTm1BeWFvZUg3UWo2QXplQ3Z5VGVIMVJkLytLSHlQenluQWx1a1hyU0lXZUJ6d1VBMGl4akp2c0J1R0lpV0ZhVUhPa0kveWlwZUpZUndoclhvVHBTU0ZkdEduaE45TGZDMTVKejN2SU5EdlBjOXhSTlkrMXRnUVQ5bUZSZGg0UEtEN0xKQWg0Y05YY2M4RGVDN09qdmlBejh5VzVuVVdjMC9YUk5qMXliQU4yaHVFenE2NS84RjRwYzhxVmNoOEJOQ1NBZkxJOFZ6MzJIbkZFUk02bERTQ29uczI2U29nOE51c0lnSHhFUkhpMGgxVGlUWlNEVm1FUFhxd3ZqN1BybkxhMU9EOVVnc3FQNkFvbVU3KzNCQUs2dDNqZVZVK3AwREUySWd3MlRNd09seGFLcEdMVUpFZFRnR0FjQmJyT0thRHp6MUczbUVWTWp6eHMyOW91QXBFWkJiOFgiLCJtYWMiOiJmNjBkYTdmYzU5ZDNkZTFiNGYyNmY4MjQ3ZmNhNDRmMzkzZjJjOGJmNGYwYThmNjg0ZDkyMDMxYWUyZWY4N2VlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxVWUsvUzd4dWdMRE9XRW13UHhTSmc9PSIsInZhbHVlIjoiT3VvV052VEZEa1BXaFhGdWZQendqVTd2UitSUytaNFp0VlozUTBTRjBMUEJDNWpGV1VaWllVb1A0Y1p1OVg4ZGw0ZVo3NjcwQnVXeTNkTEFkNTR4TmF2U2czM1cwaS8vRW1UN0VacEp6YnJ1SlZiZUJ2UVhmMDBwNStzV3NLMHhDRi94dTBtU1RUSzBHbHZxTWRGTjVoYjc2WG1RTGNsNE5GQVBDR0w3RHIwTW9FalR3NzlXcE5KNjk0bWt5d0d0U1R2dXJUYWxSNDBRMExSaEI0ZmNmalpteVQxUnpMN0NpUCsxaHpWaWczKytWMnJGeEdZamZUeUVnaTR2QkVLNzdidW44YSszb0s1Sm51bDlGdy90ajZETmRrNWd5M2RJSEU3NWhaRnU1aHNFQVdRbnMxV1krdDNXVGxrdEpobFdGYkpyY1hobVp2RHFESmM0aEl0UGMrS1FsUUVvZy8vYmp4NjM0U1loZVY0OVRYRGRHWEtYZTlpWUVFMXMzM1lQUVJRUVk0TnVqR2ZaazU1UE9xMEF3alhPYjhub3lTc0ZSR0xUZ0VRODNLZU5JSGIwVnEzTUcycWxXZXR3ZVFya3IrbUVFbUZodlF2OXh6OE1ZV0Y4VFdxcVlTR0o4aHFnUkUySEN0YnMrVnhlb3N3WE1DYzRaYmxNTCtzTFRUdmgiLCJtYWMiOiI1YmVkOTk1YWFlMzYwYjFjOWVlMzdkOGVlYzJiYTViZTljMzJjMTdkYWEwNTJkYzA0NWZjZmQ3M2Q4ZDA5YWRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898628907\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1806528457 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806528457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-147188533 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZJam00T2VrZlhVeUR4ODdaNDB2MUE9PSIsInZhbHVlIjoiK0hPbENBOGpFYVBwd1A2SXNraFkwNWZ5b2lpNU1KYi92RXRlbXJnZ215SGt0enVGb3FvMUYvNzEwN1VRNnRId2N3M2E2V2tSZ1VLWXZXR3hqOE1KTC8yUUl6djIrVFA3NGczTW9qV0FWa3U5YWNnSit5aXBZTldJMnByNy81UzZYUldMZHhzcFZMTzRzRnlNbTJwcHp1YmxzV0NBN1YvTTF1YXN5WnQrWGlYanRDdm5wM1JuNGR5RU1qQlhXQkt5QXprckpFYTB5SlVIRnJWNGI1RlBSSittNVZZck80bDZmUGYyVU5TZnVPYzBiL0Q5bnpwNGR6ajVXc2ZMUldlRHZXOVYzbUF2ek5Ic0hacGZoNnRPSDZSTXFIMzZKcG5rRVl3cE12a01RRXA4dWlNdFpZWTRBdktlcEI3OGRhaEsxa2I1QlhwM0ppOUVzZjNwTSszQ0RqVWczKzFPcFZHR2gxbkk1NkliTExUQ3ZuNDBRSlVIdis4UHAzeVJWVkw3ZExGV0xJTy9WVWtycU1QVlBBMUhiSXhYcnlkMWRIQ0dQMWZlb3Fmd2U1OFBiTzUxTGZMTGZ6a3N4NlZXT2FXUWRjeTUxekJKK0VzZVNlTzY4TEcxS2VBU0J6cTB1bmN5aEpZaXRhQnBpK2JWNlowQm1nMHppOEdjMmJoYWFZLysiLCJtYWMiOiJkNWJhMDA2Y2FiODQyYTg0MjdhMzc1NmNmYmM5ZDgwNzNjYWE5ZWJkMDI0YWQyMDdhOTI1ODBlOTYyOWE3OTEyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imh4OGFOdm4rcUh5aEVCRXJiYy9oeFE9PSIsInZhbHVlIjoiREJLcnZDMEMvUHI3Y2o4QzlwQU1QS0lrektqM0F4bllFWWFQSW5GQmMxQzEyUWNkclBlUDZCSmR3NWcwUyt3V3NTa2toZHh4RTY1SWV6ZU5oQm44VzcwQ1lTcURJZUlPc2dRcUU3cTFUekNQaWdHemRjcTlreDgvTGx5dnFNSmZsTjBuY1k5SEF2cDNHcDNKWi9LNEFuK0d4WWxuTWJ3Uyt2NUVYcHpWVXVhYlZ1ZGVUVCtDMk44elMvR1Y2ellCNlRpS2UxVU9aU1A1c01kMXo2WStyVlFWdkVOQlIvTFMzSDloUzhmWkpzVkMyVGxsR1U2YjZSK2lFWWdlSk56SWJ3WWhzb3NQQ0RnSmx4YTNTVjhldFRHMXB4T01hbDcxaFh2RjJSSVZOTWJBc2R1RjhTYnJvemdjTUJNU25oZXJNL2t1YTc0d0VTQ0ZvbGY5enV2NmZHa29sRWF2WGxJa2U3SzIxU1RZQVFJa3ZxSFp1MGNDdHF3UUdYOGpUMFIxaFVhdWJVNU5YcXJyR1FUanZTMGFaL0ZJN1AzQWl2Y2k1bnRFZVRvb01GMzhDOUsxbzVHNG8rWnp3UCt2YUxwWTQ3YlZBOHZaNGZWaVRtbUI5UFh2R3pJZFBsTkYyOWtoS3BDdi9XaTRxb3g0V3NFS2tuQm8vWFpNUzhRVnRwR2wiLCJtYWMiOiJjMDI1ZDAxMWQ5NTlhZDU1NGZiM2NlNzAwNWE5ZWI5M2QzZDY1MzNhNDk4NzYzMDMyMzlkNWU4OWEwNDc3ZTI1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZJam00T2VrZlhVeUR4ODdaNDB2MUE9PSIsInZhbHVlIjoiK0hPbENBOGpFYVBwd1A2SXNraFkwNWZ5b2lpNU1KYi92RXRlbXJnZ215SGt0enVGb3FvMUYvNzEwN1VRNnRId2N3M2E2V2tSZ1VLWXZXR3hqOE1KTC8yUUl6djIrVFA3NGczTW9qV0FWa3U5YWNnSit5aXBZTldJMnByNy81UzZYUldMZHhzcFZMTzRzRnlNbTJwcHp1YmxzV0NBN1YvTTF1YXN5WnQrWGlYanRDdm5wM1JuNGR5RU1qQlhXQkt5QXprckpFYTB5SlVIRnJWNGI1RlBSSittNVZZck80bDZmUGYyVU5TZnVPYzBiL0Q5bnpwNGR6ajVXc2ZMUldlRHZXOVYzbUF2ek5Ic0hacGZoNnRPSDZSTXFIMzZKcG5rRVl3cE12a01RRXA4dWlNdFpZWTRBdktlcEI3OGRhaEsxa2I1QlhwM0ppOUVzZjNwTSszQ0RqVWczKzFPcFZHR2gxbkk1NkliTExUQ3ZuNDBRSlVIdis4UHAzeVJWVkw3ZExGV0xJTy9WVWtycU1QVlBBMUhiSXhYcnlkMWRIQ0dQMWZlb3Fmd2U1OFBiTzUxTGZMTGZ6a3N4NlZXT2FXUWRjeTUxekJKK0VzZVNlTzY4TEcxS2VBU0J6cTB1bmN5aEpZaXRhQnBpK2JWNlowQm1nMHppOEdjMmJoYWFZLysiLCJtYWMiOiJkNWJhMDA2Y2FiODQyYTg0MjdhMzc1NmNmYmM5ZDgwNzNjYWE5ZWJkMDI0YWQyMDdhOTI1ODBlOTYyOWE3OTEyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imh4OGFOdm4rcUh5aEVCRXJiYy9oeFE9PSIsInZhbHVlIjoiREJLcnZDMEMvUHI3Y2o4QzlwQU1QS0lrektqM0F4bllFWWFQSW5GQmMxQzEyUWNkclBlUDZCSmR3NWcwUyt3V3NTa2toZHh4RTY1SWV6ZU5oQm44VzcwQ1lTcURJZUlPc2dRcUU3cTFUekNQaWdHemRjcTlreDgvTGx5dnFNSmZsTjBuY1k5SEF2cDNHcDNKWi9LNEFuK0d4WWxuTWJ3Uyt2NUVYcHpWVXVhYlZ1ZGVUVCtDMk44elMvR1Y2ellCNlRpS2UxVU9aU1A1c01kMXo2WStyVlFWdkVOQlIvTFMzSDloUzhmWkpzVkMyVGxsR1U2YjZSK2lFWWdlSk56SWJ3WWhzb3NQQ0RnSmx4YTNTVjhldFRHMXB4T01hbDcxaFh2RjJSSVZOTWJBc2R1RjhTYnJvemdjTUJNU25oZXJNL2t1YTc0d0VTQ0ZvbGY5enV2NmZHa29sRWF2WGxJa2U3SzIxU1RZQVFJa3ZxSFp1MGNDdHF3UUdYOGpUMFIxaFVhdWJVNU5YcXJyR1FUanZTMGFaL0ZJN1AzQWl2Y2k1bnRFZVRvb01GMzhDOUsxbzVHNG8rWnp3UCt2YUxwWTQ3YlZBOHZaNGZWaVRtbUI5UFh2R3pJZFBsTkYyOWtoS3BDdi9XaTRxb3g0V3NFS2tuQm8vWFpNUzhRVnRwR2wiLCJtYWMiOiJjMDI1ZDAxMWQ5NTlhZDU1NGZiM2NlNzAwNWE5ZWI5M2QzZDY1MzNhNDk4NzYzMDMyMzlkNWU4OWEwNDc3ZTI1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147188533\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-721794438 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-721794438\", {\"maxDepth\":0})</script>\n"}}