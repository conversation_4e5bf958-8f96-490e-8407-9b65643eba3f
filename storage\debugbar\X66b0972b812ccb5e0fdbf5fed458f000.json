{"__meta": {"id": "X66b0972b812ccb5e0fdbf5fed458f000", "datetime": "2025-06-30 18:10:46", "utime": **********.225065, "method": "GET", "uri": "/add-to-cart/2142/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307045.832348, "end": **********.225077, "duration": 0.3927288055419922, "duration_str": "393ms", "measures": [{"label": "Booting", "start": 1751307045.832348, "relative_start": 0, "end": **********.147846, "relative_end": **********.147846, "duration": 0.31549787521362305, "duration_str": "315ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.147854, "relative_start": 0.3155059814453125, "end": **********.225079, "relative_end": 2.1457672119140625e-06, "duration": 0.0772249698638916, "duration_str": "77.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673632, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0064199999999999995, "accumulated_duration_str": "6.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180441, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.66}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1899228, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 28.66, "width_percent": 6.542}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.202948, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 35.202, "width_percent": 8.255}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.204763, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.458, "width_percent": 8.1}, {"sql": "select * from `product_services` where `product_services`.`id` = '2142' limit 1", "type": "query", "params": [], "bindings": ["2142"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209006, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 51.558, "width_percent": 5.763}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2142 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2142", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.213079, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.321, "width_percent": 36.293}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.216753, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.614, "width_percent": 6.386}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1213390709 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213390709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208237, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2142 => array:9 [\n    \"name\" => \"STC calling card100\"\n    \"quantity\" => 1\n    \"price\" => \"115.00\"\n    \"id\" => \"2142\"\n    \"tax\" => 0\n    \"subtotal\" => 115.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2142/pos", "status_code": "<pre class=sf-dump id=sf-dump-1553413615 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1553413615\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1068036078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068036078\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-95931392 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJFZVZoOGRaOG5sQXU2ckh3azFFZnc9PSIsInZhbHVlIjoidngvSWR2VTJRaHpUVUxFazlpUnExMU1EQnZDM2doSjRlUG9pdytnaGxzOS9sYStlaHlxbUFBOUtibXpNMnB6eFhER25qS0lpVUR6RjVpVzhKRGFkVEs3QlRnQ3BoU3VUY2RGWUZDbXluL3k0MTdGam1iSitqSzBsSHBLMVJIdmQ4TGRmbWlPZlc2NWkwRHVrYmxjOEF3dkRMN3pMZjJxbGpOVlVDdmlacDFkc3RGV0M0OGlQQ3lxdlIrKzFmbG12UjJXNERlZzNFL1RoZk9DVDZVdldndkpYc0s1Z3poWStvbnpqMkVYUDlqSlovMXpyS0YyVWhRdGpZNENTRWZpQ0ptMGVDeWZ3aUNBa2JSZUx1dzdqSzMwUWhicnk4YVZydmc4UmRJRXh2UDMrQ1VBc1lndDhCUnlpSVYzOGx5Z05RM1E2bHVVcnQwMzN6UmJPZU9JdlRsOVY5OVBranFCNmlKNU9ZbDlFWDF2L29IVGxXWEJBMFhFRXJxRUxUenk0cjFCWGJTNVhaNUVYSU05MzNzUjErNWZnZmU0cjljRDBTWVNlY2pnYmg3bVRmL0xUSUo5b1FnM1BCNWY4dmVKano5TnR4cWt6MWVta0xCbDR6Rklpaks4TGtZYkNQRlBKTHlpK1dCNVk1K2oyQkxuUTFtMFd4S3JUeFRibklzVGkiLCJtYWMiOiJjNTBkZDQ4MWU0ZGZmYjhmMDUwOTM2Y2EzMTI0Njk4MGViZDU1YzUwMDk5MGFlMzA5YTg4ODRhYzBlZDc3MGNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpubWNucjdNYUFTQS9RM1oxdGhXMlE9PSIsInZhbHVlIjoiTVFzNlBXc1A1MGdKaUcxZC9ZclY3V0RsT0VaUEJZRkU2bnZRakFORlkycmpkNmVNT01Ba3VyUzF5WTF3UEFkWVcya0xJQU1DbE1STVhpR0VqdWdCN0F3WSsrbkZsbzgzYUMyWW5CMUo3Q0syTmVteC9PNGhvMTZSTzB0Tkx3L1RxdnZDMlhVWW4xL2h1dlllRzFsTURVYzRKamIwdDVLemFoSkZ3c1o2c0kxd3J5a2NqSEhxTlZ0L3RlekJGaGtIZUVFWHdvZGtveHVjenJoL0FGSTY5R043SGYrcmZPb1lHeUZPWGIySUtPeFUvYjJON3psRXlocjdsckRhK1VYZ2tCYmhDL1AxV1RQb0xlR21oQS9jZ3hPYnR2Y0o0TWRFOGtkTG5wa1lXQy9vamtLUmdWd1gxakllUTBpL0hVVFIzRU9uN1JwRzNMV3crTTk3KzFtMktIbEZua1NuWTRkM3RWZ1NBQUF5Umo4MXdFQWdKbFI1elVCQXJNM3pTRW1IT3YxK1MrM3cvM3h0dE5HRnVvOG5mWDUrZytIMzFHZnAwTThFK3pxdHZuSU93YXRjczIwcXJWTzFFekNCRHllMm1qWjMyOWhnL3pOYWQ1VVV1NW9sbmJ3ZWhSbWtQNGIra2wvUGZ3U2xtMnhOTXEwZGg0RStHL1llUWhWd1ZFUk4iLCJtYWMiOiIwMzYwODMyYjJkYzJkYTU1ZWRiMGE5NzJkNWRiYTk3MDZiYzY3ZDhlZDMwZWI2OWMxMzYyNzgzM2YzODgxNGI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95931392\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-388927820 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388927820\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-285775546 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVmNWIyazFHdzNKMFpCRlV2WkIvOHc9PSIsInZhbHVlIjoiMU44NjVUMkp3MVVDWDBqOEVDaURWQXJtUTRCTkZGdWtFVVd2UG1zNHpWUFYxY1FuM254dUxwUUxXK2N2SFRmT0tLQ0RQT1ZhR0ZVMHQyS2UzREdYSkYwTXp3T3BlZ1RMY2x1djN3aCt5MUZkSHJ6bkhlN1prR211MTV0V2FvNWU1V1QwaDlZU0VvbUpCbE1OaGNmNEs5eVhvdGFoazlkQ1VhV21CME03RzhCY3NtR0toREFySG5ZcjFaUDZXRlJ3a1dGL0JjZ1RMVzVDTUdpUmtuZEpHU3hkYXdvditDdEd1REgrYTZDem1jT243cE1RaXhDSnFIbFAwMDY1YTFITGNBOVZONDNHNmkrY2NrOFlwSlR5YXZtdlZOVHphMTdVbFUvdG5INVc0bnFjdC90czZ5MWJSRENDZUEwcGxvQ1pKbFFvVHMyM0tPZjUxZVgrUTZ3YzVJTmcvMEVVNzZybEd6VHFJeXFxMlVYc0Y3cFRGWUd0dkpFWDhNNFdjdU5wdEJ4ZjFZMHFaVHFmNG81cnppTVVCVjM3K2RHU2dzYXFlbjMxMnJEOGxZRHMvY0RPWDlqaEkyTjdFem5MK1JzTFZWclpTdWVGZHNka0tWMWtjeUtuVGU1TElMMkFwTEdITGxlb21IT01HNVNGYjZiSExXWFdPRzNuaTc1OHJNSGoiLCJtYWMiOiI5YzU3ZjdiY2FhMzdmZjNhMDU5MzBhNWU3MmFhYjU0YWMwOWQwNjdmMWYxOTIxNmFlNDVhNDExMTE0NzBlYjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5wUnYrVitXdFBHWmkyTlh2MlRNOVE9PSIsInZhbHVlIjoicE1oWWJRVlR5VWFIbGRDRy9DbmNHdHhKSll1WFUyQkFNUi9zMHFZbDZkcEwyM1kyOHEvTUltVzg3ai9meWVlMjlPK1pnREJ0T0hwT3RUd3JBcUFvYUFqdkFKTXh1Sm82NVdrNkFFNWFOM2ZJcld1eUg3d2FLTXlXS1FUc1dNZTJTT213RHV2aFFjN0RLeHEvZ0QyL1hWWUM5bEF5eWNlSUlzaFcxbkhkT3RnbjBQM1Vhdy9ML1NvNGpBLzhJL1FHMzRKbXBxOUpmMEJXZHhTUUxoa2dXUmhLQUZNYXRVTUlHc1ArQW1JcTBMYVpra0NNUEI1Tmt1K3Y2VjlaamMzZ0MydUF2ME15b0hDYi9ZRllTZ2xCSVRQemJRdWlOVGdINGFjb1BWazBCdjl0M1V1YnpSNG9SeWJrSnArWEVQMXVJSEdEZU5yeXlXN20wQWZmZFpwbXpBY2VyVitlb3NHOVFNT2c5TDR4NHdXWlBVTURVNGVDek5EZmFicUIraFNNaGZHOHcxbnFzbnNrOUQ4c2Z2a1R6VStXWURKblY3aHAxRWpXZ0tWNGJmczh2cFVFbDlDcUUyWHpQWEZVbmFaRlFaR1YzWjZwaUo5cElVV3BXZy9rcjZhclRVVzBudTlkcGtWVG1qdVI3UkFDOUVGNm9wVnVxWlpUZDZKcmI3cG8iLCJtYWMiOiI2Y2I4NGM1NDY0YzkxNzBjYTU3YzI4MmY1OTZmYjY5ZTU3MmY0ZjBkNzI1ZDRjZWQxMWE5NWIzMDNlMjA4NjcyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVmNWIyazFHdzNKMFpCRlV2WkIvOHc9PSIsInZhbHVlIjoiMU44NjVUMkp3MVVDWDBqOEVDaURWQXJtUTRCTkZGdWtFVVd2UG1zNHpWUFYxY1FuM254dUxwUUxXK2N2SFRmT0tLQ0RQT1ZhR0ZVMHQyS2UzREdYSkYwTXp3T3BlZ1RMY2x1djN3aCt5MUZkSHJ6bkhlN1prR211MTV0V2FvNWU1V1QwaDlZU0VvbUpCbE1OaGNmNEs5eVhvdGFoazlkQ1VhV21CME03RzhCY3NtR0toREFySG5ZcjFaUDZXRlJ3a1dGL0JjZ1RMVzVDTUdpUmtuZEpHU3hkYXdvditDdEd1REgrYTZDem1jT243cE1RaXhDSnFIbFAwMDY1YTFITGNBOVZONDNHNmkrY2NrOFlwSlR5YXZtdlZOVHphMTdVbFUvdG5INVc0bnFjdC90czZ5MWJSRENDZUEwcGxvQ1pKbFFvVHMyM0tPZjUxZVgrUTZ3YzVJTmcvMEVVNzZybEd6VHFJeXFxMlVYc0Y3cFRGWUd0dkpFWDhNNFdjdU5wdEJ4ZjFZMHFaVHFmNG81cnppTVVCVjM3K2RHU2dzYXFlbjMxMnJEOGxZRHMvY0RPWDlqaEkyTjdFem5MK1JzTFZWclpTdWVGZHNka0tWMWtjeUtuVGU1TElMMkFwTEdITGxlb21IT01HNVNGYjZiSExXWFdPRzNuaTc1OHJNSGoiLCJtYWMiOiI5YzU3ZjdiY2FhMzdmZjNhMDU5MzBhNWU3MmFhYjU0YWMwOWQwNjdmMWYxOTIxNmFlNDVhNDExMTE0NzBlYjM1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5wUnYrVitXdFBHWmkyTlh2MlRNOVE9PSIsInZhbHVlIjoicE1oWWJRVlR5VWFIbGRDRy9DbmNHdHhKSll1WFUyQkFNUi9zMHFZbDZkcEwyM1kyOHEvTUltVzg3ai9meWVlMjlPK1pnREJ0T0hwT3RUd3JBcUFvYUFqdkFKTXh1Sm82NVdrNkFFNWFOM2ZJcld1eUg3d2FLTXlXS1FUc1dNZTJTT213RHV2aFFjN0RLeHEvZ0QyL1hWWUM5bEF5eWNlSUlzaFcxbkhkT3RnbjBQM1Vhdy9ML1NvNGpBLzhJL1FHMzRKbXBxOUpmMEJXZHhTUUxoa2dXUmhLQUZNYXRVTUlHc1ArQW1JcTBMYVpra0NNUEI1Tmt1K3Y2VjlaamMzZ0MydUF2ME15b0hDYi9ZRllTZ2xCSVRQemJRdWlOVGdINGFjb1BWazBCdjl0M1V1YnpSNG9SeWJrSnArWEVQMXVJSEdEZU5yeXlXN20wQWZmZFpwbXpBY2VyVitlb3NHOVFNT2c5TDR4NHdXWlBVTURVNGVDek5EZmFicUIraFNNaGZHOHcxbnFzbnNrOUQ4c2Z2a1R6VStXWURKblY3aHAxRWpXZ0tWNGJmczh2cFVFbDlDcUUyWHpQWEZVbmFaRlFaR1YzWjZwaUo5cElVV3BXZy9rcjZhclRVVzBudTlkcGtWVG1qdVI3UkFDOUVGNm9wVnVxWlpUZDZKcmI3cG8iLCJtYWMiOiI2Y2I4NGM1NDY0YzkxNzBjYTU3YzI4MmY1OTZmYjY5ZTU3MmY0ZjBkNzI1ZDRjZWQxMWE5NWIzMDNlMjA4NjcyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285775546\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1800229396 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2142</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card100</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">115.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2142</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>115.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800229396\", {\"maxDepth\":0})</script>\n"}}