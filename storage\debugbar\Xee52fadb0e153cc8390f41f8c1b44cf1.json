{"__meta": {"id": "Xee52fadb0e153cc8390f41f8c1b44cf1", "datetime": "2025-06-30 18:37:32", "utime": **********.477798, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.093444, "end": **********.477813, "duration": 0.384368896484375, "duration_str": "384ms", "measures": [{"label": "Booting", "start": **********.093444, "relative_start": 0, "end": **********.431373, "relative_end": **********.431373, "duration": 0.33792877197265625, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.431383, "relative_start": 0.3379387855529785, "end": **********.477814, "relative_end": 9.5367431640625e-07, "duration": 0.04643106460571289, "duration_str": "46.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4586198, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.967}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.468506, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.967, "width_percent": 24.535}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.471701, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 85.502, "width_percent": 14.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1287 => array:9 [\n    \"name\" => \"KDD حليب شكولاتة 180مل\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1287\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-853851464 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308318634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijlpc1dSSjYzM1lnNS9URUQyY2k1V1E9PSIsInZhbHVlIjoiQ0psRTR1T1pxY2IzeHFGc2dUR2RBTVpVWEI3VDViZlFmSE9IQkxobmlnRisvWHZYVVlFT3lCbmFvNVhKNzh6bzdscGZ5bW1paHVuWldqd2ZZbFBXZ0d1TW5XVGwxRWpDRUhqQ28xdVlUcUNpMm16dnUwaGd0VUxsUGJmNW5Bc01pWjVaaFV6dHl0aHhTQzBBZXJrdjJMZS9zMkx0TzEwVEZCb1R1YjZPaXR6a21ja3FoN1IyT3pHSWh4N3g0alRxVGkzbmVqTllWTS9XZHYzUVY3UGpnUmFGRk5tdjBLL3puV3pwenE4TUMyczhud2Fnc0Z2Z2dyYURzOGRZeGNObkZoZldUN0FleGh1bFlmUTNLZUNycldhWXA1VWpQQk1yUzJ0TnRzVHVtZkt2QlFtZGYzQ1R1ZUxIclBjRk44RWdwUXUvUnVYejlRMVR5czR5aGN6VmQ1bTR3RUxVL1VZVzNoclJFYklmL2lRVVkvSzJDT1MyenZYcXdhQyswdUZFZTZnNFpvZFlZRWtlSHBvcFdjNzVmMXlqakxWemd0KzRPRUdkNkdSckE1TGljcW5KL0dJeXoyN0gvUGNNalpBVkZXOHZLRHk0TWdWdWNqN1ppb3FYaVc5WEtSZnhPMCtSc3BUSDhVTWJZeUt0UEZaRjlzL1lhcFlUUjhQblpNMVoiLCJtYWMiOiJmMzZjZTJkOTIzMzJjMWVmOThjZTc3ODVkMTk2ZDNlNjNjNWI5MWRhZDFlODA0MDkzNzUzYWUwZTk5Yjc4NWFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im96YzdreGlCRW9EWDFZN1UzUUF5c3c9PSIsInZhbHVlIjoidVluOUFsNWpuL0NKOC96QmZmSWs0bVRtY3I4NlZoajNTR0Eza1NmUlFBRzNJb3BOOEp6c3FibktrSFhwSHVJRXd2aGRUdWhjTDRwOHdpd3UwUmZmOGVsbDJvalR2aUVrMS9mam56UDlzWUpyNXoxcmJ0WGs3akhibG1nVnhhNDFXTW1kQVNaL3FxT1NHeE5ISVVMNFYza0NNNDdJbzEzQ2dCRWdzNS9rS09FY2Nid1Vrb3R0bnRkMGtzZkVucFdrekZWYjlWNWQwYWFVMXFIQmdPYTBSTXFRVWFpdGNnVXQyYnNCMjRKWmt0bVV5M1o0MUd0VW5XOWwrdTFYeUQzOUEyazBHdktZWkFvdkQ4YXJHVkNkYW1qdWNIdXJMM1U5YlYzdVV6a3dEaVpObUtjUEhETUh2OERocnRrMlhtQ25RODNHdVhSb01UOWI3NGVJMk82cUw4cUR5S01lSUMremx1dCtTdVhqTFhWazV3M1EzQ2VXMCsvK2Vzbm9oUlVqUkNDcG43MHE3NkNEY21ZcE9CZ2tYUXZxejZMNG5QODJTWEM5dGo3dzRmWWdORVNaMS84cjNsTENLMWplaS9QK1krZUNoR1FpalRSVEhhZEtTb3RBRWtDcnV6TkY1RzhGbmdhdTkyUXVFUlYzYVBLODVvTlBwRXJpRVhYTmE5S00iLCJtYWMiOiIyZmIwODI3MTRhMWJlMzljZTkzMGY3YmY1Mjc5MmUxNzNkOWYyOTQ1NTI0ZTVmZTMwY2JiOWZjYjg4YmVjODIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853851464\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1596582771 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596582771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:37:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlUyamJPbGhUK2JQMW1lRzJPYjhmTnc9PSIsInZhbHVlIjoiMHoyWE56TzlDMXRZUDduMjdjRGZsUVU2cjZtU09YUGk0STZrU2xqSzJ4aWZRbUc2OEpNMXBmbGl1OVJWRzRaVGhndEhCKzBJSXU5N1pHRzJFa0F0Y0hXMFJFL0x4dUNKbWs1Wk9GODVXZTBSZGUwL1lFTkthQUlSZjQ1cVRzVys4VnZVdmFCNmk0cWVRcXROL1hwS3ZlZ29qYVhsZmI5dXpQM1B1ODNQdjFKd0kxdjdQMndXdzlQMnZndjFUQ083bUMvN1hGNXNjclZleVFzYlJ0WDE2TTJEaHNNK1VJYTJ5dVk5YnVuU0phWis5VitCWXNWMlVQNUxWQkJTWWtTWU12WVlkbjlrQllSWVJWWXlPZUhHaGorall4UHJ0ZVFuS055REpqZ0c5K1FVTDlMeGE4c1lFeGZXR2p4ZGh4bHVHaUs3bWY0anNwamFGR0habncva0hTdXhYTlE0a09zRUIybEgzUENzYzZ5WE53RDBXSXFvWXA3cHRqWVhtZndDQlhuazZZdnYyY2wrMUUrd24zOUE2U0IxRkM2UUxya1ZSVW5Udnl6cCswK3NBZG5RTW1TZTk2enhDbWd0SldCODd1OEw0VFFKZzdnOE83L0hLbzErWnRkVWNGUTZYbndleEgxdjUvVlhjZFZ4c1hiYWNQdkFyaHIvZmRWdmpMcnkiLCJtYWMiOiI4OWRiMWI0ZDNjZGNkM2FmMzJhNDBmNTgzMjI0ZDM2ZDUwMzM1MGQ4MWY5MzA2YjIxNjJlZTAzMjE0MjAzODY3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:37:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5jVzVGWW54YVAvMDJoSjhBd20rVkE9PSIsInZhbHVlIjoiandMTW9jTmNIcXBzZHgxTG00S0syNDhIOUJSSEZNcit5blRaaDluT2JPQ0QyY29HemU3NkJFUWN6UWxTTFNna3RIOVphNi9ueXFwaUROejB2cUR3Qm9NaUhXRzY2RTRNMjRDQ2FZRFVUcnY2elpnT1ZHVWdKNlhZRkRCN1Y1ekpTYVQ0aUNGMS9PRFFjZFpZMFNKUDZrekxqdjNIRnlBQ0Ribnl2SW9UL0J4NkpJNHJDLzB1T0NMa1NDWmhUaExqelBTR0JIMHRLZVlDQVc2WXNvandkK1ZjZE1sZDF3OUhSTnZxVWcvLzJmWjE4VFFVUlJ1eTE4TWV5ZGh4K0NlZjFsajNjb2Vqd3h3Tm9JeCtrSnNqbG5NYlpWSERDc2c0a0l1MVZ3RURyRUZETmIrNkVZREcrc0VIckJEU2lxOTlsRXB3Y0ZmZm42WWlNRnBkNUJGUkUyaFFhZzA2VEFYc0xMRkw3VklPYUhkV05yNFhhUW5naVlBVzdiUnVZNUcxbFFxbnltdUFVclU5Z1kvYTNoZ2R1T0c0aVI0M2ZrdTh1bmtveHdoWExuQ3JLaWx6REdCbW0wTGRvWE40WFFRZC9FeGNtN09Ga05yeE5Hd3hSMEJLVmc4MnZSUURWTVNmUGVnUlRVMTlGQ1grd3lMRGE1N2NGcSt6MU0reWUxUEYiLCJtYWMiOiJlMGM3OWJkYjJlMWNmZWFlZjI0ZmUxODUwZmJkY2YwMmU3ZDBjYzM4ODcyZDkxZDUxOTZkNWE4ODBiNmRhMTZhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:37:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlUyamJPbGhUK2JQMW1lRzJPYjhmTnc9PSIsInZhbHVlIjoiMHoyWE56TzlDMXRZUDduMjdjRGZsUVU2cjZtU09YUGk0STZrU2xqSzJ4aWZRbUc2OEpNMXBmbGl1OVJWRzRaVGhndEhCKzBJSXU5N1pHRzJFa0F0Y0hXMFJFL0x4dUNKbWs1Wk9GODVXZTBSZGUwL1lFTkthQUlSZjQ1cVRzVys4VnZVdmFCNmk0cWVRcXROL1hwS3ZlZ29qYVhsZmI5dXpQM1B1ODNQdjFKd0kxdjdQMndXdzlQMnZndjFUQ083bUMvN1hGNXNjclZleVFzYlJ0WDE2TTJEaHNNK1VJYTJ5dVk5YnVuU0phWis5VitCWXNWMlVQNUxWQkJTWWtTWU12WVlkbjlrQllSWVJWWXlPZUhHaGorall4UHJ0ZVFuS055REpqZ0c5K1FVTDlMeGE4c1lFeGZXR2p4ZGh4bHVHaUs3bWY0anNwamFGR0habncva0hTdXhYTlE0a09zRUIybEgzUENzYzZ5WE53RDBXSXFvWXA3cHRqWVhtZndDQlhuazZZdnYyY2wrMUUrd24zOUE2U0IxRkM2UUxya1ZSVW5Udnl6cCswK3NBZG5RTW1TZTk2enhDbWd0SldCODd1OEw0VFFKZzdnOE83L0hLbzErWnRkVWNGUTZYbndleEgxdjUvVlhjZFZ4c1hiYWNQdkFyaHIvZmRWdmpMcnkiLCJtYWMiOiI4OWRiMWI0ZDNjZGNkM2FmMzJhNDBmNTgzMjI0ZDM2ZDUwMzM1MGQ4MWY5MzA2YjIxNjJlZTAzMjE0MjAzODY3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:37:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5jVzVGWW54YVAvMDJoSjhBd20rVkE9PSIsInZhbHVlIjoiandMTW9jTmNIcXBzZHgxTG00S0syNDhIOUJSSEZNcit5blRaaDluT2JPQ0QyY29HemU3NkJFUWN6UWxTTFNna3RIOVphNi9ueXFwaUROejB2cUR3Qm9NaUhXRzY2RTRNMjRDQ2FZRFVUcnY2elpnT1ZHVWdKNlhZRkRCN1Y1ekpTYVQ0aUNGMS9PRFFjZFpZMFNKUDZrekxqdjNIRnlBQ0Ribnl2SW9UL0J4NkpJNHJDLzB1T0NMa1NDWmhUaExqelBTR0JIMHRLZVlDQVc2WXNvandkK1ZjZE1sZDF3OUhSTnZxVWcvLzJmWjE4VFFVUlJ1eTE4TWV5ZGh4K0NlZjFsajNjb2Vqd3h3Tm9JeCtrSnNqbG5NYlpWSERDc2c0a0l1MVZ3RURyRUZETmIrNkVZREcrc0VIckJEU2lxOTlsRXB3Y0ZmZm42WWlNRnBkNUJGUkUyaFFhZzA2VEFYc0xMRkw3VklPYUhkV05yNFhhUW5naVlBVzdiUnVZNUcxbFFxbnltdUFVclU5Z1kvYTNoZ2R1T0c0aVI0M2ZrdTh1bmtveHdoWExuQ3JLaWx6REdCbW0wTGRvWE40WFFRZC9FeGNtN09Ga05yeE5Hd3hSMEJLVmc4MnZSUURWTVNmUGVnUlRVMTlGQ1grd3lMRGE1N2NGcSt6MU0reWUxUEYiLCJtYWMiOiJlMGM3OWJkYjJlMWNmZWFlZjI0ZmUxODUwZmJkY2YwMmU3ZDBjYzM4ODcyZDkxZDUxOTZkNWE4ODBiNmRhMTZhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:37:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2018515515 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1287</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">KDD &#1581;&#1604;&#1610;&#1576; &#1588;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; 180&#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018515515\", {\"maxDepth\":0})</script>\n"}}