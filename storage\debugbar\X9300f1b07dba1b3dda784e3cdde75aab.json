{"__meta": {"id": "X9300f1b07dba1b3dda784e3cdde75aab", "datetime": "2025-06-30 16:07:13", "utime": **********.095958, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299632.589892, "end": **********.095979, "duration": 0.506087064743042, "duration_str": "506ms", "measures": [{"label": "Booting", "start": 1751299632.589892, "relative_start": 0, "end": **********.012466, "relative_end": **********.012466, "duration": 0.4225740432739258, "duration_str": "423ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.012476, "relative_start": 0.42258405685424805, "end": **********.095981, "relative_end": 1.9073486328125e-06, "duration": 0.08350491523742676, "duration_str": "83.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45553976, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01719, "accumulated_duration_str": "17.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.044468, "duration": 0.01583, "duration_str": "15.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.088}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0713391, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.088, "width_percent": 2.967}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.080032, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.055, "width_percent": 4.945}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1627955319 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1627955319\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1565328287 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1565328287\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1188539690 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188539690\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-653527392 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhJTVJ6R2FJdU94Yjk5c0k4ZzZDN3c9PSIsInZhbHVlIjoiQjdSb0JiUkx6QnpqMUwvQXdFbFZLZXZrL0tabkZJOVZEZVl0elJHSFdabUhHN3JEbVdieStMWE81K1JmQWtBcXRFSVRsSmZQM1lmN0tHdVhFZ2FOLzVKaFBLWkM0MW9jS2c4eHZ6bUFJRmZoRE9YcTNIcVV5R1ZRczBIL1dlVzk4czJabWFGQXVDNDI4eTdzTXJBQTB2QVZrb2Z4NjEvMGNuQS8xWGpRcXYrNC8rSjM0NUY3QlpuTTVGcFY2bjVvK01rVTNlZ29QUFVXQXh0OEN5KzAwcVg2MlkvcDFnQTFLZW0xclBDdDF2dnJCVDZwbWRGaFlPb01pQmZSckZaL2Nka2ZwaU1OcW5YU0x1VnE4V1JRNDU3N0RXM3lEWFFFeEZmemFhMlBGZlA4Zmhkei9KWVpSMWp1ZFBPa2V6WFVVZU5Qem9yRXBzM3UwbFBoU2hRZWRwMXM0cHNxd29WaHZBSlNrVzYraDVQbFdrQnVqQVNHL3hLMWhRUVJTRVZyb1lzTlNmYlNGdnlONkxmQjZBZUltd1pvaTBnMlFVZExQaFNhWXhNamVTZ1RIUUk4OXpPb2VpaUlIZ0RVcVRPWkVHMnVUNFYwZVhjS2tXMXlmdFpvMHBxdXN6R3RBajRPVVF4SVRhaUtLdGVBaVVRMGxHdlVDZjlWYUhhdGlDdVYiLCJtYWMiOiIxYTJjMjA5MmE2ZjQ3MzM5MjcxNmQ4ZDM0NzgzMGIwODM5NGMwZTZhNmFiZDc4OGQwYzhmZTViNzY1YWE1ZTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhBR0NvMDlpR3pSeW9qQVA2elpvR3c9PSIsInZhbHVlIjoiSkd5VlA0Q3JSN3dYa2Evd0hUTUJQSkZYekx4b2RHQThCMS82Z2pLa1pmQlNXTTkwYndGOXE2UVRpMktuUnZLaTQzczh1N3BRR2RrLzU1a3M3c3JwM1NUYThoNzVhbDJPV05LdlRNMjhJdURoV1hGS1pYMkpYb2lsVE01N0hqMldUazNVSVNhekl0aTVKaXV1MExTdTVsMS85Vy9jWlV0c2tLY2VIOXZRMi9pOHNzb3k0NmVsSWNuVjB1Z2dwbUNBekRFMldEM3FCNWVvQWlrbzNkZUY5T0Q1Zlg2NmZJLzBMTjlNUjVDalQ5WnJCMFppQVVuVUp5ejFHY0tDWGJUSlpKbGlKbEpyeXMwZzAyNmRaenNtczlOVDdhOFg1dG9PSXA2cG5aNGNFMzlqc3YvaUxDdTMrcWYrU0pDbkp0YzhMb1JwSTErWFVuMGVsb0lKS25UVkRDdE9UbDZnaXB0QTgwOUpVbFJPWERYMWVPN1RsdlJjd3FhaXIxTHBRald5T3p3OXR6ZGt0dHlMbXlpblZ2ZjZaaThObnRRdFE5dllyQjdDZWZSakU3ZnN0RjFZZlMzb3UvdnpZWTRNVDNyTThjenlxMHBVd3krNXhPcjhwT0pSSnc4N3orQXJIZjJGNHhqbk14cG9hbkgrTi9QQlJ6QitYYTNDcVltcWIxTloiLCJtYWMiOiI5MDY3ZTFlOGU4ZjI5MWRkOWU2ZGFmYmYxYzc0M2YzOWZlYjdiZGUwNWZjYzFmYTBmZTQyMDdkMTI0ZTk5NWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653527392\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1551616723 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551616723\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1321161152 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVBa3lmNk9aMGxxaVl6Q3VvUXhEQVE9PSIsInZhbHVlIjoiby9tU3EvYi91eWNqYy8wbGdlUlBCTzhiaXJndnQwZDIyQ2gyQ2VqT3NJQU1iSHBoNEt5NVZNczJheURsQjNURTlpaGZiYi9xenVkaW5OQUJOdHlZNFdqVzgxWlJFVkRmbVR0KzUyUElVVEp3LzhuZ2hhS3g3cTVBVmRuMjY0WXRRdjJBSkxNaGxCdEthMmxjZm1XOGR5ek9EOEZhS2FFem1pSlBQMXdNQ1djUldEV1JPNktENDRKbUlvSkFpM3FwZEtWb1F6WGVwczNnRHgzSG9ia3lScDd1M0QvSkc4SXhjQ2xSamhOc1UzdytkRlV4K1puQzUzamJVYzllZHVqbEtQNk9sSXdoNWV2Z3J6Mi9ENm1EbUsyM0ZXMWQ0dnBGa01DemIyZ0pYMGJxQjZkQlNiZnB6U1g2WWYxVkdPK05oWjA1djB6bmIrMUgxZkRhZHBieVBlejl0VWxneXZ0Z0dUWW8xNzRqbmsyY0VhQlhybUsvRk9sclJUWSswOGpyZVJZN3c1TDlyTUxLcGV5RnVkZzRJR3g5Y2MrVXA1NVJnN2tISXcvRU1JOUlvODFNWmpOaTUxbG1jQVdzOGFUd2N1dzQyVUkxY0QvU1MrV0NzdUVMUzN0QjVYQXV2S2VhVlBYd0NzWTd2ZC9QRThCVmF6RjZSUklYVUdBSC9NZkIiLCJtYWMiOiI4NzZjNGQ3NWVkNTViZjhlMzU2ZGY5MzQ0MzEyZDk3MTM3ZWNjNDVlNjMyZTlkYWJkMTk0ZTk5M2UyZDdlM2ViIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVDVFhpQ0pyeHZTTndNREtPUEl4SEE9PSIsInZhbHVlIjoiaWdQWjZMaHNoNDB3Y2poRFVjZ0dKekhVRm5WUmRMMkJzUmhUU1FNV0JQWkMybk82bWljQ2Z6dDVZTTlXc2NUTjJDbVhkTCtHaTBxSVFmc2d4N1loR3hKMDJlaTUwMTBlZE4zbExZVWxqSFNYTFZmemFRU1VyUHZTTXJxeUsvTVlyTTl5RkYvZlRIbWJ3bFFBNjVCTHUzUWxFRTMxTzluMXBndFdBV09qZngybDZabFRYUzVLcDgrUEU0U1lWQUpwUDZ0QlYyYzJyT1JzZXJPZ1FoWXViOUtlSnhBV2U4a1E5eThIYWJSN3hXeXVxNVNYaHJoMnAxSHVEUXpXbmd3QVNnUmxaWE5vL0YwVjdIQ3k0M0NHWmJWZVpEYmF0OXlHL051d2o1WjZJY2ZTc1E3L1l4V09vcVhEOFlDdDgvbldGQ0lrbHpjTFRJbW9CWU9kZnVTQm9EclZ1RFhyRDZ1eXQzTVZmeVFvVjNnWEpiM0ptYThHUnprNFRzcVQrQm9KY2lWOHg4NDJOUkl0cUdEM0pJcUdkMkNVbDl2VnNBdlJiSW1NU21QQ3dFS2lkTmczL1UyM1c2dVBieGdoWkpNUElKaG9TNGJjYmlPYldZQ0dzR2hwZlZUaEo0NXJRYnNTWlFFNFNpa3B0NHFhY3RpT1AxdThoV29nOTViZDl5VEMiLCJtYWMiOiJkMjZjYTdjMDkzNTQ5ZDM5OTA3YjI2MDllY2MzY2MyYzM2MDQ5ZWFhYjU5NTEwOTk4NWVjNjNiYmU0ZGJlOGE3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVBa3lmNk9aMGxxaVl6Q3VvUXhEQVE9PSIsInZhbHVlIjoiby9tU3EvYi91eWNqYy8wbGdlUlBCTzhiaXJndnQwZDIyQ2gyQ2VqT3NJQU1iSHBoNEt5NVZNczJheURsQjNURTlpaGZiYi9xenVkaW5OQUJOdHlZNFdqVzgxWlJFVkRmbVR0KzUyUElVVEp3LzhuZ2hhS3g3cTVBVmRuMjY0WXRRdjJBSkxNaGxCdEthMmxjZm1XOGR5ek9EOEZhS2FFem1pSlBQMXdNQ1djUldEV1JPNktENDRKbUlvSkFpM3FwZEtWb1F6WGVwczNnRHgzSG9ia3lScDd1M0QvSkc4SXhjQ2xSamhOc1UzdytkRlV4K1puQzUzamJVYzllZHVqbEtQNk9sSXdoNWV2Z3J6Mi9ENm1EbUsyM0ZXMWQ0dnBGa01DemIyZ0pYMGJxQjZkQlNiZnB6U1g2WWYxVkdPK05oWjA1djB6bmIrMUgxZkRhZHBieVBlejl0VWxneXZ0Z0dUWW8xNzRqbmsyY0VhQlhybUsvRk9sclJUWSswOGpyZVJZN3c1TDlyTUxLcGV5RnVkZzRJR3g5Y2MrVXA1NVJnN2tISXcvRU1JOUlvODFNWmpOaTUxbG1jQVdzOGFUd2N1dzQyVUkxY0QvU1MrV0NzdUVMUzN0QjVYQXV2S2VhVlBYd0NzWTd2ZC9QRThCVmF6RjZSUklYVUdBSC9NZkIiLCJtYWMiOiI4NzZjNGQ3NWVkNTViZjhlMzU2ZGY5MzQ0MzEyZDk3MTM3ZWNjNDVlNjMyZTlkYWJkMTk0ZTk5M2UyZDdlM2ViIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVDVFhpQ0pyeHZTTndNREtPUEl4SEE9PSIsInZhbHVlIjoiaWdQWjZMaHNoNDB3Y2poRFVjZ0dKekhVRm5WUmRMMkJzUmhUU1FNV0JQWkMybk82bWljQ2Z6dDVZTTlXc2NUTjJDbVhkTCtHaTBxSVFmc2d4N1loR3hKMDJlaTUwMTBlZE4zbExZVWxqSFNYTFZmemFRU1VyUHZTTXJxeUsvTVlyTTl5RkYvZlRIbWJ3bFFBNjVCTHUzUWxFRTMxTzluMXBndFdBV09qZngybDZabFRYUzVLcDgrUEU0U1lWQUpwUDZ0QlYyYzJyT1JzZXJPZ1FoWXViOUtlSnhBV2U4a1E5eThIYWJSN3hXeXVxNVNYaHJoMnAxSHVEUXpXbmd3QVNnUmxaWE5vL0YwVjdIQ3k0M0NHWmJWZVpEYmF0OXlHL051d2o1WjZJY2ZTc1E3L1l4V09vcVhEOFlDdDgvbldGQ0lrbHpjTFRJbW9CWU9kZnVTQm9EclZ1RFhyRDZ1eXQzTVZmeVFvVjNnWEpiM0ptYThHUnprNFRzcVQrQm9KY2lWOHg4NDJOUkl0cUdEM0pJcUdkMkNVbDl2VnNBdlJiSW1NU21QQ3dFS2lkTmczL1UyM1c2dVBieGdoWkpNUElKaG9TNGJjYmlPYldZQ0dzR2hwZlZUaEo0NXJRYnNTWlFFNFNpa3B0NHFhY3RpT1AxdThoV29nOTViZDl5VEMiLCJtYWMiOiJkMjZjYTdjMDkzNTQ5ZDM5OTA3YjI2MDllY2MzY2MyYzM2MDQ5ZWFhYjU5NTEwOTk4NWVjNjNiYmU0ZGJlOGE3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321161152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-503269048 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503269048\", {\"maxDepth\":0})</script>\n"}}