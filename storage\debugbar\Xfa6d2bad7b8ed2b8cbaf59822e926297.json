{"__meta": {"id": "Xfa6d2bad7b8ed2b8cbaf59822e926297", "datetime": "2025-06-30 16:06:35", "utime": **********.733217, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.286522, "end": **********.733235, "duration": 0.4467129707336426, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.286522, "relative_start": 0, "end": **********.678067, "relative_end": **********.678067, "duration": 0.39154505729675293, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.678082, "relative_start": 0.39156007766723633, "end": **********.733237, "relative_end": 2.1457672119140625e-06, "duration": 0.055155038833618164, "duration_str": "55.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032800000000000004, "accumulated_duration_str": "3.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7098088, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.341}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7201169, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.341, "width_percent": 11.585}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.72575, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.927, "width_percent": 17.073}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/58\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-58312187 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-58312187\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2093504480 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2093504480\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-167076368 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167076368\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1077945836 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299580153%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkYrc0tmZklMOEdWYThRZEhCODAxVGc9PSIsInZhbHVlIjoiSnE0WHdSeU83dWpmNUVKbUZwaS9BRXdyeHJjZUF3eDBPTXk2RU5UNkZ3TWQ0RjFZbTd0aTZ4UHp4WG9CUjFQeHM4bkZLUFJKT0VkSVdWNzJiRTMzMml3d3dkVUFaNWdBWE1CSHZaMTZCT3ZnQXNUMUtpOFNtWjJ0bmxkV3Q0bm8rTVo1czExdTQ1c0gyNGlxQkM0aGI0VnZoS3BoY3k3dG1qY0ZWT2Nuc1pjUTZCSW9FOXNRU0lMcXM5OVJlYUptVCtOTEIrblFaSmJPeWtBL082RE0zcHZzb3FkZ3RDTGV2N1FhNFluZkZEc3d1SFNMRlQxd1VlSjg0ZFIwRVUyeEd4NW01bVpES2plU0M3cEpTWTVSdS9Fd3lub210c3BabFltU0h2cDk4bFpWbytpOTRYSmtRS290Uk02ZDExNXg1am9vTkhjMUU5L3M5VThOK003NWpVNHBnM1ZMVXVlQUdTVWpnSGlSSkx6SjhPZjgvM0tEa0JPUmxWc3YweXFrL2pCbXI0MU9wY2lEajA2RU5jZHptMjVWWUoya2luaGM1WjJaTGgzMDZEcEE5MXRUUVM3OXFIZXNQejZVdG5CamZlZ2s5Wk5HRXVCK1c0eW9UcXptRXJvUGRLczVwUFg4SW5rMWw2Smo3M3d4Qk4zWnJSY1JWNGlkb0NKSzdiQkciLCJtYWMiOiIzZDM1N2Q3OTU2ZGViMDIxZjQzM2RhYTBjYmMyZDU1NGNjZDMwMjhlNGU0OTg2YTMzY2Y2M2MxYTVhNzhmNzNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZDcFdiOERkOHY3ekRnazA5akFhSkE9PSIsInZhbHVlIjoiOEI3QWF6QTF5V3JldUF6NEVHS1FyNWRrTG9hU3BIaG11aGRXckZPMnhlbWdOazJ3ckkyVmorRmpMRFBQS2VvczBxZjdUY3puZ2NrR2NyTXRkaE5LWnFmRTI0eHlwL0FhSEhMN2JBL2hRWmQzdk12V3VIbmhSUjhWRkw1aHRSSmJkbnlESjFBUjBERVN0TG95a0tBNXV5SjVDb1JXcHNVWTZ4MFl3TUgzTUtrVDJUUjYvcC9UUDBuSG1zcHcxNmFYQ2xmTUM4MkJERnVCQVFsb1hnaURwci9hQkVVTFpPZllpQmZzWEZ6OWVOV1NOVUtRd3IxUWJCMkF1dENlMjNoRzlXSVVwTE5CdjhxeG9jMkdyK1lpNUE0SXZDaGViS0FsZlFXOGdJWDZzQ2JUeUNCQWZGQiswUXhzV0loSC9tenlIM3dvMkdpUFhzZEdqazZqMDgxMXBnbzJpRFZaRDhHSDJ4dWpGVDlwS1MzdENSeWFFNGxQU2d0L2R1VDg4bTdsL0lFaVloM3ZLVHFjdmY3Nld2ZHBrQU1vSFBqcnVPYitPTFh0d2VBdXc1d2tuWmJlSUFmaG04SzA2Zkw3YVZveWZJanVGOTd0NTN3cFZDUWE4dWY2SjdIazZmaEthNGVaaWh6dUUzdDMySnYwZUtEbExlZFNnV0M1MTdzazBEaGkiLCJtYWMiOiJkOGI0ZDZkY2VjOTk0Yzg4OTlmY2Y2MzczZjhmNDQ5YWEyZDFjZDNhNDE2YzJiNTBkMTI0ZGExNGM5MzJlMGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077945836\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1497005977 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497005977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2099844005 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhrdFB0QVRrVGdEZnI4dDR1ZytobFE9PSIsInZhbHVlIjoibWJJbkZzK1BEalhzdzZZQ0ZLYUUxWEg0ZGtuRGFLOUtHT2UxWkJhVGI5ekkzSDgyeC83M2xIZUxBTzN2TEdKQzdXWlluL2VQKy9XRzBRalJlTmRhcFBNMjFZS1A2MjZqRVd6OHFsNkswd052M1NBVlRGbjVkRTdTV1FRcjlKVWN1TjVxQktlcVgyamE5T0tLWFUxM2N2cDBRbFlCT01UQ0xWUC9jVXBQWjA1VFBhL3Y4b1ZNRTRYeElpdzM1VUd5TkhTTDdTenQ0SXFDWElpSnNHUXI3WHF1UTA4Q0VPaU5sWU9uRVViY0dSVkhJS1d6eDlseU9lbzhtdG5YQXZFdmJoZEdsTXE3Z21Od0lrQ1FZZjhoZG5QcmFyY1E1UGR6UnFuQWZGalFVbGVuNnZuVU55T2xJVU9GVyt4S1ROc1o5amVEdFhvRCtNM1N2MkIvdituTHFwYll1Y2xodEoxYWsxSWlCVCtBQ1J0K3lqbUJ4dC9NT3EvRkY1cDhXai9UL0pSejJYVjhGSlZyR3lZb1BDQS9aR1NBOFRTL1daemtkS1E1R0RwdFJ6RVI2YUExRkRNQXMzalltTmtaaEtSS1hnRWFiV2NOSkxDTzlpKzJJb2s2QWhab05yclF5eWlmS2V6YVViNkZ4NlJaNTB3eEVZU3Rvek1Qejd1bXIvbEMiLCJtYWMiOiI5OTQ0YTFhMTRhMGFiYmIzYTZlMWJjZTc5OTViNmQzMzA4ZGEzOTFmN2JhNmQzYmU5NjRkZmYyZmYyMzVkMTNjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdVQTBBZmx6SGg3MW9nUFVpbmtqeVE9PSIsInZhbHVlIjoiemV3RE83cDRzTi9PR1NsZGVqU0tFaDBDcks0aTlXSzcybkwxamF6d2Z1czY5TWhCR3U5ME9yNFNTcnV3dldQTCtLMTYwMGpjbWJYT2JibDB0OUVsQzhkbmxENzMzUFZ4WG0vdW1RT2ZkMWd6N2lxUU5KaGRFdHh3c0RxUWN2cVJnWDJWTk5wWXB4NUZUR2JONjkwZzloTmZMVFZRdjZONG5HYWRMWklFNjVPUzhORmpSMjdmVzFjZUt4dVlzUys4U0lDMjBnQ3c2dUhwd3RBQTN6Nk1qMjVKMWZacHpHd1dERVJLMEFTSkczTXhoL0tVQ1ZEdnNjTm5HRmlhNFFTdHNyNkJKcjM5SENibjBoenJwUkRiMlNYVjhiVS9zUytHNW9FVUw5TlhjdGJGbUprZi9CRlZ3alBORm5qZ1VaVUd6dEs5bDFFZmFvVjJqaVZGVzRELzVxTEVidFpWcHpmWk10dmVQSW5pL1I1a3pKWVB0dUNRUnVLaXBLZTZONHVUSTQ5T0RPKzJPajRQTkhHTS9qblI2eGJoMGdCZDM0RFhUbHJvdnpnY2xhSGRsQ0s4cVZ1bVRlZ0hsUWhzeFErWlRpZGJmVThuT2pkbW5QN1BxV3RCalgvclU2VExyWEZFaldXOEFmVzA2Y2pFeEJEWU84WEZnTFlrbTNkSXhSNTIiLCJtYWMiOiJjMThmOWVhNjdlYTcyYjdiZTgxOGUxOTIxOWIzMDNlMDE4ZjkzMzkzYWI0ZDkyMDhiOWI3OGNjMDQ1YzQ0M2MxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhrdFB0QVRrVGdEZnI4dDR1ZytobFE9PSIsInZhbHVlIjoibWJJbkZzK1BEalhzdzZZQ0ZLYUUxWEg0ZGtuRGFLOUtHT2UxWkJhVGI5ekkzSDgyeC83M2xIZUxBTzN2TEdKQzdXWlluL2VQKy9XRzBRalJlTmRhcFBNMjFZS1A2MjZqRVd6OHFsNkswd052M1NBVlRGbjVkRTdTV1FRcjlKVWN1TjVxQktlcVgyamE5T0tLWFUxM2N2cDBRbFlCT01UQ0xWUC9jVXBQWjA1VFBhL3Y4b1ZNRTRYeElpdzM1VUd5TkhTTDdTenQ0SXFDWElpSnNHUXI3WHF1UTA4Q0VPaU5sWU9uRVViY0dSVkhJS1d6eDlseU9lbzhtdG5YQXZFdmJoZEdsTXE3Z21Od0lrQ1FZZjhoZG5QcmFyY1E1UGR6UnFuQWZGalFVbGVuNnZuVU55T2xJVU9GVyt4S1ROc1o5amVEdFhvRCtNM1N2MkIvdituTHFwYll1Y2xodEoxYWsxSWlCVCtBQ1J0K3lqbUJ4dC9NT3EvRkY1cDhXai9UL0pSejJYVjhGSlZyR3lZb1BDQS9aR1NBOFRTL1daemtkS1E1R0RwdFJ6RVI2YUExRkRNQXMzalltTmtaaEtSS1hnRWFiV2NOSkxDTzlpKzJJb2s2QWhab05yclF5eWlmS2V6YVViNkZ4NlJaNTB3eEVZU3Rvek1Qejd1bXIvbEMiLCJtYWMiOiI5OTQ0YTFhMTRhMGFiYmIzYTZlMWJjZTc5OTViNmQzMzA4ZGEzOTFmN2JhNmQzYmU5NjRkZmYyZmYyMzVkMTNjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdVQTBBZmx6SGg3MW9nUFVpbmtqeVE9PSIsInZhbHVlIjoiemV3RE83cDRzTi9PR1NsZGVqU0tFaDBDcks0aTlXSzcybkwxamF6d2Z1czY5TWhCR3U5ME9yNFNTcnV3dldQTCtLMTYwMGpjbWJYT2JibDB0OUVsQzhkbmxENzMzUFZ4WG0vdW1RT2ZkMWd6N2lxUU5KaGRFdHh3c0RxUWN2cVJnWDJWTk5wWXB4NUZUR2JONjkwZzloTmZMVFZRdjZONG5HYWRMWklFNjVPUzhORmpSMjdmVzFjZUt4dVlzUys4U0lDMjBnQ3c2dUhwd3RBQTN6Nk1qMjVKMWZacHpHd1dERVJLMEFTSkczTXhoL0tVQ1ZEdnNjTm5HRmlhNFFTdHNyNkJKcjM5SENibjBoenJwUkRiMlNYVjhiVS9zUytHNW9FVUw5TlhjdGJGbUprZi9CRlZ3alBORm5qZ1VaVUd6dEs5bDFFZmFvVjJqaVZGVzRELzVxTEVidFpWcHpmWk10dmVQSW5pL1I1a3pKWVB0dUNRUnVLaXBLZTZONHVUSTQ5T0RPKzJPajRQTkhHTS9qblI2eGJoMGdCZDM0RFhUbHJvdnpnY2xhSGRsQ0s4cVZ1bVRlZ0hsUWhzeFErWlRpZGJmVThuT2pkbW5QN1BxV3RCalgvclU2VExyWEZFaldXOEFmVzA2Y2pFeEJEWU84WEZnTFlrbTNkSXhSNTIiLCJtYWMiOiJjMThmOWVhNjdlYTcyYjdiZTgxOGUxOTIxOWIzMDNlMDE4ZjkzMzkzYWI0ZDkyMDhiOWI3OGNjMDQ1YzQ0M2MxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099844005\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591602361 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591602361\", {\"maxDepth\":0})</script>\n"}}