{"__meta": {"id": "Xf3c0d66ca132c479e7a728715d46534c", "datetime": "2025-06-30 16:22:15", "utime": **********.217095, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751300534.782755, "end": **********.21711, "duration": 0.4343550205230713, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1751300534.782755, "relative_start": 0, "end": **********.147296, "relative_end": **********.147296, "duration": 0.36454105377197266, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.147304, "relative_start": 0.3645491600036621, "end": **********.217111, "relative_end": 1.1920928955078125e-06, "duration": 0.06980705261230469, "duration_str": "69.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363416, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01674, "accumulated_duration_str": "16.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.183778, "duration": 0.01593, "duration_str": "15.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.161}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.20809, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.161, "width_percent": 2.808}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.210814, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.969, "width_percent": 2.031}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA2RklLUG91WVgyY3AyWFNyRlFmRGc9PSIsInZhbHVlIjoiSTltR1lRc3hnZFRLOTZGVG5JeDRnQlRsaDNTS1pDTWFLU3loNVJIdjF2T0s0MVpZYkgzZUNOcGt3YlFZZXR3cFVLck1MUHM1NEludjlPUEp5cEFqZ0ZPQXFZdmdDazBwMi9venIvYzJGckJkU2U3b05XaitKZzkrVXlqd3piMGlocXVlU0xDUDJpNUUrWUxOTTFoTVNwUnJoOXlyM3VIeTF1bnV0TVhaemIwdlpJUnp4T1BobXhTNzZ3WEVRMGlsR0xORGgrUndCaFFqK1NibVFOUUNDSlU5MkE5MEwweTBaQzNoTEVVZVlTcWFlQXZzak9yMHFVdnoxMERoUWZ0eFNHL3NkVEJBVFRKZWtRT1NzY0VXMVdTVzFZUWVBQk5BeENvaEp6ZUtZM3dmQTU3MERDZmpIZXFabmlQMnN5WUIremZ2bWR3d1lwRkV3WGJUYnpDdWNNL1BiUUl3OVlaUzlyOFlYWUNtZXpDem03MUh1cmhOVXY5ZHI2a2NScU9sU0dyazFiZXNad3B2ZFdIeHB4dWpOeW9scUIzbW1EOHpWUi9iWWQ5alJ2YitabXBWSUZMN0JXMlkzR3RsblAwcHdvekYxMFpETk1MMUY0NHV0Uk5BejdzaDh5MU5rRFVCeDdOY2pTemk5NUh2UkU2d3MzTmpSckJCaUJ1ZzA5M3YiLCJtYWMiOiIxMzMxYWZlZDU5ZmY3ZWE3MjcxMjM1NGM0ODdiMWQ1ZmRkY2RiNTIyNjIxOWY2ZTQyNDg2OWViMWMzMWY2ZjAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImV5T2dvcnFKYXNKcnEwbUlYSUxDMHc9PSIsInZhbHVlIjoiREIrY1gzM3hFTEFwNTNXcC8vYTJKdHMrL2ZqZk9Ga3ZKd3ZnZXlOWGtRZ1Z3LzBkMFlxVWxtTHFwMW1lY0toSk4vMDFUeDg0OXRXVFpOa0VpQVhRT013M3N3djc5a3BGY25YMHNicEV1ZTVsdHhuYUxjYkpsSEhlbmViUzNML2NHR3AxT3psdlRoNzZZWXdNUGk2OEEveXRNTEJKMmJhb3U3VjZ1Sm5SSVpBTGE4S0R0Z0NhTXdwUzZyZHFqVDltQmtnK0VOOGQzN3BNT1QvTGVtUFppNUpEaXdsZGNqRzFrRDFZSTNCL2VlR0Q3V25FR0FEZjRZNmRzSGZVZnJhcjU1bGV3bFQ0eTV2SkJIUGFVK0JiRW91VUJ4aGU1REh3NXhtMjVoZnNqK1ljVXhrUzc4b3IzVFVzSyswTVppUHgyNEF4cWNVMnpMclZMODJPelJNKzNqdnFZQi9sR0QxYXBtWGR4TWpkc0g2MlBVR2hSL2tzWlpTMnppZ1VUWHFQb0Izd2ZvMTRiOEdUQ0Q0aTYwZjE1aWZMME14bHRWd3JaWEM3YkNaU0k3QVhYenlhaUFuQml1clFSU1ZuTkxEZ2tCejJvbmpXcExodTg5c3gxQnRVU0FSTjArZTY4VklEdGRpMmpSM2czVENDV0l5SmU4aWl2WFNMbXZDMnRIS1ciLCJtYWMiOiI4NDdmYjNkYzg1OWQyNmE2OTJiNjZjOTAwYTBhOWU1YmJhZTYzYjk4ODkyYTllMTY1YjA0MjU4NDlkYTk1MDU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-83410112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83410112\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1255401445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InY5Mk9aM1ZnUjFyTkNMSDgrNk5PS2c9PSIsInZhbHVlIjoiZzhpanFvV3lXM1FKZnAyRHdCNyt4c1llSzNvRmhsUmRFKzZhSmpPUThsTERUYWlZWlQ2WG12eTRscEIrUzlSbFg5ZmRlVWxuTzdZd1VHejYxUWhrUWJ4bThVTzl4Zlg4Yjl4VUZJTVEyd0ZVbW1xb3VsaHVIMGVLem9VZ2VDZExUZnhvTlNkWVlUeE1TQWd6UnZWVmZmWEhsYW0yYWtvRkhKU0ZWN0dyNDN6eDVwN002dGZ6ZHhjNTkxK1V1N3Z4Z09uU3hsTk9LRk5La21IUndoNlNFRUpuQ1ZqN1ByelFITGFneGovM3BLcXNZcXZEdDhVOWNCNDYwcldSeFV3Sks3MmFScWdxVktwOXN4UFJBY2ZTc1QrZVZva29IQXpiaklQTFJTUGxMMjhxZlExY056ei96UTk5TW1tdDNKNG9yMDdFeWZqTld2S3dDTEZ0UjNTVURYSGJaUGdpQm1WL0NYUlZVRUxlSllITE9kM05tQnp6VmhoMkVmeERtcmkrZ0NjSnBEUVpHRlJXam9tMWJZK2VwT2xwdTlacHZpemFqZ2wxQXg0Z3g0V2VncER6aTFVQytaTDNKUFdUdHM5V05EQzBuRGNRRjRxU2tsT1VPdnBaM3JZSTdFenlUdEk2Vkx3RTN2WEtmM2FOTmdHVm5ydVVBRWxteGE2ZFptaDciLCJtYWMiOiI0Nzg4ZTc3NzA3ZWI0ODZiYmQzMzZiMjBlMTE2MzQ1ZTEwNGNhNTc2YTlkZWY1MTg2ODg0ZWE0YzI2ZjJlZGYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imtxa2lYaXNJdWhwbVNRWGEvYnpweXc9PSIsInZhbHVlIjoieHRqSGltWXJDTUNqSmJVQ3NFeTN5UVVOdWxVazJNeXJicXBPeUR3d0pKNnk2ajhJc3k4WUJiUVVEVHJ1dk1VVFRLaEhqSE8xbVpxa2VvNHFBRXZEa0F5T2dJejN2Z0JuTnVPRXB6c1dqN1lkNHYxM1NSejhYOEdoMTFhTUd1OHdpamY1eFFQelR1L0tvYS9KM0d4OWt3ek5JRFhhTXRjQnZIclBBRVdVb0c4S1RyZ2pxdGQ4UUp3eXcxeXhScUgzSnBURUtxWnUrMUQ0czNRdmE3Q0ZDcjI3Z3Z3N1NubVFRUW11VzM5TzZZNjYyZVZnUmtCdWViMDhtZU9EcTRRRkt4MkhBemNKcGZXTENDeDhIQ2lETjBoY3paNkdXN2hpaGRxSmxhUlplWW1wN1hNTFlvM0VmWk9SMnZqbzh3QlhVdkdMYzFBckVGQlRYdXo5ZGY5aTFibElkUVBMZ201OHVTOU4rRnN1bmZ3WlpPMGg3cncwSFFtT0NLN1BVeGIwR1g5cE5iYy9uKzl2dklDQU4zaG9RNDRXMnpSKzJQM1Npd1NVWUdpSGpiL3hJbXZhWFdZUENGN1pUYXFaSzA4Skd2WnFIbldFRlA2V0FXNG1DY0VwK1I2RE80anVjUmhvbUN1dldTS2VyYjNxditSN1drTUZZbCtWRlFDdEV6eTYiLCJtYWMiOiI0ZGJiOTBhNGQyODkwNDEwOGFjNTNhNmQzZjkzMGZhOGY4YjFlMjg3ZWIzODkwNDhmNzUyNjRkNWM3YzBiMWZlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InY5Mk9aM1ZnUjFyTkNMSDgrNk5PS2c9PSIsInZhbHVlIjoiZzhpanFvV3lXM1FKZnAyRHdCNyt4c1llSzNvRmhsUmRFKzZhSmpPUThsTERUYWlZWlQ2WG12eTRscEIrUzlSbFg5ZmRlVWxuTzdZd1VHejYxUWhrUWJ4bThVTzl4Zlg4Yjl4VUZJTVEyd0ZVbW1xb3VsaHVIMGVLem9VZ2VDZExUZnhvTlNkWVlUeE1TQWd6UnZWVmZmWEhsYW0yYWtvRkhKU0ZWN0dyNDN6eDVwN002dGZ6ZHhjNTkxK1V1N3Z4Z09uU3hsTk9LRk5La21IUndoNlNFRUpuQ1ZqN1ByelFITGFneGovM3BLcXNZcXZEdDhVOWNCNDYwcldSeFV3Sks3MmFScWdxVktwOXN4UFJBY2ZTc1QrZVZva29IQXpiaklQTFJTUGxMMjhxZlExY056ei96UTk5TW1tdDNKNG9yMDdFeWZqTld2S3dDTEZ0UjNTVURYSGJaUGdpQm1WL0NYUlZVRUxlSllITE9kM05tQnp6VmhoMkVmeERtcmkrZ0NjSnBEUVpHRlJXam9tMWJZK2VwT2xwdTlacHZpemFqZ2wxQXg0Z3g0V2VncER6aTFVQytaTDNKUFdUdHM5V05EQzBuRGNRRjRxU2tsT1VPdnBaM3JZSTdFenlUdEk2Vkx3RTN2WEtmM2FOTmdHVm5ydVVBRWxteGE2ZFptaDciLCJtYWMiOiI0Nzg4ZTc3NzA3ZWI0ODZiYmQzMzZiMjBlMTE2MzQ1ZTEwNGNhNTc2YTlkZWY1MTg2ODg0ZWE0YzI2ZjJlZGYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imtxa2lYaXNJdWhwbVNRWGEvYnpweXc9PSIsInZhbHVlIjoieHRqSGltWXJDTUNqSmJVQ3NFeTN5UVVOdWxVazJNeXJicXBPeUR3d0pKNnk2ajhJc3k4WUJiUVVEVHJ1dk1VVFRLaEhqSE8xbVpxa2VvNHFBRXZEa0F5T2dJejN2Z0JuTnVPRXB6c1dqN1lkNHYxM1NSejhYOEdoMTFhTUd1OHdpamY1eFFQelR1L0tvYS9KM0d4OWt3ek5JRFhhTXRjQnZIclBBRVdVb0c4S1RyZ2pxdGQ4UUp3eXcxeXhScUgzSnBURUtxWnUrMUQ0czNRdmE3Q0ZDcjI3Z3Z3N1NubVFRUW11VzM5TzZZNjYyZVZnUmtCdWViMDhtZU9EcTRRRkt4MkhBemNKcGZXTENDeDhIQ2lETjBoY3paNkdXN2hpaGRxSmxhUlplWW1wN1hNTFlvM0VmWk9SMnZqbzh3QlhVdkdMYzFBckVGQlRYdXo5ZGY5aTFibElkUVBMZ201OHVTOU4rRnN1bmZ3WlpPMGg3cncwSFFtT0NLN1BVeGIwR1g5cE5iYy9uKzl2dklDQU4zaG9RNDRXMnpSKzJQM1Npd1NVWUdpSGpiL3hJbXZhWFdZUENGN1pUYXFaSzA4Skd2WnFIbldFRlA2V0FXNG1DY0VwK1I2RE80anVjUmhvbUN1dldTS2VyYjNxditSN1drTUZZbCtWRlFDdEV6eTYiLCJtYWMiOiI0ZGJiOTBhNGQyODkwNDEwOGFjNTNhNmQzZjkzMGZhOGY4YjFlMjg3ZWIzODkwNDhmNzUyNjRkNWM3YzBiMWZlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255401445\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}