{"__meta": {"id": "X81f221b39e7ff0598c8f799eda23d86c", "datetime": "2025-06-30 18:10:17", "utime": **********.733267, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.317152, "end": **********.733279, "duration": 0.41612696647644043, "duration_str": "416ms", "measures": [{"label": "Booting", "start": **********.317152, "relative_start": 0, "end": **********.692361, "relative_end": **********.692361, "duration": 0.37520909309387207, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.69237, "relative_start": 0.37521791458129883, "end": **********.733281, "relative_end": 1.9073486328125e-06, "duration": 0.040910959243774414, "duration_str": "40.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43873288, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00197, "accumulated_duration_str": "1.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.722592, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.279}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.727157, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 85.279, "width_percent": 14.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"American coffee-قهوة امركية\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 105\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1892 => array:8 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"1.50\"\n    \"tax\" => 0\n    \"subtotal\" => 1.5\n    \"id\" => \"1892\"\n    \"originalquantity\" => 200\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5FanEwYzRnUGQ1b1lhODhKaXNSOVE9PSIsInZhbHVlIjoiZVAyNUR1alN4Z25vUDdyTThjcGxKZWhZbk9jaW9xSmFMclEvSXE4WmNqak91akhqeS84cVBORTVIM0pVMlpmZEVSRTJIMkhlVENFT2h6VnBnOFJTZko4VUFMZmppd3JHMkZvbURiZ1ZOMDVjMlNIYXJCWmNCdmZBeWJmejd5R01xTkRxckU0YndFNVJuSjJ1Q0IvbTZsbTZTamY4NjkvL3RKOUovVU0xU05BZU9TZWZyZjJWV25xNFA5QjVidFcvbVkyTUlDdzNCWjlwbE1XUmZleXR5V2I2LzJ3bHdLMjVLTDR0M1hGMEdmcGJhSUxlUWlqUlk0Zy9uNStBdUh0L2JObzF6aTJ6OFBCR1RGaGJLdzE5cGJiRzBVRTZuZUV4VzlQMnM1ZEozS3dOZm1Fa3dXeGUrZFE2M0NibmlVRTA3b0VJZWNrbHlIYTNCWVY5TU91Ryt5dSsxMlh0OWJtSlBKZ0JEU0l2Z21KcVRkM3BZUHJkbXBsVk9FNXFxTW5qYUhKTUNTSytJM0JwdXZveTluTVRwTzltcFdBSDdIMG93aXRXeUQxR2FMSW1sd1JSUDdiR05pOFBvd2FwRm5iSjNuTWUwdXU5ZWtjVDlZR0VtbXVXM2UvTG1sQmF1UEg2U25seEwveE9RbUo3L3VnQmIvaEdaT0cwQkJNZlJLN3kiLCJtYWMiOiIzODZjN2VkZGU5ZGIzNGMxNDRiNzc2Y2RjZjcyYTZhMjE4YzZlZGNlYzcwYzJiN2JlODNiYzk2MmY1ZDFlYjQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InprbS9HQml1MFhpOW0wVVQ3b2k1RUE9PSIsInZhbHVlIjoiUENFVjdva2R0UURvS2RIa2YxM3YwL1pTbGNaMGVFSHZWZlczWk5OQWZ6YTMyYzJWT0x6NEhPa2dNQm5JMEZmRm0yYmVIdEYrQ0VuOWpsZkY2T3VyZk9od3ROVjdVQ0l6Q0J4QUFOcFUxR1UxOXZKcUkrdzNjYjN3VGdJMUEySUFaT1FzTW9xaEU5SWZzeEcySHU1NDBmczRjTzg0UXdrNGttMmhSQzRxNnRlUmdQUGVHbnlmQnFjL01Wemtnb2lGWmJlTlN3VEMrYWZLS1BrOUNHUTZjOU56WG05ME1NQlNUNXJXTUJSWUpOb3hqalZnWmVnbEh3N3dWWXJpNHlua1djRGlvWE1OVjlBdDIyYjdEeVRwdEZCVlAycEwrdGlqa3VLeCt3UWxUNlBZUHBDSjZvUUhnNDFhRFl3cVdqMEd4MHZjdFoyNjVjRHRXbGttSno2SS9rd0ErOG9IZEw1dld3OUlRVWdvb3lzMHFEeGFUSmRuVUV6UVM4Rk5kbjFFb2gwSkxGR25zTmx5RmxnRTZNMElYS0hxVlRzelF6emJvdG1iRTRlaTVEZnNxUFAxVml3QW95N21KY2daQzFpaWhQVWEvY3RGV2Y4MGtaWlRvVkduWWlxbWMycTA5RjU2cGs4TjA5QW42aGJpT3RDa0E5YVFQdFdjUTVLbG15UWoiLCJtYWMiOiI4NDM2NjM2MWUyY2Q2YzEwMWQ3YjUzZTI5N2M0Mzg0YTU4MWU5Yzg2ZDUzODAzMjRlMzVmOTEzYzZlY2Y5ODYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1228134411 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228134411\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-65345306 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlgzV3Jwa1I4U0k1L2M1N2lBbmZzaVE9PSIsInZhbHVlIjoiOElVZXlVejJYT3NhQjMwVnBNbHNNRDF2cUFYVkxaTVI4OG1YeGFQbllFTXRheitJT0RZSDAwK29wNDY1dVUrWFN2eWo2TVJna1kxU1YwVFFBWDFFQUZGSk10ZkRaaXR3T1kvU3RIalJYQ2dRWEQzbTBkNDRaQUQxOVVpRVZkQkQra2N5L2l1eld2UmdITlJDTGtiUWV3OHRhQyt0bTFWeTNQaXR1bDk2V0lJWU5CaHBmdG1EaFl1aVdick51S1dYSXJxRWZ4N0JYb3BEUXh3MG5XbzJxcFNvK1FiY1lFWjZrNnNMZkpzNXhOTlBIOUNUbWNYUnVndzE5WEZPRnN5cjJTb3A1ZGZNUFdLUDhkM01DUnlFVXBRais4UW5haFM1RUozYjdXRUlLL2VWVzVNV2ZOZVdsR3NTUFJmejBSSWsza1dLTzQ4THV4VUk5a2kwVXdzdWkxbWF2Yy9Dckl2VmxnczFuelljaWkxKzZtcTJneElWT0pxMmExbTd6Q09sNktkTkFZRmtUa1RoL2IwekRIS0dqTVY4dWpNZURZYXFzRjVubklxVHY4UExrSHJRRWVKczk4NHQyaU00NGhhTktTZkdiRUdaVk83U3k2RFZTbWs1dUFQandEYlJkZktaUHdrUE4weG45cUliRU9sbTFvRnFIOTBtS3dSSEpRNEciLCJtYWMiOiI3MTdhMTExZjQ1MDM4ZjBkMjhhZTE4ZjE1MjlmNGZmNTE3NmE0OWRkNWM5N2FiYjNmMDEzY2NlMmJlNTViYjFlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRwbHpiVW5CR0VvdWRrSDd2L3NqSEE9PSIsInZhbHVlIjoiOWM1Y3pmc1FWZHpKOWp5SVhjTHpvN1dQQ2tHb0NvcXRwcmNBbklOa0NsajloRzBXa2R3RTR4OUNLS294ckdjQmVOZVRFZUJpYXdDb2tFek16TWVUbTJNQUovRXh0dm16ampGT1dUS1JMYUt5VHkxemUxT3dGSjZ6VFhZdWQ2dUhiR3RsT1NFOWt6RWpCL3ZVeHBPNG5rMGVMZDhXbnQ1VmJsdlpYY20reEZjcjc3ZTFNQ3hxYWE5dkJWZENYdlQ4UWE3TEFoTDBOSTBMU3RjNDZVQld4ZHBPUXBJdUQ3WUQrbk8xSW5iOUEwczdBRUxVMnpXaXh4VVR3aWNnRTBxUTB4VGZpR2ZrMStsRXpIWEVCTUVYTURFSXg4Y3JEenJrbXcrYUg5RlMxa1BGcUliRUlGS3NJajBXTHNITlJzZjZoS0kwcmpIUGxsc2Q4TlptVUdiWU5OUnpwUUJtR2hxanNKMGt3aVhjNk9DcW1yR0tyTHhuZHRIMStpTUpxM1lKM25mTU1nSXU0TmlDU1JPbXFWdisvVFVJYTc4UHIvaG5welY0d3JvN3Zid2l5aUxRaUV6WmFDUi9SZ24ralc3RlJ5SisxaGZ4eldodGJRTG92NW5HM2NnWmdIY3A5b0hIMCtIZ05YdGJCbWR5T0l4Uk5sU1hKMHM4ZnJaMGRHUXYiLCJtYWMiOiJkOGFmYzlhMDBmNDYyOWNjZWE1NjFhOTg4ZGZhOTA2YTZlZTBiMTQxYjJmOGJhMmZiZGMzOGExYmIxMWY3OTI0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlgzV3Jwa1I4U0k1L2M1N2lBbmZzaVE9PSIsInZhbHVlIjoiOElVZXlVejJYT3NhQjMwVnBNbHNNRDF2cUFYVkxaTVI4OG1YeGFQbllFTXRheitJT0RZSDAwK29wNDY1dVUrWFN2eWo2TVJna1kxU1YwVFFBWDFFQUZGSk10ZkRaaXR3T1kvU3RIalJYQ2dRWEQzbTBkNDRaQUQxOVVpRVZkQkQra2N5L2l1eld2UmdITlJDTGtiUWV3OHRhQyt0bTFWeTNQaXR1bDk2V0lJWU5CaHBmdG1EaFl1aVdick51S1dYSXJxRWZ4N0JYb3BEUXh3MG5XbzJxcFNvK1FiY1lFWjZrNnNMZkpzNXhOTlBIOUNUbWNYUnVndzE5WEZPRnN5cjJTb3A1ZGZNUFdLUDhkM01DUnlFVXBRais4UW5haFM1RUozYjdXRUlLL2VWVzVNV2ZOZVdsR3NTUFJmejBSSWsza1dLTzQ4THV4VUk5a2kwVXdzdWkxbWF2Yy9Dckl2VmxnczFuelljaWkxKzZtcTJneElWT0pxMmExbTd6Q09sNktkTkFZRmtUa1RoL2IwekRIS0dqTVY4dWpNZURZYXFzRjVubklxVHY4UExrSHJRRWVKczk4NHQyaU00NGhhTktTZkdiRUdaVk83U3k2RFZTbWs1dUFQandEYlJkZktaUHdrUE4weG45cUliRU9sbTFvRnFIOTBtS3dSSEpRNEciLCJtYWMiOiI3MTdhMTExZjQ1MDM4ZjBkMjhhZTE4ZjE1MjlmNGZmNTE3NmE0OWRkNWM5N2FiYjNmMDEzY2NlMmJlNTViYjFlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRwbHpiVW5CR0VvdWRrSDd2L3NqSEE9PSIsInZhbHVlIjoiOWM1Y3pmc1FWZHpKOWp5SVhjTHpvN1dQQ2tHb0NvcXRwcmNBbklOa0NsajloRzBXa2R3RTR4OUNLS294ckdjQmVOZVRFZUJpYXdDb2tFek16TWVUbTJNQUovRXh0dm16ampGT1dUS1JMYUt5VHkxemUxT3dGSjZ6VFhZdWQ2dUhiR3RsT1NFOWt6RWpCL3ZVeHBPNG5rMGVMZDhXbnQ1VmJsdlpYY20reEZjcjc3ZTFNQ3hxYWE5dkJWZENYdlQ4UWE3TEFoTDBOSTBMU3RjNDZVQld4ZHBPUXBJdUQ3WUQrbk8xSW5iOUEwczdBRUxVMnpXaXh4VVR3aWNnRTBxUTB4VGZpR2ZrMStsRXpIWEVCTUVYTURFSXg4Y3JEenJrbXcrYUg5RlMxa1BGcUliRUlGS3NJajBXTHNITlJzZjZoS0kwcmpIUGxsc2Q4TlptVUdiWU5OUnpwUUJtR2hxanNKMGt3aVhjNk9DcW1yR0tyTHhuZHRIMStpTUpxM1lKM25mTU1nSXU0TmlDU1JPbXFWdisvVFVJYTc4UHIvaG5welY0d3JvN3Zid2l5aUxRaUV6WmFDUi9SZ24ralc3RlJ5SisxaGZ4eldodGJRTG92NW5HM2NnWmdIY3A5b0hIMCtIZ05YdGJCbWR5T0l4Uk5sU1hKMHM4ZnJaMGRHUXYiLCJtYWMiOiJkOGFmYzlhMDBmNDYyOWNjZWE1NjFhOTg4ZGZhOTA2YTZlZTBiMTQxYjJmOGJhMmZiZGMzOGExYmIxMWY3OTI0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65345306\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1411386772 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">American coffee-&#1602;&#1607;&#1608;&#1577; &#1575;&#1605;&#1585;&#1603;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>105</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1892</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1892</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>200</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411386772\", {\"maxDepth\":0})</script>\n"}}