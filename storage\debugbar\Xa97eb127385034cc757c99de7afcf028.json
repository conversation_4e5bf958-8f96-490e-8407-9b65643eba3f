{"__meta": {"id": "Xa97eb127385034cc757c99de7afcf028", "datetime": "2025-06-30 16:06:02", "utime": **********.124105, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.660321, "end": **********.124121, "duration": 0.*****************, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.660321, "relative_start": 0, "end": **********.039524, "relative_end": **********.039524, "duration": 0.*****************, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.039533, "relative_start": 0.****************, "end": **********.124122, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "84.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02773, "accumulated_duration_str": "27.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.073413, "duration": 0.02658, "duration_str": "26.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.853}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1086771, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.853, "width_percent": 1.803}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1166341, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 97.656, "width_percent": 2.344}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C**********091%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdYeFRXK1MxVnZPYWFCMkxBSGZHdmc9PSIsInZhbHVlIjoiOUlrT1BOWkxCenpzekRpcWMvNW1JRVZKVjFoditpYzloZmh5TTZWOFJmVGhERnRidjFwbGxVd2hMbHl5clpCOTdBUElReWFtNHVOY0tPcWhFWGZ0Y3d5VGxNL1ZTWWVpK1dKbWVzWTFFZUhRUGlyYkRlOGo4d1NIMG9WSWQ4cS9iZWpjODRocEpMUGVCekRFM2QwY1pCcDlLaUROLzlDdHV0Z0pKOURFUnZwRGh1Yzl4dzg5ZTR5aXZScE5MTEpEWWR4Z0FPOUdMdkpyZ2dXWFVwRDZMT01qQTZoWFJMOEd2Nnk2dHpwc01yTS9lYjhsTWJINmordjRVRkFiSENycG81V3Z0cm94RUx4NXlHWWxWR3JWSXlmS21aa1dXeU91Y1N0QVl0RHBSVzdEaTJWTmZxTGJ1NE81RG1XYzFjYXB3a0dGVy9BZDlBVWN3eVcvM1NJUkg0Mi90U3pRT2tqSlJxaERRQmtKRThUS01vTVkwc3I1UFhwb0VxbTJpN3FKS3IvVkdqT05hM2JTdUt0V3FpSWs1NFI4K2d4ajBhM1ZvQWZ6NjFTUTNSTjhOcUZMS2t3VDJpZzRvYW54bjdNYmJma3NPdVRIN1B2ZXRDRDdqQXo0K3pENEpTYUR4S0YxS1d0R3AyRmtMY0czRFRGRExwME9PeUJrZWZjOHFEZEYiLCJtYWMiOiJkY2Y2ZjY0ZGZmMzE4ZjM4YmY3MTI4MDVjY2UxZTE5YTEzNTU2NmI1MjFiOTg0NzY0M2M1ZDlmMWZmZmIwZWQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNpVE9IUk16NVhKaDNwUnpTRmJhdFE9PSIsInZhbHVlIjoiWG13c0RDbzh6bXFKbVZKenNhOUVvQVJJU2phanBDTUZwaWNUNzRDcE9pRTJpY25BSEtZM01BeXlQb1NnN0Yvd1l0MDBKQU1MVzd1MWFkNDhHMmxYNkx1NC9ZMytCR0hPbXJwTDRwTEkvalh4bzBlcXVzRDdhWkp1eUY1WkFFeGVlWFlFQkhCQWV4MWt4RklnV1h3SmRwZFFEeWI3dDlCUDJXZTJ5RmZVNFQwV3ZJeVNJZ0VGTVUyMUIzM2hkUk5JRFFPcENuQ2J4NmVqTVdmRTE5ZmVBWmY3VnkwNUFod0ROTC9hbDFzaEVnWGRTc1lNT2NEL2d6VTJzNnFpYURDcHY4RE1BMDBRMGVqeGRUWnp4SVFTQmdDelBMY0hqQS9HNWlQQ1dCUFJueERCZUJSL0dUK1grY0MwWkVCVU0xZGR1Rm1oM05MWXBPeTlBTFplV0RxOFZDN04rbUpwaFd5M3A3a1JlUE9ZY01mZ1htSlk3K2tzRDhQa1k2MVpCUjUvMkxHZWNORjRsNlZSNTI2WXhPM29XRGd6MEtuS3NFbTBqV2xFbkVJN09lZmJaZmlYNmx0WndVWTFaREh5Zk5YL2UwVnNJalhXVVNMSFJxTnlQcGZEYUlMUUxhWDYvcjVaVDhwMTFpN29QWjlNTXB0ZEhHbGdwOThoZHZ4YXcrK3oiLCJtYWMiOiJmNDYwN2JiZjI2ZWRmMjk2N2UzZDViOTdkZDQzZjlkM2IzY2I4Nzg2YWFiOGJlMTUyY2FkZjIzMTc5ZGE3ODQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1225930649 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225930649\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1644871197 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpOZ3B1dG9YOFRxTzRuMWNHRlFzWmc9PSIsInZhbHVlIjoiR0dOQllBTzYwWVFnL0k5UDFFLzZMK29IbEJwY1RRU1BCWWVnV2hZUmlFSDQwQkJXZkFsQTVRT0NxV0VjSnZHMy9zbldWSkJxc2ZmazVMVFM2WW1LUVZSK2huMGFnOFMrZjdaWnI2eHorSHpBQjNKZ2YyYkowZGtwT3BicVllTTlQNTZqa05Zd3JJeXk1M2RxVUYvVUN3cHRndk5zMTVnVjV4anliSEwyRmpXVTRQUWtIeFNhMGoyRDJpdmR6bkI4ZVg5c2tTTHNtYWJLRURBVWgvZ3hGSUpoWDU2R3RPVmk2S3JrcFFpSjc3a3RhakZuQ2FhYkhwVkhqZmVWNjYvbWpid0FLR2cyM2RRWkZFdFROS015QjlrVmdRU0RMUlJOTWd4Z2haTDIwNi9rZk1oM2o4WGhJUEJZd0VkR2ZxZHR5ZmlqUjFnY2dCOGNPYTk0dXRjNmZnVGRPMEpycTFZeGFyS1o3WTdmbFAvUmV4NkFEaVZJOXZRWXB4ZGgzSjF2QWlFa0JpR2lFMGdDci9CcGFINnlUOWQ4c1pMTHB2djZIM1I1eURwRVBDcTlDaXpCSWUwU3JGa21yZ1lUUDZaN2xkcFptdVc0MEtmTWEzZHlqanE4a3ZQSVR0QnlwMVJrcTM2SDVhRjBLZWUzN010SitsS0VFV0JKeWJTVERNM2ciLCJtYWMiOiIwODkwOTU3YTE1OTAzZjU1MDE4NGY2MDc4N2JjZGY1NWIyYjc3MmE5NzY2NGFjOTJmNjc5ZTk4MjQ5ZjliYjAxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRhMnVLbE1OQ1Y1QzBKTitYa1BZOVE9PSIsInZhbHVlIjoiMDRTb1hiSHEvMTNRKzRGS2RQeHNXaFRkd2g3b3YvZ09sV3dWdnNvRHpweldMZFRhOGVaTzJTT1VNcXgrSDFUMkpGUlI0YTFjSmIyd3gwUzczVzhsQS90ekZ1bWxVcjg2cFJUZXhDUXdsQ1lmVFBieXh2bmJ0aWVML3lZRGZWa2JTQS9HT1FyZTJqWi9iaTRDNHVFY3o0Z1lZMGxub0VDZXVmWG9qTU5oeVJSNnZPRkI5MTRNclk1bSs2aU9QcVY4Z0RmUkh1bGo3WVFYTW45MmFJaGRJWFlmZVQzaWYvRnNraEl4Y0ozYkZhUmNaZVV1MEh5elViVlhMWnhvaCtDSmNNTE53NzNZWWQyTzZFQWRRQ1J5SHBCQWdBT2x5cmlJQWFTaHgzVzFIWXFHZ0V6Y1djY29LaW9Lei82dTlCYkMrTVg0b1VKVm1WOVU1WFZPUWwxQU1JS2pibjQvdkdoNXdRRzZOSDJ0SlZLUmZsbmg1YmRGa09RWm4yV0FGOEJGTTRrRFNuc0ZrbkVsZnFYcHdTMnRtRENDMlBWVTZuZU9DZXBoUkhMWnd6WTlaNmFxb0NKWXNNZVI0U2pWVmluYi9EeGNZNU9sYkpWNUYrRTBqUzZVZVUxYkptTnJMZ0NVK0lYbXFLNFduRlBpOVphaG9mckFkTExnQzRWbWpGaEwiLCJtYWMiOiJjNjY4OTU5ZjEyMThlNWM4N2Q4MWM5ODFmZTI2MzRhOWViZmZjOTc3NWYyOTM1M2JlNmUyNDc4OGJjZTI0Yzc3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpOZ3B1dG9YOFRxTzRuMWNHRlFzWmc9PSIsInZhbHVlIjoiR0dOQllBTzYwWVFnL0k5UDFFLzZMK29IbEJwY1RRU1BCWWVnV2hZUmlFSDQwQkJXZkFsQTVRT0NxV0VjSnZHMy9zbldWSkJxc2ZmazVMVFM2WW1LUVZSK2huMGFnOFMrZjdaWnI2eHorSHpBQjNKZ2YyYkowZGtwT3BicVllTTlQNTZqa05Zd3JJeXk1M2RxVUYvVUN3cHRndk5zMTVnVjV4anliSEwyRmpXVTRQUWtIeFNhMGoyRDJpdmR6bkI4ZVg5c2tTTHNtYWJLRURBVWgvZ3hGSUpoWDU2R3RPVmk2S3JrcFFpSjc3a3RhakZuQ2FhYkhwVkhqZmVWNjYvbWpid0FLR2cyM2RRWkZFdFROS015QjlrVmdRU0RMUlJOTWd4Z2haTDIwNi9rZk1oM2o4WGhJUEJZd0VkR2ZxZHR5ZmlqUjFnY2dCOGNPYTk0dXRjNmZnVGRPMEpycTFZeGFyS1o3WTdmbFAvUmV4NkFEaVZJOXZRWXB4ZGgzSjF2QWlFa0JpR2lFMGdDci9CcGFINnlUOWQ4c1pMTHB2djZIM1I1eURwRVBDcTlDaXpCSWUwU3JGa21yZ1lUUDZaN2xkcFptdVc0MEtmTWEzZHlqanE4a3ZQSVR0QnlwMVJrcTM2SDVhRjBLZWUzN010SitsS0VFV0JKeWJTVERNM2ciLCJtYWMiOiIwODkwOTU3YTE1OTAzZjU1MDE4NGY2MDc4N2JjZGY1NWIyYjc3MmE5NzY2NGFjOTJmNjc5ZTk4MjQ5ZjliYjAxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRhMnVLbE1OQ1Y1QzBKTitYa1BZOVE9PSIsInZhbHVlIjoiMDRTb1hiSHEvMTNRKzRGS2RQeHNXaFRkd2g3b3YvZ09sV3dWdnNvRHpweldMZFRhOGVaTzJTT1VNcXgrSDFUMkpGUlI0YTFjSmIyd3gwUzczVzhsQS90ekZ1bWxVcjg2cFJUZXhDUXdsQ1lmVFBieXh2bmJ0aWVML3lZRGZWa2JTQS9HT1FyZTJqWi9iaTRDNHVFY3o0Z1lZMGxub0VDZXVmWG9qTU5oeVJSNnZPRkI5MTRNclk1bSs2aU9QcVY4Z0RmUkh1bGo3WVFYTW45MmFJaGRJWFlmZVQzaWYvRnNraEl4Y0ozYkZhUmNaZVV1MEh5elViVlhMWnhvaCtDSmNNTE53NzNZWWQyTzZFQWRRQ1J5SHBCQWdBT2x5cmlJQWFTaHgzVzFIWXFHZ0V6Y1djY29LaW9Lei82dTlCYkMrTVg0b1VKVm1WOVU1WFZPUWwxQU1JS2pibjQvdkdoNXdRRzZOSDJ0SlZLUmZsbmg1YmRGa09RWm4yV0FGOEJGTTRrRFNuc0ZrbkVsZnFYcHdTMnRtRENDMlBWVTZuZU9DZXBoUkhMWnd6WTlaNmFxb0NKWXNNZVI0U2pWVmluYi9EeGNZNU9sYkpWNUYrRTBqUzZVZVUxYkptTnJMZ0NVK0lYbXFLNFduRlBpOVphaG9mckFkTExnQzRWbWpGaEwiLCJtYWMiOiJjNjY4OTU5ZjEyMThlNWM4N2Q4MWM5ODFmZTI2MzRhOWViZmZjOTc3NWYyOTM1M2JlNmUyNDc4OGJjZTI0Yzc3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644871197\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1119644248 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119644248\", {\"maxDepth\":0})</script>\n"}}