{"__meta": {"id": "Xd3b46c9c5db02fc52087bd0e574408cc", "datetime": "2025-06-30 16:09:54", "utime": **********.572313, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.015577, "end": **********.572335, "duration": 0.556757926940918, "duration_str": "557ms", "measures": [{"label": "Booting", "start": **********.015577, "relative_start": 0, "end": **********.468692, "relative_end": **********.468692, "duration": 0.45311498641967773, "duration_str": "453ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.468701, "relative_start": 0.4531238079071045, "end": **********.572338, "relative_end": 3.0994415283203125e-06, "duration": 0.1036372184753418, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45556144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028409999999999998, "accumulated_duration_str": "28.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.509326, "duration": 0.02736, "duration_str": "27.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.304}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.551931, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.304, "width_percent": 1.83}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.559116, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.134, "width_percent": 1.866}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-371284794 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-371284794\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-813227452 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813227452\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-132282800 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132282800\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299642499%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlXakJqVk0yejZKWjBCTFNXRWhCaEE9PSIsInZhbHVlIjoiSjRMN0hqakI2RDBHOE90OVNtUDBEekV6bHBuZjl0V0tEd0pYT2VXeS9JTkhDeENrNzJxNmwvdUdwMkx5WlFDMEk2dDV6QjNTRDJvRDZObFRHMzRzUnYycmdFS0l0QnF1azhLUVNaY281MDEyeS81R3oxdHFxMVcwUHVZdENSLytRdEMwYmlMbWpYZWh0d1Y0aVZrRnA1em1XR2o1bng5ZUhEQ3lGTFduNWVLMjVHK0lJeHoya05VMkxXSjVCUDVjckgrb1pkVXA2Qkgxd2pOVm4ycE1CUzIxT3dzcmQzUkoxaDY2NEVCbUV5cTVLRUJUeUV3YnBoTE4xSHNVeDc4SVQwTUV2cTR5eG13MEhEWHlPRy94dFhoR2dsellKYmsvd2FiOTVQSDlFSGRsR3JJUDlNaE1idmc5WjB0dnlJU20zcmZyVDZxR3lOKzgwMkM2MjVoWldUQ2NnODd4Y1pjK2lpNWRSUUJ4bVpUelF5bXBmUVNNb3ZVL1ZjcXU2VHdIa3drQVRsQ2twbFVFZkttU2kwQ2FXTkZWR1BlRUIwVFJQWEFiSFRPQTEyVzkydERNZUJ2bHlDVGVVTUQwbU5kYzNPbEh1OThuQWwvdDhzSzBoQnBRTFNYbUZhOW15MHZxY1I5RFp0Zk95N1ZHZUxhVmVjZ0VtOVpKaDU4K29uOWsiLCJtYWMiOiIxYmVmMTg1OGFhMGE3Nzc2ODU2NGJiMDQ3MGVlNTY0N2UzYWJiMTdlOTZmYTJjMjA0ZDllOWE1NWE3MzVhOTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1pRitFZDJOYTJpZWdwYk5Tc2tveVE9PSIsInZhbHVlIjoia1V4aGRDbms0R05RR2JlRlpSQWpzVi9WTlFzQU5hOFFmS3V2bVBOMUREZXJBQUY3M1NOMHJaUGdHcHB6SW52cGwxV2FjTFppVkR3dHBYZVFua3FmWEZEQlNWanlQYzVqOEtaRUpBYXlkL3FPbWZveCtiWTRPdGNxM2lXMU5zUmtlTDROODRLWVpXQUw5WGZpQTYzNGZnbFlRVFA5UVdlQVAxNklRMlFsbFl2czJqL3NoUlJBd1dYelhhYVZpUlpYN0kwVnZCNHd3clNxOFQ0RjdaTTFRRldjZDBKdCtYMlRPNXM5WVVYNVJWWFI3WUFSMWhvVERDTXBSaklJa09xYzY0WTdmYmF6T3RMK3FRUW9mOExqOW1wTVJkOFZYc2VPc1U4Tmp1VWpnQ09CbHNnN08rWXZTaUJrVi9ET0Nid1g0K2tlYVpIdVQ2ZXdoRmFpaStha3VjSjVRRFBJVUF5MllnY3lCK3B3MVVxTVU1bzVjNCswMEhqVVFJTlB0SHhVNzlacHhTaXB3UUVqYXRuU0NkQ3c5MU13TFREaDBYWHNtL0lDQ3g4WXZXN095VkNpUkhDMkE2M1Bld3VPdmYvakk3WHFrQkhXdW43ZmhGOC9pdTNRNWU2SFF1U3Y3dGpNaWFycFVRWjlISVkyNGFjanVCMnVRbXp4dWxJY0o2cloiLCJtYWMiOiI0OGRhYjMwYjU0Yjk0NzIzNzVjYTljYTZkM2ViYTZjZGYxZjQ3YzhkMWQwYWMwNTI4N2IzMTVjZjU5YWY4OWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1583449731 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583449731\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1628642799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitwZkVldXhEakZ4dVh5NzhrT0dvNVE9PSIsInZhbHVlIjoiVGU0MkhMSXU3UlZUTURhbkRBNEVrY3hwS2hidU16QjVCTSszdE41WU1DRXdVVG5nV05VYTZISStldTl5b2hSS2FjV1d0Mk5ZdmZTd1FpNlZuWXUxWnJZVDFvWjZkSVRZT1cxYVB6WVNNTk45aTJpQjE2Y05FSHpRaVRsY0dLRy9VRXY1eXNGN1NZbjhUci9vNVVNdWR0cU5XWjA1enBaSXNMWHNkY0lHZEVKY1RtWG03T0dzdUJtaVJxNVcxQmgrRDhuVjRCaU1sMVFVdndhZkNWYUdQWHEzZ2JSTGd0Vk1ZdDhPMWxZaE1UT3d2QnRpL25MTis1NmZhN1FmMHZFUUtrVENOQVVFSGkwNXExbGZYdDN2RitjWWVQQTVuOVdGVURPZVF1Y3Y3cDA1MmNCSjFQaWdiVk1JZktEZ241eWlhdC9xTUNUcjh3TWt0d2JGMXJrNzJUaWtyQkZ4WUpMMkNWU3Fyd0Q2M053N1Y0SmY0S2dFMTBCOEdSazBzemEzU3VQTWdYUCtZOGZsbzhPNzhnN2pyaS9mNnpRVVBBeGRkUnVTeG9HU0tBOVBwZ1huaUlRRlJDblU3b2gyU0pweHJkWHhERklXekZSWkh3bVg1TjZPU3E4c0VkQ2lFbVVscVdKa0ZvSS9sQ3BDV2hKZnNRNENVcy9UcWJJcDZZY3UiLCJtYWMiOiI4MzUwODliZTQxZDRkOWM2OWVkNmY1NmJjNWJiNWJhYTdjMThiMTRjZTJlNjFkNGMyMDU0OTI4MThkNWY5MDg5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZWbkFCVzBZd1NybXFzMUhSZlVROFE9PSIsInZhbHVlIjoiOFlQc3RPOU9TZGxYRXRrcGZKYURjQVZNZzlobWdPZ29DSTlkUTNRMVNpK213cWxEY2NMN0N4cHN6OVRSRzNVNzdBVitnb1NUNzZPUXd3N1RRTHgvNmlONFlyQzN0eXdZMW5TMVVyMHFXbHl2QUNCOWdGVWhQMWlkRExsY1p6eTh6bWlnQnRCdUh0UDFLUmNTUUYzeExqNUVyT0FtMm9uZnIvbUg2VGxLT0wwcVB1NkJVREd0MjFLVU9QajgvRWZkNHhyZnZFRDFUNWhvNTFEYitZUmNFMVJobXYwR1lwYjlHT0V4SGQxNG4yeTdhWTFqRmk0WkpBZ1cwV2htYWh6U0RBT3I4bEhQZEV1NTNON2VwRTBjeEJQUDUrVXhwME03eGRQSWk5cDQ2enJTNCtVaExmclJlNEFJbnlFMUJtOEFaKzFVTTJNOFdUYlZWSStoeEZUbENSQkpUaXdRdEVqa24ybzgrck5MV25pMXF0TDNFM0JLYmU1dDU4OUdjWE54a2JSVXJ3ZFlQU1hCMDZvcVR3cTB3ZXZXVVBkTm5uS0o5b0dVMlJSTTNQYjdXaS9lanVMa0hGL2syY1dDbHI5MUhSalQ0R2FjaW1ZR0dRc1lZTk5CeVdBSkNVNGErWXhmY256QzVBcUh2NEdIUC9RTCtZTnRqTmR4bEZvam1BT3YiLCJtYWMiOiJkMmFhY2Y3NjAxNDNmNGQyM2VkNjk3MDIyZTIwMzBlOTUzNDMyMDg3N2I1ZjQ4ZjZjZmRmNGEyYjQxNjdiZDA4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitwZkVldXhEakZ4dVh5NzhrT0dvNVE9PSIsInZhbHVlIjoiVGU0MkhMSXU3UlZUTURhbkRBNEVrY3hwS2hidU16QjVCTSszdE41WU1DRXdVVG5nV05VYTZISStldTl5b2hSS2FjV1d0Mk5ZdmZTd1FpNlZuWXUxWnJZVDFvWjZkSVRZT1cxYVB6WVNNTk45aTJpQjE2Y05FSHpRaVRsY0dLRy9VRXY1eXNGN1NZbjhUci9vNVVNdWR0cU5XWjA1enBaSXNMWHNkY0lHZEVKY1RtWG03T0dzdUJtaVJxNVcxQmgrRDhuVjRCaU1sMVFVdndhZkNWYUdQWHEzZ2JSTGd0Vk1ZdDhPMWxZaE1UT3d2QnRpL25MTis1NmZhN1FmMHZFUUtrVENOQVVFSGkwNXExbGZYdDN2RitjWWVQQTVuOVdGVURPZVF1Y3Y3cDA1MmNCSjFQaWdiVk1JZktEZ241eWlhdC9xTUNUcjh3TWt0d2JGMXJrNzJUaWtyQkZ4WUpMMkNWU3Fyd0Q2M053N1Y0SmY0S2dFMTBCOEdSazBzemEzU3VQTWdYUCtZOGZsbzhPNzhnN2pyaS9mNnpRVVBBeGRkUnVTeG9HU0tBOVBwZ1huaUlRRlJDblU3b2gyU0pweHJkWHhERklXekZSWkh3bVg1TjZPU3E4c0VkQ2lFbVVscVdKa0ZvSS9sQ3BDV2hKZnNRNENVcy9UcWJJcDZZY3UiLCJtYWMiOiI4MzUwODliZTQxZDRkOWM2OWVkNmY1NmJjNWJiNWJhYTdjMThiMTRjZTJlNjFkNGMyMDU0OTI4MThkNWY5MDg5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZWbkFCVzBZd1NybXFzMUhSZlVROFE9PSIsInZhbHVlIjoiOFlQc3RPOU9TZGxYRXRrcGZKYURjQVZNZzlobWdPZ29DSTlkUTNRMVNpK213cWxEY2NMN0N4cHN6OVRSRzNVNzdBVitnb1NUNzZPUXd3N1RRTHgvNmlONFlyQzN0eXdZMW5TMVVyMHFXbHl2QUNCOWdGVWhQMWlkRExsY1p6eTh6bWlnQnRCdUh0UDFLUmNTUUYzeExqNUVyT0FtMm9uZnIvbUg2VGxLT0wwcVB1NkJVREd0MjFLVU9QajgvRWZkNHhyZnZFRDFUNWhvNTFEYitZUmNFMVJobXYwR1lwYjlHT0V4SGQxNG4yeTdhWTFqRmk0WkpBZ1cwV2htYWh6U0RBT3I4bEhQZEV1NTNON2VwRTBjeEJQUDUrVXhwME03eGRQSWk5cDQ2enJTNCtVaExmclJlNEFJbnlFMUJtOEFaKzFVTTJNOFdUYlZWSStoeEZUbENSQkpUaXdRdEVqa24ybzgrck5MV25pMXF0TDNFM0JLYmU1dDU4OUdjWE54a2JSVXJ3ZFlQU1hCMDZvcVR3cTB3ZXZXVVBkTm5uS0o5b0dVMlJSTTNQYjdXaS9lanVMa0hGL2syY1dDbHI5MUhSalQ0R2FjaW1ZR0dRc1lZTk5CeVdBSkNVNGErWXhmY256QzVBcUh2NEdIUC9RTCtZTnRqTmR4bEZvam1BT3YiLCJtYWMiOiJkMmFhY2Y3NjAxNDNmNGQyM2VkNjk3MDIyZTIwMzBlOTUzNDMyMDg3N2I1ZjQ4ZjZjZmRmNGEyYjQxNjdiZDA4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628642799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-81142754 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81142754\", {\"maxDepth\":0})</script>\n"}}