@php
    $totalPrice = $sales['total'];
@endphp
<div class="p-4">
    <h2>{{__('Select Payment Type')}}</h2>
    <form id="paymentForm" action="#" method="post" class="mt-3">
        @csrf
        <input type="hidden" name="payment_type" id="hiddenPaymentType">
        <input type="hidden" name="total_amount" id="totalPrice" value="{{ $totalPrice }}">

        <div class="mb-3">
            <label class="form-label">{{__('Payment Type')}}:</label> <span class="text-danger">*</span>
            <div class="btn-group" role="group">
                <input type="radio" class="btn-check" name="select_payment" id="cash" value="cash" required>
                <label class="btn btn-outline-primary" for="cash">💵 {{__('Cash')}}</label>

                <input type="radio" class="btn-check" name="select_payment" id="network" value="network">
                <label class="btn btn-outline-success" for="network">💳 {{__('Network')}}</label>

                <input type="radio" class="btn-check" name="select_payment" id="delivery_cash" value="delivery_cash">
                <label class="btn btn-outline-secondary" for="delivery_cash">🚚 {{__('Delivery Cash')}}</label>

                <input type="radio" class="btn-check" name="select_payment" id="split" value="split">
                <label class="btn btn-outline-warning" for="split">💰💳 {{__('Split Payment')}}</label>
            </div>
            <div id="payment-error" class="text-danger mt-2 d-none">{{__('Please select a payment type')}}</div>
        </div>

        <div id="cashFields" class="d-none">
            <label class="form-label">{{__('Amount Received')}}:</label>
            <input type="number" step="0.01" class="form-control" id="amountReceived"
                placeholder="Enter amount received">
            <label class="form-label mt-2">{{__('Change')}}:</label>
            <input type="text" class="form-control" id="change" readonly>
        </div>

        <div id="networkFields" class="d-none">
            <label class="form-label">{{__('Transaction Number')}}:</label>
            <input type="text" class="form-control" name="transaction_number" id="transactionNumber"
                placeholder="Enter transaction ID">
        </div>

        <div id="deliveryCashFields" class="d-none">
            <div class="alert alert-info">
                <i class="ti ti-info-circle"></i>
                {{__('This amount will be recorded as delivery cash in the financial records.')}}
            </div>
        </div>

        <div id="splitFields" class="d-none">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">{{__('Cash Amount')}}:</label>
                    <input type="number" step="0.01" class="form-control" id="splitCashAmount" name="split_cash_amount"
                        placeholder="Enter cash amount">
                </div>
                <div class="col-md-4">
                    <label class="form-label">{{__('Network Amount')}}:</label>
                    <input type="number" step="0.01" class="form-control" id="splitNetworkAmount" name="split_network_amount"
                        placeholder="Enter network amount">
                </div>
                <div class="col-md-4">
                    <label class="form-label">{{__('Delivery Cash Amount')}}:</label>
                    <input type="number" step="0.01" class="form-control" id="splitDeliveryCashAmount" name="split_delivery_cash_amount"
                        placeholder="Enter delivery cash amount">
                </div>
            </div>
            <div class="mt-2">
                <label class="form-label">{{__('Transaction Number')}}:</label>
                <input type="text" class="form-control" name="split_transaction_number" id="splitTransactionNumber"
                    placeholder="Enter transaction ID for network payment">
            </div>
            <div id="splitAmountError" class="text-danger mt-2 d-none">
                {{__('Total split amounts must equal the total price')}}
            </div>
        </div>

        <div class="mt-3">
            <h5>{{__('Total Amount')}}: <span class="text-primary">{{ Auth::user()->priceFormat($totalPrice) }}</span></h5>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-primary">{{__('Process Payment')}}</button>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('Cancel')}}</button>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        // إخفاء جميع الحقول في البداية
        $('#cashFields, #networkFields, #deliveryCashFields, #splitFields').addClass('d-none');
        
        // معالج تغيير نوع الدفع
        $('input[name="select_payment"]').change(function() {
            let selectedType = $(this).val();
            
            // إخفاء جميع الحقول أولاً
            $('#cashFields, #networkFields, #deliveryCashFields, #splitFields').addClass('d-none');
            $('#payment-error').addClass('d-none');
            
            // تعطيل جميع الحقول
            $('#amountReceived, #transactionNumber, #splitCashAmount, #splitNetworkAmount, #splitDeliveryCashAmount, #splitTransactionNumber').prop('disabled', true);
            
            // تحديث نوع الدفع المخفي
            $('#hiddenPaymentType').val(selectedType);
            
            // إظهار الحقول المناسبة بناءً على نوع الدفع المحدد
            if (selectedType === 'cash') {
                $('#cashFields').removeClass('d-none');
                $('#amountReceived').prop('disabled', false);
            } else if (selectedType === 'network') {
                $('#networkFields').removeClass('d-none');
                $('#transactionNumber').prop('disabled', false);
            } else if (selectedType === 'delivery_cash') {
                $('#deliveryCashFields').removeClass('d-none');
            } else if (selectedType === 'split') {
                $('#splitFields').removeClass('d-none');
                $('#splitCashAmount, #splitNetworkAmount, #splitDeliveryCashAmount, #splitTransactionNumber').prop('disabled', false);
                
                // تحديث مجموع المبالغ عند تغيير قيم الدفع المجزأ
                updateSplitTotal();
            }
        });

        // دالة لتحديث مجموع المبالغ في الدفع المجزأ
        function updateSplitTotal() {
            let totalPrice = parseFloat($('#totalPrice').val()) || 0;
            let cashAmount = parseFloat($('#splitCashAmount').val()) || 0;
            let networkAmount = parseFloat($('#splitNetworkAmount').val()) || 0;
            let deliveryCashAmount = parseFloat($('#splitDeliveryCashAmount').val()) || 0;
            let totalSplit = cashAmount + networkAmount + deliveryCashAmount;

            // التحقق من أن المجموع يساوي السعر الإجمالي
            if (Math.abs(totalSplit - totalPrice) < 0.01) {
                $('#splitAmountError').addClass('d-none');
            } else {
                $('#splitAmountError').removeClass('d-none');
            }
        }

        // تحديث المجموع عند تغيير قيم الدفع المجزأ
        $('#splitCashAmount, #splitNetworkAmount, #splitDeliveryCashAmount').on('input', function() {
            updateSplitTotal();
        });

        // حساب الباقي للدفع النقدي
        $('#amountReceived').on('input', function() {
            let totalPrice = parseFloat($('#totalPrice').val()) || 0;
            let amountReceived = parseFloat($(this).val()) || 0;
            let change = amountReceived - totalPrice;
            
            if (change >= 0) {
                $('#change').val(change.toFixed(2));
            } else {
                $('#change').val('0.00');
            }
        });

        // معالج إرسال النموذج
        $('#paymentForm').submit(function(e) {
            e.preventDefault();
            
            let selectedType = $('input[name="select_payment"]:checked').val();
            
            if (!selectedType) {
                $('#payment-error').removeClass('d-none');
                return false;
            }

            // التحقق من صحة بيانات الدفع المجزأ
            if (selectedType === 'split') {
                let totalPrice = parseFloat($('#totalPrice').val()) || 0;
                let cashAmount = parseFloat($('#splitCashAmount').val()) || 0;
                let networkAmount = parseFloat($('#splitNetworkAmount').val()) || 0;
                let deliveryCashAmount = parseFloat($('#splitDeliveryCashAmount').val()) || 0;
                let totalSplit = cashAmount + networkAmount + deliveryCashAmount;

                // التحقق من أن المجموع يساوي السعر الإجمالي
                if (Math.abs(totalSplit - totalPrice) > 0.01) {
                    show_toastr('Error', '{{__("Total split amounts must equal the total price")}}', 'error');
                    return false;
                }

                // التحقق من إدخال مبلغ واحد على الأقل
                if (cashAmount <= 0 && networkAmount <= 0 && deliveryCashAmount <= 0) {
                    show_toastr('Error', '{{__("Please enter at least one payment amount")}}', 'error');
                    return false;
                }
            }

            // إرسال البيانات
            processPayment(selectedType);
        });

        function processPayment(paymentType) {
            let formData = {
                payment_type: paymentType,
                _token: $('meta[name="csrf-token"]').attr('content')
            };

            // إضافة البيانات حسب نوع الدفع
            if (paymentType === 'network') {
                formData.transaction_number = $('#transactionNumber').val();
            } else if (paymentType === 'split') {
                formData.cash_amount = $('#splitCashAmount').val();
                formData.network_amount = $('#splitNetworkAmount').val();
                formData.delivery_cash_amount = $('#splitDeliveryCashAmount').val();
                formData.transaction_number = $('#splitTransactionNumber').val();
            }

            // إرسال الطلب
            $.ajax({
                url: "{{ route('pos_v2.data.store') }}",
                method: 'GET',
                data: formData,
                beforeSend: function() {
                    $('button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> {{__("Processing...")}}');
                },
                success: function(data) {
                    if (data.code == 200) {
                        show_toastr('success', data.success, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        show_toastr('error', data.error, 'error');
                    }
                },
                error: function(xhr) {
                    show_toastr('error', '{{__("An error occurred while processing payment")}}', 'error');
                },
                complete: function() {
                    $('button[type="submit"]').prop('disabled', false).html('{{__("Process Payment")}}');
                }
            });
        }
    });
</script>
