{"__meta": {"id": "Xa46e46f8f81bae295cfdc0f5eacc0809", "datetime": "2025-06-30 18:38:36", "utime": **********.302277, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751308715.831227, "end": **********.302292, "duration": 0.47106504440307617, "duration_str": "471ms", "measures": [{"label": "Booting", "start": 1751308715.831227, "relative_start": 0, "end": **********.21984, "relative_end": **********.21984, "duration": 0.3886129856109619, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219848, "relative_start": 0.38862085342407227, "end": **********.302293, "relative_end": 9.5367431640625e-07, "duration": 0.08244514465332031, "duration_str": "82.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45721560, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02263, "accumulated_duration_str": "22.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.252115, "duration": 0.02162, "duration_str": "21.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.537}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.284624, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.537, "width_percent": 2.828}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.291136, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.365, "width_percent": 1.635}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2096341724 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2096341724\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-346764429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-346764429\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1895547667 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895547667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1853816300 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308713578%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkorOVdOSTlpTmMwZmNUU1F2LzJkSWc9PSIsInZhbHVlIjoib1F4bnhvT01RU1R6TXhwQ1lxWjhxY25UTDkydHA5bVl3dllIMWdVNTAxejVYMU1KQXpYRGpuaEpzN3I3ak1VMTVzSFZPd1REa05KUVBFSGp3VlMzMitJaituRXZTaW9ITkdsQzdqclVMNFBSWmlsTVArckdQZG1BQWFiWmxaMXhub2NlN3JxSjE1dzByTVJzNEpUTnNwYjJLcmRmeThhUm82SG1QMzEyRkZIbDM3T0h0RFZTMGpYSERPYW0xOTlkeGV4R2hnWHBMK05JVmNreVBucW5rek5lS085dmRsSU9MUmV2MC93M3I1VFpJTnJpMnBRZWxocWpFNTVRSXF6cGpqQWMvaE1hNWN0bG90RFNqcy9rbFVDODFqWlF0SmxvelBLdm5JY0xaSGFKNy9oYzJFWEtjUkExK2F1b3czQ0VEN0Ixc1RLVVArN1doZU03eStOc0V0dTVpYVVlNHpQVVNGSFM2NWhkVHNpTUVqQkVlVVhsaHAxbkk3NGgrb1ZROHVqU05pclF2RlZQNnU4di9jb1JrOFFkeXNobmR4blRmZHBlQkFEa3JDTUdHc0VEYWhEZUR3bzFyV0NPTTMrbWQyZGE5VjdwTlZtVHF5VElUTXRDakRLUUVjWHNCdlFZM2lVZkk4SWZTRWtNdW5DQ091WjB6d0pSd1JvVkxOT2ciLCJtYWMiOiJhOTBiZDk3ZTU3Zjk0N2NkNDQ0ZDc2NDZjMWZmMGUzOTZmNzY2M2U3ODU4ZTEyNDBmNzJmNDFiMTJmNTlhMGY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhqNFpCaGdiTktkSncvRDQ1ckc4bWc9PSIsInZhbHVlIjoibjY2T2NYQUZKL3BWZmxmU21sQVltZ2pIU0ZrbW5FOUpFZFBuOUxhUjdJSHFiWlFFdlkraUdyUTBoWGdPRUpYaEVXMitLTldMVTZKRmM1Y0NlK2xTdXdUY0U2c1oyTXIwSnZvNUFUMVA0Z2lkcnBhckRKRWJ0czI5SWFmYldGK05pV0VlRm1vL1ZsN21VU3Y0Unlqd1d3blRIQUxrUEQ0KzhXTERldUFITFdpclYxeTI2aWluYU90MFFGVGF1QmNUQlRVWHUveW1SMkN1cnBHcStzbnhIWUJBRVR5UWVGSXlqVnlrRXNaT1dOQ2dWVFRBRVI5amdtVVNWRS9Wd1QrcEhrRjJJVmcreFhGVVdoY0l0N2hWRVNaZms0Si91cEtUTVhmcDV0OW8rSGYzVHpJZkNLWksyRllKdVV3bGYydEhld3pxR0FRcDlmQWtXZHYvTVMvRThtS3A0SEF0a1RVS1RUZEpINVQySHhndnBONU91eERUNG1GLzdFaDE4c0ZHU3VNU0VVRjlNSTE4UHB1U2N0UlhVR1hqYVFmUjdkTkdCYlZ3c3ZKY0FrN3I2RVdYdi96S05oY2NEU3BrazYvM3FPS0xUbklCWEdLTnA2cXR0Y1ltL2VEQlZuOEVqbW1PZUl6QW95QllTcHR1Q1YrS0JxWkY1emNOZnpZU1o2dVYiLCJtYWMiOiIzYWFmNzZkYTI4OTUxZjZmNmY0MDE0NDQzYThjYjFhODYzMWM5MWI0Yzg4MzcyYTc2NWQzZmUwN2QzYzNhYzQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853816300\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1048815998 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048815998\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-200443320 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:38:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFvNzRkeUl0V3ZtdEoyU2tuWW1uQXc9PSIsInZhbHVlIjoibHg4aFIzQ0VwcDRJY0NOT1Y3NmRuZlByeUs4UE9BZll4QWxxSFlyTlEyWjJqWlNsYUl4RGVTNHlJY1A0Q2tNR3ArV29EM0hhdFhGUTBMOVRLUDNRcUhGYUJmbk56WXZORDdPTkxSazIvQ2ZScHJYdjFzck45OVh3cWI4U1VJZVFGMU4xWmhJNU5wNjNlUzJoVGVLOUM4RlBMR3RzTkJWTjZWWEJzTTVMQ0lRRFBkQ2Y3a3J6bXovbGNqREF4SWdUbzI3cDRub0swTk9iUkxvRmEzSCt4QXFLNUlrbjBHYTNQVDV2Wm9uWGZqbCs1Zk01Y3ptV2kreGVGVzdIK1dDb3hqb0VPR3ZoaWtBTmpwZ3NEL2Z1MmlKdGsxcVhRQzZRUUlvUWhUYnc2NWJic0F6b2VCQmF1OTJRUGpOdWFvQ2FhTE43bUt3TDdxRXVMSUtReFppOHRPenR2bFZJdlpBZWZZY1pZNjl4alBSZUx1d3Ywak1KWXdyZEhVUTIzdDZ6SFRVT09sWTAyTVo4Y2xUZ1IxWkVkSnBaUndmNVpSRUl0SFBEY3ZBRHZTVmF5dzIzNW5XdWFxMlgxRDhHRTBydmlwdXRkbXBhNWREYXZVeW5pU2pYazMzSnBOUGg1M0huaGhXWTFnalVpVytnS2tIVzl3Q042b3h1L2FkdEVpMXIiLCJtYWMiOiI4MmNmOGVkMTRkNzBlNTgyZmZkZTI1ZjY2OTFmMjlmODkzNGRiYjAyMDhmZTAxNTI5NGIxNmExMDk3ZGVlZTM1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjQrM2JxTFBpdmtMcGtXSjVZejVMUVE9PSIsInZhbHVlIjoiTkEwZ3VSbGtwTWFGTi9tamZnNVpvckh4Qmd0NkJ2eGJPTHpCOHlPd1ZoODFMMVF1VHd2YVpCbUYvWkdGVCtNYTVSWUtkNUV6bnIyNHB0c215NXM4R3hBelpUZk9GdkhPRW1BYUl6K0hQYnJoQ1RITTBwS2hlQUF2YXI0RWNHSFhTVWtBT2Y4RDhHS1VlVGliNm5hYlA0QmRZYm9YSkJnYmhUNG9vNjJkcTFaQWZuQlJlakgxTTlOV2wrSkV1bDQvWVF6R0piVVZoMUprSXM1UStOUW81WjN5eHYrRzgwNHhrMm9qR2RIbHpoVUxnamtyamJZNk5LeUtuOHFTdWUySmtRTnFsclQ4TnFoK3hCK29xem5SOC9ycUg5UiswM1pQb1BqSjJJUjU3aTVEK0FsNXQvWTBQUFFSWW9zQWJkS0VuMlZBYzVpcXZwRmNHZWh2NmFxT1g1a2JSRnRyRGJqM2E2UXBLTzU5QnRSMG5rNXFOaTNUZnR3emRxbEw4bmp2dkh3UDh1bFBrMVRGZzVMMkdDdEQySzZ5SDlGS1ZSdURaZndVUGhUZDhxb00vZk8rWU1xU25OdXBVVXo3bUxCcG9IanpxVEUxeEowMEJ5bmdjcDhoOFpMQXJFQWRTTFg0QUJMczc1aUJiYkNrVWsyUXIyMy9XdUNtNzRWR3Izb2QiLCJtYWMiOiJhZGFiMTVkODA3ODAwMTBkZGE5MDFlZDZjNjY3ZWEwNzllYzU2ODA3MDI3ZjNmNTBjMWNlNjk0M2RlYjYxNDVmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFvNzRkeUl0V3ZtdEoyU2tuWW1uQXc9PSIsInZhbHVlIjoibHg4aFIzQ0VwcDRJY0NOT1Y3NmRuZlByeUs4UE9BZll4QWxxSFlyTlEyWjJqWlNsYUl4RGVTNHlJY1A0Q2tNR3ArV29EM0hhdFhGUTBMOVRLUDNRcUhGYUJmbk56WXZORDdPTkxSazIvQ2ZScHJYdjFzck45OVh3cWI4U1VJZVFGMU4xWmhJNU5wNjNlUzJoVGVLOUM4RlBMR3RzTkJWTjZWWEJzTTVMQ0lRRFBkQ2Y3a3J6bXovbGNqREF4SWdUbzI3cDRub0swTk9iUkxvRmEzSCt4QXFLNUlrbjBHYTNQVDV2Wm9uWGZqbCs1Zk01Y3ptV2kreGVGVzdIK1dDb3hqb0VPR3ZoaWtBTmpwZ3NEL2Z1MmlKdGsxcVhRQzZRUUlvUWhUYnc2NWJic0F6b2VCQmF1OTJRUGpOdWFvQ2FhTE43bUt3TDdxRXVMSUtReFppOHRPenR2bFZJdlpBZWZZY1pZNjl4alBSZUx1d3Ywak1KWXdyZEhVUTIzdDZ6SFRVT09sWTAyTVo4Y2xUZ1IxWkVkSnBaUndmNVpSRUl0SFBEY3ZBRHZTVmF5dzIzNW5XdWFxMlgxRDhHRTBydmlwdXRkbXBhNWREYXZVeW5pU2pYazMzSnBOUGg1M0huaGhXWTFnalVpVytnS2tIVzl3Q042b3h1L2FkdEVpMXIiLCJtYWMiOiI4MmNmOGVkMTRkNzBlNTgyZmZkZTI1ZjY2OTFmMjlmODkzNGRiYjAyMDhmZTAxNTI5NGIxNmExMDk3ZGVlZTM1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjQrM2JxTFBpdmtMcGtXSjVZejVMUVE9PSIsInZhbHVlIjoiTkEwZ3VSbGtwTWFGTi9tamZnNVpvckh4Qmd0NkJ2eGJPTHpCOHlPd1ZoODFMMVF1VHd2YVpCbUYvWkdGVCtNYTVSWUtkNUV6bnIyNHB0c215NXM4R3hBelpUZk9GdkhPRW1BYUl6K0hQYnJoQ1RITTBwS2hlQUF2YXI0RWNHSFhTVWtBT2Y4RDhHS1VlVGliNm5hYlA0QmRZYm9YSkJnYmhUNG9vNjJkcTFaQWZuQlJlakgxTTlOV2wrSkV1bDQvWVF6R0piVVZoMUprSXM1UStOUW81WjN5eHYrRzgwNHhrMm9qR2RIbHpoVUxnamtyamJZNk5LeUtuOHFTdWUySmtRTnFsclQ4TnFoK3hCK29xem5SOC9ycUg5UiswM1pQb1BqSjJJUjU3aTVEK0FsNXQvWTBQUFFSWW9zQWJkS0VuMlZBYzVpcXZwRmNHZWh2NmFxT1g1a2JSRnRyRGJqM2E2UXBLTzU5QnRSMG5rNXFOaTNUZnR3emRxbEw4bmp2dkh3UDh1bFBrMVRGZzVMMkdDdEQySzZ5SDlGS1ZSdURaZndVUGhUZDhxb00vZk8rWU1xU25OdXBVVXo3bUxCcG9IanpxVEUxeEowMEJ5bmdjcDhoOFpMQXJFQWRTTFg0QUJMczc1aUJiYkNrVWsyUXIyMy9XdUNtNzRWR3Izb2QiLCJtYWMiOiJhZGFiMTVkODA3ODAwMTBkZGE5MDFlZDZjNjY3ZWEwNzllYzU2ODA3MDI3ZjNmNTBjMWNlNjk0M2RlYjYxNDVmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200443320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-380097870 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380097870\", {\"maxDepth\":0})</script>\n"}}