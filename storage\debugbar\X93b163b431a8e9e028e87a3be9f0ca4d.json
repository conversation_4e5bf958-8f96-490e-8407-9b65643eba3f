{"__meta": {"id": "X93b163b431a8e9e028e87a3be9f0ca4d", "datetime": "2025-06-30 16:03:21", "utime": 1751299401.676014, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299399.486287, "end": 1751299401.676033, "duration": 2.1897459030151367, "duration_str": "2.19s", "measures": [{"label": "Booting", "start": 1751299399.486287, "relative_start": 0, "end": 1751299399.943566, "relative_end": 1751299399.943566, "duration": 0.4572789669036865, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751299399.943576, "relative_start": 0.4572889804840088, "end": 1751299401.676035, "relative_end": 1.9073486328125e-06, "duration": 1.7324588298797607, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50138944, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.635081, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1751299401.005383, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1751299401.550541, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1751299401.610505, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.14251000000000003, "accumulated_duration_str": "143ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1027021, "duration": 0.038729999999999994, "duration_str": "38.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.177}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.144732, "duration": 0.00638, "duration_str": "6.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 27.177, "width_percent": 4.477}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1788552, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 31.654, "width_percent": 2.652}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.94165, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 34.306, "width_percent": 0.386}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751299401.291106, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 34.692, "width_percent": 0.344}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751299401.431702, "duration": 0.08926, "duration_str": "89.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "kdmkjkqknb", "start_percent": 35.036, "width_percent": 62.634}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751299401.52348, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "kdmkjkqknb", "start_percent": 97.67, "width_percent": 0.274}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751299401.5252151, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.944, "width_percent": 0.196}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1751299401.606325, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.14, "width_percent": 1.86}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7YI2hf8hGvabVcNjupAqhAyfdrMB0zAHlgvM2pvv", "url": "array:1 [\n  \"intended\" => \"http://localhost/receipt-order\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1063150609 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1063150609\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-148520752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-148520752\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-477718561 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-477718561\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299389604%7C1%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjAzaVg4S2JFWWNETmxLSytwSWRZeGc9PSIsInZhbHVlIjoiK0x6TWZvelZyRk16K1pGVCt5dGRYQ0dKZzZrZWpjd2lDSDZLY1RiRnlrZ3dSTllpSE5kZURnT2NncUhhWkZpVmhTNzZtQklNZlAzdUlqV1hUdzQ5YWJsdlY5RDd2dXZmM0prUlp1RzN4Ny9ybUN0eTVFMFFOK2NHMEdYZk5RUHFxNXU4c1FSTHRXelpQSlM2TEN1ZFpwdWVsZkVLQ3pmK2tFaFZqdHM3NXpFYTNpczJpZTEzOFpBNHgrUW1reVpPSzJEM0h5ZmY1WmRYNHlxTW1XV3NkTWF5K05PZUpNSnB4dElKVVJDdWNaa25GQTF4Z2JFQUJZWFRzQ3lxU0F1MlBiK2ZrZ2xhUTRUa0NDVloxR2prREFDZGhnekZjZU9aWEx1S0tUd3J5clhXRFczeFV5WHcyN0JkU1JMWS9idFFuYmR4SzMyNWtDRmp0RUVGT2xIZHZNZWNZZEc2YVZuakg5YjRscCtYdllnNTM4aDlRWUN6SDhROTBhN0tudEJFY1M2SkFCeXNmTkttWnhWMUxzSHF6R01wUkp2VG12SFBHaWIzNGNPUUNld3RMK2Q5V1B4Ly9LemRLYWRIRm9QeE90M0tmM2VQdFg0aVlPOFNhUjRUL1dsTGxvdmJNc3p5UENXdW12TGxXUi9YRXcwb0NudDJydnFCY2lpaWFnWVQiLCJtYWMiOiJjYWZiM2U0MmQ3NjFmOTRjYjVlZjAxYTBhNzc0Y2JiYjU3MDY4NDdiNWVlNmJhYjlmYzA2ZWY2MjI0YzMyN2UyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlMwYWtaVGVScUw3RE9OLzJOS1JYVFE9PSIsInZhbHVlIjoiY1RYQm11VC9yNjVFbkt5dGh2WUtMaERWdnl3SFFKbDQ3aFcwNmRRRlhnTTlrN3lZdXc0b0kzTTJDdzdCSVZWaHlqQTdVTUNOR2FxcG0vT1FVWktyNzRsczZKV0FEQWNRcWYwMm8wL3dJN2lSQnA1bGxHSVFlSXNwSWtaZVJXT1Q4Z0poNHFIaTFUOVNDVG9MZml4V25HYmV5Z3NKd2w5aTA5RmdpLzQ2UVlxNlJINVAyZGZnK0NGOElOOVd4SVIrNXNqUFV6L0JuOEQyMW54Vy9PZFhlM3lCRTlueklYd3NhQ2VKdW1KbU9SWnV0YlJoUGZJMnJZSHp6UUZkL3NsK3dOcncvd2FVRDFOdFFJQnV1aWYwZi9wSERrdmRscVQ1WEFkVlh0WVJsMHZwbUs2UEhDWjEvNXBLWDNaTHNuRUtBQ0dmSW00SnhsN1lPd1RLckVOQjBRbzkydHB3Tkt0cms2Q1J2ZDdWMDc4ZnNYWHYyWDNCaFVlYldkejlHbnZLODRhVnFJVDRTRXora0hTck0wMlJLd0tFcFI4dEpLeEZQRkNpc3llS3ByLzU2L0NyVisrY0xlQjNCb3Z6cGRpSzhncmY5TW1nQ3JXcVRoUWR2OXc2OTNSbW1GMTFITkI4ZHNIVHhuMjl2ZDNXTC9DUDRLaWdyeWVzdGVBZ1lrbDgiLCJtYWMiOiJjMmRkM2E5ODUzODI4OWViNjg1NjNkMzhkZDg0YTZmMjMxMzhjMGExNWIxZTRkOWY5YTRjNGIxZTA3ZGFlOTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-928515808 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7YI2hf8hGvabVcNjupAqhAyfdrMB0zAHlgvM2pvv</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h9WVO03qniSiJmg5fuu4tum1lr0j0c1k18JkoyAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928515808\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-992624828 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:03:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBoeExWTzdpbUZrRTZNZmNGSjA3MWc9PSIsInZhbHVlIjoiSCtsb3RMR1RtT1U1NlkyV3RES0x4dHVQbzFucEhkTXBUN3YxUE9HeXc2dGZXaGRCSEd5cXR4aTZtQS9BMzdrQm1WZEhGVHpBOEZTbTQvRUlKSENzN1VWUHhka0xKdk1BeEtWQ1ZEWmJHQ1dLNzVFK0pRY3puWGtHYVpUUzJoZlVNaWhia29GQVJYcG1xUUJCQmwxazB1YlVoVUVDRkNzemRvcTU0ZUNpRzIwMkFlMEFnZjF6OVRTazJnTTY0WDJZUjFHMUlwQVFyWnVQUnM2aXBZTVZ0VnJCL24wbXJnQzhUNUQvMllNS0xwZUtsRExHcjNPVGZsSkV1QjB4bGc1OG5UQlVkZnNBTlpTM1VNV2VjcWhkOU96T2pUODc4VjJKeVR3OUJvdzRObXh0UkhOTXNSRWFEMWkwZG1oUWhrMVROZTJrL2REdTVoR2psei95U3E4OVlDY2RzblBqNjh6Z29ZWXhodjNhWVNsUVRDREw0U0tSY3hvN3VCamR1UmdXTEhzYXJIRlR6NjlmOVNCb3dIQWZmZlRpQzU5b0VXK2lzZHV2YUE0VTgwSVd0YlpiL1NiZllCV0YyU083ZDhHQ3E5eTBYQm52RXIxR1RtRzZkRDUvY21SVVo1WFVBUXZjUmZJVlNxWXljZDl0Q3B6Y2U2YURBZCswYXlLdnh5ZG8iLCJtYWMiOiI3MjFmYWU2NDZkNGExZDhhZWQyNGE5NGU2M2EzMDA0YzY0MzY1ZWMyYTc3Mjg1NjBjN2M1YjNhYjczNjY3N2ZjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImV0V1ZLOHJCN25IMmdEWnJheVB5Mmc9PSIsInZhbHVlIjoiMEMrc0dKU2ZKSG0xNGRUdUFQQllhRXpLcitpaTRJaFExVFVBUlNWQldHYWEzNDdUTTFuVUFNU1hmNlRTdDJSM1E0OS9INkRKTzc5eVBwYXZqeTZvdXJCMDZLZmRGMnBScXgxU1Z3d2Q0YjQwNCsxc0dZZFdOYUN2UExjSUxQTEVUVFlkbTdncGI0dVFTVWhmSk1vRWFOeEZBT1JtT0RKczAyOXlBbi8zZUk0b1JPaWs5TEt6Vkg3bFlUdkwyS2FsMTJkNUZRbm1wTkt4amJPNDRtNEYvWGNTWml5OVhyZE5UWEh4QzZhVURYcDJ2RkdEamtHc1ZYZjdFS2E3UTczUHFONTA1MFNOQklnbUdrd3FyNU16UXc0bFNQNW9KVlZCdzNOV1VraTMrelFnMmZxS3dVWVpVZFkwYStnek84ak9hcDdMOTZjelJHRWRFRnVZUnJ1ZVBUZll6M1JZMUI4SnVhNUZXaDUwTFhZRG0rNlJFZUZiODE3a0t1U1ZURWFGRXZqNVZLVFRaVjFpRTBDWUo5Q0d6SVFtZG9ZdkFySzQ5KzJEblNwenAyL3k2dDNsbVQxamw2QXltL3NrYlNnMHVEVGpQYW9od2E1SWRRd2p0bXdiWWJBSkp4aktYb05JbnQyV1hlOTZlcGxHN3pOY1dtWHBuNHRQbFVqSzBHcHciLCJtYWMiOiI0MTViOWRmMzhhODBkNTlkNTU3NjQxM2U0NWFmMDM4MmU2YzY2OTk5NGJkZTlhNWI4YzMzMjZiZDM1NWYzYjdmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:03:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBoeExWTzdpbUZrRTZNZmNGSjA3MWc9PSIsInZhbHVlIjoiSCtsb3RMR1RtT1U1NlkyV3RES0x4dHVQbzFucEhkTXBUN3YxUE9HeXc2dGZXaGRCSEd5cXR4aTZtQS9BMzdrQm1WZEhGVHpBOEZTbTQvRUlKSENzN1VWUHhka0xKdk1BeEtWQ1ZEWmJHQ1dLNzVFK0pRY3puWGtHYVpUUzJoZlVNaWhia29GQVJYcG1xUUJCQmwxazB1YlVoVUVDRkNzemRvcTU0ZUNpRzIwMkFlMEFnZjF6OVRTazJnTTY0WDJZUjFHMUlwQVFyWnVQUnM2aXBZTVZ0VnJCL24wbXJnQzhUNUQvMllNS0xwZUtsRExHcjNPVGZsSkV1QjB4bGc1OG5UQlVkZnNBTlpTM1VNV2VjcWhkOU96T2pUODc4VjJKeVR3OUJvdzRObXh0UkhOTXNSRWFEMWkwZG1oUWhrMVROZTJrL2REdTVoR2psei95U3E4OVlDY2RzblBqNjh6Z29ZWXhodjNhWVNsUVRDREw0U0tSY3hvN3VCamR1UmdXTEhzYXJIRlR6NjlmOVNCb3dIQWZmZlRpQzU5b0VXK2lzZHV2YUE0VTgwSVd0YlpiL1NiZllCV0YyU083ZDhHQ3E5eTBYQm52RXIxR1RtRzZkRDUvY21SVVo1WFVBUXZjUmZJVlNxWXljZDl0Q3B6Y2U2YURBZCswYXlLdnh5ZG8iLCJtYWMiOiI3MjFmYWU2NDZkNGExZDhhZWQyNGE5NGU2M2EzMDA0YzY0MzY1ZWMyYTc3Mjg1NjBjN2M1YjNhYjczNjY3N2ZjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImV0V1ZLOHJCN25IMmdEWnJheVB5Mmc9PSIsInZhbHVlIjoiMEMrc0dKU2ZKSG0xNGRUdUFQQllhRXpLcitpaTRJaFExVFVBUlNWQldHYWEzNDdUTTFuVUFNU1hmNlRTdDJSM1E0OS9INkRKTzc5eVBwYXZqeTZvdXJCMDZLZmRGMnBScXgxU1Z3d2Q0YjQwNCsxc0dZZFdOYUN2UExjSUxQTEVUVFlkbTdncGI0dVFTVWhmSk1vRWFOeEZBT1JtT0RKczAyOXlBbi8zZUk0b1JPaWs5TEt6Vkg3bFlUdkwyS2FsMTJkNUZRbm1wTkt4amJPNDRtNEYvWGNTWml5OVhyZE5UWEh4QzZhVURYcDJ2RkdEamtHc1ZYZjdFS2E3UTczUHFONTA1MFNOQklnbUdrd3FyNU16UXc0bFNQNW9KVlZCdzNOV1VraTMrelFnMmZxS3dVWVpVZFkwYStnek84ak9hcDdMOTZjelJHRWRFRnVZUnJ1ZVBUZll6M1JZMUI4SnVhNUZXaDUwTFhZRG0rNlJFZUZiODE3a0t1U1ZURWFGRXZqNVZLVFRaVjFpRTBDWUo5Q0d6SVFtZG9ZdkFySzQ5KzJEblNwenAyL3k2dDNsbVQxamw2QXltL3NrYlNnMHVEVGpQYW9od2E1SWRRd2p0bXdiWWJBSkp4aktYb05JbnQyV1hlOTZlcGxHN3pOY1dtWHBuNHRQbFVqSzBHcHciLCJtYWMiOiI0MTViOWRmMzhhODBkNTlkNTU3NjQxM2U0NWFmMDM4MmU2YzY2OTk5NGJkZTlhNWI4YzMzMjZiZDM1NWYzYjdmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:03:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992624828\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1196090072 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7YI2hf8hGvabVcNjupAqhAyfdrMB0zAHlgvM2pvv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196090072\", {\"maxDepth\":0})</script>\n"}}