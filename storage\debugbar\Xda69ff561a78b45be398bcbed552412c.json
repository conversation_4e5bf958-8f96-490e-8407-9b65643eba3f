{"__meta": {"id": "Xda69ff561a78b45be398bcbed552412c", "datetime": "2025-06-30 18:38:36", "utime": **********.302243, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751308715.831227, "end": **********.302257, "duration": 0.47102999687194824, "duration_str": "471ms", "measures": [{"label": "Booting", "start": 1751308715.831227, "relative_start": 0, "end": **********.234018, "relative_end": **********.234018, "duration": 0.40279102325439453, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.234027, "relative_start": 0.4027998447418213, "end": **********.302259, "relative_end": 1.9073486328125e-06, "duration": 0.06823205947875977, "duration_str": "68.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709008, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01391, "accumulated_duration_str": "13.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.260892, "duration": 0.01282, "duration_str": "12.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.284594, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.164, "width_percent": 4.817}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.291075, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.981, "width_percent": 3.019}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1516405672 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1516405672\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-799273811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-799273811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-433951795 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433951795\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-568060240 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308713578%7C3%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkorOVdOSTlpTmMwZmNUU1F2LzJkSWc9PSIsInZhbHVlIjoib1F4bnhvT01RU1R6TXhwQ1lxWjhxY25UTDkydHA5bVl3dllIMWdVNTAxejVYMU1KQXpYRGpuaEpzN3I3ak1VMTVzSFZPd1REa05KUVBFSGp3VlMzMitJaituRXZTaW9ITkdsQzdqclVMNFBSWmlsTVArckdQZG1BQWFiWmxaMXhub2NlN3JxSjE1dzByTVJzNEpUTnNwYjJLcmRmeThhUm82SG1QMzEyRkZIbDM3T0h0RFZTMGpYSERPYW0xOTlkeGV4R2hnWHBMK05JVmNreVBucW5rek5lS085dmRsSU9MUmV2MC93M3I1VFpJTnJpMnBRZWxocWpFNTVRSXF6cGpqQWMvaE1hNWN0bG90RFNqcy9rbFVDODFqWlF0SmxvelBLdm5JY0xaSGFKNy9oYzJFWEtjUkExK2F1b3czQ0VEN0Ixc1RLVVArN1doZU03eStOc0V0dTVpYVVlNHpQVVNGSFM2NWhkVHNpTUVqQkVlVVhsaHAxbkk3NGgrb1ZROHVqU05pclF2RlZQNnU4di9jb1JrOFFkeXNobmR4blRmZHBlQkFEa3JDTUdHc0VEYWhEZUR3bzFyV0NPTTMrbWQyZGE5VjdwTlZtVHF5VElUTXRDakRLUUVjWHNCdlFZM2lVZkk4SWZTRWtNdW5DQ091WjB6d0pSd1JvVkxOT2ciLCJtYWMiOiJhOTBiZDk3ZTU3Zjk0N2NkNDQ0ZDc2NDZjMWZmMGUzOTZmNzY2M2U3ODU4ZTEyNDBmNzJmNDFiMTJmNTlhMGY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhqNFpCaGdiTktkSncvRDQ1ckc4bWc9PSIsInZhbHVlIjoibjY2T2NYQUZKL3BWZmxmU21sQVltZ2pIU0ZrbW5FOUpFZFBuOUxhUjdJSHFiWlFFdlkraUdyUTBoWGdPRUpYaEVXMitLTldMVTZKRmM1Y0NlK2xTdXdUY0U2c1oyTXIwSnZvNUFUMVA0Z2lkcnBhckRKRWJ0czI5SWFmYldGK05pV0VlRm1vL1ZsN21VU3Y0Unlqd1d3blRIQUxrUEQ0KzhXTERldUFITFdpclYxeTI2aWluYU90MFFGVGF1QmNUQlRVWHUveW1SMkN1cnBHcStzbnhIWUJBRVR5UWVGSXlqVnlrRXNaT1dOQ2dWVFRBRVI5amdtVVNWRS9Wd1QrcEhrRjJJVmcreFhGVVdoY0l0N2hWRVNaZms0Si91cEtUTVhmcDV0OW8rSGYzVHpJZkNLWksyRllKdVV3bGYydEhld3pxR0FRcDlmQWtXZHYvTVMvRThtS3A0SEF0a1RVS1RUZEpINVQySHhndnBONU91eERUNG1GLzdFaDE4c0ZHU3VNU0VVRjlNSTE4UHB1U2N0UlhVR1hqYVFmUjdkTkdCYlZ3c3ZKY0FrN3I2RVdYdi96S05oY2NEU3BrazYvM3FPS0xUbklCWEdLTnA2cXR0Y1ltL2VEQlZuOEVqbW1PZUl6QW95QllTcHR1Q1YrS0JxWkY1emNOZnpZU1o2dVYiLCJtYWMiOiIzYWFmNzZkYTI4OTUxZjZmNmY0MDE0NDQzYThjYjFhODYzMWM5MWI0Yzg4MzcyYTc2NWQzZmUwN2QzYzNhYzQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568060240\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-224189829 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224189829\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1792968699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:38:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZsTDVkdktKTU5KSmZIQ0R3U0o0OUE9PSIsInZhbHVlIjoiZmdOTHNNUU1SK2M4WUpJV2ZJN0VFbWdrbnNYN1lsK1g0MTFhNENaVWdrQkE1YmJETXp6OWtUT1RVUVhVdWthTTFLSitjZWovbWoxSnRjUXpLRk4xajluVzVmMlQ1RnlhWGZDWS83L203Q0lJeHdua1gwaUM2cFpoU2c2SDVJNHk4cEQ4SlN1OGp2KzJRTE14QjZ3dVgyTmpmbHhPNjhJSkNHK1N3a0lsMHVJZUxXMHlBaVBnQUUwa3lwTm9jNFMwbnFTV085djNhdVc2aVdZZUMvZGFFK1oxKzdxaTBOc3V0RzIyV1ZQTXhsSVlGWWpVMTVvZnVtaHc3MG0vS1NPbEkzYkU0anUxY25WTWNLU0hpcXRjd0hOb3d1RCtobGlaUDNHVlRPUUU4Tm9zQWRNamRBOTcwYS81SXg0Yll0Yno4R3Erb2QwTWdFaEhPaVFhanA4U0FpQXY0dXpBVzVFbWZMVTlIQm9aa3ZGblh2QzBOSk1MYUJUWTBhVUM4eHBJSXNaQ2k2bmp0YjB3QUJHRDMyR05MWHM1UkpSZ0NWKzZVb3ZTalU4OHNpOVFDNllHci9tY1JXNVJBWkRyOUlJWXVVWmZlbXlFTnM2WlFBOU90ckxrb2dOYmxENzk5cTRlczdxM0NTWjBURUR2dHE5OXg4RHFjOEFGcWpYeTlSZXAiLCJtYWMiOiI2ZWI2Mzc2MmFiYzMwMGI3N2M5MjAxYmVmYjVkMzAzNGY1ODA4NTYxYTc1OGQ0MzVhNmU2ODkyYTBhNWM3ODU3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1vaFZJS3g5d0VZZHUzK1NJMGhxdHc9PSIsInZhbHVlIjoiRTc4WTY3RlB2TDRwSi8xMGxUWCszRUFKTGtnOEU2NzNFQzFiQmd5NDN4Z0pMM2FVVlQvdGhGNWlEYVFVMmVkbDQ2bDh6Qkl6V2tCVGFlYVdMaHh3OVhmazY2TksvclgxRkxmRGdEdTd2M0YvL0E1OVVhbTRwMzRLYW5iTzZUTVRNYTdnTlNueUpsVzlTK2hRMGZzc1h6d1JhRHZYSkk1M3pXeXhkT3Fsd0hhcFFjVUsvQkltSk45THRRRGhKTW1VamJpVFNST2Q5bjJWa0xXMG9obnJQUkR5MUNoTy9VTDhmNnhCcFQwTHl1SVJSNnc5K1VhRTJZeHBpMjdObkl4UHY1b2hyRm0wcEpzMyt1S2Z4M1kvWklkbW9Oc2FRWUR2ajZGL1YxZ0Radm9PZjBVTnVxZjJZYXp5a2ptckVqdDJ1aExzeVVRUVNwa2lHTmJoTGlaNU02bFFWNlNHWTNEa3FQcVBlY2dNTEJxWHM4c2NrREk1UDRFNXdCd3BRYTVEZEtERzhlNXMxbEs4RTZXRUZvcTB1NFo1N0pLWGFqTEl0R2JtdDVOWEZrVlUwYkVpMjBaU25ZSzkwWmRGQzRwQnB6L3I4TWJqZHY1c01Xa0hxcXgwNHc5Q1pMeW9vdE02WitNVnc1QXVpcWZwemFFWWxabkZRd25UZlkyUkJhM0wiLCJtYWMiOiI0MjljNTFlZDAyZGYzNTc0ODQ4ZTYzM2E4NzdiMmVlNDYwNzZiNTkyZjY2NzEyYjc4ZWZiYzM3MjA0ZmIzMGQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZsTDVkdktKTU5KSmZIQ0R3U0o0OUE9PSIsInZhbHVlIjoiZmdOTHNNUU1SK2M4WUpJV2ZJN0VFbWdrbnNYN1lsK1g0MTFhNENaVWdrQkE1YmJETXp6OWtUT1RVUVhVdWthTTFLSitjZWovbWoxSnRjUXpLRk4xajluVzVmMlQ1RnlhWGZDWS83L203Q0lJeHdua1gwaUM2cFpoU2c2SDVJNHk4cEQ4SlN1OGp2KzJRTE14QjZ3dVgyTmpmbHhPNjhJSkNHK1N3a0lsMHVJZUxXMHlBaVBnQUUwa3lwTm9jNFMwbnFTV085djNhdVc2aVdZZUMvZGFFK1oxKzdxaTBOc3V0RzIyV1ZQTXhsSVlGWWpVMTVvZnVtaHc3MG0vS1NPbEkzYkU0anUxY25WTWNLU0hpcXRjd0hOb3d1RCtobGlaUDNHVlRPUUU4Tm9zQWRNamRBOTcwYS81SXg0Yll0Yno4R3Erb2QwTWdFaEhPaVFhanA4U0FpQXY0dXpBVzVFbWZMVTlIQm9aa3ZGblh2QzBOSk1MYUJUWTBhVUM4eHBJSXNaQ2k2bmp0YjB3QUJHRDMyR05MWHM1UkpSZ0NWKzZVb3ZTalU4OHNpOVFDNllHci9tY1JXNVJBWkRyOUlJWXVVWmZlbXlFTnM2WlFBOU90ckxrb2dOYmxENzk5cTRlczdxM0NTWjBURUR2dHE5OXg4RHFjOEFGcWpYeTlSZXAiLCJtYWMiOiI2ZWI2Mzc2MmFiYzMwMGI3N2M5MjAxYmVmYjVkMzAzNGY1ODA4NTYxYTc1OGQ0MzVhNmU2ODkyYTBhNWM3ODU3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1vaFZJS3g5d0VZZHUzK1NJMGhxdHc9PSIsInZhbHVlIjoiRTc4WTY3RlB2TDRwSi8xMGxUWCszRUFKTGtnOEU2NzNFQzFiQmd5NDN4Z0pMM2FVVlQvdGhGNWlEYVFVMmVkbDQ2bDh6Qkl6V2tCVGFlYVdMaHh3OVhmazY2TksvclgxRkxmRGdEdTd2M0YvL0E1OVVhbTRwMzRLYW5iTzZUTVRNYTdnTlNueUpsVzlTK2hRMGZzc1h6d1JhRHZYSkk1M3pXeXhkT3Fsd0hhcFFjVUsvQkltSk45THRRRGhKTW1VamJpVFNST2Q5bjJWa0xXMG9obnJQUkR5MUNoTy9VTDhmNnhCcFQwTHl1SVJSNnc5K1VhRTJZeHBpMjdObkl4UHY1b2hyRm0wcEpzMyt1S2Z4M1kvWklkbW9Oc2FRWUR2ajZGL1YxZ0Radm9PZjBVTnVxZjJZYXp5a2ptckVqdDJ1aExzeVVRUVNwa2lHTmJoTGlaNU02bFFWNlNHWTNEa3FQcVBlY2dNTEJxWHM4c2NrREk1UDRFNXdCd3BRYTVEZEtERzhlNXMxbEs4RTZXRUZvcTB1NFo1N0pLWGFqTEl0R2JtdDVOWEZrVlUwYkVpMjBaU25ZSzkwWmRGQzRwQnB6L3I4TWJqZHY1c01Xa0hxcXgwNHc5Q1pMeW9vdE02WitNVnc1QXVpcWZwemFFWWxabkZRd25UZlkyUkJhM0wiLCJtYWMiOiI0MjljNTFlZDAyZGYzNTc0ODQ4ZTYzM2E4NzdiMmVlNDYwNzZiNTkyZjY2NzEyYjc4ZWZiYzM3MjA0ZmIzMGQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792968699\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1848731343 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848731343\", {\"maxDepth\":0})</script>\n"}}