{"__meta": {"id": "X10e51eaaa8c07dc31c4c098897dbebc9", "datetime": "2025-06-30 18:10:14", "utime": **********.132834, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307013.742482, "end": **********.132847, "duration": 0.3903651237487793, "duration_str": "390ms", "measures": [{"label": "Booting", "start": 1751307013.742482, "relative_start": 0, "end": **********.059029, "relative_end": **********.059029, "duration": 0.316547155380249, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.059038, "relative_start": 0.3165559768676758, "end": **********.132848, "relative_end": 9.5367431640625e-07, "duration": 0.07381010055541992, "duration_str": "73.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673472, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.005810000000000001, "accumulated_duration_str": "5.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.090178, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 30.293}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0998302, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 30.293, "width_percent": 7.917}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.112183, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 38.21, "width_percent": 6.024}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.113801, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.234, "width_percent": 6.54}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.11798, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 50.775, "width_percent": 6.196}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.121617, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 56.971, "width_percent": 38.038}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1250741, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 95.009, "width_percent": 4.991}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1356398027 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356398027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117245, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"American coffee-قهوة امركية\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 105\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1273752305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1273752305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1854014129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854014129\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhWbnloNkZZTFpoK0I4ejl6RzdwSFE9PSIsInZhbHVlIjoiODJMaUhGSE5La0padmtucHQ0akxXQ0tzdUJjL2xtWHEzdDhRcWkwcUZaNzlXazVZeVVTNDg0MGJ2RWk2TWU4QkRGdENlZkdRWURabUozZ2xLMGhhVzN6bEM5OWxYWUxsdVk2S2U5RjZnYkZxY3lTVTNUU3RraHJwWitJNGxhOWk0UjFzeUs3L1U3UWxuZCs5dkRSUFA3blpabU45akVrV3NFWnZhZFB5azhzNklSOFREZ1JCMzB2Ly9WQmVGSTVXQkY3RVRmakNkOXp0bWd3VmliMGxUT1pRdVJFRTNaQ25ML3lEbk5kZ0J3M0hvb0JDaWRIaDFOTFVqYlh0NWlJRGp5RTRwc2tBVVU2T0NSZVIwaS9IZmkzOHVNZ05GRitCQWtoOUJyNktQV2poMWgwRFJvSzh1Z001UHBiNUFXc3ZGT2JjUUFYZ1JnUUl6QUJ0LzhhOTByU2krNXJ1OTZDcVBnaEd0R1BjT0V5ZmxqQ084NDhFeUFqVFE2bWoyVWZpU2dBK1JwMHZ1NXpkaUNPdXNsWk1YRSsydjY1TUJjeGRmVjZTSUMrVXp3cjNXcFN6NFFUeUFqY0NPZHVJdXFaeDh0RzVrNWNMM3BSNWR2WmVVT3VpVTNUN3dwMWovYXBBczducWMwdUNtYmxVK1JYMnd1aDVFdnIrUTZOVzFkbTMiLCJtYWMiOiJhMzBiZDIwMTIzNGQ1OTNhOGVhNjQxNDU4NjBhYWYxOTg5ZTkzOGVlMzVlZjc4N2Y0NjZlZjhmMzg3NmMxMTBlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik42bm53NE5zcURnWGM2aGt6eFZMZnc9PSIsInZhbHVlIjoiZXdwYTlKMVQ5V3dlTjI4MVlZRHFVOUVnZWxKNzdJU3pIQk5vM0xaKzF1OHdrdC9SelJMa1ZDa2xwY0pnaW5xVUFyV21qQlFPM3k1Y1ZacWdlejhBR0d2TVlJY2kxczlCTko0T3Rocng0MTA0VVRML29FMXEzRThQdng0SFpMZzd5dGV4OFJ6enhaZ3ovWWVRK3VLNUJ1b2xzQmxkWFNFWi9zOGJ6ZUE3K3NhQWp2NTZobmN0K1V3d1BkN1REdFpTS0toTW9waHpONE9vRndlL1pDWktlYzJSK2ZjQ1BDWFVXeW5wTTQyYkZQZEFxdDZXQXhHM1VEUnQyWGlKMWZZSm5OS3N0UW9USjFQSEI4Zk5XcnB2Mkc5dzJkWkJTVDUvK3AyQTFjZ3I4L0dwdG5SYVFDblFBTm81b01jN0QvcDJQdkx1VlpUUkwvNGFxeklQcjlwYmpRZGUwb2NNc1piY0xwVVV2QW5sWFNhZTgxdTZISnczeTlnU3ZhZzFrNjdDMHVjWndnbkxIcmdpTHhPVngwZUhOSVg4ck1saEx1Z1ZMdC9pVWhGTVdOQ283akNMSkNyRVR1cE9PNnlSZy9FN0ZTTGJGZmtWd3BzQThiRHdXbDd0dG5aUXpUTE1DMmRvZFNEaW5wWDJTTTdDM3RqYkdMUVUzV2svOURmQUxpdm4iLCJtYWMiOiJkNjQ5NTllYjJlZGFiNTBiNTZkYWNjOWI0ZjIwZjdiY2RjNDNmOGRiMmNiYzQ0Yjg4ZmNhMmI4MWM0N2FjZmY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1160343752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160343752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-60004336 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRtYjRKWHczeWY2V1hMd0ZyWDVCMkE9PSIsInZhbHVlIjoiaGFjS1NkSTB0SVpMQ041MWpORm1CRktjbDdZWFAvL21pOGhYcEM5cEJYSzFPdDZFR0FROU9DbUF6UjdvaVZEK0pkc3prMUp6THVxZENwYWsrZmx1TXo3WjA2UkEydlpoNXVIZ1dJTDlUQUdkVC9OWUpZN0Q1OFJzNFUxVWdtVXRQNThIRS85cEtlQ0N1NVV0dk9KN1dTbGtza0tYemlHWWhuK3VIUzRWbTRuMmxEYjJmMlVVOTRqTTBTQWpZbjBZVm9zOEN0NUhScHo1UEo1TGphY1JpL0JGUTBwRmVIZ0N6NW8wR2h3MDQ3VWgxWXFpbzVmaEZ4STJuOHF0anB1UnQ4dUNrRkwvSENRektWd2FlU0tvVk1PRDExT3JaMlVRRFBpUk5DMkRBSmpOa0JDZ05wdkhRVDlWdzFkdjVlR0F6LzZFV3pVMkpkVzhpUVQ0M0NEM056MHNBL3NqWFR1UWUxUlY3OVVRL0pSMWFTYm9vbjR5b0RhRDVrM3owVXloSllweXBCaGZLd3RERUx6ZTlEV3NjNXA4ODY4KzE1Y0k2dkVDNUUzd0NmdGdoakNYa0lOemNra0VJMzg3clU1TjVQS2NmSm1MNk95dHVjc3IxZ3Z6UFd1ZFJiWHBXcHJ0OW4vMHpQNEduWkkrTzJHc3J0WFhFRmlhKzU1TnNmR1ciLCJtYWMiOiI2Yjk2ZWM4NjE3MmIwNGJjMzZhN2RkZDc1MTk3MjMxNzYxODY5MmI1YjMxMjQwMDA1NDYyZDIwZTgzMjhmN2VlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdsQk1oclhnQkNCM0lZYkVTTWNzNUE9PSIsInZhbHVlIjoickRsMUhTYStPakxQOVlZdDk0ckVzdzBQSTNKRkhIQUxSdS9RVlpCaU9tS1pCajg0Ym5ibDFCUlZyOGh2ZE9rQ0ZMMFdMaU00d2tmUzBhTnFUdXozcG9JMzVFNE14QzRYeFpiVy9IcmsyV1dlY2FzalJvd3JVdll2U1VFMWVETnl6QXAxT1ZLbkszRktwSmNJVmIxZGROaFVYUmt4OGJPeHdVNmZEVzBXOGZrQktHNHl2cXk3TkpGOXA2V1JaTm1tdmxpdUVHZnlxQ0JUUWxLSkFUL3Vldmxkb3gzT05zOVYyZ08xRXRaMHlkVFdSNTVJaVRQcjR1enc3RGtZck5Yc3gxcWVUT3JBYjhLVU5RWTBNODlmU2R2MlpieC9RbldKb0V3cmRNK01HaVVzWjdnMGs3MXJMZGJ3dXRSL2JHMmEzZmdFK2ZRNEhtRGtzZW94ZmhUbXU5SVNCMm9GaWk2UDFxMXpvZ1B5L0NRMHZ3N2NQT0Uxb2FxTFFjL1Z0NEQ0MFFDR1dKRlFZbUJBeDZyVnlFTkcwdFplM2FycGIxaVRjV0dPemQwQTRWR3VzYktVZVBxZkg4eTM2dUtpTzRzZ25rZlA2V0g5MkRrOTFGOTBFeHdRdFVIMnZocm9BUTlMTXNvYzNZTVg2SU1uVGVxcFJjcTVjUlBreHVsUE0vb0wiLCJtYWMiOiJjODdiMzVhZDgwYjVkMjg2YmIwYmFkNDQwYzk3OGQxYWQ5NWYxZGI0YjVhOTJlOWZjZTM1ZDc1MWZmMGI4NjUyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRtYjRKWHczeWY2V1hMd0ZyWDVCMkE9PSIsInZhbHVlIjoiaGFjS1NkSTB0SVpMQ041MWpORm1CRktjbDdZWFAvL21pOGhYcEM5cEJYSzFPdDZFR0FROU9DbUF6UjdvaVZEK0pkc3prMUp6THVxZENwYWsrZmx1TXo3WjA2UkEydlpoNXVIZ1dJTDlUQUdkVC9OWUpZN0Q1OFJzNFUxVWdtVXRQNThIRS85cEtlQ0N1NVV0dk9KN1dTbGtza0tYemlHWWhuK3VIUzRWbTRuMmxEYjJmMlVVOTRqTTBTQWpZbjBZVm9zOEN0NUhScHo1UEo1TGphY1JpL0JGUTBwRmVIZ0N6NW8wR2h3MDQ3VWgxWXFpbzVmaEZ4STJuOHF0anB1UnQ4dUNrRkwvSENRektWd2FlU0tvVk1PRDExT3JaMlVRRFBpUk5DMkRBSmpOa0JDZ05wdkhRVDlWdzFkdjVlR0F6LzZFV3pVMkpkVzhpUVQ0M0NEM056MHNBL3NqWFR1UWUxUlY3OVVRL0pSMWFTYm9vbjR5b0RhRDVrM3owVXloSllweXBCaGZLd3RERUx6ZTlEV3NjNXA4ODY4KzE1Y0k2dkVDNUUzd0NmdGdoakNYa0lOemNra0VJMzg3clU1TjVQS2NmSm1MNk95dHVjc3IxZ3Z6UFd1ZFJiWHBXcHJ0OW4vMHpQNEduWkkrTzJHc3J0WFhFRmlhKzU1TnNmR1ciLCJtYWMiOiI2Yjk2ZWM4NjE3MmIwNGJjMzZhN2RkZDc1MTk3MjMxNzYxODY5MmI1YjMxMjQwMDA1NDYyZDIwZTgzMjhmN2VlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdsQk1oclhnQkNCM0lZYkVTTWNzNUE9PSIsInZhbHVlIjoickRsMUhTYStPakxQOVlZdDk0ckVzdzBQSTNKRkhIQUxSdS9RVlpCaU9tS1pCajg0Ym5ibDFCUlZyOGh2ZE9rQ0ZMMFdMaU00d2tmUzBhTnFUdXozcG9JMzVFNE14QzRYeFpiVy9IcmsyV1dlY2FzalJvd3JVdll2U1VFMWVETnl6QXAxT1ZLbkszRktwSmNJVmIxZGROaFVYUmt4OGJPeHdVNmZEVzBXOGZrQktHNHl2cXk3TkpGOXA2V1JaTm1tdmxpdUVHZnlxQ0JUUWxLSkFUL3Vldmxkb3gzT05zOVYyZ08xRXRaMHlkVFdSNTVJaVRQcjR1enc3RGtZck5Yc3gxcWVUT3JBYjhLVU5RWTBNODlmU2R2MlpieC9RbldKb0V3cmRNK01HaVVzWjdnMGs3MXJMZGJ3dXRSL2JHMmEzZmdFK2ZRNEhtRGtzZW94ZmhUbXU5SVNCMm9GaWk2UDFxMXpvZ1B5L0NRMHZ3N2NQT0Uxb2FxTFFjL1Z0NEQ0MFFDR1dKRlFZbUJBeDZyVnlFTkcwdFplM2FycGIxaVRjV0dPemQwQTRWR3VzYktVZVBxZkg4eTM2dUtpTzRzZ25rZlA2V0g5MkRrOTFGOTBFeHdRdFVIMnZocm9BUTlMTXNvYzNZTVg2SU1uVGVxcFJjcTVjUlBreHVsUE0vb0wiLCJtYWMiOiJjODdiMzVhZDgwYjVkMjg2YmIwYmFkNDQwYzk3OGQxYWQ5NWYxZGI0YjVhOTJlOWZjZTM1ZDc1MWZmMGI4NjUyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60004336\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1321823331 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">American coffee-&#1602;&#1607;&#1608;&#1577; &#1575;&#1605;&#1585;&#1603;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>105</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321823331\", {\"maxDepth\":0})</script>\n"}}