{"__meta": {"id": "Xf9f67f15b8abd7534d186a435569ed52", "datetime": "2025-06-30 17:58:14", "utime": **********.284597, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306293.829171, "end": **********.284615, "duration": 0.4554440975189209, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1751306293.829171, "relative_start": 0, "end": **********.237139, "relative_end": **********.237139, "duration": 0.40796804428100586, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.237148, "relative_start": 0.4079771041870117, "end": **********.284617, "relative_end": 1.9073486328125e-06, "duration": 0.04746890068054199, "duration_str": "47.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43346776, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.268291, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEXyoiTANh8qPLaXlQ5WQSdt8eQQWYvkJCqk22Te", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1044089375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1044089375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1206083044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1206083044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1280947156 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280947156\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-643970278 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643970278\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1164701300 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:58:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ2VGNNaDMrek1lcUFqUEp4N0pkK3c9PSIsInZhbHVlIjoieEVZV09DeFZSYjAwM3dLMmxtaVdFZ2lmdDVlWG43cW1ySHZXRkxJVCt3YWJ3OVNyWXRVOXQyZlF6UzdCTVNYbDZPekhWeno4cFZjVnpkWjlFNEtSNVhvcXUxRmxZNDhCdjJRNG1UTGliOXJ6bDZoanFOV1JpWW1wa1JpM0p2emFZbGc4U3ZVeEllemF1eGM1RWVXeGFNdUpiL3lvM3RMeXY0Qzl1dytaMWV6VUM0ZGVRR1NhTjY4cWFJeUtoNWZ6R1FtS2RDbFRZRm0vbG40UUZ6ZTh0UVpjV1ZEUkNib2NyNjVmbjdYbWRpQ21ZNVZldUM4YmJRbDI4VU0wUEFIVXYzMWRlbEFjUk01bHl1U0U5NjUvLzM5SGpaZXFJYXB2bjhhOEJLQWlCMCtDRmZWd1hwcVRIcnM4STZlWUtrR1FFUHc1ZW4rWGNzOTRVQ1ZJR2dQU1hNdUdZQ05aRThPZ0E1OHlRNEN4L0hpVXhVWWJNT2QzN2h1TVVOWGZlblBKRk9zSXZXOWdvZmtnV1pYaHhsNmN5a2dWeGNYRVVpd1pVZi9HYzR1TUk5OHBKR1dWb012TUx6QTBZL0kxZ29PQnRYcnZkMXk1SXZsaVh1WlprSDlEbWVCeWRXd3hBUURZalpHU0VVMG9wUzhzcWxhSHA1elZod2xBV1NhUUlrRUIiLCJtYWMiOiIyZWY1NjkzYWVlZWI5MGJiOTAzMTU2NjYwZWRkZmM5NmRiMzM4MWQ2N2M0ZjdmNmFmNDdjZmY0MzEwZDk4YmFhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZqSmVTMVlUelVhKzRKMGRuUWtQTFE9PSIsInZhbHVlIjoiOTBqOThHUzZXaC9mb1J0dER3bG9lMjZ6SUVManNieTdqLzFwT1JhZENuTElNNUNDQmFMZHQzVnJYbDRhVkdhL0NMZFcxRnNRVlloTEIrWEk2L2NuU21uM0ZpVkpZcnhKanlucE03YVdVU3pWa0dhbHJnbUpBTVJOR0xGR0RHR05GZWZPZ0JpQUY5ZTRkejA3TzhFdVJvU2k1RHI0OXhQTlUwY09CcW92d2htelY5OXViV1JOc1RtblQrVVlUTE9va2RydzZheDF2WkZyODh3YXZ3NEd2K05uN0p1R0pEYWhpVWo2alhWNDl4QmladDdwVExJa2FqSjducm95N1grSTlQdjlYckdKTkQvMGxORXJSNzlYbmFwNHZYdWJqOFgxU1NmWWUvQ2NRMGkwNkhIYnVQWkhQbXpkVjV3a3JpbzBwUDNSTVdRSHF2OFhoNW1zaEdRbnV6WFRKbWhFam1OWWZPSTYyZWJ5eXNNS3llUGc0TnhpR3AraFhQNk9KK3NjU2pFaFBJWUo1NHdsajdyK0o0Q1ZLZEpreHlYbkgvbFhRbGFFMStabEZyNExWaDBDd0xRVkNybVBCU01Wa3JyS0ZZMVF0a3pITklKbjF5eS9jTjRwSHFhaUQrUUFqRUx2NEcvOXNrU3JjZU1laWYxdFdYV0F3VWtVbXhyaE1MWXoiLCJtYWMiOiI0ZDQ4YTA0OGQxMjJhNGViZDdiNWVkNTE5YjAwZDk3N2VjMzBiMTNiNmJmYjUyYWRkZDg5MTQwZjU3NWRlYzljIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:58:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ2VGNNaDMrek1lcUFqUEp4N0pkK3c9PSIsInZhbHVlIjoieEVZV09DeFZSYjAwM3dLMmxtaVdFZ2lmdDVlWG43cW1ySHZXRkxJVCt3YWJ3OVNyWXRVOXQyZlF6UzdCTVNYbDZPekhWeno4cFZjVnpkWjlFNEtSNVhvcXUxRmxZNDhCdjJRNG1UTGliOXJ6bDZoanFOV1JpWW1wa1JpM0p2emFZbGc4U3ZVeEllemF1eGM1RWVXeGFNdUpiL3lvM3RMeXY0Qzl1dytaMWV6VUM0ZGVRR1NhTjY4cWFJeUtoNWZ6R1FtS2RDbFRZRm0vbG40UUZ6ZTh0UVpjV1ZEUkNib2NyNjVmbjdYbWRpQ21ZNVZldUM4YmJRbDI4VU0wUEFIVXYzMWRlbEFjUk01bHl1U0U5NjUvLzM5SGpaZXFJYXB2bjhhOEJLQWlCMCtDRmZWd1hwcVRIcnM4STZlWUtrR1FFUHc1ZW4rWGNzOTRVQ1ZJR2dQU1hNdUdZQ05aRThPZ0E1OHlRNEN4L0hpVXhVWWJNT2QzN2h1TVVOWGZlblBKRk9zSXZXOWdvZmtnV1pYaHhsNmN5a2dWeGNYRVVpd1pVZi9HYzR1TUk5OHBKR1dWb012TUx6QTBZL0kxZ29PQnRYcnZkMXk1SXZsaVh1WlprSDlEbWVCeWRXd3hBUURZalpHU0VVMG9wUzhzcWxhSHA1elZod2xBV1NhUUlrRUIiLCJtYWMiOiIyZWY1NjkzYWVlZWI5MGJiOTAzMTU2NjYwZWRkZmM5NmRiMzM4MWQ2N2M0ZjdmNmFmNDdjZmY0MzEwZDk4YmFhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZqSmVTMVlUelVhKzRKMGRuUWtQTFE9PSIsInZhbHVlIjoiOTBqOThHUzZXaC9mb1J0dER3bG9lMjZ6SUVManNieTdqLzFwT1JhZENuTElNNUNDQmFMZHQzVnJYbDRhVkdhL0NMZFcxRnNRVlloTEIrWEk2L2NuU21uM0ZpVkpZcnhKanlucE03YVdVU3pWa0dhbHJnbUpBTVJOR0xGR0RHR05GZWZPZ0JpQUY5ZTRkejA3TzhFdVJvU2k1RHI0OXhQTlUwY09CcW92d2htelY5OXViV1JOc1RtblQrVVlUTE9va2RydzZheDF2WkZyODh3YXZ3NEd2K05uN0p1R0pEYWhpVWo2alhWNDl4QmladDdwVExJa2FqSjducm95N1grSTlQdjlYckdKTkQvMGxORXJSNzlYbmFwNHZYdWJqOFgxU1NmWWUvQ2NRMGkwNkhIYnVQWkhQbXpkVjV3a3JpbzBwUDNSTVdRSHF2OFhoNW1zaEdRbnV6WFRKbWhFam1OWWZPSTYyZWJ5eXNNS3llUGc0TnhpR3AraFhQNk9KK3NjU2pFaFBJWUo1NHdsajdyK0o0Q1ZLZEpreHlYbkgvbFhRbGFFMStabEZyNExWaDBDd0xRVkNybVBCU01Wa3JyS0ZZMVF0a3pITklKbjF5eS9jTjRwSHFhaUQrUUFqRUx2NEcvOXNrU3JjZU1laWYxdFdYV0F3VWtVbXhyaE1MWXoiLCJtYWMiOiI0ZDQ4YTA0OGQxMjJhNGViZDdiNWVkNTE5YjAwZDk3N2VjMzBiMTNiNmJmYjUyYWRkZDg5MTQwZjU3NWRlYzljIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:58:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164701300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-105429099 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEXyoiTANh8qPLaXlQ5WQSdt8eQQWYvkJCqk22Te</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105429099\", {\"maxDepth\":0})</script>\n"}}