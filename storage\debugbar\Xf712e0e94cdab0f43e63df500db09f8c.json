{"__meta": {"id": "Xf712e0e94cdab0f43e63df500db09f8c", "datetime": "2025-06-30 18:53:36", "utime": **********.888667, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.426703, "end": **********.888681, "duration": 0.4619779586791992, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.426703, "relative_start": 0, "end": **********.80094, "relative_end": **********.80094, "duration": 0.374237060546875, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800962, "relative_start": 0.37425899505615234, "end": **********.888683, "relative_end": 2.1457672119140625e-06, "duration": 0.08772110939025879, "duration_str": "87.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48095000, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.016749999999999998, "accumulated_duration_str": "16.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.834298, "duration": 0.011609999999999999, "duration_str": "11.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.313}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8547382, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.313, "width_percent": 2.627}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.86809, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.94, "width_percent": 2.507}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8703501, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.448, "width_percent": 2.985}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8749628, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 77.433, "width_percent": 15.045}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.879907, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.478, "width_percent": 7.522}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1487760643 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487760643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873983, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-448467553 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-448467553\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1827465064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1827465064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2085455393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2085455393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1851190926 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6InpNU0lDUjduTFdkeU1MYW5QS0syV3c9PSIsInZhbHVlIjoiV1BQZE1aUFZQdS8vRStLamlQSmhLL1dmUkhBTXlUT3F6ZllveUFKeGVTR1ZneDZsMDhYK05tN2lMdmtNdUwwZWZ1SzBDVWMvOWtLM3RDMkp6NThYRmtUcUxrS29LOHJPYUpFRGRPMkxOSGhXLzhxeW9iS1ZNV0F5Tm5NT1VSMmtzRGdtZlFwMlBXMUxnUmttVGNtcGdab0RHcG14T0pqa3orS1VjL3UyNUtsZjkzUnpvck00d2ZHNkdRWDNPUW1naVphSUNNNnZtK0xvYlVkSTN3Ym9NYWpHUU12TUFUQUlMM3dENmo0TjBjcFR4RVJWc1RYdS9RRm5YaCtXRitHY3ZkMEQrczZLVEVXRmY5OVRBS0pvY0JUeHY0aHRVaXUwL3hQWW10VmZRdU5adDY3ZVE2Uy9ITkllaDNLeklVS1RTZTNvcXVxd0pjZlQ4ekJpbVRDUW1rendNS0g4Z0x3bmRPRnZnWWdrT2dxVDJ5U04xMitvUDl6bG1WSFZqcFVKcmJkSTZWdkhwWW5nYUF2ZUQ1S3ZMY1ArVE9SL2U3dWtUVVRJL0ZDOUgzZ25YdWJKR1NqL0xlQjQxeDFHbGVyTFE0ZzFJL3YwMmZLQTN1WmxTcmdLVUlEK2ZqRy9yWk1Bb0xFOSt3UXhIMkQxQVNPZlViemUrMzl1RUtabzlnQ0MiLCJtYWMiOiI4ZDY1YTdkNjRjYzcxYTJiYzZkYmUyNDZhN2UwYjU3NTFkZDQ4OTBkZDM0ODM2MTMxMTFiMjBiNGU0YjRkYjIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhObHoxQTg1cVY2cEhkalRIbzVIVEE9PSIsInZhbHVlIjoiT1N4aFVidTBjKytSbVJKRjZVc1BNL3hMV1NwZFF1bGwrblgwcnhpeWFSdFVsazFOd0s5K2VwTVpIOExMSVpZUXdkZjMveG9YV3FoN1MzdUpVeW5iZ0FMOG90UVNUNllWUEFlRjZDOW9hcG5pczZYbTZTeVNnRTdMblovMzNnOGdXMHFOajBoUDdUdk1yMk9kalBlZFNHUSt4WDdsT3NteDczZnpyVTVWYWgwaXFFdCtmK1BzdDhmNUFjZlJWTTV1cEtjdmFYTzA2S1hJR0JKUmlQV05yaHdtM29Sc3pPY1ZtTWVrVVJQTlFVaS9FVnpDV2JtL2Y3NVo5dWNzekYzOWlKMUJ1N1VqeGdwUTdwQmN1YUxsTW1oelI4dDYwbkRneWkvcDhwNk5jSkZ5elhLSHdjMjh0R1lJT24xclN5dk5Fek9NeCt2ZTRrWkxlekNPQVZKa0tuVFo5RXJ1Q1hNQ0pScGNRT01DSEs1b2pTUXBoZ1UwNnN5aDNMQ0UxNzdiVWlUZmxuUFRBV2RNeU1KdTFuaVI0dGRaaDF2WFRJU0J4bjVBa0tXYzhLdVFRTTBYRHlJeEpEMHlyOXBEalFRR1pkclg1UWZaWEdUMlBEMFA4WjA1VzJIQU90QW9HU2dweXFDY3dXZXpWOHZiaWdBNVhjc0dqemdTS08vNWJhKzAiLCJtYWMiOiI4NmVhOTc5ZjJmNGE2NjhiMDY5YzliYzljNjY2NWRlM2RkNjRlNWRkMDcyMjE1YjNiMWM5ZTE2NGRkOTlhYTYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851190926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2074134830 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074134830\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-639230091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:53:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im4zUDJKeUtSSmpRUDczRGVFUXFoenc9PSIsInZhbHVlIjoiMUhOYktBSzlqRjJwcUFCc0ZTQm9QNEM2OG5wbWJOck8zRTFXQUx5czBiZlNkcU50d1I3Z0owS2RwdDdqZ0FVWkxCWThXeVlpejBmZDh5Wk5yQ0g5NGFRL2VoZjNOTlhzZXVpY2swWW4vNERBNkg0M05qdElMZ0U4T1FvS3NhRTRCUDMzdFcvdjZvL0xaRHBCL1hFNWlsZ1BaNC85N0t2ck9Wa2w1T0g2bmVIMnc3NmJOZzQ3ZHhKaWJDdFNBRnRzOGhxNyt3ZTZWTFBiSytqK1B5VW9VOU9sSkJqQjdVOUhQZWsvbk9xbWJDOHpaS2VlMnlJNDlublJQdVBITjdaMmllQWlUZ3MwTGtsQzc4cm12N2hUeXJGc1licFI4c3FsTVFabnZQWVhmTkViZXNtZXJpS01xNGZocnZZdXFva3VvU2FjdEo4OWkxbnovZlAvZ0RRdnF0L2N5YmIwb2M5YVkwNFhiRmFaOEZIL3IxMWNFajlESEw3N2owT1RHUVpkN0laN1FmVWlHOEd6aElMVnJkWnJmd0QveE9CNXh2NldraUJ6c3JiK01ubzlmelk1SzJWdjA4TkhHaEpQQTQzM1lNaTN0Vnh1Ukdmb0ZRUXNya0pQY2syRjUxVC9qTHp0LzR6YzhablcrVFFieVE4UlFOUTFyV3FZbXArOTlkbysiLCJtYWMiOiI1NWI4M2U5MTk5OTBmZGJhZDBlNDViY2EwN2NlM2Q4NWUyZjVmMGY4MmU0Y2I2M2ZmMDE4MmYzYWY3MzFmNzBjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:53:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9DWnRSK0src1RRNUljUDArRVkzOFE9PSIsInZhbHVlIjoiMnlzK1BuazZVWjA4SWgyMXNxZXpmbG1LMk1zYXd3TEhjdWNHK2tNNE9PRkluaVlPSmNmTmNGMzhScy8xVVV1Y1ZLT0FML1NxR0g4QWc3UVRwNHJtdkFGUmRxb0R0RlhFcXNJV25KeHZtbnhkbnNxalI4eWlISDVmKzBkZnNER3dDclpuT2g0NnNXZFdmOXEyUW5pRUFUdXduTElMZ2VXWmx3a05oQ0pjVmVJMHQ3ZWtGR1JWNlk2QUVvc2cremE1UUpyZWJRZS82eWNSUlZYeTdaUjVlMlhvcXdVU0hGVk1WUTFjaG0wNlIzbEd3aStlUVdlRm5ZOWpWaWNFYjhZemFtcmFyVG9odlliYkpaMUYrTkh6bm1VaEhOdkFobDRPeG9UL3dZNlFjU2Y1Y3ZKdjlScUlsSll4dmh5ZmZia3FJaWlGOXFiUktNNlNKNkpQeDZPbWtUNS9uRlhLYTdGRzU4b0hVa3B3eFZyd010T0pib2pHeHJOQkZWVFpVVkp1dkZ2RTlwRmF4ZWRzUEpDVlZJN1ExbXFrc3Y2OXFFVWt2eGFkZTdnWklkOWpWb2pEM3M1cXVtQnVGSGhieDlBZlljdnBTN3NZZXdSRmtiY0xCRmVUQ0M4ZmdhR1Z2UmVmSnp0Y0xENldvZkcwZjFBdi9VSHVnLzErcnpndkplQkMiLCJtYWMiOiJlNDA4NjY4YTQ0MmI0YWZlNDhlZDhmOTZmMzRhMmFiYzViNzM0OGJmNzA1MTk0NmE4NTk4MTExZGE2OWEzOGE2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:53:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im4zUDJKeUtSSmpRUDczRGVFUXFoenc9PSIsInZhbHVlIjoiMUhOYktBSzlqRjJwcUFCc0ZTQm9QNEM2OG5wbWJOck8zRTFXQUx5czBiZlNkcU50d1I3Z0owS2RwdDdqZ0FVWkxCWThXeVlpejBmZDh5Wk5yQ0g5NGFRL2VoZjNOTlhzZXVpY2swWW4vNERBNkg0M05qdElMZ0U4T1FvS3NhRTRCUDMzdFcvdjZvL0xaRHBCL1hFNWlsZ1BaNC85N0t2ck9Wa2w1T0g2bmVIMnc3NmJOZzQ3ZHhKaWJDdFNBRnRzOGhxNyt3ZTZWTFBiSytqK1B5VW9VOU9sSkJqQjdVOUhQZWsvbk9xbWJDOHpaS2VlMnlJNDlublJQdVBITjdaMmllQWlUZ3MwTGtsQzc4cm12N2hUeXJGc1licFI4c3FsTVFabnZQWVhmTkViZXNtZXJpS01xNGZocnZZdXFva3VvU2FjdEo4OWkxbnovZlAvZ0RRdnF0L2N5YmIwb2M5YVkwNFhiRmFaOEZIL3IxMWNFajlESEw3N2owT1RHUVpkN0laN1FmVWlHOEd6aElMVnJkWnJmd0QveE9CNXh2NldraUJ6c3JiK01ubzlmelk1SzJWdjA4TkhHaEpQQTQzM1lNaTN0Vnh1Ukdmb0ZRUXNya0pQY2syRjUxVC9qTHp0LzR6YzhablcrVFFieVE4UlFOUTFyV3FZbXArOTlkbysiLCJtYWMiOiI1NWI4M2U5MTk5OTBmZGJhZDBlNDViY2EwN2NlM2Q4NWUyZjVmMGY4MmU0Y2I2M2ZmMDE4MmYzYWY3MzFmNzBjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:53:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9DWnRSK0src1RRNUljUDArRVkzOFE9PSIsInZhbHVlIjoiMnlzK1BuazZVWjA4SWgyMXNxZXpmbG1LMk1zYXd3TEhjdWNHK2tNNE9PRkluaVlPSmNmTmNGMzhScy8xVVV1Y1ZLT0FML1NxR0g4QWc3UVRwNHJtdkFGUmRxb0R0RlhFcXNJV25KeHZtbnhkbnNxalI4eWlISDVmKzBkZnNER3dDclpuT2g0NnNXZFdmOXEyUW5pRUFUdXduTElMZ2VXWmx3a05oQ0pjVmVJMHQ3ZWtGR1JWNlk2QUVvc2cremE1UUpyZWJRZS82eWNSUlZYeTdaUjVlMlhvcXdVU0hGVk1WUTFjaG0wNlIzbEd3aStlUVdlRm5ZOWpWaWNFYjhZemFtcmFyVG9odlliYkpaMUYrTkh6bm1VaEhOdkFobDRPeG9UL3dZNlFjU2Y1Y3ZKdjlScUlsSll4dmh5ZmZia3FJaWlGOXFiUktNNlNKNkpQeDZPbWtUNS9uRlhLYTdGRzU4b0hVa3B3eFZyd010T0pib2pHeHJOQkZWVFpVVkp1dkZ2RTlwRmF4ZWRzUEpDVlZJN1ExbXFrc3Y2OXFFVWt2eGFkZTdnWklkOWpWb2pEM3M1cXVtQnVGSGhieDlBZlljdnBTN3NZZXdSRmtiY0xCRmVUQ0M4ZmdhR1Z2UmVmSnp0Y0xENldvZkcwZjFBdi9VSHVnLzErcnpndkplQkMiLCJtYWMiOiJlNDA4NjY4YTQ0MmI0YWZlNDhlZDhmOTZmMzRhMmFiYzViNzM0OGJmNzA1MTk0NmE4NTk4MTExZGE2OWEzOGE2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:53:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639230091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-102482876 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102482876\", {\"maxDepth\":0})</script>\n"}}