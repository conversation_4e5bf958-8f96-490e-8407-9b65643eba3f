{"__meta": {"id": "X2b7ee6ff009e9a2bda2c11f6c4b72420", "datetime": "2025-06-30 18:49:00", "utime": **********.504163, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.026716, "end": **********.504178, "duration": 0.4774620532989502, "duration_str": "477ms", "measures": [{"label": "Booting", "start": **********.026716, "relative_start": 0, "end": **********.437575, "relative_end": **********.437575, "duration": 0.4108591079711914, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.437582, "relative_start": 0.41086602210998535, "end": **********.50418, "relative_end": 1.9073486328125e-06, "duration": 0.06659793853759766, "duration_str": "66.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45363072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01752, "accumulated_duration_str": "17.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.469185, "duration": 0.016800000000000002, "duration_str": "16.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.89}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.494549, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.89, "width_percent": 2.169}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.497335, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.059, "width_percent": 1.941}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inp3M2xxYngyT3J0V3JyZ1l2ZjRkRkE9PSIsInZhbHVlIjoiMlhyODRCa3VqbHJIUTBDV2FZdmNuWWJ2NnNCSEpqMlF3OWVWc0o5L05lY2NRNjZUMGZ4eVRQa2w5dWJ4UHZCMncvdWxxL2JXSGEwVWQzcEJ3S1kxZkZBaUtZNFVqTWxUS25jTzZTM00yWm5tWFV5RUZLMU9rQlYzUytySXoyeCsyKzQ4blU2cU1DdVlKQysrRWdQNW9kbTlKS2EwdnMvTFdjdFhmaEVFaXl4Tzc0U0Y3aGZTdkk5YkRkbkNjditBSjF0T2NZSGhxMVNZcFZkbng3bG4rNUtHbDBKdC96U2MycmMycnRaR0l4WDFpczN4N3BnOS9rbUFtam5XYjEvTTFaMmlCald5TjdOc3N4cGp5REd0d0FNYWVQa0JqMkFJcllpbkhhanhQUkw3YkVXYUVIem4yYjNTc2g4LzQzUXJOVHNNNGRVUE9KUk1VT281VlBsdngyMEFndFptVkJJdzNidFllUk96YnlIQUlnU3V3WlF0OUlRdGxEUmQxRWorNEZtakw4V012SzUxbWR4RHYzSWNxK2I4QnFtaVpiNFJQejVacmNIamExcWpmakpKS2ljdlhYd1NWa2xtNjRWeVA2bXRlWjJWUWRWaGc2MFRyQTRlNWwwUWNBdFowSkRVRkUwNFhyQ3gvaHpPQVRnQWJYVTFxOUdWenpkQXFSMGciLCJtYWMiOiI4ZjEyNWRkODhlNTU5NzUxMjRhNjQwYWQ0N2Q4Yzc0YWI1MWQxN2JhOTE3ZjcwMjhjODZkNTM0OTQzYzZjNWE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1zdmVkWmowMnJ4Q3lBR3p6aWpxNmc9PSIsInZhbHVlIjoiclFKZmkwRVZQZDhONkRabWZuT3REYlU2S1RLOXg5eVVTVjBXSTdDOENGbWVqTGtRVTJQRUp6cWs2aGliV1FTQ0FHcm1SSDZ6TWdIZ29RZEFtbDMzU2toN055cXR3dUlCWXJ1NTZkZDZCVXJtR0FzSkxLYWNrRlJVM1AwWGg3UlNQY0g2bWFsMW16My92cVQwQ0lHdGJrWWJHUCttRUFGSW1jaHNUdnB4ZW82ZmRSa3hxSHFyTnRWN3FPM1hyNUV5dG9jb3ByOGhKTlBXOVdtUUVBTjhlR0c1RVFhSzE1R1ZVUzl0QkljSUxZaDVSUWRvVlp1ayswVTkrckR2K2xrdnlreUVpVldTaHZpejUxZXQzUWhyUXhhU2lNajRhUjVta1c4ZllrcmVMM3pyMTd4MlZlTGo1bWpZTlBWaW44U3R0RTY1d3lBbXVneVVRUlZha2RGaGRQNEl2bDB1aEtkMURVTXlMQnhBOFl6YTN5aXhVMXN1YVc0WDFTY2ZUc2ZGMURscXQ2L1ljYUQ3azU0UndMQmJ4djV1Slk3U2xIQWJoWjNVUXFRN2pNQnlGWmd0cHpDUG4rUjlMT2EwWmlnMmRFYUgxb3ZvdTlmVXBvYzF1L1hJZDdSVHBDZEhnNUZxc1VWMWlhRUY0NmFKN2lsYUJtV3Y2bXRZcXFMejhkSGQiLCJtYWMiOiI2OTFiZmFlNjM4ZDdjYTM4NzgwM2YzZDVkNTkzM2Y2YmVmYjEzZWM2ZmNhMWE2MTJiNDZkZWUwNThjNmQ2MDk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2127743809 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127743809\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-944623255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJIWTVpTGQ0LzdqUFgyNTJyTTBFREE9PSIsInZhbHVlIjoiNzdudmhtSUVIcDZhVEFXa2tvSFBOak91Tm12OSszaTMrSzZWcEJ0bUxDaTYyUkNBV05XVWg4czhPZmJleVlNQlA1K21pVDNVeEljRHI5UmVUYzQycWV3VWNHU2NTY2RpTTZCblg2MjQycUwxTlZyUlFFZ055ZDh0YVRaQytTMlVINHg2SDR1a3BWQ0lITzA2YUJyV2lYTzFiU1JHQTRZRWpLTkVaenlzRmJOaWxualUxWWxrVnd2UU9mMnR4TUR1TUh5Skk5TUNjS1gvd3lvaWxlN2dXRnhCUXZNand3WFo3R1ZkRm95eXpsY0U5YnlxemYrN3pnWkhVQTRGWDdzNTJvUG1UbUJadVdNSUFsYVcxZ3BkUEVEUHh3SkszbHBiWFdpdVhsK1pxd0NWKzI2bWJ0bWU0UlVKNTZnMDYwQ1NrY2RSclptME13bHk4RUo3MHhJM01hSnFOb2dYcWVmVU9XYmFTKzFPVkgyaGxnS2RYZDZuUHdpRUFhWWhsb09zZ2J3aTAyQWZ3Z2JzMWlmd09adytDd1I0ZmlWU0F0ejlMUHpaUFlJZStKUll4aXBJbWpzRkxPSExaNjNXSGdmdmJjcU5jMWlKYXNZWXJZVEM4V3pzTVZpWWlLRTAzQ1g1TlJ2OVZZYUE1SkU1dU9JMmdVYk1icklaS0NtR1ZhQTMiLCJtYWMiOiJlNmJhYzA4OTJkYTI0Y2I0YzA5Njg3ODlhOTE3ZTk3MDI5YWY5ZDgxNDM2MTliYmUyNjM4ZjllMTdhMDVmNjFhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBGNVV4V2YxR1ZGeEFnWndJWmFaN2c9PSIsInZhbHVlIjoiYW5BMW04MENzbWtmM0FzdWxXS0FLOVFGTkc3Tm9hemZtMkJRajgxemNXYytIWi83S2RyazJNaDBpamYxRURxNGRaUE1TYmVEWXZTOSt5YmF5Yk1hdW01eWVGem9LS1RPODFVcm5IL2M5NUNZMmt1Y3VsZjhZZ1drYzdsQ01KSUdweENsenF0L0MzbnFpZDhnYXFVYzArYmUwWEhRc3pLZWp3SmZRT0VSMm9abWhpQVJPQjF0LzdwenFob0NEa2Y4VmVQeHRhb3F0Z3NYNHFHVmtkeFJNbTZvdXA1dXRCWjBoVW84Umt2UEg5NytCL2FSZ2ZXNnYyaldiTHlUSHpsSm00QU5IWU9KbXVWbk5rZlJFQnJMT1o0N0hIcnlLbGdvQ1N5bGZkMnROUW5YYVpaWGczc3NyM3RSUTNMWGVjZFBadURsSC8wSnFyOEdBK3VITEpvdk9wcmZReVRIZit2R2JXYUVxMXRxcHl1eEpoSnNvZDIwYm9KODVIbC8wdFdPK1dFdkZ2QWpodFdlSkc5ME1hbnpyVHdrRFNzeEFqM1hVc1VYN2ljelh3UWgwSGlqVSt5N1RFVDhVbHJMS2UzcUxQR2c0WjV5Z1JWOHRlNzdORjBmbG5tYmhBUlRpQThLUDlXZkJEOW5ySkpWelRydHFmR0FpZWE3Z3JQYzFNeGUiLCJtYWMiOiJhNTU3OTZiMGRmYWM1ZTk0ZDM5YWY2MDgzY2RmNWU0M2FiNTExZDhhYzBlNjNiNDkxYWIzOTIxZjZjMWNmNjRiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJIWTVpTGQ0LzdqUFgyNTJyTTBFREE9PSIsInZhbHVlIjoiNzdudmhtSUVIcDZhVEFXa2tvSFBOak91Tm12OSszaTMrSzZWcEJ0bUxDaTYyUkNBV05XVWg4czhPZmJleVlNQlA1K21pVDNVeEljRHI5UmVUYzQycWV3VWNHU2NTY2RpTTZCblg2MjQycUwxTlZyUlFFZ055ZDh0YVRaQytTMlVINHg2SDR1a3BWQ0lITzA2YUJyV2lYTzFiU1JHQTRZRWpLTkVaenlzRmJOaWxualUxWWxrVnd2UU9mMnR4TUR1TUh5Skk5TUNjS1gvd3lvaWxlN2dXRnhCUXZNand3WFo3R1ZkRm95eXpsY0U5YnlxemYrN3pnWkhVQTRGWDdzNTJvUG1UbUJadVdNSUFsYVcxZ3BkUEVEUHh3SkszbHBiWFdpdVhsK1pxd0NWKzI2bWJ0bWU0UlVKNTZnMDYwQ1NrY2RSclptME13bHk4RUo3MHhJM01hSnFOb2dYcWVmVU9XYmFTKzFPVkgyaGxnS2RYZDZuUHdpRUFhWWhsb09zZ2J3aTAyQWZ3Z2JzMWlmd09adytDd1I0ZmlWU0F0ejlMUHpaUFlJZStKUll4aXBJbWpzRkxPSExaNjNXSGdmdmJjcU5jMWlKYXNZWXJZVEM4V3pzTVZpWWlLRTAzQ1g1TlJ2OVZZYUE1SkU1dU9JMmdVYk1icklaS0NtR1ZhQTMiLCJtYWMiOiJlNmJhYzA4OTJkYTI0Y2I0YzA5Njg3ODlhOTE3ZTk3MDI5YWY5ZDgxNDM2MTliYmUyNjM4ZjllMTdhMDVmNjFhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBGNVV4V2YxR1ZGeEFnWndJWmFaN2c9PSIsInZhbHVlIjoiYW5BMW04MENzbWtmM0FzdWxXS0FLOVFGTkc3Tm9hemZtMkJRajgxemNXYytIWi83S2RyazJNaDBpamYxRURxNGRaUE1TYmVEWXZTOSt5YmF5Yk1hdW01eWVGem9LS1RPODFVcm5IL2M5NUNZMmt1Y3VsZjhZZ1drYzdsQ01KSUdweENsenF0L0MzbnFpZDhnYXFVYzArYmUwWEhRc3pLZWp3SmZRT0VSMm9abWhpQVJPQjF0LzdwenFob0NEa2Y4VmVQeHRhb3F0Z3NYNHFHVmtkeFJNbTZvdXA1dXRCWjBoVW84Umt2UEg5NytCL2FSZ2ZXNnYyaldiTHlUSHpsSm00QU5IWU9KbXVWbk5rZlJFQnJMT1o0N0hIcnlLbGdvQ1N5bGZkMnROUW5YYVpaWGczc3NyM3RSUTNMWGVjZFBadURsSC8wSnFyOEdBK3VITEpvdk9wcmZReVRIZit2R2JXYUVxMXRxcHl1eEpoSnNvZDIwYm9KODVIbC8wdFdPK1dFdkZ2QWpodFdlSkc5ME1hbnpyVHdrRFNzeEFqM1hVc1VYN2ljelh3UWgwSGlqVSt5N1RFVDhVbHJMS2UzcUxQR2c0WjV5Z1JWOHRlNzdORjBmbG5tYmhBUlRpQThLUDlXZkJEOW5ySkpWelRydHFmR0FpZWE3Z3JQYzFNeGUiLCJtYWMiOiJhNTU3OTZiMGRmYWM1ZTk0ZDM5YWY2MDgzY2RmNWU0M2FiNTExZDhhYzBlNjNiNDkxYWIzOTIxZjZjMWNmNjRiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944623255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}