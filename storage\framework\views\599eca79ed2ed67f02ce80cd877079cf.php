<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('تحليل أداء المنتجات')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('الرئيسية')); ?></a></li>
    <li class="breadcrumb-item"><a href="#"><?php echo e(__('العمليات المالية')); ?></a></li>
    <li class="breadcrumb-item active"><?php echo e(__('تحليل أداء المنتجات')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <div class="d-flex align-items-center">
            <!-- أيقونة تحليل المبيعات المتقدم -->
            <div class="me-3">
                <a href="<?php echo e(route('financial.sales.analytics.index')); ?>" class="btn btn-info" title="تحليل المبيعات المتقدم">
                    <i class="fas fa-chart-line me-2"></i><?php echo e(__('تحليل المبيعات')); ?>

                </a>
            </div>

            <!-- فلتر المستودع -->
            <div class="me-3">
                <select class="form-select" id="warehouse-filter" style="min-width: 200px;">
                    <option value=""><?php echo e(__('جميع المستودعات')); ?></option>
                    <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($warehouse->id); ?>" <?php echo e($warehouseId == $warehouse->id ? 'selected' : ''); ?>>
                            <?php echo e($warehouse->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <!-- فلتر الفئة -->
            <div class="me-3">
                <select class="form-select" id="category-filter" style="min-width: 200px;">
                    <option value=""><?php echo e(__('جميع الفئات')); ?></option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->id); ?>" <?php echo e($categoryId == $category->id ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <!-- فلتر التاريخ -->
            <div class="me-3">
                <input type="date" class="form-control" id="date-from" value="<?php echo e($dateFrom); ?>">
            </div>
            <div class="me-3">
                <input type="date" class="form-control" id="date-to" value="<?php echo e($dateTo); ?>">
            </div>
            
            <!-- زر التحديث -->
            <button type="button" class="btn btn-primary" id="refresh-data">
                <i class="fas fa-sync-alt"></i> <?php echo e(__('تحديث البيانات')); ?>

            </button>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><?php echo e(__('تحليل أداء المنتجات المتقدم')); ?></h5>
                <small class="text-muted"><?php echo e(__('تحليل شامل لأداء المنتجات ودوران المخزون')); ?></small>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3 id="total-products"><?php echo e($productData['total_products'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي المنتجات</p>
                                <small class="text-light">في النظام</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3 id="active-products"><?php echo e($productData['active_products'] ?? 0); ?></h3>
                                <p class="mb-0">منتجات نشطة</p>
                                <small class="text-light">لها مبيعات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3 id="stagnant-products"><?php echo e($productData['stagnant_products'] ?? 0); ?></h3>
                                <p class="mb-0">منتجات راكدة</p>
                                <small class="text-light">بدون مبيعات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3 id="expiring-products"><?php echo e($productData['expiring_products'] ?? 0); ?></h3>
                                <p class="mb-0">منتهية الصلاحية</p>
                                <small class="text-light">خلال 30 يوم</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات مالية -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h4 id="total-stock-value" class="text-primary"><?php echo e(number_format($productData['total_stock_value'] ?? 0, 2)); ?> ر.س</h4>
                                <p class="mb-0">إجمالي قيمة المخزون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h4 id="total-sales-revenue" class="text-success"><?php echo e(number_format($productData['total_sales_revenue'] ?? 0, 2)); ?> ر.س</h4>
                                <p class="mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h4 id="avg-turnover-ratio" class="text-warning"><?php echo e($productData['avg_turnover_ratio'] ?? 0); ?></h4>
                                <p class="mb-0">
                                    متوسط دوران المخزون
                                    <button type="button" class="btn btn-sm btn-link p-0 ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="كم مرة تم بيع المخزون خلال الفترة المحددة. معدل عالي = مبيعات سريعة">
                                        <i class="fas fa-info-circle text-muted"></i>
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h4 id="active-percentage" class="text-info"><?php echo e($productData['active_percentage'] ?? 0); ?>%</h4>
                                <p class="mb-0">نسبة المنتجات النشطة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التبويبات -->
                <ul class="nav nav-pills mb-3" id="product-analytics-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="top-selling-tab" data-bs-toggle="pill" data-bs-target="#top-selling" type="button" role="tab">
                            <i class="fas fa-chart-line me-2"></i>أفضل المنتجات مبيعاً
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="category-analysis-tab" data-bs-toggle="pill" data-bs-target="#category-analysis" type="button" role="tab">
                            <i class="fas fa-layer-group me-2"></i>تحليل الفئات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-turnover-tab" data-bs-toggle="pill" data-bs-target="#inventory-turnover" type="button" role="tab">
                            <i class="fas fa-sync-alt me-2"></i>دوران المخزون
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="stagnant-products-tab" data-bs-toggle="pill" data-bs-target="#stagnant-products" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle me-2"></i>المنتجات الراكدة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="expiring-products-tab" data-bs-toggle="pill" data-bs-target="#expiring-products" type="button" role="tab">
                            <i class="fas fa-clock me-2"></i>انتهاء الصلاحية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="receipt-orders-tab" data-bs-toggle="pill" data-bs-target="#receipt-orders" type="button" role="tab">
                            <i class="fas fa-truck me-2"></i>أوامر الاستلام
                        </button>
                    </li>
                </ul>

                <!-- محتوى التبويبات -->
                <div class="tab-content" id="product-analytics-content">
                    <!-- تبويب أفضل المنتجات مبيعاً -->
                    <div class="tab-pane fade show active" id="top-selling" role="tabpanel">
                        <div id="top-selling-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="top-selling-content" style="display: none;">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>أفضل المنتجات مبيعاً</h5>
                                    <small class="text-muted" id="top-selling-period-info">الفترة: --</small>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="top-selling-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الباركود</th>
                                                    <th>الفئة</th>
                                                    <th>الوحدة</th>
                                                    <th>الكمية المباعة</th>
                                                    <th>إجمالي الإيرادات</th>
                                                    <th>عدد الطلبات</th>
                                                    <th>متوسط السعر</th>
                                                    <th>هامش الربح</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="10" class="text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        جاري تحميل بيانات أفضل المنتجات...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب تحليل الفئات -->
                    <div class="tab-pane fade" id="category-analysis" role="tabpanel">
                        <div id="category-analysis-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="category-analysis-content" style="display: none;">
                            <!-- محتوى تحليل الفئات سيتم إضافته لاحقاً -->
                        </div>
                    </div>

                    <!-- تبويب دوران المخزون -->
                    <div class="tab-pane fade" id="inventory-turnover" role="tabpanel">
                        <div id="inventory-turnover-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="inventory-turnover-content" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-sync-alt me-2"></i>تحليل دوران المخزون
                                        <small id="inventory-turnover-period-info" class="text-muted ms-3"></small>
                                        <button type="button" class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#turnoverExplanationModal">
                                            <i class="fas fa-question-circle me-1"></i>ما معنى دوران المخزون؟
                                        </button>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="inventory-turnover-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الباركود</th>
                                                    <th>الفئة</th>
                                                    <th>المخزون الحالي</th>
                                                    <th>الكمية المباعة</th>
                                                    <th>معدل الدوران</th>
                                                    <th>أيام التوريد</th>
                                                    <th>قيمة المخزون</th>
                                                    <th>سرعة الحركة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="10" class="text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        جاري تحميل بيانات دوران المخزون...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المنتجات الراكدة -->
                    <div class="tab-pane fade" id="stagnant-products" role="tabpanel">
                        <div id="stagnant-products-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="stagnant-products-content" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>المنتجات الراكدة
                                        <small id="stagnant-products-period-info" class="text-muted ms-3"></small>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="stagnant-products-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الباركود</th>
                                                    <th>الفئة</th>
                                                    <th>المخزون الحالي</th>
                                                    <th>قيمة المخزون</th>
                                                    <th>أيام منذ التحديث</th>
                                                    <th>تاريخ الانتهاء</th>
                                                    <th>أيام للانتهاء</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="9" class="text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        جاري تحميل بيانات المنتجات الراكدة...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب انتهاء الصلاحية -->
                    <div class="tab-pane fade" id="expiring-products" role="tabpanel">
                        <div id="expiring-products-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="expiring-products-content" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-clock me-2"></i>المنتجات القريبة من انتهاء الصلاحية
                                        <small id="expiring-products-period-info" class="text-muted ms-3"></small>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="expiring-products-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الباركود</th>
                                                    <th>الفئة</th>
                                                    <th>المخزون الحالي</th>
                                                    <th>تاريخ الانتهاء</th>
                                                    <th>أيام للانتهاء</th>
                                                    <th>مستوى المخاطر</th>
                                                    <th>الخسارة المحتملة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="9" class="text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        جاري تحميل بيانات المنتجات منتهية الصلاحية...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب أوامر الاستلام -->
                    <div class="tab-pane fade" id="receipt-orders" role="tabpanel">
                        <div id="receipt-orders-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                            </div>
                        </div>
                        <div id="receipt-orders-content" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-truck me-2"></i>تحليل أوامر الاستلام
                                        <small id="receipt-orders-period-info" class="text-muted ms-3"></small>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="receipt-orders-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الباركود</th>
                                                    <th>الفئة</th>
                                                    <th>نوع الأمر</th>
                                                    <th>الكمية المستلمة</th>
                                                    <th>إجمالي التكلفة</th>
                                                    <th>متوسط التكلفة</th>
                                                    <th>عدد الأوامر</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="9" class="text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        جاري تحميل بيانات أوامر الاستلام...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- نافذة شرح دوران المخزون -->
<div class="modal fade" id="turnoverExplanationModal" tabindex="-1" aria-labelledby="turnoverExplanationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="turnoverExplanationModalLabel">
                    <i class="fas fa-sync-alt me-2"></i>شرح مفهوم دوران المخزون
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-lightbulb me-2"></i>ما هو دوران المخزون؟
                        </h6>
                        <p class="mb-4">
                            <strong>دوران المخزون</strong> هو مؤشر مالي مهم يقيس <span class="text-success fw-bold">كم مرة تم بيع المخزون واستبداله خلال فترة زمنية محددة</span>.
                            يساعد هذا المؤشر في فهم مدى كفاءة إدارة المخزون وسرعة حركة المنتجات.
                        </p>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-calculator me-2"></i>كيف يتم حساب معدل الدوران؟
                        </h6>
                        <div class="alert alert-light border">
                            <div class="text-center mb-3">
                                <h5 class="text-dark">معدل دوران المخزون = <span class="text-success">الكمية المباعة</span> ÷ <span class="text-info">المخزون الحالي</span></h5>
                            </div>
                            <div class="row text-center">
                                <div class="col-md-6">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <h6 class="text-success">الكمية المباعة</h6>
                                        <p class="mb-0 small">إجمالي الكمية التي تم بيعها خلال الفترة المحددة</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-3 bg-info bg-opacity-10 rounded">
                                        <h6 class="text-info">المخزون الحالي</h6>
                                        <p class="mb-0 small">الكمية المتوفرة حالياً في المستودع</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-chart-line me-2"></i>تفسير النتائج:
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-success mb-3">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-arrow-up me-2"></i>معدل دوران عالي</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0">
                                            <li><strong>معدل ≥ 2:</strong> سريع جداً 🚀</li>
                                            <li><strong>معدل ≥ 1:</strong> سريع ⚡</li>
                                        </ul>
                                        <hr>
                                        <p class="text-success mb-0"><strong>يعني:</strong> المنتج يُباع بسرعة ويحتاج تجديد مستمر</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning mb-3">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0"><i class="fas fa-arrow-down me-2"></i>معدل دوران منخفض</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0">
                                            <li><strong>معدل < 0.5:</strong> بطيء 🐌</li>
                                            <li><strong>معدل = 0:</strong> راكد ⛔</li>
                                        </ul>
                                        <hr>
                                        <p class="text-warning mb-0"><strong>يعني:</strong> المنتج يُباع ببطء أو لا يُباع إطلاقاً</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-clock me-2"></i>أيام التوريد:
                        </h6>
                        <div class="alert alert-info">
                            <h6>أيام التوريد = المخزون الحالي ÷ (الكمية المباعة ÷ عدد أيام الفترة)</h6>
                            <p class="mb-0">
                                <strong>المعنى:</strong> كم يوماً سيستمر المخزون الحالي بناءً على معدل البيع الحالي؟
                            </p>
                            <ul class="mt-2 mb-0">
                                <li><span class="text-success">≤ 30 يوم:</span> ممتاز - مخزون مثالي</li>
                                <li><span class="text-warning">31-60 يوم:</span> جيد - مخزون مقبول</li>
                                <li><span class="text-danger">> 60 يوم:</span> يحتاج تحسين - مخزون زائد</li>
                            </ul>
                        </div>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-target me-2"></i>فوائد تحليل دوران المخزون:
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-money-bill-wave text-success fa-2x mb-2"></i>
                                    <h6>تحسين التدفق النقدي</h6>
                                    <p class="small mb-0">تجنب تجميد الأموال في مخزون راكد</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-chart-line text-info fa-2x mb-2"></i>
                                    <h6>تحديد المنتجات الناجحة</h6>
                                    <p class="small mb-0">التركيز على المنتجات سريعة الحركة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                                    <h6>تجنب الخسائر</h6>
                                    <p class="small mb-0">اكتشاف المنتجات الراكدة مبكراً</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-info" onclick="window.open('https://ar.wikipedia.org/wiki/دوران_المخزون', '_blank')">
                    <i class="fas fa-external-link-alt me-2"></i>اقرأ المزيد
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تحديد القيم المختارة في الفلاتر من URL
    const urlParams = new URLSearchParams(window.location.search);
    const warehouseId = urlParams.get('warehouse_id');
    const categoryId = urlParams.get('category_id');
    const dateFrom = urlParams.get('date_from');
    const dateTo = urlParams.get('date_to');

    if (warehouseId) {
        $('#warehouse-filter').val(warehouseId);
    }
    if (categoryId) {
        $('#category-filter').val(categoryId);
    }
    if (dateFrom) {
        $('#date-from').val(dateFrom);
    }
    if (dateTo) {
        $('#date-to').val(dateTo);
    }

    // تحميل البيانات عند تحميل الصفحة
    loadTopSellingProducts();

    // أحداث الفلاتر
    $('#refresh-data').on('click', function() {
        loadActiveTab();
    });

    $('#warehouse-filter, #category-filter, #date-from, #date-to').on('change', function() {
        // إعادة تحميل الصفحة مع المعاملات الجديدة
        const warehouseId = $('#warehouse-filter').val();
        const categoryId = $('#category-filter').val();
        const dateFrom = $('#date-from').val();
        const dateTo = $('#date-to').val();

        // بناء URL جديد مع المعاملات
        const currentUrl = new URL(window.location.href);
        if (warehouseId) {
            currentUrl.searchParams.set('warehouse_id', warehouseId);
        } else {
            currentUrl.searchParams.delete('warehouse_id');
        }
        if (categoryId) {
            currentUrl.searchParams.set('category_id', categoryId);
        } else {
            currentUrl.searchParams.delete('category_id');
        }
        if (dateFrom) {
            currentUrl.searchParams.set('date_from', dateFrom);
        }
        if (dateTo) {
            currentUrl.searchParams.set('date_to', dateTo);
        }

        // إعادة تحميل الصفحة مع المعاملات الجديدة
        window.location.href = currentUrl.toString();
    });

    // أحداث التبويبات
    $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
        const target = $(e.target).data('bs-target');

        if (target === '#top-selling') {
            loadTopSellingProducts();
        } else if (target === '#category-analysis') {
            loadCategoryAnalysis();
        } else if (target === '#inventory-turnover') {
            loadInventoryTurnover();
        } else if (target === '#stagnant-products') {
            loadStagnantProducts();
        } else if (target === '#expiring-products') {
            loadExpiringProducts();
        } else if (target === '#receipt-orders') {
            loadReceiptOrders();
        }
    });

    // تحديد التبويب النشط
    function loadActiveTab() {
        const activeTab = $('.nav-link.active').data('bs-target');

        if (activeTab === '#top-selling') {
            loadTopSellingProducts();
        } else if (activeTab === '#category-analysis') {
            loadCategoryAnalysis();
        } else if (activeTab === '#inventory-turnover') {
            loadInventoryTurnover();
        } else if (activeTab === '#stagnant-products') {
            loadStagnantProducts();
        } else if (activeTab === '#expiring-products') {
            loadExpiringProducts();
        } else if (activeTab === '#receipt-orders') {
            loadReceiptOrders();
        }
    }

    // تحميل أفضل المنتجات مبيعاً
    function loadTopSellingProducts() {
        $('#top-selling-loading').show();
        $('#top-selling-content').hide();

        $.ajax({
            url: '/financial-operations/product-analytics/top-selling',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                category_id: $('#category-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث معلومات الفترة
                    if (data.period) {
                        const periodText = `من ${data.period.from} إلى ${data.period.to}`;
                        $('#top-selling-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول أفضل المنتجات
                    let productsTableHtml = '';
                    if (data.top_products && data.top_products.length > 0) {
                        data.top_products.forEach((product, index) => {
                            const profitMargin = parseFloat(product.profit_margin || 0);
                            const profitClass = profitMargin > 0 ? 'text-success' : 'text-danger';

                            productsTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${product.name || 'غير محدد'}</div>
                                    </td>
                                    <td><code>${product.sku || '--'}</code></td>
                                    <td><span class="badge bg-secondary">${product.category_name || 'غير محدد'}</span></td>
                                    <td>${product.unit_name || '--'}</td>
                                    <td><span class="badge bg-primary">${parseInt(product.total_sold || 0).toLocaleString('ar-SA')}</span></td>
                                    <td class="fw-bold text-success">${parseFloat(product.total_revenue || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td>${parseInt(product.order_count || 0).toLocaleString('ar-SA')}</td>
                                    <td>${parseFloat(product.avg_selling_price || 0).toFixed(2)} ر.س</td>
                                    <td class="${profitClass}">${profitMargin.toFixed(2)} ر.س</td>
                                </tr>
                            `;
                        });
                    } else {
                        productsTableHtml = '<tr><td colspan="10" class="text-center py-4">لا توجد بيانات للمنتجات في الفترة المحددة</td></tr>';
                    }
                    $('#top-selling-table tbody').html(productsTableHtml);

                    $('#top-selling-loading').hide();
                    $('#top-selling-content').show();
                } else {
                    $('#top-selling-loading').hide();
                    $('#top-selling-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات أفضل المنتجات: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات أفضل المنتجات:', xhr.responseText);
                $('#top-selling-loading').hide();
                $('#top-selling-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                    </div>
                `).show();
            }
        });
    }

    // باقي الدوال ستكون مؤقتة
    function loadCategoryAnalysis() {
        $('#category-analysis-loading').show();
        $('#category-analysis-content').hide();
        setTimeout(() => {
            $('#category-analysis-content').html('<p class="text-center">سيتم إضافة محتوى تحليل الفئات قريباً</p>');
            $('#category-analysis-loading').hide();
            $('#category-analysis-content').show();
        }, 1000);
    }

    function loadInventoryTurnover() {
        $('#inventory-turnover-loading').show();
        $('#inventory-turnover-content').hide();

        $.ajax({
            url: '/financial-operations/product-analytics/inventory-turnover',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                category_id: $('#category-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث معلومات الفترة
                    if (data.period) {
                        const periodText = `من ${data.period.from} إلى ${data.period.to}`;
                        $('#inventory-turnover-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول دوران المخزون
                    let turnoverTableHtml = '';
                    if (data.inventory_turnover && data.inventory_turnover.length > 0) {
                        data.inventory_turnover.forEach((item, index) => {
                            const turnoverRatio = parseFloat(item.turnover_ratio || 0);
                            const daysOfSupply = parseInt(item.days_of_supply || 0);

                            // تحديد ألوان المؤشرات
                            const turnoverClass = turnoverRatio >= 1 ? 'text-success' :
                                                 turnoverRatio >= 0.5 ? 'text-warning' : 'text-danger';

                            const daysClass = daysOfSupply <= 30 ? 'text-success' :
                                             daysOfSupply <= 60 ? 'text-warning' : 'text-danger';

                            // تحديد لون سرعة الحركة
                            let speedClass = 'bg-secondary';
                            if (turnoverRatio >= 2) speedClass = 'bg-success';
                            else if (turnoverRatio >= 1) speedClass = 'bg-primary';
                            else if (turnoverRatio >= 0.5) speedClass = 'bg-warning';
                            else if (turnoverRatio > 0) speedClass = 'bg-danger';

                            turnoverTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${item.name || 'غير محدد'}</div>
                                    </td>
                                    <td><code>${item.sku || '--'}</code></td>
                                    <td><span class="badge bg-secondary">${item.category_name || 'غير محدد'}</span></td>
                                    <td><span class="badge bg-info">${parseInt(item.current_stock || 0).toLocaleString('ar-SA')}</span></td>
                                    <td><span class="badge bg-primary">${parseInt(item.total_sold || 0).toLocaleString('ar-SA')}</span></td>
                                    <td class="${turnoverClass} fw-bold">${turnoverRatio.toFixed(2)}</td>
                                    <td class="${daysClass} fw-bold">${daysOfSupply > 999 ? '∞' : daysOfSupply} يوم</td>
                                    <td class="fw-bold text-success">${parseFloat(item.stock_value || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td><span class="badge ${speedClass}">${getMovementSpeed(turnoverRatio)}</span></td>
                                </tr>
                            `;
                        });
                    } else {
                        turnoverTableHtml = '<tr><td colspan="10" class="text-center py-4">لا توجد بيانات لدوران المخزون في الفترة المحددة</td></tr>';
                    }
                    $('#inventory-turnover-table tbody').html(turnoverTableHtml);

                    $('#inventory-turnover-loading').hide();
                    $('#inventory-turnover-content').show();
                } else {
                    $('#inventory-turnover-loading').hide();
                    $('#inventory-turnover-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات دوران المخزون: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات دوران المخزون:', xhr.responseText);
                $('#inventory-turnover-loading').hide();
                $('#inventory-turnover-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                    </div>
                `).show();
            }
        });
    }

    // دالة مساعدة لتحديد سرعة الحركة
    function getMovementSpeed(turnoverRatio) {
        if (turnoverRatio >= 2) return 'سريع جداً';
        if (turnoverRatio >= 1) return 'سريع';
        if (turnoverRatio >= 0.5) return 'متوسط';
        if (turnoverRatio > 0) return 'بطيء';
        return 'راكد';
    }

    function loadStagnantProducts() {
        $('#stagnant-products-loading').show();
        $('#stagnant-products-content').hide();

        $.ajax({
            url: '/financial-operations/product-analytics/stagnant-products',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                category_id: $('#category-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث معلومات الفترة
                    if (data.period) {
                        const periodText = `من ${data.period.from} إلى ${data.period.to}`;
                        $('#stagnant-products-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول المنتجات الراكدة
                    let stagnantTableHtml = '';
                    if (data.stagnant_products && data.stagnant_products.length > 0) {
                        data.stagnant_products.forEach((product, index) => {
                            const daysToExpiry = parseInt(product.days_to_expiry || 0);
                            const daysSinceUpdate = parseInt(product.days_since_update || 0);

                            // تحديد ألوان التحذير
                            const expiryClass = daysToExpiry <= 0 ? 'text-danger' :
                                               daysToExpiry <= 30 ? 'text-warning' : 'text-success';

                            const updateClass = daysSinceUpdate > 90 ? 'text-danger' :
                                               daysSinceUpdate > 30 ? 'text-warning' : 'text-info';

                            const expiryDate = product.expiry_date ? new Date(product.expiry_date).toLocaleDateString('ar-SA') : '--';
                            const expiryText = daysToExpiry <= 0 ? 'منتهي' :
                                              daysToExpiry > 999 ? '--' : `${daysToExpiry} يوم`;

                            stagnantTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${product.name || 'غير محدد'}</div>
                                    </td>
                                    <td><code>${product.sku || '--'}</code></td>
                                    <td><span class="badge bg-secondary">${product.category_name || 'غير محدد'}</span></td>
                                    <td><span class="badge bg-warning">${parseInt(product.current_stock || 0).toLocaleString('ar-SA')}</span></td>
                                    <td class="fw-bold text-danger">${parseFloat(product.stock_value || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td class="${updateClass}">${daysSinceUpdate} يوم</td>
                                    <td>${expiryDate}</td>
                                    <td class="${expiryClass} fw-bold">${expiryText}</td>
                                </tr>
                            `;
                        });
                    } else {
                        stagnantTableHtml = '<tr><td colspan="9" class="text-center py-4 text-success">🎉 ممتاز! لا توجد منتجات راكدة في الفترة المحددة</td></tr>';
                    }
                    $('#stagnant-products-table tbody').html(stagnantTableHtml);

                    $('#stagnant-products-loading').hide();
                    $('#stagnant-products-content').show();
                } else {
                    $('#stagnant-products-loading').hide();
                    $('#stagnant-products-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات المنتجات الراكدة: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات المنتجات الراكدة:', xhr.responseText);
                $('#stagnant-products-loading').hide();
                $('#stagnant-products-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                    </div>
                `).show();
            }
        });
    }

    function loadExpiringProducts() {
        $('#expiring-products-loading').show();
        $('#expiring-products-content').hide();

        $.ajax({
            url: '/financial-operations/product-analytics/expiring-products',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                category_id: $('#category-filter').val(),
                days_threshold: 30
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث معلومات الفترة
                    $('#expiring-products-period-info').text(`خلال ${data.days_threshold} يوم القادمة`);

                    // تحديث جدول المنتجات منتهية الصلاحية
                    let expiringTableHtml = '';
                    if (data.expiring_products && data.expiring_products.length > 0) {
                        data.expiring_products.forEach((product, index) => {
                            const daysToExpiry = parseInt(product.days_to_expiry || 0);
                            const riskLevel = product.risk_level || 'تحذير';

                            // تحديد ألوان المخاطر
                            let riskClass = 'bg-secondary';
                            if (riskLevel === 'منتهي الصلاحية') riskClass = 'bg-dark';
                            else if (riskLevel === 'خطر عالي') riskClass = 'bg-danger';
                            else if (riskLevel === 'خطر متوسط') riskClass = 'bg-warning';
                            else if (riskLevel === 'تحذير') riskClass = 'bg-info';

                            const daysClass = daysToExpiry <= 0 ? 'text-danger' :
                                             daysToExpiry <= 7 ? 'text-danger' :
                                             daysToExpiry <= 15 ? 'text-warning' : 'text-info';

                            const expiryDate = product.expiry_date ? new Date(product.expiry_date).toLocaleDateString('ar-SA') : '--';
                            const daysText = daysToExpiry <= 0 ? 'منتهي' : `${daysToExpiry} يوم`;

                            expiringTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${product.name || 'غير محدد'}</div>
                                    </td>
                                    <td><code>${product.sku || '--'}</code></td>
                                    <td><span class="badge bg-secondary">${product.category_name || 'غير محدد'}</span></td>
                                    <td><span class="badge bg-warning">${parseInt(product.current_stock || 0).toLocaleString('ar-SA')}</span></td>
                                    <td>${expiryDate}</td>
                                    <td class="${daysClass} fw-bold">${daysText}</td>
                                    <td><span class="badge ${riskClass}">${riskLevel}</span></td>
                                    <td class="fw-bold text-danger">${parseFloat(product.potential_loss || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                </tr>
                            `;
                        });
                    } else {
                        expiringTableHtml = '<tr><td colspan="9" class="text-center py-4 text-success">🎉 ممتاز! لا توجد منتجات قريبة من انتهاء الصلاحية</td></tr>';
                    }
                    $('#expiring-products-table tbody').html(expiringTableHtml);

                    $('#expiring-products-loading').hide();
                    $('#expiring-products-content').show();
                } else {
                    $('#expiring-products-loading').hide();
                    $('#expiring-products-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات المنتجات منتهية الصلاحية: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات المنتجات منتهية الصلاحية:', xhr.responseText);
                $('#expiring-products-loading').hide();
                $('#expiring-products-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                    </div>
                `).show();
            }
        });
    }

    function loadReceiptOrders() {
        $('#receipt-orders-loading').show();
        $('#receipt-orders-content').hide();

        $.ajax({
            url: '/financial-operations/product-analytics/receipt-orders',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                category_id: $('#category-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث معلومات الفترة
                    if (data.period) {
                        const periodText = `من ${data.period.from} إلى ${data.period.to}`;
                        $('#receipt-orders-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول أوامر الاستلام
                    let ordersTableHtml = '';
                    if (data.receipt_analysis && data.receipt_analysis.length > 0) {
                        data.receipt_analysis.forEach((order, index) => {
                            const orderTypeClass = order.order_type === 'استلام بضاعة' ? 'bg-success' :
                                                  order.order_type === 'نقل بضاعة' ? 'bg-info' : 'bg-warning';

                            ordersTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${order.name || 'غير محدد'}</div>
                                    </td>
                                    <td><code>${order.sku || '--'}</code></td>
                                    <td><span class="badge bg-secondary">${order.category_name || 'غير محدد'}</span></td>
                                    <td><span class="badge ${orderTypeClass}">${order.order_type || '--'}</span></td>
                                    <td><span class="badge bg-primary">${parseFloat(order.total_received || 0).toLocaleString('ar-SA')}</span></td>
                                    <td class="fw-bold text-success">${parseFloat(order.total_cost || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td>${parseFloat(order.avg_unit_cost || 0).toFixed(2)} ر.س</td>
                                    <td>${parseInt(order.receipt_count || 0).toLocaleString('ar-SA')}</td>
                                </tr>
                            `;
                        });
                    } else {
                        if (data.message) {
                            ordersTableHtml = `<tr><td colspan="9" class="text-center py-4 text-info">${data.message}</td></tr>`;
                        } else {
                            ordersTableHtml = '<tr><td colspan="9" class="text-center py-4">لا توجد بيانات لأوامر الاستلام في الفترة المحددة</td></tr>';
                        }
                    }
                    $('#receipt-orders-table tbody').html(ordersTableHtml);

                    $('#receipt-orders-loading').hide();
                    $('#receipt-orders-content').show();
                } else {
                    $('#receipt-orders-loading').hide();
                    $('#receipt-orders-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات أوامر الاستلام: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات أوامر الاستلام:', xhr.responseText);
                $('#receipt-orders-loading').hide();
                $('#receipt-orders-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                    </div>
                `).show();
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/financial_operations/product_analytics/index.blade.php ENDPATH**/ ?>