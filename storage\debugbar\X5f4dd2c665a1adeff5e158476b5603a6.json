{"__meta": {"id": "X5f4dd2c665a1adeff5e158476b5603a6", "datetime": "2025-06-30 16:22:26", "utime": **********.850268, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.410778, "end": **********.850284, "duration": 0.43950605392456055, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.410778, "relative_start": 0, "end": **********.783311, "relative_end": **********.783311, "duration": 0.37253284454345703, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.783322, "relative_start": 0.3725440502166748, "end": **********.850286, "relative_end": 1.9073486328125e-06, "duration": 0.06696391105651855, "duration_str": "66.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016959999999999996, "accumulated_duration_str": "16.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8153212, "duration": 0.016079999999999997, "duration_str": "16.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.811}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8400862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.811, "width_percent": 2.653}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8428519, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.465, "width_percent": 2.535}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1459178391 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1459178391\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1057059332 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1057059332\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1786388629 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786388629\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300543798%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktJa1I2UUk4V3J3aTBKYmk1dTNFcFE9PSIsInZhbHVlIjoiU1pGQmltVlVWSktqNi9hSDNGMHFvbW8xVmZPWnlLeStMdFR1U05HS0R0QmNRZlBUY0JmL0loSTAxUVRieGFuQTJlWC9qZWRyak91N1JyZlBuUmFxQlpaSFpib1NmSFB6WjNnZHVLWHZKbVhrc0NRbFp0Mml4MjJUblFka2liZDI4S2tIRmo0WkpiUjJURlUyNGpGTnEzWTlRam1yYzlXK0FJZW5tYVJ0Q3FmajZ0cnFYR3RjWm1ud0xDazhWNm5hR2Z4OGNKZjcrc0FnTEw0VFJpeERUbjkvYUd4UThSOEtHZW1mTTk5R1BGbVhTOGdvTUFJTEQ5VzMyR3JTa3plUGNLYWt1NjU2Q1RFMmgzbDExTW9nK3pJSU92MGZZeUJnNUZucXA3UGo0cXlxWkpmNmJtY0YxVFZwVTJvZlZqVGhCSW9kYWxndDJMZ1VGZXlIRGJYS1FDcUZuS3J3ZkRBeFd4Y0NXdXpRVS8rZXkvL1VlandEN3pQdVo5aUZ3MkZBUFBuTW0zb0EwRjU5TVVaRUlERmJlWHJoUXZZOFc3bjE3enlGYlVxRENFVDVLcjhmbGpwZW56dlkvVW0zVUtydkwvUm5vY3ZFeG1sWUNTbllYcVZYWGU5REdOOEtFeTZkRjdxZE9KQ1R0eGo2NUJHV2YwQXFRTGZtNTVNcnRXWGUiLCJtYWMiOiJlZTJhMjQ4ZTE0Y2ZiOGUxZmE0Zjc1ODI5ZjVkYzQ4ODdkYzc1N2ExMGY4M2ZkYmU2NDc1NmIyM2U4YTdmMDI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBsdFo1N1VJa2ZCaElaNW9ING9hNGc9PSIsInZhbHVlIjoiT1dzd3I0UjBoWlBwd2N1KytXdnRvZVNqdVdjOWhMY3dBMTdSbVBGcmtuU0hqM2FLYzZWQWM4U1hISVFudEJQKzRtd0pta0IxZS9PQ3UzTzNwaDVISytjN0hSUk9EY0Q1aUlWY0kxbzF3eWNDODZFUWlNeHFSek1qZE1Scm5Oa1lEUitGdWk5VDBrTDN2ZGozTVl4U296WGdxbWtveHBKVC83cUtMc2RQc2Jac2NXcGx0dkFnU0FJNnJnS202ZCtSUXpkS1hZQVNySXVnRmx4SXRTWkYvcmhBU2wra2g2MGw3MHBIYUVNY0VHbkxneWJUcHdmQlBkNWFYcmJidWxEaHBuM2IrZGVvTndmZlF2bzVDZ2JNRFNPSXp4c0FGUGJGNUhtQUZ1S0czRU50SWdoM0FuN05vL2lGbk5DVVVENlk2SWNJRkI1U2ZSNHp3RkhCa3AzaHI5V240MEp4KzMrblgzWU9MNmkyNi9WUGdGWEdSYkZEQjhKTjJOYk0rQUVXVUFFQk9lUnMrUTFnV0VLdUdXcHpoNFlUNGVscjFJbm02L1Y3M1EyVytuMmx3bUcxbGlZYnFLV3drMHYxU2lMTjF2ak9qVndDMzZvMGtsbnJxMmpjd21OdFdCak1DdjdZUGNPV2ZOVGtXUmRFaWpUM3Y5d0QzcnhyejR4ZjVBMTYiLCJtYWMiOiJiOWNiZDQ1OGU4ZGQwODQ0MjM2ZGU5NTI3MTVlOWJjNmExOTA4OTAzZGY3MjljYWJjMzdlNjk5N2Q1MTExNThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1611188451 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611188451\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-406687990 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:22:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlViUG4yRnl5ek9DZFZDRGxSUjREdlE9PSIsInZhbHVlIjoiSmU2TTd4Rm1GbWhidWgvbWI2bnN3d3N5c0JaekdRbmVVU0hlMUdTRThpQmRSYzE2MGR5Mmt5SFVQc1BNM0hEcFZ6K1NYaWJlRHdydHV5UkxOQ2lRMlNkUytkNWVnYWNFY3V4dFh1VXptOWZzNE9DVzk4d3p0Nm5Oelg4VjVMeG4rQlRtcWdOTVJydDBMdnFlVG50eXFKMWJVcjNqZEdFT2ppam03T0V5bWFMWC92QXc1MHVLVEtZNzVZTkFacTdtYTE3NlZvSzVIdm1yWXF0SHFoMndHZEdGZ3VBZENPN1RwTVBMN0pTUW5zY2tjSUdobzhaZWNZOVdRZnRaY3NJZTRLVm9TbFRQalRWUEticjhvU0lsMkkyc3cxNDNPMkZSNEp4QzJZMUVRdnJ1dVZUWHFOcDBiZlovQlNGcVRmQlNkTjFJYUpBcUJkTlk0VEsrU09kaVRQTTJvRldyVjdhQXBLd3hRT3RxSExaeXRMV0h4Vy9JQStZSndjZmFHcXZSeUFQOVJRUjRCUnJxQ1JoMTFTMnI1aXpDUFI5ZktURjR1OWloeSt1dGlwSUQvSzBKd1pHQzhpWDVzc3Z0eFN5L0MvZ0UvTmhJTSszVDFQbk10QTVmamlPeExzSHBZWXdxU25OeXpZL3hYN2t1Smtsb0ZDR0F4OC9CczIvdjhVNm8iLCJtYWMiOiI0MzRkN2VkZWFmN2MxMjM3Y2ZmZDZhZmQxNzBjYjE5YjU5Mjk4MzM1MDAwNTYwY2Q0Njc0NDFiZDUxNzNjNTgwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktEMzMvbEYxOVg4TkxkUm1tTEFQc2c9PSIsInZhbHVlIjoiSG5nSy9oUDVwWnRFYlVQY3YrRzNyby96Q0xZMzJ5alptcmV0WmlxZ2NZZGVKSUQ0MlpWRUFhOUFDcXB5dmg1bVdTMXJzN00vY3lmeXdqeHRVdlVpOXVNRStaTURsa0dSMWVRcTA0NnFyQ2I0RkxtV0pyQmFWc3gyY0wwYWNpSUFBMVNvVEpDSC9NcDFVOG5oRndoVHRtbFFPdHdaZmM3Qy90UHJoNm91VTJpSzJlUFQ1TUZ1aW1uUFltNzhUbUxGQm1JdEZSUVBvMlBtSURzbnNSL0N2U2JoaXdWNXhIZ0JUb1hiZklTdGVPQ09QTGdWSS9ocjZ1QWdtSFU1YWd2dktzUCsraW13a3YxWXV5U090OERaYk5TWTh2SURwcW9JY2p4eUxxa251RktFUlphdGU4VHZhQ1Znay9tUHRXenNodDdLK1lyV3N4amthNk4rVm1IckoxRVpHRSs0c0VaempxYWQrOXRnWUZlV1dxTmFXbk4wUHdOOEdlUDlWb3BHL293NXM2bnh6QUNqMHpGZjdBaGsxbjNaLzNVUXY5Q2VzZ2xIcVhnRmhjdmROdnFiMFFqVVFLbDU3SDVnamp0MTV0U2NMSk1Ibng1SU5WR3VSeEJ0UTZESjJFbUV5b2hLRDNSVCtqWUIzUkJPY2ZZclFYL1JVaWk0K2ZnM3RTK0YiLCJtYWMiOiJkOWMyNTY2NzY1NWE1YmQzMTYzMTdlNDJkMzE3YzQzMTQ5ZjdjODg2NzM3NzlhZjZiOTVhYTVkOGEzMWVmOWQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:22:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlViUG4yRnl5ek9DZFZDRGxSUjREdlE9PSIsInZhbHVlIjoiSmU2TTd4Rm1GbWhidWgvbWI2bnN3d3N5c0JaekdRbmVVU0hlMUdTRThpQmRSYzE2MGR5Mmt5SFVQc1BNM0hEcFZ6K1NYaWJlRHdydHV5UkxOQ2lRMlNkUytkNWVnYWNFY3V4dFh1VXptOWZzNE9DVzk4d3p0Nm5Oelg4VjVMeG4rQlRtcWdOTVJydDBMdnFlVG50eXFKMWJVcjNqZEdFT2ppam03T0V5bWFMWC92QXc1MHVLVEtZNzVZTkFacTdtYTE3NlZvSzVIdm1yWXF0SHFoMndHZEdGZ3VBZENPN1RwTVBMN0pTUW5zY2tjSUdobzhaZWNZOVdRZnRaY3NJZTRLVm9TbFRQalRWUEticjhvU0lsMkkyc3cxNDNPMkZSNEp4QzJZMUVRdnJ1dVZUWHFOcDBiZlovQlNGcVRmQlNkTjFJYUpBcUJkTlk0VEsrU09kaVRQTTJvRldyVjdhQXBLd3hRT3RxSExaeXRMV0h4Vy9JQStZSndjZmFHcXZSeUFQOVJRUjRCUnJxQ1JoMTFTMnI1aXpDUFI5ZktURjR1OWloeSt1dGlwSUQvSzBKd1pHQzhpWDVzc3Z0eFN5L0MvZ0UvTmhJTSszVDFQbk10QTVmamlPeExzSHBZWXdxU25OeXpZL3hYN2t1Smtsb0ZDR0F4OC9CczIvdjhVNm8iLCJtYWMiOiI0MzRkN2VkZWFmN2MxMjM3Y2ZmZDZhZmQxNzBjYjE5YjU5Mjk4MzM1MDAwNTYwY2Q0Njc0NDFiZDUxNzNjNTgwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktEMzMvbEYxOVg4TkxkUm1tTEFQc2c9PSIsInZhbHVlIjoiSG5nSy9oUDVwWnRFYlVQY3YrRzNyby96Q0xZMzJ5alptcmV0WmlxZ2NZZGVKSUQ0MlpWRUFhOUFDcXB5dmg1bVdTMXJzN00vY3lmeXdqeHRVdlVpOXVNRStaTURsa0dSMWVRcTA0NnFyQ2I0RkxtV0pyQmFWc3gyY0wwYWNpSUFBMVNvVEpDSC9NcDFVOG5oRndoVHRtbFFPdHdaZmM3Qy90UHJoNm91VTJpSzJlUFQ1TUZ1aW1uUFltNzhUbUxGQm1JdEZSUVBvMlBtSURzbnNSL0N2U2JoaXdWNXhIZ0JUb1hiZklTdGVPQ09QTGdWSS9ocjZ1QWdtSFU1YWd2dktzUCsraW13a3YxWXV5U090OERaYk5TWTh2SURwcW9JY2p4eUxxa251RktFUlphdGU4VHZhQ1Znay9tUHRXenNodDdLK1lyV3N4amthNk4rVm1IckoxRVpHRSs0c0VaempxYWQrOXRnWUZlV1dxTmFXbk4wUHdOOEdlUDlWb3BHL293NXM2bnh6QUNqMHpGZjdBaGsxbjNaLzNVUXY5Q2VzZ2xIcVhnRmhjdmROdnFiMFFqVVFLbDU3SDVnamp0MTV0U2NMSk1Ibng1SU5WR3VSeEJ0UTZESjJFbUV5b2hLRDNSVCtqWUIzUkJPY2ZZclFYL1JVaWk0K2ZnM3RTK0YiLCJtYWMiOiJkOWMyNTY2NzY1NWE1YmQzMTYzMTdlNDJkMzE3YzQzMTQ5ZjdjODg2NzM3NzlhZjZiOTVhYTVkOGEzMWVmOWQ5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:22:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406687990\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1720928230 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720928230\", {\"maxDepth\":0})</script>\n"}}