{"__meta": {"id": "X3ac164134fc49d98d52739c161a91b73", "datetime": "2025-06-30 18:10:27", "utime": **********.365811, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307026.918619, "end": **********.365824, "duration": 0.4472050666809082, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751307026.918619, "relative_start": 0, "end": **********.285988, "relative_end": **********.285988, "duration": 0.3673691749572754, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.285997, "relative_start": 0.36737799644470215, "end": **********.365826, "relative_end": 1.9073486328125e-06, "duration": 0.07982897758483887, "duration_str": "79.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00629, "accumulated_duration_str": "6.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.323048, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.119}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.333348, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.119, "width_percent": 6.995}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3463721, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.114, "width_percent": 7.631}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.348339, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 39.746, "width_percent": 5.564}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.352721, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 45.31, "width_percent": 35.294}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.35725, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.604, "width_percent": 19.396}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-905899716 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905899716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351734, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1076509877 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1076509877\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-456659134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-456659134\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2051282578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2051282578\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-391182482 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ild2bVFsSjRhSTR2eTRHOUJrRlBCb0E9PSIsInZhbHVlIjoiRlBDc0hqRWVGQmNNNHRlV2RSKzhZeityd0Fzb1VXcmJLY1VNV0hkYjJ0aWN3WnZDOUJ3NktnYnV6cGhWS0lLc3RoMnNmallxV3BrZk9uRm9JSEdMbk9WdWVUSlJ3KzYyZ0w5c1d2YmxHTXp4d1Zsa1JSdXUyNDExcVBGb01rRU02OFNNdi80YkFjWnpKTmZXa1dzQ1ZTRW44elMxcTRIekJyRTBNNVRrMkdrdXdVb0c2ckNwb0F3SUVMdmJ6aUc0U2xoTGwxTGhaRDZHVlRBb09TLzc2MGk2eStaK1l5UW9qa0pJL0VXaU0wYklOTEJtQlllSG9BV0dicStYbUk4YVRTT1R1QmlSWnRWb083M1Qzdll4dzNYL0RsK2paVmxFd2drRWJ2alN3YVhoTS85MzhpLzdlR0VxVXJWN0gyek4rQk1QUlVpOVFvWkNFTUZQTW11b1N3VmNiTTk3cEdXdTVjOWtYNnBJWGFiOGdxWHl5QU1iS1d0cTJ0WEwwbXdLU3doVEJuVmhqcFZPL2E2dzBEQW8yQjB4dWk0VTVqZnhMbDVWcTdqbWhDcERXMWI5U1lJckQ1WmxKMnVWQVlxdWUvVHdURFB4WCtxM2hHT3Fmc0pFQTFkQUdpNGxyNmxMVkQvbks2ZVR4R1Q4aXA0T2tlT1ZGQWlzWjYvL2lyQ2UiLCJtYWMiOiI0MWVlOTMxZjFmZjUyYTI1MGZhMDliOTUzMGYzMjIyMTdhZjAyZDQ0OGQwZWEyZGY3MmVmNWY0NmUzNzJmYmI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJEcFlxN0cvL2Rla21TRDNObDF1aHc9PSIsInZhbHVlIjoibkNubmN4alRDa2oyam9zM0doYlJ2ZENRQUs2Ni9na1JQZE9kbWtCSXMyWUpxTXdLMDFjOXdYNm0rK1FXWFF6b1c1TE1YS2ZwZ1ZGSGh3bU1zSWoxRGhKS01adDE0QlRmR3dGcFV4NzVzNEJaVEtORVQyL1Q3eHZxOUhjbHRQTzFSeDlMN1lHcndQQnpsVWIvWmZ5U1JZZUQ1RnFoTGwvdm5tV3NDMkJQZi81bHJHakgrbUNHcFI5dTJYS09EMlg3cDRBN09MK1pZWE9vZk4yK25rTVZjMVFZdUtlTkIxdTRueEVHanp3b0trT1F1NFpqZWxkQ0RIRXFzV3gyV0V6YUt2WlNrUXZSOFRIY0xJMk8zN2hMZ2lpQjNqVnM3RVRzai9FaWtKWWh6VGtQUW13Wk11RWRJWlliTkZ1MVhhcUFrcVdKelVHUm1Od0hFUEk2a2FRQXh4dVlzMjdXK0laZ042ZkpDTjcvS0c1MlBqT0VDVGZoNlRsMVNZdFlFMDBjdFVLMCsvMGxLVmFPbWpta2lHRGRNUC9oWWFEdVJGWXoyUHBSRFVZS0V2dUNLd0FSYUt6TEo0NnlMWDl5SXlnYjVSTm80aFFCNkd6ejI3UnZjQTBJZnhmYVIrbVM3R215SjJHNlFBemJaekg2RWJtN0swTVlVd2liS1dwdmQ5Y2siLCJtYWMiOiI3YzM3YzM2MTI4NmZjYzYyODQwYjMxNmM0NDJkM2QxNDcxMDlhMDg0YWYzNzY3MTU1MzUxOWQ4M2NkOWE4Nzg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391182482\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2040171755 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040171755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1410525962 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNKVzJzdlBQelNrNFlNeFF5YVZEYnc9PSIsInZhbHVlIjoiY05ucmpTNGFyR2Frc2NmaFQ4UXdWRW1VZHFkRUdncnlhUTNqWkJvK3RYemZvQkxvSXlXbk5RWXBmV3phZWFVSzRrakdTR0FGemRtQ3c4QllYZVNTekQ2SGllQnNpVnhyZWlmOWdUdlo4cEcrNlBwSUZLRVNWUTduUGxUdUtIRCtxUGdkckdvRVJQSXVWQWNsQjM2aVdBQWx5MTZGTnNHZUpkN01ubHdwd1EvNE8xeFl0b2V0MmF2N3pRVStrNlYrdDRBY0dvakp4TGpPL2dCUExGSURtSGFoZE13QUl2RHpKVUxBZEp1djB6MTlLeEpsTlVrL3oyM3MwS1I1d2NlOTNIbFZXeEE4blFZZ2k3b0NyZUZaeEpqNHJNNkFUT3V6MlJaTjlPbmw2cS9QaDIySXhRSEhkaC8zMG5GQ2lQelgxQ3g4YXVvMldoYmdsM21ncXB0MkpNdXRyZ09TSGNMZUZGdkJibEpwMWhjOFNCcFVxTXRxY2hDVUQ3dG13Ri84NDFPUk5yTytBS21OMzJwa2huY05OYTgvLzJ4QVFpSjhzdHlWUmI3a1dQUUY1M3RPdktCbTBMZTMydXpkY0dyckhLRjZ0aUtVOXhlUmEvNG1BZ0JPOGJJeERmTDRLU3R1SXJ1UDdEWW90RURXN1IyMi9IQmpSaWFtdFIvY1FCcXoiLCJtYWMiOiJkOTgzOWY0ZTdmZjY5ZGNkOTk2ZjRhOTEzODhhMTdlZGE5ODg1Njg5NzE5NjJkM2IyMWI0NDY4OTRkZWEzMGI3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdBekxwRy9ld3pOT1h5eGVzbjdPTWc9PSIsInZhbHVlIjoiTFpHYVlPMExWeUNrRmFrRnl1QTIzMWU4VldkMnl1aTFlSVFxdlA0Q1FDaWpHenV5N1lDQnFzaEkxeXQzQllNaG5EbWR3eVRRd3F4bzJDTFd5VVAwYVVHVjZvcG1SbWpTeDVXL2Y5YlNWdjN0eGxyNXhqMDFuM29Uck50TmxsTnI3cVBHbm5EMGMvNjNzY1JFQnluYkRSQkFjR1FweWR3cWFWanlLZ0o2OGtoeXVIUlM2YndXTDNRNTQwWThYUm1OMmFYWmJNVWZDd0VIZHF3Uzg0UGxDeEZnYnhWc3RhZitUdkhmakdsc3BiVHhhQVRuM1ZWb0duMktXMndJMUhPVnMxb1p3NVFWbm1seXpCQTY5V01YTzQ1OEJzTVBjZEdkTk1KOTdUTzlxbStxREVuQkpYdDdqUCtiejRQMnB1eGNpc3pjYW93bzJ6WUZmWVYySE9Yd3FFQnNPajRrdjArZVI3NUpmTEZpbk9uQU1vREc1aVhSazk3OWNXQzhOYjh2NFB3VGIzQXZpMHBjYnpaOXUxMksrMmk4djN3WjNTV2xnU2RxTXkxalJhd21McTRTL2w3b3hDVnZTbXRpWVBPMjRYR01FeTVkK2VjaXM5R3FjcHpldUloSENwczJHdXM3Y2Q4WVBTUnRRcUhFV0pMQm5ORUtsbDFLTHFDN1VvQTIiLCJtYWMiOiI3YmMzY2RiMDgzOGQ5MzU2ZDYzZjY2YzY1MmI0ZGNmNTg3MjQ0MjcyZDI4Y2UyYTljNDc4MjhjOTljZTIwMTQxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNKVzJzdlBQelNrNFlNeFF5YVZEYnc9PSIsInZhbHVlIjoiY05ucmpTNGFyR2Frc2NmaFQ4UXdWRW1VZHFkRUdncnlhUTNqWkJvK3RYemZvQkxvSXlXbk5RWXBmV3phZWFVSzRrakdTR0FGemRtQ3c4QllYZVNTekQ2SGllQnNpVnhyZWlmOWdUdlo4cEcrNlBwSUZLRVNWUTduUGxUdUtIRCtxUGdkckdvRVJQSXVWQWNsQjM2aVdBQWx5MTZGTnNHZUpkN01ubHdwd1EvNE8xeFl0b2V0MmF2N3pRVStrNlYrdDRBY0dvakp4TGpPL2dCUExGSURtSGFoZE13QUl2RHpKVUxBZEp1djB6MTlLeEpsTlVrL3oyM3MwS1I1d2NlOTNIbFZXeEE4blFZZ2k3b0NyZUZaeEpqNHJNNkFUT3V6MlJaTjlPbmw2cS9QaDIySXhRSEhkaC8zMG5GQ2lQelgxQ3g4YXVvMldoYmdsM21ncXB0MkpNdXRyZ09TSGNMZUZGdkJibEpwMWhjOFNCcFVxTXRxY2hDVUQ3dG13Ri84NDFPUk5yTytBS21OMzJwa2huY05OYTgvLzJ4QVFpSjhzdHlWUmI3a1dQUUY1M3RPdktCbTBMZTMydXpkY0dyckhLRjZ0aUtVOXhlUmEvNG1BZ0JPOGJJeERmTDRLU3R1SXJ1UDdEWW90RURXN1IyMi9IQmpSaWFtdFIvY1FCcXoiLCJtYWMiOiJkOTgzOWY0ZTdmZjY5ZGNkOTk2ZjRhOTEzODhhMTdlZGE5ODg1Njg5NzE5NjJkM2IyMWI0NDY4OTRkZWEzMGI3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdBekxwRy9ld3pOT1h5eGVzbjdPTWc9PSIsInZhbHVlIjoiTFpHYVlPMExWeUNrRmFrRnl1QTIzMWU4VldkMnl1aTFlSVFxdlA0Q1FDaWpHenV5N1lDQnFzaEkxeXQzQllNaG5EbWR3eVRRd3F4bzJDTFd5VVAwYVVHVjZvcG1SbWpTeDVXL2Y5YlNWdjN0eGxyNXhqMDFuM29Uck50TmxsTnI3cVBHbm5EMGMvNjNzY1JFQnluYkRSQkFjR1FweWR3cWFWanlLZ0o2OGtoeXVIUlM2YndXTDNRNTQwWThYUm1OMmFYWmJNVWZDd0VIZHF3Uzg0UGxDeEZnYnhWc3RhZitUdkhmakdsc3BiVHhhQVRuM1ZWb0duMktXMndJMUhPVnMxb1p3NVFWbm1seXpCQTY5V01YTzQ1OEJzTVBjZEdkTk1KOTdUTzlxbStxREVuQkpYdDdqUCtiejRQMnB1eGNpc3pjYW93bzJ6WUZmWVYySE9Yd3FFQnNPajRrdjArZVI3NUpmTEZpbk9uQU1vREc1aVhSazk3OWNXQzhOYjh2NFB3VGIzQXZpMHBjYnpaOXUxMksrMmk4djN3WjNTV2xnU2RxTXkxalJhd21McTRTL2w3b3hDVnZTbXRpWVBPMjRYR01FeTVkK2VjaXM5R3FjcHpldUloSENwczJHdXM3Y2Q4WVBTUnRRcUhFV0pMQm5ORUtsbDFLTHFDN1VvQTIiLCJtYWMiOiI3YmMzY2RiMDgzOGQ5MzU2ZDYzZjY2YzY1MmI0ZGNmNTg3MjQ0MjcyZDI4Y2UyYTljNDc4MjhjOTljZTIwMTQxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410525962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1066329541 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066329541\", {\"maxDepth\":0})</script>\n"}}