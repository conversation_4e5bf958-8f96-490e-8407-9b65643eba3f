{"__meta": {"id": "Xb478ee0ac05d9e8a392ff5a69306c6d9", "datetime": "2025-06-30 18:57:51", "utime": **********.892717, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.485247, "end": **********.892732, "duration": 0.4074850082397461, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.485247, "relative_start": 0, "end": **********.847939, "relative_end": **********.847939, "duration": 0.36269211769104004, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847948, "relative_start": 0.3627011775970459, "end": **********.892733, "relative_end": 1.1920928955078125e-06, "duration": 0.0447850227355957, "duration_str": "44.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43888576, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00246, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.878999, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.081}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.885175, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 80.081, "width_percent": 19.919}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 10\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1554478194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1554478194\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6Ik11S04vSE4zMERlWG1BdEkwSmpUdlE9PSIsInZhbHVlIjoiU3pjV0xqVW9QRDFTYXM2ZDljMjNIZlN5MnNFOWhRZ3gxNVZSYjJKWkxLdWRxeWRFVlpLRmxuUWxnOW1LSFJva1VqOU5wRlVYeGRuM0x5VTlWY3RBM1JTcXVXdkd6bkFxTXQyTWpNbWI5bG1uOHB1bjVvOTU4d05QRnQvZFBvRE9tZDVjcEJybzhONFdsR3l5ckVnZlAxNW5rY2k1Y3lYc1NzV3orY1hWenlYWCtvZDF3aFBEamEvY3p1c2xwUWVDNkVzNTJ4SGl0YlJlSCtZY0cvUjRybjhsWnJmSWYwN1NlR2duNWhGV2Y1Z2diMFpPRkg4Sll6a0hkY1NTcHd0dGVSTDFpV3dOU01mdHJZWDdSNk9jWE1xcEdCMU9MckpTN2lNNHFIK1FnRS94VlVhNFpVNEtteUdCd015d3BFT0FBQVA5enF1Mk1nSzBGdFlnUWl0OW1sZXdQNXNtdFZsOUowMlJTU0U0ZFZYdU5zL0FTanBER21vODBTTUJvTXh4elRHZGUrMHRUZEx3d09ZaHVSakphS2k2V051ZlZxWVArSjNwSjNvR3Y5dFhuaHoyemFadEhoc2FEWmpEQ25VNndxWlVwZFNiZTltYU5uTWQ0dHpRY0NDVjdTYkxrMjI4WHhYSVdEb0FLQlVLQ0ZqNzN2Y0pzRzRubTJQM2pCYUkiLCJtYWMiOiI4YTlkNTZjNGFkY2I3YzMzNDY0NzYzNjM0YTA3YTc1NDY0MTc4YjczODE2MjEyYzZlYjhhNWVkZDljZjRlOTYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlgyODRiYXltaEF1VUZPZGk1cWxyUEE9PSIsInZhbHVlIjoidTZWTzY4R0pjaHlCVisvV2ZNMXQ3ZXU4NnZPZTBJVWdWRCswZGR4QXBwVnFVYlNsMWV4UFFRSE5iUFFuMUJRMkJ2T0dYL04yclBqWW5rYTNKeFJWQnhQNTZ4MmdWZG9pei9DUE5zRTY0OUhHME9ITjJ0MlN6SVNnZmJLMngvSmtHQW5ZY0RicENpNEJQR2xqYzlrVVRiZVhRdHNRNjUzalZVZ1NLR0NiK2lHUnFIODArUnc3dG5Tc2c2bC81NWhlcTVMbXlQS3F4TThsZmFGcjdIRUFzOUdoaG5nVVpEcWhaaEpnaTc4NTE4MCt6VlhINWFvYjRneENxaWcvVGdQQ3FCTUR4bFRQY2NMLzhxVVlOeGVLUHA3b3pJb1lJYmUrdCtjZUxhdTdpTUhMQ1lGcytIV2htUFNOY3F6czNiTWJDMFpkT3l5dzF1MlhCdERhcDcyeks3Wms1eHQwd3RXd0RTVG5DMDNqZWZCL1podm8vZmMwcGhVa2ljV0NiRTJhdzNKYTVmeCtVdDZucXpObWZpalh6Q3lUTndDanM2YjRMQitESmcrdEJTWktqMHBzYko5WUhQTWN5YXNYaFF1VElVQTBXQWI0enNsdDRLSjF5NjZaczM1L0ZLYU1qVE9vTFdhcmFMa011UTZJZzgzNzByM2lhUWhEQzEwMlJRRnYiLCJtYWMiOiI5NzQ3NGRiYTU1NWM4MThjMGNmZjcxMWZhMTJjNWYwNWYwYmQ2NGE2Zjg1MmQ4NjZkMzUyYWY3YWIxOGViZmU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-907672688 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907672688\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1180615360 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:57:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjF2QVJ3V3hGcG5BYjV6a3JMSUV6L1E9PSIsInZhbHVlIjoiUzU4dGllOHFsanZiTklCRlprMitPVjM2YzJJelNTczdyWkFMMkFINkRIUzViMnN2bEZ2WEpWdmw3YkloLzZJeUxVUnJmaER1V0RsdFllbGM1TzV0ZG9ESXhyWmcxVDc2dXRWR0x0R3c1TVFGOXFTelEvWFhHck41VFhsR25mejlTOXp2T1MvZXJweXZvL29qeXZMMmI2eDdtVEhjWHpMbU1LNW0yS0xxUkVPMkN6QnJOMGk5cVE3dTNNb3U4TzFSSUJSTDliYXlrb1k4Nmk0dkw5ZnAyZ2I1SXg4bnFNQjZVa0J5SjhKMnU0V29qRThxSDZkM0ZDT2VnQmdTNEVkZHZsVXIxeUZ4ME5pOVdOR04vU2RMZ1drWFp1Tmpvank0K2ZtVFRpTXJrYS82VGUyT3FnWW1ZWWFLMUVsOThHVENLNzQ5RWtsQU5ONXZqQ0Z4SnROV3JyZzhtSkc1dWxLbWZXRzJGUTZkTWx5VU1mZnIrRExWRGNkQnJIZ09QNDhyd211UTFxS2ZCM1REWE5iYUpHQVk3WGtDbUVqK1ZNZXFlQTBlR2ozZDQ1T25TS25abmVCODRGbmRYZExPSkx4M2VCNWQ3TmF6WTQvc1B1eGhqNkYzN284aTJyYjRGMFMwcnFBa25lVGVqbG1aRllZRkpvOU9lV2R3N25iUHppcE0iLCJtYWMiOiI3N2E5MDc2YWY1YTVmY2NiMzkyODAwMDdlZmQ4NmQxNjM5M2NiZjQ1NTk4ZDQ2ZWYxYTEwOTdlNWJkMGZjMjhlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:57:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjliUU1wdjc1aFVpTEZpcDlGNlV6aWc9PSIsInZhbHVlIjoiZ1FEckNVcEszOWdiWWw3MWJPREhYaG5NSlRhcVV2Ni9qVGxlNDdaVXpJZVc5ZnFuSnBLalQzZWJzcXZxQWViSHZWaEx3aEZUcUMrZzI2ZSt4YlErTi9yM2dsaWpoRnJJWVVYYk1BbitJU2VCWWNSMllFL0QxRWtMTW1xZVFZYmNtVHN1UWdoYXdmTk5Nd2x6c045OEYxQ3ozc2xIcVFtT0FVNWUxMlJQQUtPV3AxNjdjbWdrb0FCWllRQUdSYkhsVzhySkZjMnQzR005cUxHTUZZRTNMSkllaEg0SXUvTGhJb2k4R1hyQnkzMXdMTG9kK3Z0WXduMkZqTkNiOXBRN0RPUjhVdm15WXBadlFVNzhLUTRjazV1MVZqZk12cmtKNmtBR0wyamZCRGRlU214SU5aYWRlOGw2SW95NU1VMmR0em9QUFA5b2FoYkFUWEVLR0lVNmRkdm8vMUIzTnRKemsyVTRCRWcwaVBsMmJFU2NMVlZOT05QcG9vV1Mrd1lHQ1diMHJEUVhIZ3BscERHZklST0xoV3VrTy8vWTgrcFl6TU1KMG85cWhLYTBvNDlwZ0Ftbno2TkdqOHUyN000ekMxVnJiekhrNU9vOGxiQ0QwWFZyQndTb3VRM096YU5YNytvVGpteHFpbnFleSs4M0lseE9pQm8va2xvVVFSMC8iLCJtYWMiOiJiOTE0ZDYwZDkxZTM4MTRlZWFkZjZkYzQyZDYwNmM0YjYwYmJmYmEwYmY2NDY2YjY1NjZlZDJhMmJmZDIxMTFhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:57:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjF2QVJ3V3hGcG5BYjV6a3JMSUV6L1E9PSIsInZhbHVlIjoiUzU4dGllOHFsanZiTklCRlprMitPVjM2YzJJelNTczdyWkFMMkFINkRIUzViMnN2bEZ2WEpWdmw3YkloLzZJeUxVUnJmaER1V0RsdFllbGM1TzV0ZG9ESXhyWmcxVDc2dXRWR0x0R3c1TVFGOXFTelEvWFhHck41VFhsR25mejlTOXp2T1MvZXJweXZvL29qeXZMMmI2eDdtVEhjWHpMbU1LNW0yS0xxUkVPMkN6QnJOMGk5cVE3dTNNb3U4TzFSSUJSTDliYXlrb1k4Nmk0dkw5ZnAyZ2I1SXg4bnFNQjZVa0J5SjhKMnU0V29qRThxSDZkM0ZDT2VnQmdTNEVkZHZsVXIxeUZ4ME5pOVdOR04vU2RMZ1drWFp1Tmpvank0K2ZtVFRpTXJrYS82VGUyT3FnWW1ZWWFLMUVsOThHVENLNzQ5RWtsQU5ONXZqQ0Z4SnROV3JyZzhtSkc1dWxLbWZXRzJGUTZkTWx5VU1mZnIrRExWRGNkQnJIZ09QNDhyd211UTFxS2ZCM1REWE5iYUpHQVk3WGtDbUVqK1ZNZXFlQTBlR2ozZDQ1T25TS25abmVCODRGbmRYZExPSkx4M2VCNWQ3TmF6WTQvc1B1eGhqNkYzN284aTJyYjRGMFMwcnFBa25lVGVqbG1aRllZRkpvOU9lV2R3N25iUHppcE0iLCJtYWMiOiI3N2E5MDc2YWY1YTVmY2NiMzkyODAwMDdlZmQ4NmQxNjM5M2NiZjQ1NTk4ZDQ2ZWYxYTEwOTdlNWJkMGZjMjhlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:57:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjliUU1wdjc1aFVpTEZpcDlGNlV6aWc9PSIsInZhbHVlIjoiZ1FEckNVcEszOWdiWWw3MWJPREhYaG5NSlRhcVV2Ni9qVGxlNDdaVXpJZVc5ZnFuSnBLalQzZWJzcXZxQWViSHZWaEx3aEZUcUMrZzI2ZSt4YlErTi9yM2dsaWpoRnJJWVVYYk1BbitJU2VCWWNSMllFL0QxRWtMTW1xZVFZYmNtVHN1UWdoYXdmTk5Nd2x6c045OEYxQ3ozc2xIcVFtT0FVNWUxMlJQQUtPV3AxNjdjbWdrb0FCWllRQUdSYkhsVzhySkZjMnQzR005cUxHTUZZRTNMSkllaEg0SXUvTGhJb2k4R1hyQnkzMXdMTG9kK3Z0WXduMkZqTkNiOXBRN0RPUjhVdm15WXBadlFVNzhLUTRjazV1MVZqZk12cmtKNmtBR0wyamZCRGRlU214SU5aYWRlOGw2SW95NU1VMmR0em9QUFA5b2FoYkFUWEVLR0lVNmRkdm8vMUIzTnRKemsyVTRCRWcwaVBsMmJFU2NMVlZOT05QcG9vV1Mrd1lHQ1diMHJEUVhIZ3BscERHZklST0xoV3VrTy8vWTgrcFl6TU1KMG85cWhLYTBvNDlwZ0Ftbno2TkdqOHUyN000ekMxVnJiekhrNU9vOGxiQ0QwWFZyQndTb3VRM096YU5YNytvVGpteHFpbnFleSs4M0lseE9pQm8va2xvVVFSMC8iLCJtYWMiOiJiOTE0ZDYwZDkxZTM4MTRlZWFkZjZkYzQyZDYwNmM0YjYwYmJmYmEwYmY2NDY2YjY1NjZlZDJhMmJmZDIxMTFhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:57:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180615360\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1575079634 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575079634\", {\"maxDepth\":0})</script>\n"}}