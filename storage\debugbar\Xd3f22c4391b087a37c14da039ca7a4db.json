{"__meta": {"id": "Xd3f22c4391b087a37c14da039ca7a4db", "datetime": "2025-06-30 18:49:50", "utime": **********.741026, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.30176, "end": **********.741039, "duration": 0.43927907943725586, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.30176, "relative_start": 0, "end": **********.691235, "relative_end": **********.691235, "duration": 0.38947510719299316, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.691243, "relative_start": 0.3894829750061035, "end": **********.741041, "relative_end": 1.9073486328125e-06, "duration": 0.049798011779785156, "duration_str": "49.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45350680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00283, "accumulated_duration_str": "2.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7200232, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.011}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7314, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.011, "width_percent": 26.855}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.734554, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 85.866, "width_percent": 14.134}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "pos": "array:2 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1655719972 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309344290%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFNcTFaOCtqVFhTWVUwTFhPV09mY0E9PSIsInZhbHVlIjoiZSs3NWZhMFo0QktFcTJzUTB3N2JVblZPTS9BYnBqRmcrUWs0SytBQlg4ZVVuZjAvVncwclg5clk0dTE2VWN2ZVdKbHk2WGlhSXl5VXg0ak11OUdpcTVPZVNpSDAzdTVUdVhwSEVLVWhlU20vbE1kbTc4Qi81c0ZFYXBOSU9QYTVsRlMwendJclBjSU0wYzdlMGd1VGVEYUMwOHgvcTltMVNXc09Cb0dwWTNPNk5ic2VXS0kvSnpNMTZqK09mOWVqUUJiWEl1dE5VV2VSOEYra0wrNG9hWXp1eWZUd3BrUFdvSkd3TXJXNm1CbzZEY2FjZHI1elhjZS9pNC9xQ1VpbzM0cHBLZWlKdENLRFRHN3VXVWVMWTR5YkR5TEN2bFZCZnM0Uy9OVmxFVEtYd3dLdWlJdHhEcVJVdkNqMG05TWF2Q2xwYlBNTW0wVkJISm5xTENSWDhZK3FiQ3NsbTk5RTlSZUtuSXo4Y0pxV0NVYTlsdi83OVF4eHdpVUJCZ3NTSEVMa0M1b3VZcDR1OG0wRTYvY3AzWFBLS3gvWjNUZzhpM1dhYldONmZuK1ZEdVhBcCtLaXJBNi8wKzlZVncrWS8zZVZhLzcwRjlwbmlZOEpPY1ZIb0ZvSEdpV3A4YlVaSXJXbklQdU5DeEVHZ1FaRWM3ZEZaMmtIUTZHRGQvN2ciLCJtYWMiOiJkMjc2YzhlOGEzNzg3YmQ1YTg3MGM5OTY2MmVmMTAxNWVjYzhiZGM3MGIwM2QxMDE5YzlkZTMxZTgxMDFmM2Q3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRFcDdiYXpwN3ZzbWZ3Q3kxOFJwUWc9PSIsInZhbHVlIjoiRkVZN3p2T2oxSnpWYXdPbnNIYm1vUFZTdUovKzUyaEkwaS9RSjR1d0tIQUc5L3lUS2l4bGlObFhNQmtzL3I5VTlYTFhZZzgwVVphaTRUWjgrc3J2RVNMa3hYcGRzZWdac1JLZmtEOEZLMXlkbmhBeHBEV0pqZTg4RUZ2VDdocVhUSEJYbmdya0F5SGNZUVB0SWNYR3ZLcFcway9TQ3RXSE9SbG10aVFZUTNidnpWdGp5U0RWbFJSNEFBY3FmeWtGaTVoUjdGbmgvWFBmNkxKUjZCbURyVmJBcTNNUEUyL3FXcUNuUEVteis2cUE4RFpHUlV6YXhvcXIzTU9mKzZvRE40VlZVZ3dzdWkycFJsRitrR2VWSTkvU1ZvYU1zdEpxK1N5ajR2Uy93RVUwcklRNGo5QVBlV09VcHo0NCtsZWNTMDZjVnp5ZkNWSkc4R092SWdLMXI5ZHFUVEhOQnVGQjZ5ZjFURG8zajhKSkErUFd4Qld2d01tZm4xeU54SW1LQ05PQ0dONzlDcjhBWHEzd2hxemJRWDg5ZW0vVElrNjd6SzRJVzdQK0ZKMjNkdnRXckZxSW1IWUVpMGRncTUvbjJBa21BOGZQL216UFg3YVJRaDEyL0dtZE1SWXBjOStnMnJrb3hxcXJ4Y3BkdVYwRnU4OWp4VFFKTnRXOVdYdEgiLCJtYWMiOiJkMWI1OThlMDI1NTdiZGE0ZjM0MTM1ZWM2N2FiZWVjNDIyNzcxNmVmY2JlZTRkODgxMThkY2M2Nzc4MmVkNzY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655719972\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2121698519 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121698519\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1369758570 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklzNTNtVUdDbDgxdHJqbGNSMU5QeUE9PSIsInZhbHVlIjoia0JYWThqVWVVZThOUW1keEgzNStuRnJXMThHTUNIRTNEcS8yQ3grSlY2RnFFZDEzMStvdlN3V3dQZDdpcndQY3pIK0FwUnh2TkkvSGVtMTY1Ymt1emVLUUxHczlaL3NxSlVCRDhMRWRYM3hIMG5vcm1mVm9LdWxEeHlGa1BjVzJDdnh2UFgxVjZhVmE2RUExNUNLR1ZoWUt2bEUreXNuanhISTBqUnA1QXFKY1h1TjhsRnVyRTgvR3FTaWxmSGtGMlVMeWFON1FIZjJGbmU3Ujh3azloaFNJbi9IaEcvaDJ2TDlsTHdpSlBKWUxMT3I3S2l1ZHZHcDc4YXpQNHAvamhwczA0dVhMU2Z3UVl5cDdIZndnTEdsS0lFZDRmWnM2UFZyd3J1T2pydm40T1N4bVZDdGoyWXdueXpGaGVUQTlUZXFkRWxIR1FxOTZmcUthUlBFNUJuM1Awek5jeWJnbnhXeVB5aW1hcjZrc3dkM0pBczNVbDBtRnp6YmVGREFoNSt3Z2N5Q3RKUnBqVjJSZlVrd2NHQlo4eDVScU1UWDI3VGJMZG04bnpLemtzQmJQNWdldUJSU1pPaEJuZ09COURZWEhUNU1QeDJZMU13aTlCMys2bTB0dU5Xby8rZmthN094MTFWeEF3dzVYamxueWlHVWJpVVBxZit1aU5nT1oiLCJtYWMiOiIzMDBjYTQ0NDFhODQxYjRmOWEwYmM0YWQ4YjU3MDAwZDQ2NGM2ZDg5YTIwYTk3NDQyOTA5MDE5YmZiY2Q1NTg5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InA3dUtTN09mVWdvMVJ0c3U0Vy9kMXc9PSIsInZhbHVlIjoiM21yL1U1d3NtRjM1bmQ3OU1tdzUyRUdTVUFmQTlRMlhyMDNVcWtzR3d4TEZhSXNNTHBqWU1TOFJqRlZNd0R6MkRQS1lmVDlIRlVRbVp6eWI5UkZKSjliV2liZjhKc01CMm9NTDdEeHIyZ1RDTmJCREpEK1dFUGgwVkw2WHBPVExxUkNyeUw0TXJTYmhWNEc2SG5aeDdUdVNyV2N5QVZZeCtiVDd2WWl1MGw3TnRGNmVrTlhjRmtZcDVjaDVFaldEckNZbU5LeXFrQWp5c3h2UStkeFZYWXUvZkk0NjQ4U1BSd2JwYVVMTDAwR0hZWEFhZm45V1RuMnBZY0UzRzg0aVZBWE1QaSt4WWQvVjlNd0k5M0lUREsxTEdIYVdtVHVuV2Z1RzJFcks3TjNZdGtKakFSazllL0NOSmp5VjRrS0pldHlBS2VWUlkzaVN4dzMrbFNJNXREbUxCK3p3QlpETzJibTVBa3AwSVcveGlieFNUY3B2bDdnRnFkV1lBSUxMaGdNZkVVK25zSWdZeTV0NVBWU21yMXBPYlRlOVBHeGJjOUdPeFJlcVhYUGhWUHA1cStKbVhGU2NMRXY3SG9ZVFkyYkUvV1ArNCs1Q3RxamJzVUdZdlFSSHROQyt0K3N0L0diK3YxUHBEb0MydGN2WU9iVkttRFhtRkVOUkZIT3giLCJtYWMiOiI4ZDEzODI2ZjM5ODlkNzQ4OTY1MmI3MTYzYjcxMDgzMTRmOWQ2ODAwMDczODIwYmM4ZWZhNzk1YTU1YzhlMGY4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklzNTNtVUdDbDgxdHJqbGNSMU5QeUE9PSIsInZhbHVlIjoia0JYWThqVWVVZThOUW1keEgzNStuRnJXMThHTUNIRTNEcS8yQ3grSlY2RnFFZDEzMStvdlN3V3dQZDdpcndQY3pIK0FwUnh2TkkvSGVtMTY1Ymt1emVLUUxHczlaL3NxSlVCRDhMRWRYM3hIMG5vcm1mVm9LdWxEeHlGa1BjVzJDdnh2UFgxVjZhVmE2RUExNUNLR1ZoWUt2bEUreXNuanhISTBqUnA1QXFKY1h1TjhsRnVyRTgvR3FTaWxmSGtGMlVMeWFON1FIZjJGbmU3Ujh3azloaFNJbi9IaEcvaDJ2TDlsTHdpSlBKWUxMT3I3S2l1ZHZHcDc4YXpQNHAvamhwczA0dVhMU2Z3UVl5cDdIZndnTEdsS0lFZDRmWnM2UFZyd3J1T2pydm40T1N4bVZDdGoyWXdueXpGaGVUQTlUZXFkRWxIR1FxOTZmcUthUlBFNUJuM1Awek5jeWJnbnhXeVB5aW1hcjZrc3dkM0pBczNVbDBtRnp6YmVGREFoNSt3Z2N5Q3RKUnBqVjJSZlVrd2NHQlo4eDVScU1UWDI3VGJMZG04bnpLemtzQmJQNWdldUJSU1pPaEJuZ09COURZWEhUNU1QeDJZMU13aTlCMys2bTB0dU5Xby8rZmthN094MTFWeEF3dzVYamxueWlHVWJpVVBxZit1aU5nT1oiLCJtYWMiOiIzMDBjYTQ0NDFhODQxYjRmOWEwYmM0YWQ4YjU3MDAwZDQ2NGM2ZDg5YTIwYTk3NDQyOTA5MDE5YmZiY2Q1NTg5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InA3dUtTN09mVWdvMVJ0c3U0Vy9kMXc9PSIsInZhbHVlIjoiM21yL1U1d3NtRjM1bmQ3OU1tdzUyRUdTVUFmQTlRMlhyMDNVcWtzR3d4TEZhSXNNTHBqWU1TOFJqRlZNd0R6MkRQS1lmVDlIRlVRbVp6eWI5UkZKSjliV2liZjhKc01CMm9NTDdEeHIyZ1RDTmJCREpEK1dFUGgwVkw2WHBPVExxUkNyeUw0TXJTYmhWNEc2SG5aeDdUdVNyV2N5QVZZeCtiVDd2WWl1MGw3TnRGNmVrTlhjRmtZcDVjaDVFaldEckNZbU5LeXFrQWp5c3h2UStkeFZYWXUvZkk0NjQ4U1BSd2JwYVVMTDAwR0hZWEFhZm45V1RuMnBZY0UzRzg0aVZBWE1QaSt4WWQvVjlNd0k5M0lUREsxTEdIYVdtVHVuV2Z1RzJFcks3TjNZdGtKakFSazllL0NOSmp5VjRrS0pldHlBS2VWUlkzaVN4dzMrbFNJNXREbUxCK3p3QlpETzJibTVBa3AwSVcveGlieFNUY3B2bDdnRnFkV1lBSUxMaGdNZkVVK25zSWdZeTV0NVBWU21yMXBPYlRlOVBHeGJjOUdPeFJlcVhYUGhWUHA1cStKbVhGU2NMRXY3SG9ZVFkyYkUvV1ArNCs1Q3RxamJzVUdZdlFSSHROQyt0K3N0L0diK3YxUHBEb0MydGN2WU9iVkttRFhtRkVOUkZIT3giLCJtYWMiOiI4ZDEzODI2ZjM5ODlkNzQ4OTY1MmI3MTYzYjcxMDgzMTRmOWQ2ODAwMDczODIwYmM4ZWZhNzk1YTU1YzhlMGY4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369758570\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}