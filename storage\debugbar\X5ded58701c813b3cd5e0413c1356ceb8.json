{"__meta": {"id": "X5ded58701c813b3cd5e0413c1356ceb8", "datetime": "2025-06-30 18:49:38", "utime": **********.028303, "method": "GET", "uri": "/add-to-cart/2303/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.611621, "end": **********.028316, "duration": 0.41669511795043945, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.611621, "relative_start": 0, "end": **********.95106, "relative_end": **********.95106, "duration": 0.33943915367126465, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.951069, "relative_start": 0.3394482135772705, "end": **********.028318, "relative_end": 1.9073486328125e-06, "duration": 0.07724881172180176, "duration_str": "77.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48673800, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00676, "accumulated_duration_str": "6.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.983945, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 23.521}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9930391, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 23.521, "width_percent": 4.734}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.005654, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 28.254, "width_percent": 9.615}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.007551, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.87, "width_percent": 7.249}, {"sql": "select * from `product_services` where `product_services`.`id` = '2303' limit 1", "type": "query", "params": [], "bindings": ["2303"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.01175, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 45.118, "width_percent": 6.213}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2303 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2303", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.015402, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 51.331, "width_percent": 39.349}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0194142, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 90.68, "width_percent": 9.32}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852533445 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852533445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011011, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2303/pos", "status_code": "<pre class=sf-dump id=sf-dump-1547487779 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1547487779\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1831111769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1831111769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1804695623 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1804695623\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1693933088 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309344290%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg3clEvdjRmR0ZLdm0yS3NMMVF1dWc9PSIsInZhbHVlIjoidHZFSCtFeE1DRGNlQkZxdG4wRnBjM0tGV0c2TXNaTUEwa2NRYTNQc2hab0J2QU95aHpyR1REeHg2NE9pOE9uQkhGdXNodnlUMjNGWUJoS3BjNkY4WGRTdllDeVk0d0VYT1dUZStSa255d29yUjBHQ1dJUzY4WjRpeFFEcHpFNjlRYk5vNC9CZkVwR3dsam1ZdDBUcmRrQmExYUNRODYrV3J3dHBRQnFuOThvcVRaS0Y0TW03Y0t2Q3ZDWXBXOGkzMFp4QTUzMmFLYjE4cTdpeUtFSnJobmZSbVIyRjU1TDlPaGdMUUp4aEo4ZWxNWVQ1bzBtL1pBc2pIL1VRa3ZxRUhQRmNEQ2FOTlZEdnZZemVIYXBzc1VrTEMxRWdVTVJRVzltbE9JeGhiTnFuNjFwM29yMWE3SDZ6RDQvYk9kTm8vSFZaS1E2eXFHVnhWNHFzREx1T2IwbkxaQzRWdHFnV3VkdXh6N1Zua0w3VFlaTDE5ZURpbzJ3amcwWVpQYU9KaW4zS0FFOC9qOHVhSGQ0Qmo0VmJTVjBMNnppVzNkVmFzQmRjTS8vMFFmdGRBcDNWY2hTeWpCbWs2YS9PaThabDFXdUIwaGpySlQ0ZUdGU3NRNDJybjhGeC83b1krci9PMEN2Y0lrSUI4d1VJMVk0WXhjWGk3Y2RYWmFITG5VZDEiLCJtYWMiOiI3YTQ3NDYzYTllZjZhNGIyMjllZWY1MzE0OGI2YzU4NTlkMGM3MTdhMmE3ZjU4NDY0NjIxMWIwMjM0Yjc1ZDk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjQ0c2NDQ2lPMVBuRW9CL0pIMnlicEE9PSIsInZhbHVlIjoiY2hCczUvN1R3M0YvRlkwOEpHampiNnFLclFMeXRwbXNCeGk5VmNtTW5OY2tZWXNENXhhY1AxRzNheDlDeDQzU1pwRHBqU1FFMFRvaStsU2pQdFFHVlRrMEk4cktSODJKTWxiU29lMW9wVDBURU9sVjArZ0pHbVRkbVlqV2txOEZJaGlPK04vT3Y1KzlhbTl3ZXVrZjJaREJvQ2JGMk1rVHNEMTBqUFRmckNLWmpTYk14Z3RBNzYyTXRkQjBQY1hkQktEU0NDYnVPaWV6R0VzQThkUnV1U2VndzF3QWFHUk9seGI1emhVWFAxVjJtRXZpQ3lQYUpBTXQ1eWFmYTFqR1hxOGxFMGNaZExiTVJOUGNSUUpyUHdvLzkzQzNKYkRaOWFhSktTZWpITENrNDR3QjRQTUl3QUpGWFlQNTFxOUREQzRKR0Q1Z29NVDJoa1BNM2VPaUtiMVcvc3lCZk00OGpoMnJ0QXc2TlBXYk5DdTNvTGxaZFFYb0pDWVZsN3ovSlJJamN1WFVGTjM2ODJyQVl4bENCQ0JzWnRNWDZ3LzNsZ2x3M2F3S1hBYk82NTAvK1dGcTJhSEZIdlVNMlJnaGVrSWo2Q0l5eithdmN2ekJCKzExWlZzY1FrYlVHUmwxdzk2SjFDdDJCeEVCQUFNZ2xxc3hGSWY5R1NPNXR4cHgiLCJtYWMiOiIxZGEzYjI2NGE5YjdiODVhMGU3YzI5OTAwZTAwNWQ3YTg0OWY2ZjIzMGE0OWY0MjFkYmIwNzMzNzZhNDUxNWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693933088\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1034704331 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034704331\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-985403658 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilk5b21Xdnp6d29xZjRaWDhSVjZHL2c9PSIsInZhbHVlIjoiSkRlem5BRE9iV2pQd3ZibGg1cnY2K2NsRUZiRGFUeFFuUlJMYlY5MGZJaVRMTk01bUwyMXNTTllHRmhtUHAyWWhJZkhKbW9Mc2pBZVRtbDVPY0xIZ2ljcnVSamdJNU15eW5YVEFwSmh2N0M4MGFlZ1pLbmdxNTNVWkh5RUtnZzl4QVBxTFZDUklkV2VabE1nZXZnQStSOE12VXRBQ0Fpc0pOakd0YlVkQW1EUE1sVDNiY3VjTy90RlFZZEV6Z25PSmNWdTllZWkwSWdtcG5IUy9Sa2JFTm1RNHRXcFVNTzBhQzJYcjBBa1VqYTc2ZUFJVk4yTXhUTVZFUURxbWVIR3dEbXNaOXM0dnlYK09mWXVLb2d4QWZyZmdBQjhSZ242SHd6MzNGa0xuN1lITVlhbFJYNkVoTXBxR3RSTENBVkcvQ2ZQQmJXdVJIdzdzZ0l6eFRkNVo1ZkloS3VNQnI2S2JjVW9ka2k0eEFGMjhCMmlTTzVDMGs0N2lycXI0dWpxQzYyQnBpSW9TSU5IUkM1R2dBUzJTMjY0UEs1a2hVenpxMGJsYi9zc21KOXdBSEl0L3htUFRTTm9sYTFIY21GcEN2RDQzbjg0LzJYa1VjSUdSb3hLOXpvTGxneG82YzR1RUdGWFF1cDNtVHhKdWVaNjlBVWR3R2FCYi9YY1RhMDciLCJtYWMiOiI2ZGFjODJkNmMzYTg2NjE1NDU0MTg4ZjRlM2E4MzQwOTE4MDhkMzJjN2MyZGI0YjI3ZjdkYWMzMzUyOGU2ZGVmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNxUVh5Zks3VXg5RlNXZy9RM25NeXc9PSIsInZhbHVlIjoiYlRQMUd1WXRoc2x6c1JxY2ppNHV6TXlTMnNveWxWaDQwcFlobEIvN25JZFR6cEFtTjFlajBRTzFvcHFzcHp0ZlViUEFZNHg3SlVHYVhBcWJnd25mZnp4UjN1TG5Vd05LOXJma20yUDhiNjNnc3crWmhYb21SOE0wU2NxQXhlcEpJYWR1SEdiSjhSbHpMM3h1bW1rMnMvVDJZU252QXNqM3NkWVEzODRXTGdDNHRiSGUyNitrcUhuQmwveXpnaURiOFBVNzUxVVdNd2hZZ1FxWU01QWd6UUZSaXBVWEZxeEdRZjJiQWEyYk56aWErRVNsQmdtNE9BZGt6WEdNbFpYdS9vMGVDek5qOFBXUzJlL3BSczZQM0ZJcWdGZ3UrSXo0MlFHZTJjdUZyN1JRWkpxOTVqQkRSejMvSVQ5ZDlYcWJWaWtmQ0FlZzBlQzE5ZGxSL0JDK0Q4VXZtOUhia3B2YjVOVDBKaG0rRmQyZFJrclFEUWpQL1BNeHpBVWtsVzdLVTNYemZXNVZRRnUwRUx1U1JIc3pMeUVIenE3TUNibVFHdHFCNzkwYTQ3VlFWZUx2eHFyS0MvTWkwQkF4UFdybDFiZWQ2K3pBdC9pbFpmemJHWG9TdTRZZkZCMVBsdzJNanU1dENmV2hUK3NsUWpVaWRzaHlVb1AxWWxzS1V5dFUiLCJtYWMiOiIxZDY2ODVkMjc5YmFmZDE3MTE5ZDI0YjNkNjhkYThlYWE2ZGI4ZDI1YjFlNDlkNzNjNzYzYTM4NWQzYzQ5NGQyIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilk5b21Xdnp6d29xZjRaWDhSVjZHL2c9PSIsInZhbHVlIjoiSkRlem5BRE9iV2pQd3ZibGg1cnY2K2NsRUZiRGFUeFFuUlJMYlY5MGZJaVRMTk01bUwyMXNTTllHRmhtUHAyWWhJZkhKbW9Mc2pBZVRtbDVPY0xIZ2ljcnVSamdJNU15eW5YVEFwSmh2N0M4MGFlZ1pLbmdxNTNVWkh5RUtnZzl4QVBxTFZDUklkV2VabE1nZXZnQStSOE12VXRBQ0Fpc0pOakd0YlVkQW1EUE1sVDNiY3VjTy90RlFZZEV6Z25PSmNWdTllZWkwSWdtcG5IUy9Sa2JFTm1RNHRXcFVNTzBhQzJYcjBBa1VqYTc2ZUFJVk4yTXhUTVZFUURxbWVIR3dEbXNaOXM0dnlYK09mWXVLb2d4QWZyZmdBQjhSZ242SHd6MzNGa0xuN1lITVlhbFJYNkVoTXBxR3RSTENBVkcvQ2ZQQmJXdVJIdzdzZ0l6eFRkNVo1ZkloS3VNQnI2S2JjVW9ka2k0eEFGMjhCMmlTTzVDMGs0N2lycXI0dWpxQzYyQnBpSW9TSU5IUkM1R2dBUzJTMjY0UEs1a2hVenpxMGJsYi9zc21KOXdBSEl0L3htUFRTTm9sYTFIY21GcEN2RDQzbjg0LzJYa1VjSUdSb3hLOXpvTGxneG82YzR1RUdGWFF1cDNtVHhKdWVaNjlBVWR3R2FCYi9YY1RhMDciLCJtYWMiOiI2ZGFjODJkNmMzYTg2NjE1NDU0MTg4ZjRlM2E4MzQwOTE4MDhkMzJjN2MyZGI0YjI3ZjdkYWMzMzUyOGU2ZGVmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNxUVh5Zks3VXg5RlNXZy9RM25NeXc9PSIsInZhbHVlIjoiYlRQMUd1WXRoc2x6c1JxY2ppNHV6TXlTMnNveWxWaDQwcFlobEIvN25JZFR6cEFtTjFlajBRTzFvcHFzcHp0ZlViUEFZNHg3SlVHYVhBcWJnd25mZnp4UjN1TG5Vd05LOXJma20yUDhiNjNnc3crWmhYb21SOE0wU2NxQXhlcEpJYWR1SEdiSjhSbHpMM3h1bW1rMnMvVDJZU252QXNqM3NkWVEzODRXTGdDNHRiSGUyNitrcUhuQmwveXpnaURiOFBVNzUxVVdNd2hZZ1FxWU01QWd6UUZSaXBVWEZxeEdRZjJiQWEyYk56aWErRVNsQmdtNE9BZGt6WEdNbFpYdS9vMGVDek5qOFBXUzJlL3BSczZQM0ZJcWdGZ3UrSXo0MlFHZTJjdUZyN1JRWkpxOTVqQkRSejMvSVQ5ZDlYcWJWaWtmQ0FlZzBlQzE5ZGxSL0JDK0Q4VXZtOUhia3B2YjVOVDBKaG0rRmQyZFJrclFEUWpQL1BNeHpBVWtsVzdLVTNYemZXNVZRRnUwRUx1U1JIc3pMeUVIenE3TUNibVFHdHFCNzkwYTQ3VlFWZUx2eHFyS0MvTWkwQkF4UFdybDFiZWQ2K3pBdC9pbFpmemJHWG9TdTRZZkZCMVBsdzJNanU1dENmV2hUK3NsUWpVaWRzaHlVb1AxWWxzS1V5dFUiLCJtYWMiOiIxZDY2ODVkMjc5YmFmZDE3MTE5ZDI0YjNkNjhkYThlYWE2ZGI4ZDI1YjFlNDlkNzNjNzYzYTM4NWQzYzQ5NGQyIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985403658\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}