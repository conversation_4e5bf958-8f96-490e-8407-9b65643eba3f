{"__meta": {"id": "X081ed1e19d5a297831c1d7affd56cc61", "datetime": "2025-06-30 17:59:35", "utime": **********.244895, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751306374.835078, "end": **********.244908, "duration": 0.40983009338378906, "duration_str": "410ms", "measures": [{"label": "Booting", "start": 1751306374.835078, "relative_start": 0, "end": **********.162097, "relative_end": **********.162097, "duration": 0.32701897621154785, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162111, "relative_start": 0.32703304290771484, "end": **********.244909, "relative_end": 9.5367431640625e-07, "duration": 0.08279800415039062, "duration_str": "82.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48672232, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00573, "accumulated_duration_str": "5.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.19889, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.208587, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.923, "width_percent": 7.33}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.222104, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 35.253, "width_percent": 7.853}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.223941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.106, "width_percent": 4.887}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.228245, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 47.993, "width_percent": 6.283}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2329361, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 54.276, "width_percent": 39.267}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.236503, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.543, "width_percent": 6.457}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-697465930 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697465930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227377, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 1\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 57.5\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-1892324434 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1892324434\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1069395958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1069395958\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVpZWUxRkNIN1FZeW5icVYzbG1leHc9PSIsInZhbHVlIjoiNzZJRFUySGVRRk1Od1l1TUpWU0MvYVhHRzU5czMwY0ZIQU9NUzdrNy9udEhpQmRPT1NPV1ZYTERDdjRLRkw0NUpKTzFHWTZoT2xuOHNnU2ZjUHJyNWpRQ2JOMnNFbGRqcEtIWHBlbGZneDNLejJLMlhQWDZrd3Z6MzQ1TDVwcm94Rm5walBwRWU1TStxaUpTTDBUN3ZwUTI0NGxodXBpcnhZTUVoL1NhWEZaTDcyUlRZT3FLdGwyS2dlOCtYa0FsOGlwNHRaQzFXbVROMnpuMnJ5N05rRkE4Nlc1KzhlY29SeFZQRHJNUUJEc1V4dUtYbnp1YWZQbXFtZHNiUk14cVAzRXJWMTdsZXdQSThmM0tMTkliQnpFMU1hdDE3cmI4bEdTWE42cGpZU2hhcmpWVEEySGZKR25MTUhGRGNiR2JIblV1K2hwZ3cxbFhrVGxTREZ2VUlZZm1xUmlnbHFQM1dRR1lKaFFJWDk5ejYvbTRnbE1jQXdGcFhubmUzaXhsRnQvQUJ2Q1RCYVJJWkpQOWtEQ3doTUJXNExnYVBVbnI2YlEyb0loelRwUlpOQmx1Tm1pb2puSCtHMEZBYVR2a1RPNXdETGZFT3NrMFNDYVdBYm5nT1J6cE14VDJESGpuSUFEWmQyU08ya1BFS0JNWVlYc2p4SXJaWGI5aDgwbjUiLCJtYWMiOiJiMmQxZmU4Nzg0MjM2M2U3YjkyMDYwODFmZTdhYmY2NTdjNzQzZjVhYmMzYWM2YWU5NmEyOWM5MDc1ZWI1Y2I3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFnMGFNQlI5Rnl3aVFwMDRWYTVOS2c9PSIsInZhbHVlIjoiVWZtLy9TaVUzZDNyOFB5ZlIvaUtabE9Kc2YrRWE2WHNrMi9Va2tNWTlBVXlZUU1EYjZhRnJ6SHFZK3ROUmhKYXh4N1luNzFNNEw1WFp3ZEwvUjROcTNPNTI3NWg5Mkx2eEhINW5BK28wY1pjRzNNeWQrRGFHT1VaWlJHck5BMmR4N1pSZ3FnZHowcllOVVN3NW1zWHZvSDhjNFFLaUl3dERwbjM3Z25QTXlad1pFT1JzOGJiVHBib2hocksrbTlqR1ZnQXVpbnBES2t2bHVLVU9kVmpTK3BMblZ3c2dhVEFtdXZQOGxYNy9ZZmwrTVRqRGZIaEFLbHJkVGRCVmFCM1hzRXlVeE5WbEtJbnNDQi9iamphQnJSaHFMTjZYM0xDcVo3NCsvMElhdTVlaExzeFR0ZC9RSjhlUFBFb0IyVGlGSHFLMGR3MVN5WVVhUjA2Zi80d0NVRnBIZVN2azlRS0F3aEhsOE5kLzVqSjlDbkhzTHcxcDFBbFNFTnVHbkNncjl4clhiRnN4YmEvdUF5UWk2MzlHK2Y0dlVNbDhvNmFoWVVqTnJMUktBQ2ZqM1JrRWdQUUNxOU5tRndIQ1VYb29PdXZWZS9DaWdvaSt5anRqRm9zTjFyc1AvZkQyN2IyWDJ5OUhDQlA3bDRiU1k1RTV6MzdhZGFaZ3B4NVY4RXEiLCJtYWMiOiIwNmNkN2U4NDE2NTQ4OTM0NzcxMWJkNzM5ODBhNzljYmY2ZTlmZjg3YTk0MmU5YjY4ZDk5Nzc5NGNkMjYxOTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-218308074 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 17:59:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJkK25PSWpvWXVCSXpnMExuRVBVSEE9PSIsInZhbHVlIjoicG96YlhqNGV2TERPVUNkVCtNK0Y0czczalhJdEhYOE5EbUtleHExTWZLTWFxNG5pSzRWTkdpRUhoWi9HUEZSSmxqZmdYMjlPREhxbE9IUlIvQ3VDVVlaS01QQk1SNmQ1VnVnVGdTdXdISUVJRmJmb3dxRThMS0dqSUxwdWxsVTNYZ0xRYkFVK1RjQjNDRk43RVVveXJxNUFOaHdZeCs2K29zOGY5YjZlTVFIR1Nwd0E0cXE2QTdJdC9zaFVEcUplZ3poNUFTK0VyenFML0p6cmdtbTZmR3ErakNaTTBEcEd4S0dYcUZqOGhNTHZ5d2E3NUF3cndEeHBEblFtV3lnaDd3QzU3eHhFU0lybU1laWliMFZKU0NkeGJJNkxKbTcvdW1XVCtsVWl0YzZNNHR4Ylh3dFJkYU1MTlJYY2JPbGI3RUQzN0ZIZENlZTJXTXNLS1dzS3RRdnAxMlp5bmc1WkpobzlkbGp2UFRMMmk5REl2NEI0bnZ2WjRZWldiZkdLb0J1NTRrZkRITHRlL1NDajBXNkFMMXViMndIZUwxaHZhSW14RWU3Vjh5VU5KeDJBY3k5ZFRPOFFMRkN3VkE1Mm1kZnhkLzB0aHVXbGNNTktZbXp3N0VWeFROcjJnTEh1bG9XMXVzWFRyYllVTkJwRjNidzNoU3lYUHBsaTMwODMiLCJtYWMiOiJhNGZmZDg4Njg4MDhjMzdiZWIyODQ5MDllNmQ4ZDc5M2JlN2I5ZGUyMWY0MDg1NjEwNjJkMDU5MDgyYzllNzJmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:59:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZYU3VEak5OZGUrRHpJSmhhY0RQRHc9PSIsInZhbHVlIjoiWCtqb3pONXFnejI0bDZQb242cFBGK1hnVktrSGl3cVByRXJ3WG1vRWtXSnJDU201RnoybnNuaHhmL2NzRDlTWjhZeExBTjh0bjVNL3JuTXY0amxMVTNZamVjeHB6QjNYaXZiendveU53T3BiMUdQMVZIb0VFVE0zU2JBSzhwOWE4dHlBenlLV2ozTG9rTHZPV3lHSGQ2aGZhOGw0LzYzeGw2QXpHUnZzUXBSVmFXU2hkV2p4Sm5la2dCb0RCMm1zS0w1N1BnTGRoZXBGSWNhbGtpcStHcCtFbnM3SDUyQUNKYnNoQUNjR1ZjZExZYUpHTzNianl5aXpBalJsM1BZYTFGRUtTakNsNU1yNk1SVkFlc3ZWaWJYejdKWGxmczIxQk03eklQV2tXNjE2NEFCM1RvRW1vTWVLaTB3cmUvbGltRmhiYjJYalorWUppd3ZuUEQrbXlEWTBWc24zUFBNQ2JOcFhVUUZHY3FEak0yS1NIeEhNdSs3ZkJKdzBWaWZyM1ZNTVEyRHc1YkpTTzVUVDhGQXAwNlRNTUtXMFhyMmpJdHFhbUo0aVVmSkF1UmRQTExmKzAzMGpZM1pJbE9MaWdVYkROUmNidGp6S0I5TFlqRjhqTzFnQWM4bVc4TWNUMDRIN1kwSXBzSXBQbTlQRnZmQXcrMDY3Zm5xbGZuWWgiLCJtYWMiOiI4Mzg0NDkwNGRkZTA5N2ZkMjI1MjhkOTBlNTU3ZTBlNGZiNzIxMjkzNzgyMDc5ODNjMzVhOTRhODY1NDFlMDA0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 19:59:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJkK25PSWpvWXVCSXpnMExuRVBVSEE9PSIsInZhbHVlIjoicG96YlhqNGV2TERPVUNkVCtNK0Y0czczalhJdEhYOE5EbUtleHExTWZLTWFxNG5pSzRWTkdpRUhoWi9HUEZSSmxqZmdYMjlPREhxbE9IUlIvQ3VDVVlaS01QQk1SNmQ1VnVnVGdTdXdISUVJRmJmb3dxRThMS0dqSUxwdWxsVTNYZ0xRYkFVK1RjQjNDRk43RVVveXJxNUFOaHdZeCs2K29zOGY5YjZlTVFIR1Nwd0E0cXE2QTdJdC9zaFVEcUplZ3poNUFTK0VyenFML0p6cmdtbTZmR3ErakNaTTBEcEd4S0dYcUZqOGhNTHZ5d2E3NUF3cndEeHBEblFtV3lnaDd3QzU3eHhFU0lybU1laWliMFZKU0NkeGJJNkxKbTcvdW1XVCtsVWl0YzZNNHR4Ylh3dFJkYU1MTlJYY2JPbGI3RUQzN0ZIZENlZTJXTXNLS1dzS3RRdnAxMlp5bmc1WkpobzlkbGp2UFRMMmk5REl2NEI0bnZ2WjRZWldiZkdLb0J1NTRrZkRITHRlL1NDajBXNkFMMXViMndIZUwxaHZhSW14RWU3Vjh5VU5KeDJBY3k5ZFRPOFFMRkN3VkE1Mm1kZnhkLzB0aHVXbGNNTktZbXp3N0VWeFROcjJnTEh1bG9XMXVzWFRyYllVTkJwRjNidzNoU3lYUHBsaTMwODMiLCJtYWMiOiJhNGZmZDg4Njg4MDhjMzdiZWIyODQ5MDllNmQ4ZDc5M2JlN2I5ZGUyMWY0MDg1NjEwNjJkMDU5MDgyYzllNzJmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:59:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZYU3VEak5OZGUrRHpJSmhhY0RQRHc9PSIsInZhbHVlIjoiWCtqb3pONXFnejI0bDZQb242cFBGK1hnVktrSGl3cVByRXJ3WG1vRWtXSnJDU201RnoybnNuaHhmL2NzRDlTWjhZeExBTjh0bjVNL3JuTXY0amxMVTNZamVjeHB6QjNYaXZiendveU53T3BiMUdQMVZIb0VFVE0zU2JBSzhwOWE4dHlBenlLV2ozTG9rTHZPV3lHSGQ2aGZhOGw0LzYzeGw2QXpHUnZzUXBSVmFXU2hkV2p4Sm5la2dCb0RCMm1zS0w1N1BnTGRoZXBGSWNhbGtpcStHcCtFbnM3SDUyQUNKYnNoQUNjR1ZjZExZYUpHTzNianl5aXpBalJsM1BZYTFGRUtTakNsNU1yNk1SVkFlc3ZWaWJYejdKWGxmczIxQk03eklQV2tXNjE2NEFCM1RvRW1vTWVLaTB3cmUvbGltRmhiYjJYalorWUppd3ZuUEQrbXlEWTBWc24zUFBNQ2JOcFhVUUZHY3FEak0yS1NIeEhNdSs3ZkJKdzBWaWZyM1ZNTVEyRHc1YkpTTzVUVDhGQXAwNlRNTUtXMFhyMmpJdHFhbUo0aVVmSkF1UmRQTExmKzAzMGpZM1pJbE9MaWdVYkROUmNidGp6S0I5TFlqRjhqTzFnQWM4bVc4TWNUMDRIN1kwSXBzSXBQbTlQRnZmQXcrMDY3Zm5xbGZuWWgiLCJtYWMiOiI4Mzg0NDkwNGRkZTA5N2ZkMjI1MjhkOTBlNTU3ZTBlNGZiNzIxMjkzNzgyMDc5ODNjMzVhOTRhODY1NDFlMDA0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 19:59:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218308074\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1888069570 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>57.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888069570\", {\"maxDepth\":0})</script>\n"}}