{"__meta": {"id": "Xaa0d8a05007680f3515ad838f87c1449", "datetime": "2025-06-30 16:14:03", "utime": **********.837222, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.33455, "end": **********.837242, "duration": 0.5026919841766357, "duration_str": "503ms", "measures": [{"label": "Booting", "start": **********.33455, "relative_start": 0, "end": **********.778347, "relative_end": **********.778347, "duration": 0.44379711151123047, "duration_str": "444ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.778357, "relative_start": 0.44380712509155273, "end": **********.837244, "relative_end": 2.1457672119140625e-06, "duration": 0.05888700485229492, "duration_str": "58.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00301, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.810456, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.478}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8215702, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.478, "width_percent": 21.927}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.827706, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.405, "width_percent": 21.595}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-867475041 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-867475041\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1246649503 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1246649503\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-816406295 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816406295\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-333753400 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjRGcjh0M21aUWdoYmhZWHdWZmFZaVE9PSIsInZhbHVlIjoiYjkwMGUyazdvV1VlcFRMbWxLZHZIeFhCejVmNHJnMWpPaGowODlSSDV5U2o5Q201NlFEMnJXYWNkKzd5aTBNZDFMZ2RUVC9ZVzl5cGhKUGxCWWlKVWdxVmpYUmZrZkVFMTJVMzFjR2lHS0l5OTIxL0RHd1QvbmVCamN3RS9mVnlicExnTDBmTjJ2cE1VRUZNYXdmSXlkc0pLQ3c2QW5BMnN4b1hDdm9QR0x1eTM3M3hNSWVMUkttRzNTNlE1OU9LMDU5U0poVUQzOHRoYm5JYTEwaEhNK2NIRkRpMk04eTBmYUJSSHVXeS9UOXFkbFVrNWRJbTNVNkovSElVQzZaQTRZcGNRcnkyVkdvYUtZdzFPRUZEb25OQVZ4eGtMd1BaUDZVRUdMc01LTkZIOVJXUHY3TVlRcUtCTms2eTZxYmxLM2NVcm92SmxuNG1YUDRGaG11M3JRdlRVUzhSQXhtU2gxSit4V3IzV0l2L1JVREpvYVFGc1h5ZnBQRHF5Q2ZvcGlsRGEzQlByemQ0bG8wV3V5cjZHQThFazRjNHQvMnE5SE5CRmdUNEtpYkFwWEwveVdZOElyaThGUlJISEk4cDNZVGQ5K2xTQnRHeXBnYndEOWFyTkRYZ2xNUTJJOEhBYWwyT2FnNjdsdHFqbFcvN0JrVXRJWFE5VnhpWUFJcjQiLCJtYWMiOiJlNTZhM2QwMWM2MjE2NzM2ZjIzYTAyYTQwMDhhMWEwZmFiOTA4MWNjYTRjOTlmYWU1ZjgzZGMzY2I2YmFjM2IwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVkM2ZKZTZFUm4zSjY1V24rNjUzdVE9PSIsInZhbHVlIjoiQ2FTR1ZvbHpCTEo4Vjg0QjR0ZzF0NkxIcDMrajBVR1NkM2drVnNCaStDNHRzam5iQUpPUTBCSjBWRnhtcUR4Mk5sMys2ZUp6ZVFFMHo0SWJTQnJNOTZXUEE5OFZ0d2k3Z2JOUW1XejhiSVp3OWVEMmZnQitCN2xYQzFIRmlVNGxQRkhJNUlBQTAwN0hxMThNaEdDdU5hckwrSVREVVJZUmhzdWVmMkNrcXNxRzhNa0N5a2xKM0pvdWc3bjZhL3A2Q1BUdTViRXE5M0ZKc0Uyay91NEpPZGhGMnppc0ZBbFlXcTVCRmlRd3llK25iaGkyMFo0ZDdXcEJHUndRQjhNRW9JS1JNaDVTd3dxcVFtM2dsQXgxZnREV002aXRQazErTHJEdXV4b3RaaFBkb2JYL0tVVFB0NTNrRS9wcy90K1FGMzJBNkRhRGRtWFFJd1hvK3Y1UzdLZk56MDB3aDJHVkl2dllkcHVpcFZJZTlXajM0RjVUcnpIdmtFMkdOZ3FMcHI4MUZqY2NSS0FqVVhjOGVvZlBVN3kyTzBIdWhlQ05GcHc0WlZnUXZqL0l5eFR3MVhmSGszZ0lKNjVCVU1zOGpYb0R4RC90Ky9uclJxUDVVbnk1RUN4V2t5eHUwbkRvWmpOOUY4NytvelpLbFkwRHh3eEhNR293QW5tM3duSVciLCJtYWMiOiIwNDllNDA3MTQxZjdhYTEyNGFjMzFhMWI0MTUxNjA3MDMxOWNlN2EwNDkxOTdlMWY4NTBkYTM4YTA5ZWIxNjAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333753400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-896435191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:14:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJwS2swa1dnenRUQXF3NWdjUE5EMEE9PSIsInZhbHVlIjoiVE5ZcUx3cStheEZCc0lIQVRoN1grazhrM1J6YmFaK0djR2dGYWc2czR6bFcyNGQrWTUzc0hmVVl5OWtjT2xtbitlUE16cjZSeTM1SDl3TnZDNWIrOFpPQmdoQ1RMdzZlRVhBZEtXdHZtNVExV1NmUU5IamtlUXJ4T2pJSDY0bkZCZENLbWZpS0t3MXNKaDFkemhGZlBpSWJ6dHYxSk5KdklSUGFGKzNPWlR4emgvSTNvL3crSmxtUzNnM01ZY2lNalpodmZQVFpSMWR1eVM1NHcxYU9uNytBbUlJdDduM1RmTTdnY2plNlJDbUxMNkJwV2hCZVIxRkpBaC9ZVVBPY2E3Z1dMdVpIdG90UW9XSWtaMlU0ZXZuTkRZc1dsajhvTVhId3Zqby9uV0dvd3FsalBUNXdPUHJsMGhyNUc0MzRxZzJ0WThNc2FDcHQ0N010T2xWYzRWMHVYWnBBVjlvcGZmR0IvNDI2TlY4WUZPbXNQeG4rSnV0UUVqYm1XUnZZZjF0cUUzalI0WjBZYk9XU28vYUg3akFqamJDNGZreXMxckpmK2lTSFhoSzNBWjA2REdkRWtOelZmMTlyMjgvbXhDNytNYXI0SzRZblE4dzZEQnRJVnlHU1dpb3hvcDFrYk45ZEJNZFZYaUlYNmlzUWpwRDhXc2FsYW93TlBRbEgiLCJtYWMiOiI1MmVmNWVjZmRmMGE4MzdjYzNkMzBhODkyNmE0NzNmY2FjZDYyMmRjZDkwYWVmMWI1MzQ3MzE3NWZhNjA2OTAwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:14:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJBZmRFT0pIZ0k5UFAwYVhlQ0tTeHc9PSIsInZhbHVlIjoidzJqSFJaUjllZzlBUFQwSTVCcEltY0xHeTRKRDBWRmNySU1WblByRXk3TUllNzY2RUx4bGhJa0hiUFNKTHRrZ29GdVgyVkd1a2U3bzdRRWxZNng1VkIwU242ODBtZ3ZNbkVzeUZodnVKdlI5VU9qN3NtTHIyRWo3SGl0M09kclBsbENVK0dqWXFwSWVNbTI1clB6aE5KbTQrcHYwSytDRzRXZXVQUlVscVUrb2tBcFprVkEyZE85WnZYVEtoRWYvNlJMYTE0TWlHTzFJMnVQcG5WNUhGL1I2VEQ4dzlEeG1Gd3B2bG1MTDZLMnhSNi9MTFMrSGc5RitSYWJoTzE0RVI3SlB0RnZQM2pjNXFWaEs5K1JGbmdqUk51TG9UL3dVUFpGVVBJSGpiYjE3Rlk1T0tmZVo3cjBuMHBkVllvZFR1elFiV0dQQ2VLQWpLRWl6emZrSmpFUzZ5OGlOWnZPMnVDOVJBZlA4ZW9YSDhZTjc3bFJvWEtiUnk2aTluREJpVS8rcFErT3ZPeGJpcFVEcWNNZEtjTU9uWHF3U0djY0RwYU83L2V1cUdsSzdLWnMrZnJ0cWpRL0pJY09OeTVZeGt6NEFxVlExVVhFcWVFUTM2amU0V0x6cEZuc0ZvMkxMQjduazBOSVZ6SEEvQVY4Szh1T1E3RmZxUFcvWEJyOTgiLCJtYWMiOiIwOGFmODcyMDE4NjJlNzA3NmE1YTUzNWQ5NTQyMmMwM2NhM2JlOTA1YTkxZWNkNjYyNmFlOTQ1MjE0NWE3MTM3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:14:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJwS2swa1dnenRUQXF3NWdjUE5EMEE9PSIsInZhbHVlIjoiVE5ZcUx3cStheEZCc0lIQVRoN1grazhrM1J6YmFaK0djR2dGYWc2czR6bFcyNGQrWTUzc0hmVVl5OWtjT2xtbitlUE16cjZSeTM1SDl3TnZDNWIrOFpPQmdoQ1RMdzZlRVhBZEtXdHZtNVExV1NmUU5IamtlUXJ4T2pJSDY0bkZCZENLbWZpS0t3MXNKaDFkemhGZlBpSWJ6dHYxSk5KdklSUGFGKzNPWlR4emgvSTNvL3crSmxtUzNnM01ZY2lNalpodmZQVFpSMWR1eVM1NHcxYU9uNytBbUlJdDduM1RmTTdnY2plNlJDbUxMNkJwV2hCZVIxRkpBaC9ZVVBPY2E3Z1dMdVpIdG90UW9XSWtaMlU0ZXZuTkRZc1dsajhvTVhId3Zqby9uV0dvd3FsalBUNXdPUHJsMGhyNUc0MzRxZzJ0WThNc2FDcHQ0N010T2xWYzRWMHVYWnBBVjlvcGZmR0IvNDI2TlY4WUZPbXNQeG4rSnV0UUVqYm1XUnZZZjF0cUUzalI0WjBZYk9XU28vYUg3akFqamJDNGZreXMxckpmK2lTSFhoSzNBWjA2REdkRWtOelZmMTlyMjgvbXhDNytNYXI0SzRZblE4dzZEQnRJVnlHU1dpb3hvcDFrYk45ZEJNZFZYaUlYNmlzUWpwRDhXc2FsYW93TlBRbEgiLCJtYWMiOiI1MmVmNWVjZmRmMGE4MzdjYzNkMzBhODkyNmE0NzNmY2FjZDYyMmRjZDkwYWVmMWI1MzQ3MzE3NWZhNjA2OTAwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:14:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJBZmRFT0pIZ0k5UFAwYVhlQ0tTeHc9PSIsInZhbHVlIjoidzJqSFJaUjllZzlBUFQwSTVCcEltY0xHeTRKRDBWRmNySU1WblByRXk3TUllNzY2RUx4bGhJa0hiUFNKTHRrZ29GdVgyVkd1a2U3bzdRRWxZNng1VkIwU242ODBtZ3ZNbkVzeUZodnVKdlI5VU9qN3NtTHIyRWo3SGl0M09kclBsbENVK0dqWXFwSWVNbTI1clB6aE5KbTQrcHYwSytDRzRXZXVQUlVscVUrb2tBcFprVkEyZE85WnZYVEtoRWYvNlJMYTE0TWlHTzFJMnVQcG5WNUhGL1I2VEQ4dzlEeG1Gd3B2bG1MTDZLMnhSNi9MTFMrSGc5RitSYWJoTzE0RVI3SlB0RnZQM2pjNXFWaEs5K1JGbmdqUk51TG9UL3dVUFpGVVBJSGpiYjE3Rlk1T0tmZVo3cjBuMHBkVllvZFR1elFiV0dQQ2VLQWpLRWl6emZrSmpFUzZ5OGlOWnZPMnVDOVJBZlA4ZW9YSDhZTjc3bFJvWEtiUnk2aTluREJpVS8rcFErT3ZPeGJpcFVEcWNNZEtjTU9uWHF3U0djY0RwYU83L2V1cUdsSzdLWnMrZnJ0cWpRL0pJY09OeTVZeGt6NEFxVlExVVhFcWVFUTM2amU0V0x6cEZuc0ZvMkxMQjduazBOSVZ6SEEvQVY4Szh1T1E3RmZxUFcvWEJyOTgiLCJtYWMiOiIwOGFmODcyMDE4NjJlNzA3NmE1YTUzNWQ5NTQyMmMwM2NhM2JlOTA1YTkxZWNkNjYyNmFlOTQ1MjE0NWE3MTM3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:14:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896435191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-515441280 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515441280\", {\"maxDepth\":0})</script>\n"}}