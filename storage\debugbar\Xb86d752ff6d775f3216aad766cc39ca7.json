{"__meta": {"id": "Xb86d752ff6d775f3216aad766cc39ca7", "datetime": "2025-06-30 18:53:32", "utime": **********.921498, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.483266, "end": **********.921515, "duration": 0.438248872756958, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.483266, "relative_start": 0, "end": **********.867689, "relative_end": **********.867689, "duration": 0.38442277908325195, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867697, "relative_start": 0.3844308853149414, "end": **********.921517, "relative_end": 1.9073486328125e-06, "duration": 0.053819894790649414, "duration_str": "53.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45569024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00242, "accumulated_duration_str": "2.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.895578, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.355}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9061291, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.355, "width_percent": 19.835}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.911433, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.19, "width_percent": 12.81}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1993220758 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1993220758\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-842215987 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842215987\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-436605578 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436605578\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1247756929 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309436053%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFvR2F1YkNzSDdFYzBiZWw2QVExMmc9PSIsInZhbHVlIjoiVDZwdGp6cWpXb01MSFJzYWlCcHBMdkk1dXZabU9PWk1mdHRWTWtrOUdWRS9zR0xTM2tmNEYyVSt4azhKbVlWbVpnVHN3cXYvYlA4L0hRK241MTJrM0M0d2g1VFBCVjVPWFBIdUxER3crOTVoemZoR1cxQkc0dWNjNGRNQVArbkZ3QzViNXMvbkpMRTFyQUlEVjlMdmI3NHVvOWhKemsxcmUrOVd2MGZKTnVGTEFUVFBiejNXOTFLWGk1Q1kxT0JPUjNyUkpKOGdSSzV3SHBoYm9LcytEeFAxZ2VoMnA1UENjeFBXSnFFaU5KYWthV3pUM1Uzb1ZCZjdYb0dpY0kwc0hqZktRcmI4ZDhFb2JEcGJrOVJadFE2RmpvSkZ6KzNnekExbFNtck5SQStkcFlpSitqb0FtS0tncGdMM0NyVjBmL1RtRVAwemd4eXdYaHF1Y3hBSXVtK1NyNlNLR0xaLzBjOFd6cXRjTEpaN2tEQmJra0pOTDJQcU1pZjdVbEtFT3JCV3ova1dMZEdKYmFYQzFsc0RSRGMxZ1BjVndreWk4UysyMnNwSUs3TXk2RmRSMEt2bE5HRlRSOW4yVzl4L3NzbytXVmV6VU1mcW9FeWg2MUxzbXora0FyakJuTjkvVnhTOE5UNWJWb0tJOWhSZjEyVUw3Zlp4Q1l2dllqRTEiLCJtYWMiOiIyYjM4NzMzN2IwMTZmNmVlMDg3MWU5MDBhY2U4ZjkxNjg2YjZhODhjNDM0YTM4MTEwNjgwYjQ4MjIzYjBlOTExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVFeTdMOHhiODVod0hVQW9rWmVOT0E9PSIsInZhbHVlIjoiN3FseHNFekp2QjNKSXBhajZITWo2UjBnTkc3RWNsUUxnZU1iSnZFZXF1SFoxaVdVcGI4VEhET2p0T1dEZXJvbWZEZC94SVEyZmZ1ZEg2eWZjbHJZSnByV2FpSE5sSmpUM3JKMit2dFYzN0ZJbnVLV0s3bnhIZ2Jmc3c4eHpCY0xKOFl3TEROaEZBOHQ3UkpRZThoak5ZQkFpMmkwQ2xwWU9ubXU4OHpldVBWcmoyb3FGWUxYQk5mWTZIM2tkYXJoMFRZZlBlc0JONDNEWW12QnhjUGtCaWorK3BhUGVjSVJlRkM3QVNZVXdoTmVlRDFVVHkyeUpKU2laNDE0QzVrMFlLbzJEajVtWlR0L0Y4ZGlrdHFMSFN6em1PWGVGK0NvWmpkVTd6Sk5HSmJ1alZLUUtOR3hRQllsTkFiQ290MWFPOHpvMXhndWxETXdNQU56TXVnbkJIdE9EdW5DR3E0Ti83YWRkVUsrWDZWRmxQV1g4NWp0L3liTjQzSWs4c0N4VDdLVFZ1MEpMRythSFcwZWkvS3JQL0JNcFd1dmZOYWxjaldkbnNUeXpXSWFvbTd3elRya25HRHpmc1VKeUxpaW45T2h0alBTZ1NTYmZOVjNjNjQ1MFFlZ3ZZQjIzcDJHVXJuRlVYRkEvK1ZiRkJJNVp3NzRGVDNYVzkvd1Rja1IiLCJtYWMiOiJlYmNjYzUxMDAzMjk2MWM2ZTUxNWE3MDE1ZWRkYzE4ZDhiNjFhYzcwZjk1YWY4OWI5OGU5M2Q4NzVhMjZiODUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247756929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1399478790 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399478790\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-786569925 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:53:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImErNHJJVU9IYXpmYzM5WHdoOVZ5SkE9PSIsInZhbHVlIjoiWTc4YVZ4MXNUTFN2ZGdCSERpL0VtTWZqc0lGZUdoaFhkNjMyL0E4Wk5RQUY2ZDVZVGlodFhaTUtyVEV5cWZNR0JpbDVYZTU0c2dIbFU4RWJvV2xJelR3Q2U2RU11dWRyYkYzV21xVUpJd2lHYWtzUUhtK3NvZ1ZpSXZvS3dZQ0pidjNOa2Y2SXc4cWdoM2dMMlRJNnUrOS9FM0lUOHpnaklrVlE2YlVmMGIxR3A0M2RIclZ3T09yWW9aeFpvUFVuUmJ4Z3dQalA5ZmNld21zZi9IN1VYRHRJVVhFUzBCei9wL29EMTF2L25xZWhVeVZhRzBTT1VsLys0cmF1L2ZMZy9pVFdlQXlQdzN6M0NEaGZzRzNuQThtZGpGY0RGYUFDR2tRTWdvWVp3WEt2RGJTaGVrK3FRRHFhbVVFb2JpYzg0WTEyaDdqQnRlUGpLaTAwWnhvd01hNUxmczdYRDR4NldiODRsRzl5QjVKNzIwSjZxNmlDemlkNWY0eERIY3I1Y01zdUFDOCtYaFZsK1NWZjE2Mko3NlhkeHc2bWYzOEx0VTlxNWxuK2JQcSt6bVlTVHpBdkVqQ0lzaVd5TFdCVUN6ODFQR2pJT2paSEdzSXF0cTlSOXZDYWJWeXBhRDMwUkFCYmpFZ21vdjJSS2hhOW1jYTBHT2tJemJPUnRudDkiLCJtYWMiOiJmZDQzMzdiNDExYzFlMjgzYmRjOTZkOTY2MTRmZTdjMTkxYzZjNjExZDhjNGMxN2IwY2FkMDZjODUzMTgxMDBiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:53:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklOcDZvWGtBakFGenRYcmo0TjR2cnc9PSIsInZhbHVlIjoiSHNRbkZOeGFhM0R6MldaNEdNeTV6a1lzakJSWXg2WmwzdjNxMzN2U2tpMnBrUHZpbHAvSmxpeHEwSW1oOFhlbXRyTi9KZDZjRHgzU0JJRFFGMi9pcmF6VnlESFNaY29VdmhZcVpLMG5FRDBnSGs1VFhnaERXSlYzbnN5OG1NRlN1cUFyNWhBc2hNakZjSUNya1A5dVdCNUpvRzV0bGY0TFpacGJ3bU5DU3YxNXNLSWxQTnlRb3ZDT0I4RENIdEFpKytGWlZpL3RKdDhJdkJEb2Z3dzJYQWVyQzgxU05vMjNndnl2VU90VWtRTnR3dlJVc25LVEhEaGU0TFFhYU00aXJTNjVsbFpXeDR4YkVRVk9mamZQS2RpSENMd1V3UU40RVdvQ0tYOGdVODgwSjJ5STNQcm1RRVh4aVNjM1MxeDczdkFXUVplUXhmZTIyTnJYWThxcDVLWDYvNnhJSXVSZ0pCUElxTjY5NkYzaUJ6MFA2Y1RUbVZobHUyYVE4TENObTh3OEd0VTd2TVREQ282VFREQXhpUW96R3ZrYnQ4b0V3MG1yRlpNVmlnTVdNc0IxVDZGc0h5SFdYRlVMVHlMdnBDaUw2YjBNOS9vdklmYWZaUnJUL09ySkVJazBJWUcydFlUTkFWcVIvTkhSVllSYmJsbGhsVFo4VXNHcDI4bFUiLCJtYWMiOiJkMjllNzg0YTA4MmE4ODg1NjU2ZDAxYWI2YWRiZDQxMjFhMGViNzJhNTQ3OWE2NDBjMDVjMDFjN2FhZmViMzFhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:53:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImErNHJJVU9IYXpmYzM5WHdoOVZ5SkE9PSIsInZhbHVlIjoiWTc4YVZ4MXNUTFN2ZGdCSERpL0VtTWZqc0lGZUdoaFhkNjMyL0E4Wk5RQUY2ZDVZVGlodFhaTUtyVEV5cWZNR0JpbDVYZTU0c2dIbFU4RWJvV2xJelR3Q2U2RU11dWRyYkYzV21xVUpJd2lHYWtzUUhtK3NvZ1ZpSXZvS3dZQ0pidjNOa2Y2SXc4cWdoM2dMMlRJNnUrOS9FM0lUOHpnaklrVlE2YlVmMGIxR3A0M2RIclZ3T09yWW9aeFpvUFVuUmJ4Z3dQalA5ZmNld21zZi9IN1VYRHRJVVhFUzBCei9wL29EMTF2L25xZWhVeVZhRzBTT1VsLys0cmF1L2ZMZy9pVFdlQXlQdzN6M0NEaGZzRzNuQThtZGpGY0RGYUFDR2tRTWdvWVp3WEt2RGJTaGVrK3FRRHFhbVVFb2JpYzg0WTEyaDdqQnRlUGpLaTAwWnhvd01hNUxmczdYRDR4NldiODRsRzl5QjVKNzIwSjZxNmlDemlkNWY0eERIY3I1Y01zdUFDOCtYaFZsK1NWZjE2Mko3NlhkeHc2bWYzOEx0VTlxNWxuK2JQcSt6bVlTVHpBdkVqQ0lzaVd5TFdCVUN6ODFQR2pJT2paSEdzSXF0cTlSOXZDYWJWeXBhRDMwUkFCYmpFZ21vdjJSS2hhOW1jYTBHT2tJemJPUnRudDkiLCJtYWMiOiJmZDQzMzdiNDExYzFlMjgzYmRjOTZkOTY2MTRmZTdjMTkxYzZjNjExZDhjNGMxN2IwY2FkMDZjODUzMTgxMDBiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:53:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklOcDZvWGtBakFGenRYcmo0TjR2cnc9PSIsInZhbHVlIjoiSHNRbkZOeGFhM0R6MldaNEdNeTV6a1lzakJSWXg2WmwzdjNxMzN2U2tpMnBrUHZpbHAvSmxpeHEwSW1oOFhlbXRyTi9KZDZjRHgzU0JJRFFGMi9pcmF6VnlESFNaY29VdmhZcVpLMG5FRDBnSGs1VFhnaERXSlYzbnN5OG1NRlN1cUFyNWhBc2hNakZjSUNya1A5dVdCNUpvRzV0bGY0TFpacGJ3bU5DU3YxNXNLSWxQTnlRb3ZDT0I4RENIdEFpKytGWlZpL3RKdDhJdkJEb2Z3dzJYQWVyQzgxU05vMjNndnl2VU90VWtRTnR3dlJVc25LVEhEaGU0TFFhYU00aXJTNjVsbFpXeDR4YkVRVk9mamZQS2RpSENMd1V3UU40RVdvQ0tYOGdVODgwSjJ5STNQcm1RRVh4aVNjM1MxeDczdkFXUVplUXhmZTIyTnJYWThxcDVLWDYvNnhJSXVSZ0pCUElxTjY5NkYzaUJ6MFA2Y1RUbVZobHUyYVE4TENObTh3OEd0VTd2TVREQ282VFREQXhpUW96R3ZrYnQ4b0V3MG1yRlpNVmlnTVdNc0IxVDZGc0h5SFdYRlVMVHlMdnBDaUw2YjBNOS9vdklmYWZaUnJUL09ySkVJazBJWUcydFlUTkFWcVIvTkhSVllSYmJsbGhsVFo4VXNHcDI4bFUiLCJtYWMiOiJkMjllNzg0YTA4MmE4ODg1NjU2ZDAxYWI2YWRiZDQxMjFhMGViNzJhNTQ3OWE2NDBjMDVjMDFjN2FhZmViMzFhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:53:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786569925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-108686071 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108686071\", {\"maxDepth\":0})</script>\n"}}