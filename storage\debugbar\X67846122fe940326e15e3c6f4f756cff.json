{"__meta": {"id": "X67846122fe940326e15e3c6f4f756cff", "datetime": "2025-06-30 16:06:38", "utime": **********.901048, "method": "POST", "uri": "/branch-cash-management/close-shift/58", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.403069, "end": **********.901062, "duration": 0.49799299240112305, "duration_str": "498ms", "measures": [{"label": "Booting", "start": **********.403069, "relative_start": 0, "end": **********.787278, "relative_end": **********.787278, "duration": 0.38420891761779785, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787287, "relative_start": 0.3842179775238037, "end": **********.901063, "relative_end": 9.5367431640625e-07, "duration": 0.11377596855163574, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45692464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST branch-cash-management/close-shift/{id}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\BranchCashManagementController@closeShift", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "branch.cash.management.close.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=151\" onclick=\"\">app/Http/Controllers/BranchCashManagementController.php:151-193</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00498, "accumulated_duration_str": "4.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8210912, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 34.137}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.831211, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 34.137, "width_percent": 9.438}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 154}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.835173, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:154", "source": "app/Http/Controllers/BranchCashManagementController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=154", "ajax": false, "filename": "BranchCashManagementController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 43.574, "width_percent": 0}, {"sql": "select * from `shifts` where `shifts`.`id` = '58' and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["58"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 157}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.836295, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:157", "source": "app/Http/Controllers/BranchCashManagementController.php:157", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=157", "ajax": false, "filename": "BranchCashManagementController.php", "line": "157"}, "connection": "kdmkjkqknb", "start_percent": 43.574, "width_percent": 10.843}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-30 16:06:38', `closed_by` = 15, `shifts`.`updated_at` = '2025-06-30 16:06:38' where `id` = 58", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:06:38", "15", "2025-06-30 16:06:38", "58"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.838284, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:168", "source": "app/Http/Controllers/BranchCashManagementController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=168", "ajax": false, "filename": "BranchCashManagementController.php", "line": "168"}, "connection": "kdmkjkqknb", "start_percent": 54.418, "width_percent": 31.124}, {"sql": "select * from `users` where `users`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8412771, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:171", "source": "app/Http/Controllers/BranchCashManagementController.php:171", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=171", "ajax": false, "filename": "BranchCashManagementController.php", "line": "171"}, "connection": "kdmkjkqknb", "start_percent": 85.542, "width_percent": 6.225}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-30 16:06:38' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-30 16:06:38", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 174}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8428838, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:174", "source": "app/Http/Controllers/BranchCashManagementController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=174", "ajax": false, "filename": "BranchCashManagementController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 91.767, "width_percent": 8.233}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/BranchCashManagementController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BranchCashManagementController.php", "line": 177}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.894349, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BranchCashManagementController.php:177", "source": "app/Http/Controllers/BranchCashManagementController.php:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBranchCashManagementController.php&line=177", "ajax": false, "filename": "BranchCashManagementController.php", "line": "177"}, "connection": "kdmkjkqknb", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/58\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/branch-cash-management/close-shift/58", "status_code": "<pre class=sf-dump id=sf-dump-1062406694 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1062406694\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-985894289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-985894289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1389482896 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389482896\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1213150401 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299595611%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIwM2p5WUk2TDNOYkRDbG1kMENBQlE9PSIsInZhbHVlIjoiV1IvcG9kWFpubnk0UEVkVjhYdjlBVkZVVHQxazN1WEJHZHkwcTlNbEJpVmp6MWo1aEN0emc3amxyYk9VNWJvYmp6cXJ0MFNFZkdvN1hWU2JYVTZxSjZ3K1N1cHMzc2JqaGlNSHdTeDBFSmo1YmsrTUNic3NuNjZFRUJsTWJCeUVicDVETGw5ZFZuZUZtajNjVTBsMXlrWXNBN2VrVXFMMk1tQlFvOFVCR0VuZ01jN3U4REZsRUgwdUhvU2kwNWJaNVc3eWhqTlZ4blg2MytDSTlnZEVkeFlBSkF4THpYYXhBVU5WeXFLQ0o5MGZYZldPQTlFamp5d2l6anpQNURPUTc2RHRUdUI1ZEVCcEtzcUo1bDRSczd3UGFjLzg4TEtMSUtlWGNJalhpWitlbktLK2ZXczdvejc2VlZJZGZpR3lJWWl1c3ZJalBFU3RTcjk1Z0VMd1A2NG5td3B3Y3BWbkkrNEtCM2prNjMrWVUwYjIxN3U4VVdmZDFnVGwyeTg2M3AzdlJET1ZZclk4MjhUdm52a0JPV0owTkhGY0NSZ2kxbk40OVVmcFI3WnZWUUl0WXEra1hJdSt2TEhURWtWUUh6MS9DbFRmT2FBbWRuNmJEU21SYXRLcThxeWtWbmFiRzEwMFhuTWtjL2JrUkYwRGs1dVN1SzFsNVFzK0RoTlIiLCJtYWMiOiJiMWJiNDgyN2IxYjFhYzc1YzNhYmNkYWZkMjhjOWY3ODU5YTcwZGRhMDg5NmNlYTdlZjg0YTgxYmNjYTYwMzllIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InlJckdHM3djTkllOUo2VkZsdWdweHc9PSIsInZhbHVlIjoic3JiSWlJSVB4Y0hlRGwvc1FiWFA2cDRmdWROc1BYWW9Nbllvdlhwc3p1TkVFVWdKclowTE0wbnNUSlRRUFFPZjFhTWduWEQxeXg0Zm1ZM2tHSlJLKyt2dHl3QVBnVEpuSWZKVjhGRldKR1FhOHcvRndCTzJhYjR5VDBGRnMvekZkUC9JSFF3WjczWUg1Tkd0cEQrdi85Q0VRYWhaRmVvZTN1Y3VFSHkvZkhFTjJKVi9nR0ltb0VrbXlBSDYyTEJIWjBhT3NRejFkVCtEdjBMTjR6cGFqSlBDMmZoQWZ6K3k1V3IyUTNmL0FqK2s3SGZuMDk0OGo1dXhKZ0hwclpuZCszem1FMHFoK3lOd0w5cmF1VjhES3BjcFl2bWJBTHR6RnJZWk05VjltUjFGTkV6MEJHdlJseEtiQ3hJdUVDajdXRlFNZGJpc3RzdUtkZElXV0V3MUl5NkZSVXFpUGRqZ1BlMjhuWVNnUWdBOHZ5RGFvbUd4RjllU1BjVTZ6ZlE1bHozSWhRNHc1dVJlV3Y2MWVmbGRrYVBsNEhKeFVFeW5NVXgzemhwT3kyTmwyYmdBeDRWa1FyTWNvYmVNWmhxUEtuMmxMcE14L2VieHdlRGlpMVQxbysvR25MbXE3UmdwR2hpOWlUSE9XWGd6VHV6UlRDY1IwM2ZmbGdWL2I5ZXUiLCJtYWMiOiJhZTBiNDY2ZTYyM2NjOWU5YWI3NTRkYzg3MjM4ODVkMDZkMGRmN2JlN2NjYjZmZjAwNTJlZmMzNzI4YmE0MWU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213150401\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1832336580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832336580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-295903320 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im50bDVoUXhzM25hWGorbEhzOUMzYVE9PSIsInZhbHVlIjoieDB3Y3NXMTVBS3JYbHZjTjBtYzdxWng3clFlV0RCM2VIZDRDQkJTaTIxTmVTTUlVaHc3cmhGbGswTDl4TG5OWlRhanFSRXZWb0ZVczNLMUtRTGpUMkE1VXVHaHMrdlBaM21weWdHenpCaWZvNkI3bWlDa0g4dWZNRFl6bWFmVTM5OC9JSHZ2VDJGTzBQOU8vVjJUUm1vZmpYZU9NR1h3UWZjTnE0OE80VjBvNklLMVlpU3E5MHUyS1JRSHlva2RDbm1yWisrdFZzcFU1RzUrSmpKSUtDenJ3ZFJCQk85TWZPRUs2RS9nM1JySVpxY004TkMzdkNvVFVHM2pSUTI4eWRIa1ZlR1oxVWg1V05YNWhmVWtaSWFjMnd4QWNLZld0SkJ1RWVFekRDdjZsWk81MVNSb2pIQ1Rrc3ZZVTF3UDkxT0NTbG9EbUZJRVdMZXFFc0gwejdqcEZrZHFDRGd0aW1jaGJObjJEeVdWS1YvblZObTNpT0dsM1h2VDdDRjJteEJTRFRVMldZZWF4T0NnK0tNK3pXZ0hpU2hjZmpWSEc3dTVYclRnZis5S3h0WHZ4QkVuV0pTR3ZaVFR2cEhjaXBqVHo3dnRHSE1KMmk1TTJqNUZ6MG8yYnd2QUlqMTZWbmxiZ2hhRGtaNmQ5M3dmdTU5c0xCKzFKenJNcWs1UUciLCJtYWMiOiI4OGUyYzQ4ZGE5ZWEwODE1MmM0MjhjYzZkN2IxNTdjNGQyZWJmMjM5YWVmN2QyYmEwYTZlYTJlOGNhYzhiNDljIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJ4eHdlNWtiUEloalovbmlCSmxhZFE9PSIsInZhbHVlIjoicTdWay9BcGI5Wk5iU2dzM0RDQWJpTk9sNnVjRHc1akhpbncvcDFscjBraE9WWXBCOVFibG1DUjd2SEhIZk4wU0plbzVodnVKU3hhNERWd0dtOGFHUVR4MXV5aTh1ZUVKcE9MOTdnRXJiMUgrb0FRMHBYL1JqR0lsKzI5QVJUbjlFWXZmbERrTEd6NUNqRkdJT1dXeHRlVXl3OU1TL0hMdzVPY2hvWDRaSk9aOVVrRXh5SlQxenl4OEZXcHI4MURTM01SYWcrZU9SUHRWc0ZOMXpQLzhDMWdpNXBnL0VXanYrMmF2c3R6MXFGWHhuTWp5WUJ0MGdXMkVUU1Q5c09DL0o1M09MWWRHOHF3MWRRcDc0Y0pTcXRSRUwxOVFaUHEyTXdteFBwaGhzZ3VGK1ZWQUtCUDJhT2N0UW9zMHRaUGtpTHEyWU5DbkV6bnBFQUlVNjl6YTNSMXd0am5rOXhJM0V2NVZGcEVkdE9ndXBYMlVqQ3p6VzFwRi9aK3RzbjFQMkxlTnpua2FxK3dXakJDaHFWVEJoRnNWbzNlZ1FBa1c3TjVsK2RKajRuczFIQ2RTZVlVODhSakJZWnA0NmM2aEpyZ0E0aU5MTVNZbFlIc3RHdFBYL1Frem5TS1d0Z3cyK05jZENNUVBIRVUyNVh3dVhGZDhwVjB0K1JQWnM1dHUiLCJtYWMiOiI2OTZlMzQ3NjhjNTQyNzIzYjJlZDEwZmY1NzA1ZTFiNjFlN2YzZGJjOGE2MGU0NWYwNzRhNjUzZmYyNDY1MGYwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im50bDVoUXhzM25hWGorbEhzOUMzYVE9PSIsInZhbHVlIjoieDB3Y3NXMTVBS3JYbHZjTjBtYzdxWng3clFlV0RCM2VIZDRDQkJTaTIxTmVTTUlVaHc3cmhGbGswTDl4TG5OWlRhanFSRXZWb0ZVczNLMUtRTGpUMkE1VXVHaHMrdlBaM21weWdHenpCaWZvNkI3bWlDa0g4dWZNRFl6bWFmVTM5OC9JSHZ2VDJGTzBQOU8vVjJUUm1vZmpYZU9NR1h3UWZjTnE0OE80VjBvNklLMVlpU3E5MHUyS1JRSHlva2RDbm1yWisrdFZzcFU1RzUrSmpKSUtDenJ3ZFJCQk85TWZPRUs2RS9nM1JySVpxY004TkMzdkNvVFVHM2pSUTI4eWRIa1ZlR1oxVWg1V05YNWhmVWtaSWFjMnd4QWNLZld0SkJ1RWVFekRDdjZsWk81MVNSb2pIQ1Rrc3ZZVTF3UDkxT0NTbG9EbUZJRVdMZXFFc0gwejdqcEZrZHFDRGd0aW1jaGJObjJEeVdWS1YvblZObTNpT0dsM1h2VDdDRjJteEJTRFRVMldZZWF4T0NnK0tNK3pXZ0hpU2hjZmpWSEc3dTVYclRnZis5S3h0WHZ4QkVuV0pTR3ZaVFR2cEhjaXBqVHo3dnRHSE1KMmk1TTJqNUZ6MG8yYnd2QUlqMTZWbmxiZ2hhRGtaNmQ5M3dmdTU5c0xCKzFKenJNcWs1UUciLCJtYWMiOiI4OGUyYzQ4ZGE5ZWEwODE1MmM0MjhjYzZkN2IxNTdjNGQyZWJmMjM5YWVmN2QyYmEwYTZlYTJlOGNhYzhiNDljIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJ4eHdlNWtiUEloalovbmlCSmxhZFE9PSIsInZhbHVlIjoicTdWay9BcGI5Wk5iU2dzM0RDQWJpTk9sNnVjRHc1akhpbncvcDFscjBraE9WWXBCOVFibG1DUjd2SEhIZk4wU0plbzVodnVKU3hhNERWd0dtOGFHUVR4MXV5aTh1ZUVKcE9MOTdnRXJiMUgrb0FRMHBYL1JqR0lsKzI5QVJUbjlFWXZmbERrTEd6NUNqRkdJT1dXeHRlVXl3OU1TL0hMdzVPY2hvWDRaSk9aOVVrRXh5SlQxenl4OEZXcHI4MURTM01SYWcrZU9SUHRWc0ZOMXpQLzhDMWdpNXBnL0VXanYrMmF2c3R6MXFGWHhuTWp5WUJ0MGdXMkVUU1Q5c09DL0o1M09MWWRHOHF3MWRRcDc0Y0pTcXRSRUwxOVFaUHEyTXdteFBwaGhzZ3VGK1ZWQUtCUDJhT2N0UW9zMHRaUGtpTHEyWU5DbkV6bnBFQUlVNjl6YTNSMXd0am5rOXhJM0V2NVZGcEVkdE9ndXBYMlVqQ3p6VzFwRi9aK3RzbjFQMkxlTnpua2FxK3dXakJDaHFWVEJoRnNWbzNlZ1FBa1c3TjVsK2RKajRuczFIQ2RTZVlVODhSakJZWnA0NmM2aEpyZ0E0aU5MTVNZbFlIc3RHdFBYL1Frem5TS1d0Z3cyK05jZENNUVBIRVUyNVh3dVhGZDhwVjB0K1JQWnM1dHUiLCJtYWMiOiI2OTZlMzQ3NjhjNTQyNzIzYjJlZDEwZmY1NzA1ZTFiNjFlN2YzZGJjOGE2MGU0NWYwNzRhNjUzZmYyNDY1MGYwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295903320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1280128548 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280128548\", {\"maxDepth\":0})</script>\n"}}