{"__meta": {"id": "Xae8ac0b9a0be41b970ffd32bb7438687", "datetime": "2025-06-30 18:11:31", "utime": **********.066359, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307090.651562, "end": **********.066372, "duration": 0.4148099422454834, "duration_str": "415ms", "measures": [{"label": "Booting", "start": 1751307090.651562, "relative_start": 0, "end": **********.02221, "relative_end": **********.02221, "duration": 0.3706479072570801, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.022219, "relative_start": 0.37065696716308594, "end": **********.066374, "relative_end": 2.1457672119140625e-06, "duration": 0.044155120849609375, "duration_str": "44.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43872592, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00217, "accumulated_duration_str": "2.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.054491, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.636}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.059458, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 86.636, "width_percent": 13.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 18\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 1035.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1387100052 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1387100052\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1554898646 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1554898646\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1705089905 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlQV1RWSDBNczJZVmV5d0F0YmtHeWc9PSIsInZhbHVlIjoiRWlQMjRXa3JCTmRQTWxvanZMbUd0aUN1RldnN1dSc05jWG90aXZiZTZ5Vkk2dGFLbW9YbEIvelZGMlhKd29nNlZDcWtRL2RGSXUzelhOd2RvdkV3UldCakxwTjZUSnYxejZRS0FyM0RTeHg2RW1Kbi9tUWZBZjFhL0VuV04zYjlXUEd3NmdYREJVWnh2U1hEOWJvQzJpWUc0Mjh6SitFQjM5dlF5eG5hdzFnbW1ERWREQS8zek85dEZLZGxBSnVxVFpBemxwNzVjYW9jQnBwUEpGZFFDYit1SDN2bXV3aGp0SDJDdVFFakR2MHIxR1BYTDd1aHY3TmlYcEpkWERXV3ZFekFHYUkrbzVaM1paV1pKdjFBVlFFeEVMdWcyU0tqWHRpeWlZVGFwbHFLRDVNNVA0QjNJcHJqcCtsYnlVWGs3Yy9UZG1sdnV3M21jMkF3amFQb3VhN3RYd3M5ODNPZ1RVb05nUHFhYUprdXJMTDhhZTB2YVo4azcrSzFZTjNJbldROUVkcFZIY1lJWlZIUDhpcHlwSERENTVURldTN3JhL1ZIUWRmMXFiRHAycE1kZzRjbW5WUFhrMHQ2a0JadDB6YnI2ZEZ3Y0dFTlpyTnkrZDJ2OGpuaXZzWm9qazNQWTFZWVhHOTRQbzNhbU1UUEhpcjAzbjJiK09hSDNVQUgiLCJtYWMiOiI3NTE3M2U2ZWQ0ZjNjNGMwNTc5MTY0NTQ5YzQyNjlmYWRkYzY2NzJhNGQ4YzgxZGU0ODMyYWFiOTUyN2U3ZmE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdkZC9HZ3QxSjJMQ3M3eFJncEpINGc9PSIsInZhbHVlIjoicFkraFpqeVVhTjZMN3ZBNDVIVzQrajRrSTBXa2lkVElSMEl2MDZ1SG1DVFlyN3EzWjU5MjBsZUNjVnA4OWtYei9vQVdsbUZJeENTb0xlc3ZkNmpLZkt4RThyeWJKelFSVWR5a1hsQzNBazZNSWNva05TWnR6LzBCM2M3OHdRM3Y4cUcySzN1MWQ4eWdLMVQ5THZMRkJkU1VBcGRPaHhKM0hZRm1YdjZ3cUNVSk5tQVp3RFlJeUU5dmhlamRUN3k0R25mQmRrM1Y2dmJPNzhHSllRV2V4OHlZOEF2Z2NGVUNnUEFUdTc0dmYvZnVJM3hSblVWNk9DdTBpaG5uVFZPWFBFU2hmM1ZJUnhmR1hyU29nc2xDY3d5em5uRGNpMnZHUlZJTmRldlVzMXU0MUE2Q3FNcWNSMC94b1c4cGhJSEVwRkd3YURaSTkwR3NGMVlYZ2tTTlNZVWYvVThra3doU2dIYk1aaHJDb3hjdUV4SFpzdG96a1c5YmcwOHZnWFFDTTZRajhXd0cvSmpsdW14NXJLeXFEbDRLOHgwU2JQejJhN1hINVJmb0h6cldBT1R2anZVeU9XcjZWaGxtcWZNZGJremt4eUxLd2dMWjFwNnBnTHFDUENld0RuakFZQUFSdXFHZlV4TTA5SjM5eTVTQ0RiZHpRa1ZpZ1Y3RWV0U0YiLCJtYWMiOiI0YmFmNTlkMTM1MDJhZTY0NDMyODI4MTUzYzJlZWFiODkzNzE4NTYzZGU5YTU5NmY4MzQxOTQ4OWJmYTcwOTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705089905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-154132603 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154132603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-898912582 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4vbTJJNndGV2U4TXFNdXhBZDl0Q1E9PSIsInZhbHVlIjoiSGZFTHI4MERCTGNSVlBtMlJCVytrRE1NU1ZtVGZBa09GT1dvck4vUjJ5ei9YOWUzWXoxSktOWXp0NHR2ZHVUTVRDMWM0Q2lmcE5Sa1ZZbDR6MVhucEExL0MvYjZIL0xLclRYQm51SGxVMGFkZWU4MkJyVEI4MHY1S2d3N1NUTy9WaGhmeVdGTC94UUd0ZFlKOE5rZlB3clZqUGNhVW10TkdoNGNXOTNlZktQOEVmU3IrYXhCZlJUR1VSOWtQMGdWb3QzS28ra2lXbnZaVXlHOTdrU0FBMlZkVW45WC8yZUl1Y2p5Q2E1U25NRDZRclo0aXg0Y3ZDaXYxbGw4bGJIc1ZKQ3RBSUpSWkFEMzEvbFYzcTFNb0JtaGxYUXdOMFNpSFNVTXVWNkxOWTd6ZUkrSjZOOURxU1ZSU3JxVEhNUlJPV2l1TUt6NlhPaGplMFBCWGZpK0ZORE5aUmlzdGtxWFN4cjdUUXpsTGdmNUpITGZjSFRkaUp2Z2xJMmZDd0hUMkMyVFRwMTlaZy9hQ2lJcGJHSGtEay9XaDk3S2twQWxKVXV0bXFwRDc2OXc2NER5WXMvbGd5WVNlTHlqOGxBYWdMYm9aQUt4T2tDUXVpamQyS04rbC9PZTBybDh0SDBia1hncXRPenZBYnk1bkFNOXFXaUJhWi9BRVo5SFBoZkEiLCJtYWMiOiIxZDhhOTU1OGM1ZDYzZTgzMWI3ZWQxZDQ4NjNmMmJjYjlhOTJlZWJhOWU0M2Y4NmE4YjE3YmU2MGRlMDc5ZDEwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhiOUIxSnJHMUdQaW9qN1lySWx6TXc9PSIsInZhbHVlIjoiMmEwRmJjZXJaaURhVUZXVEQ3SHN6blI4bUllOFRGSlZ1YzVRTFJqZCtIOUh3OW1uTGkySitMVmtYTTI1U1R4WkJ3aWgrcmtUYTRJVGJHdkFhM3hxeXhDSHhJVUpvVmplbG1oVk1FamRIeFNhaXdYelRuUTd2L3RKbjArcUdVZzRTR1EvNjRFa3NxNjEwUUhCeS9lVW84OWxub3dvTW4vQmNYbzQrWDJvcGNwcTF3VmNvV0gwSkdlLzNFRGVKTUJpWWloZkxZNElrdFpyd0p1aWxCY2pjNjBWc0NRczdLTmd0RGZHRllCcytsNVVpalQ5ODlyQWc0WHF4Q2ExdmV6SjE3MitGZDNKYU1lVEpua1A0YWxOWnB3cW5rN3VtZGt2TGFEcWFWWXRRTGxTVDJMSndwV3lIK1k2ZXZFRDJzZVV3c3NpQzNSZWtyQldNTWZoa3Rwb1djemdyQnc5d3ZxblUxcWJoaWhaRHNvb3J2TnNFeDByazQyM0I2Q1hTaTErNlBnRFZJNUtFeDRDeFZSeTV6SEZ4THdXQk5EOVZXckFTV0RYSC9LcXZ2UFNnRDZLQnZwZGZEMzBHYnd6ZVVIUjBMdDdjUEdoWmNoUS8rLzRxeEpmRTNQUURVY2dpRWg1dFJaMU9yb2VJLzEzSWVEYTJJOVJyZkgrUk9DZkh6T0UiLCJtYWMiOiIzMzllODNlYWYwNzc4ODMwNTVlMjNlMWEyNTE2OGQ2MTczNzRhYzgzYzQ2N2ZmMjkzOTMzYTBhMjkxMmIzODJjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4vbTJJNndGV2U4TXFNdXhBZDl0Q1E9PSIsInZhbHVlIjoiSGZFTHI4MERCTGNSVlBtMlJCVytrRE1NU1ZtVGZBa09GT1dvck4vUjJ5ei9YOWUzWXoxSktOWXp0NHR2ZHVUTVRDMWM0Q2lmcE5Sa1ZZbDR6MVhucEExL0MvYjZIL0xLclRYQm51SGxVMGFkZWU4MkJyVEI4MHY1S2d3N1NUTy9WaGhmeVdGTC94UUd0ZFlKOE5rZlB3clZqUGNhVW10TkdoNGNXOTNlZktQOEVmU3IrYXhCZlJUR1VSOWtQMGdWb3QzS28ra2lXbnZaVXlHOTdrU0FBMlZkVW45WC8yZUl1Y2p5Q2E1U25NRDZRclo0aXg0Y3ZDaXYxbGw4bGJIc1ZKQ3RBSUpSWkFEMzEvbFYzcTFNb0JtaGxYUXdOMFNpSFNVTXVWNkxOWTd6ZUkrSjZOOURxU1ZSU3JxVEhNUlJPV2l1TUt6NlhPaGplMFBCWGZpK0ZORE5aUmlzdGtxWFN4cjdUUXpsTGdmNUpITGZjSFRkaUp2Z2xJMmZDd0hUMkMyVFRwMTlaZy9hQ2lJcGJHSGtEay9XaDk3S2twQWxKVXV0bXFwRDc2OXc2NER5WXMvbGd5WVNlTHlqOGxBYWdMYm9aQUt4T2tDUXVpamQyS04rbC9PZTBybDh0SDBia1hncXRPenZBYnk1bkFNOXFXaUJhWi9BRVo5SFBoZkEiLCJtYWMiOiIxZDhhOTU1OGM1ZDYzZTgzMWI3ZWQxZDQ4NjNmMmJjYjlhOTJlZWJhOWU0M2Y4NmE4YjE3YmU2MGRlMDc5ZDEwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhiOUIxSnJHMUdQaW9qN1lySWx6TXc9PSIsInZhbHVlIjoiMmEwRmJjZXJaaURhVUZXVEQ3SHN6blI4bUllOFRGSlZ1YzVRTFJqZCtIOUh3OW1uTGkySitMVmtYTTI1U1R4WkJ3aWgrcmtUYTRJVGJHdkFhM3hxeXhDSHhJVUpvVmplbG1oVk1FamRIeFNhaXdYelRuUTd2L3RKbjArcUdVZzRTR1EvNjRFa3NxNjEwUUhCeS9lVW84OWxub3dvTW4vQmNYbzQrWDJvcGNwcTF3VmNvV0gwSkdlLzNFRGVKTUJpWWloZkxZNElrdFpyd0p1aWxCY2pjNjBWc0NRczdLTmd0RGZHRllCcytsNVVpalQ5ODlyQWc0WHF4Q2ExdmV6SjE3MitGZDNKYU1lVEpua1A0YWxOWnB3cW5rN3VtZGt2TGFEcWFWWXRRTGxTVDJMSndwV3lIK1k2ZXZFRDJzZVV3c3NpQzNSZWtyQldNTWZoa3Rwb1djemdyQnc5d3ZxblUxcWJoaWhaRHNvb3J2TnNFeDByazQyM0I2Q1hTaTErNlBnRFZJNUtFeDRDeFZSeTV6SEZ4THdXQk5EOVZXckFTV0RYSC9LcXZ2UFNnRDZLQnZwZGZEMzBHYnd6ZVVIUjBMdDdjUEdoWmNoUS8rLzRxeEpmRTNQUURVY2dpRWg1dFJaMU9yb2VJLzEzSWVEYTJJOVJyZkgrUk9DZkh6T0UiLCJtYWMiOiIzMzllODNlYWYwNzc4ODMwNTVlMjNlMWEyNTE2OGQ2MTczNzRhYzgzYzQ2N2ZmMjkzOTMzYTBhMjkxMmIzODJjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898912582\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1035.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}