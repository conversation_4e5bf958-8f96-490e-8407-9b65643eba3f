{"__meta": {"id": "X1bde27f37c5a7a6555446c19f5e7a886", "datetime": "2025-06-30 18:07:40", "utime": **********.507491, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.089099, "end": **********.507504, "duration": 0.41840505599975586, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.089099, "relative_start": 0, "end": **********.456624, "relative_end": **********.456624, "duration": 0.3675251007080078, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.456634, "relative_start": 0.3675351142883301, "end": **********.507505, "relative_end": 9.5367431640625e-07, "duration": 0.05087089538574219, "duration_str": "50.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028699999999999997, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.487562, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.519}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.498181, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.519, "width_percent": 15.679}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.501239, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 89.199, "width_percent": 10.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-552758767 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-552758767\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-389603609 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389603609\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-770588785 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-770588785\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-26861016 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZwM29Pa1RpUXRMWUxYa2Jmalo4a1E9PSIsInZhbHVlIjoiOVpUa0E0S3MzTFlMVVNBRXJFS2dGdmpXZzlwVXE0bXc0SVk4UnM5SmRJYUxnSEIzTjVvVGViSFpuWmwxRndDWURmUUhoaUtYWnhQanN5WXdkN2NRUGdEZjJMeHNtaWY1ZzZHbHVneXNDTCtNUVlkemJ0NzZ3cU0zSmdqQlNnL2hXM3FnT08xanNrYVpBQjRZRzcyelB4Qk9pbnFqVnQ4ZnZvd3MrL3ZMQ2NpS2tVSWI4d1VLTVllak5yYXhHdlJHWDhOQTV6bDAzVzVWWEx4clNBWVFJUUVTblhWUW1oNzFxWmJadmhLMzNpYVpITUFqWldJckNXbHlWTWNVVVpBMG44TjUxWFB1VVdRSmJFMys2WHlRbHFyaXdpZWYrWVVhSXN5dDhTRmhBSDFZWmRmNzNWTndhc0ZhRENkTzZJUHR4WVp3MEdMVVUyRk1jRndUZmhPRWVldkNCUk40OTh5ZERpdXFNQ3dXS2ZJZWVHdXNmdjJNOXdvNmppTUluMVFIbzFQTTlydW5EVyt1UTRQUWZWNEpIdy93QXRTekRlSTRYSUg4a1ZmMnZVVG9NY3RYeGpjaVBiZ1BLSlpycC9GMWRJUXprai9wVTd2UFh5cmdNMXNYeUJFMjFyclpEMWFmRzUrZkY0ZUpiZU9uQmtZRjdQa0FjUXV4YWRPQU5FdDgiLCJtYWMiOiI4NmMwMGJmYjNhOGMzZjFhYWI5NDE5YzVlNmM4ZGViYjg2ODVhYjQ3OWQyNWRlMmYxNzVjM2FlZmRiMGJhNmUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlR1Z2lVbStvVUZzdlBvMjFLUEU5a2c9PSIsInZhbHVlIjoiU3ZsNERmdSt1WjRsazhWcXkzWEVnNjlGOVBpN0RBZzUxUDBWcStJQWtHNTdZQ0NlM2JOUXpyaGc0enlDTEQvcVY4L3Jqd0dsYUpEWDBuOVdqWEs0K0ZsQXRUR1NDb0h2Q1pWK21NeUxER3hoZGZjelgzcW4wa29SSmJCb0VUVVlQSzRGZVllaE54RmRyL1ZUZ01peUxNQWlKaiszQmVFNGhzd2FuODVpYnA0RzBUd2c4VnBBUE01S2JqU2FEampCNzF0dG4yajV1SWFtUlhWdFVlcmJKM09SVC96UVZvbDAxUE5BYmlLbkU5ZTRFa2VKVzNrK204UXBGNTVIRzd4d2VEd2NtSTlFV0RWWmtOVzdDSFdteU9FTjVQZ1hpUWIxR1A1ZS8wMnB0MWliMEpJdFlvdi9HSVRyUFZhdzlPRGQzem9LM2NQTTEwQVFIM3YxRHo1RE5QWk80TVJHQ1p5bWdsSk9pbnZLOHRVL0FmeVlXR3ZTZTNNSTk2bG5ranZRbG5CemdBc1ZJdWZ5NUp0YWpEa1dNeElDdEZZYzU5L3JMeUNSb1ZwNXJUSGg0QzBpZWUwYW1DVHZPcGhWRWs1azlDMFFnblpkRGg4UkgrcTlya2t1MndRZDhqOXpoWGVmYUMyd2JVbmJRblpFTWVYeURaVG9jYkdNcFIxZXhUU2wiLCJtYWMiOiIzM2IwNzJlOTYyYWY3YTc4MzAzYjgzZmQ0NmQyYmNjM2VjODMzZGQ4NTQwNzA4MWUzNjgwZjM4ZDQzNTA3OWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26861016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078941166 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078941166\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2138875857 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:07:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxuVTMrdDZpVk93U01DTUc1Ui9DaFE9PSIsInZhbHVlIjoiNTR0VUJzaFpsRUVMWVJhL0xUVVNkOUVBMnpacUlQWW96UG1pWGViT0lGWjJib2hOcW1aNUdmdWoyenhjWTQ1d2pUbUI1R0xHVWlmOEhKd3dxSmFzU1JSdFg1Y2NpdmZFekJYN2o1Yjdma042UjBlc2I2bk1PYmJka2hFelBhckZKWkJtTVpZSk9lcStoNWR4Z3hxS0orMGoreFRFcmRTZkN0OXQyVFl3ZWJ2dkJCeXlwaU5uaXlHR2xEZmhOMDY5ZkM3L1pUOVFWNFdDdUhVWnQyUmIvQ0tvMWlScUkvaG51eE9EWUlBTldFblF5MzhDOC9ab29nUTkyU0ovTkl6MVRBdGxJUFFNdzc5QXhnLzRrTXhmaUgwVlJaazJhc2YwTHpsNVRDaG9MUmhVTVVXbk5ITDQ1S01TZXhwM3FSaXF5enZMNVk5WGpadFRyYnNpVGw0ZmhtVEg0STMra1FsenBlUFJ5dDlLKzRNbjJnTUttSnBjWS9tb3krajF4M3RpSnJxckpLNk1scFVsT2JxTTUrNGtBbldWSU9EUkYxcFBoL3JCek5qaXI2ekREOWZoelZOYzVaZlBBa1NoS2xESjBibXplL0RJMklYbFFVc1JmN3FkdjhiOXU0elBHY0w2UWlnTDRRNFYvYmozc2FobjVOdjBBZWw3d3pkQVBEOTQiLCJtYWMiOiI0NDI4OTZhMjI2YTYxMWQ4N2ZkMTkyM2JjODg0OGUwYjVhZTEyOGJmZmQ3ZjExOTQ1MDk3YmZlMjVjNzY0NjUzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdQK0w4N3dWYWV0d0h0dnpNeHZyUlE9PSIsInZhbHVlIjoiRlBheHpiYVV2K1lIaEFjSWE3L3NqaGRqOXdadGJ4K3o0WklQdmNBcmM3cm1qY2tRSHlIZUNUMG9VWC9XYlF6WHNraS9vTzZNTzZQK1N1c0RHVzFDWHUzdForbVJGMllRMWkzcnRtL3pXbmtEdnpUVFJIWjMvQ1RDTWlYNm9PT1U3eFJyaUZsWkFaN25ybW9vSDZBMjJ3S01RcUlsb1hzbkk4dVBveEcwNU1VZXVXVlAvUC9ma2RJNnUzTXdjblVaeGprQSsrOUo0ay85WGVzbGRDdFNnekx6REUrTVBKSGsvcUlIUWFmd3lpYnpsdnMvcmg2a0JuTjJMUWlsV2JUOElSK0UxQ0I0NXk1RlRMSjR0dEd3RHFVdE9BdmVMTEpYdWZTTUVMdzhCa1hCVmZFNmxaNjJiOWlHQjYvUjVIZ1ZydEZHMVF1NlNpdU9iS05EUW90cklhL0Y5dytVUFAxMUNKZ1p5RlN5MjNFbkIxQ1BvVEdteW0vdHk3SmtJcW53UHBpdWlyZ05tT01rK0NTQ1ZxZkNoSFlKN1RXQUVURzI5MUUyZUlHK1BtRVY0K2FvTXlrTkp1MG52elptcS9ZRHk5eVY5Z1VkOFkwZ2JwZndGdEhCWndWSHRvSzcxbStDM2F3N0c4ODFSbHRvU25WL0NtZStEWUI3NEJuYTZEYmQiLCJtYWMiOiJmYTUwNzgzOThmYTZmMDE3MDY0ZmQxZWI0NDkwNDczMzA4Zjk3NzA3NTg0ZWVhMjExZTQ2MGNiNTAzN2U4ZTEzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:07:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxuVTMrdDZpVk93U01DTUc1Ui9DaFE9PSIsInZhbHVlIjoiNTR0VUJzaFpsRUVMWVJhL0xUVVNkOUVBMnpacUlQWW96UG1pWGViT0lGWjJib2hOcW1aNUdmdWoyenhjWTQ1d2pUbUI1R0xHVWlmOEhKd3dxSmFzU1JSdFg1Y2NpdmZFekJYN2o1Yjdma042UjBlc2I2bk1PYmJka2hFelBhckZKWkJtTVpZSk9lcStoNWR4Z3hxS0orMGoreFRFcmRTZkN0OXQyVFl3ZWJ2dkJCeXlwaU5uaXlHR2xEZmhOMDY5ZkM3L1pUOVFWNFdDdUhVWnQyUmIvQ0tvMWlScUkvaG51eE9EWUlBTldFblF5MzhDOC9ab29nUTkyU0ovTkl6MVRBdGxJUFFNdzc5QXhnLzRrTXhmaUgwVlJaazJhc2YwTHpsNVRDaG9MUmhVTVVXbk5ITDQ1S01TZXhwM3FSaXF5enZMNVk5WGpadFRyYnNpVGw0ZmhtVEg0STMra1FsenBlUFJ5dDlLKzRNbjJnTUttSnBjWS9tb3krajF4M3RpSnJxckpLNk1scFVsT2JxTTUrNGtBbldWSU9EUkYxcFBoL3JCek5qaXI2ekREOWZoelZOYzVaZlBBa1NoS2xESjBibXplL0RJMklYbFFVc1JmN3FkdjhiOXU0elBHY0w2UWlnTDRRNFYvYmozc2FobjVOdjBBZWw3d3pkQVBEOTQiLCJtYWMiOiI0NDI4OTZhMjI2YTYxMWQ4N2ZkMTkyM2JjODg0OGUwYjVhZTEyOGJmZmQ3ZjExOTQ1MDk3YmZlMjVjNzY0NjUzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdQK0w4N3dWYWV0d0h0dnpNeHZyUlE9PSIsInZhbHVlIjoiRlBheHpiYVV2K1lIaEFjSWE3L3NqaGRqOXdadGJ4K3o0WklQdmNBcmM3cm1qY2tRSHlIZUNUMG9VWC9XYlF6WHNraS9vTzZNTzZQK1N1c0RHVzFDWHUzdForbVJGMllRMWkzcnRtL3pXbmtEdnpUVFJIWjMvQ1RDTWlYNm9PT1U3eFJyaUZsWkFaN25ybW9vSDZBMjJ3S01RcUlsb1hzbkk4dVBveEcwNU1VZXVXVlAvUC9ma2RJNnUzTXdjblVaeGprQSsrOUo0ay85WGVzbGRDdFNnekx6REUrTVBKSGsvcUlIUWFmd3lpYnpsdnMvcmg2a0JuTjJMUWlsV2JUOElSK0UxQ0I0NXk1RlRMSjR0dEd3RHFVdE9BdmVMTEpYdWZTTUVMdzhCa1hCVmZFNmxaNjJiOWlHQjYvUjVIZ1ZydEZHMVF1NlNpdU9iS05EUW90cklhL0Y5dytVUFAxMUNKZ1p5RlN5MjNFbkIxQ1BvVEdteW0vdHk3SmtJcW53UHBpdWlyZ05tT01rK0NTQ1ZxZkNoSFlKN1RXQUVURzI5MUUyZUlHK1BtRVY0K2FvTXlrTkp1MG52elptcS9ZRHk5eVY5Z1VkOFkwZ2JwZndGdEhCWndWSHRvSzcxbStDM2F3N0c4ODFSbHRvU25WL0NtZStEWUI3NEJuYTZEYmQiLCJtYWMiOiJmYTUwNzgzOThmYTZmMDE3MDY0ZmQxZWI0NDkwNDczMzA4Zjk3NzA3NTg0ZWVhMjExZTQ2MGNiNTAzN2U4ZTEzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:07:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138875857\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2014730002 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014730002\", {\"maxDepth\":0})</script>\n"}}