{"__meta": {"id": "X2ec799226562a6b21a67ac1c247299c6", "datetime": "2025-06-30 16:50:44", "utime": **********.489265, "method": "GET", "uri": "/add-to-cart/1839/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.074878, "end": **********.489277, "duration": 0.4143989086151123, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.074878, "relative_start": 0, "end": **********.41178, "relative_end": **********.41178, "duration": 0.3369021415710449, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.411788, "relative_start": 0.3369100093841553, "end": **********.489279, "relative_end": 2.1457672119140625e-06, "duration": 0.07749104499816895, "duration_str": "77.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48672864, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.005229999999999999, "accumulated_duration_str": "5.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.445658, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 30.593}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.454965, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 30.593, "width_percent": 5.927}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.467887, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 36.52, "width_percent": 8.222}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4697938, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.742, "width_percent": 4.971}, {"sql": "select * from `product_services` where `product_services`.`id` = '1839' limit 1", "type": "query", "params": [], "bindings": ["1839"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4739568, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 49.713, "width_percent": 4.207}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1839 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1839", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4773269, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 53.92, "width_percent": 40.727}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.480946, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.646, "width_percent": 5.354}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-942976028 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942976028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473146, "xdebug_link": null}]}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1839 => array:9 [\n    \"name\" => \"ميداليات شكل محتلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"1839\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1839/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1848512919 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751301269373%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlIdW5Dd01tWDZWSXF5aVkra29IL2c9PSIsInZhbHVlIjoiQlZBaHBRaHVyRW84MXB5TjZBRWtqRHpiRi9nQjRPeTAwOWRVWWZZMTdVaVJDL1RjeXRwVTNJUkEyRndHUTk2RFhOSlYzQVN3NWFVVEtrbW1JekFrL0d5aXB4NlNDcjlpSFRrT3B2WnhWYVJCY0U0MjNZaFdaK0p0SDRBY295TGpaMTY4OWc5M1Rya1lVTkcrd2NyVldVRkRjTmtxT3pod3JoUE5tT3A2Zzh4cVFIb0JhcDVJN2RzbVZ0YUI4ZVBaWHZOZW5zQ29vQmJOd0VDLzJyMWttb2JlNldidUdUWUNURlNnUG9iR1p4bHJpaDdXL2FsblBkUlRGVHNiMWd4WVBSQ2h0Wi9JUmxhdGwzUVcyM25KczNVT2pSaE13WFl2Z0NObEZDWk1ydHUyd241WHpqZDJlVE05Q2JxUjJac3lBU0dRUUw5YmN3Sk90a3pacXYzbzU4WmpEWnFGWXR6UGtrQ1RrRnJta0RlV1RKL0RVWUNPUytFMWRXTWJ3alZYTzltUFZqSFFnMkhSWHBHN1dQT3RWL3V2ZkhTYmF1MXhmSms2SXg5TUVlT0ZyR3RJdUJ0UmtKTEZWQnVZNkIvbStJMnFqRlZoeTV0ZXB3d28yV1RqbnFYWUVuQnJPWHhBd1FxQnZhV0xRZlVYWHEwaFF1d08zSExWNVA1SS9vdGgiLCJtYWMiOiIwZjg0NjRlMTBhZDA3MzIxNjBhZTA0ZTI1ZDhhNTcyYzBlYTM4OWY4OGRmZDVhYzIyODU4ZTA4MzM1NmQ3MGFjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndmL2tEejFlbVB5Wm1DeUIzT1ExYlE9PSIsInZhbHVlIjoieDJ4RVhOYzAyaS9JSmlIWnRRbFhKNmJPQmhrUzVNbjVHNWZzZkpLRWw0SjArZ2s0L1hVRzhmbTVYWlQwWXVkL2xFSVI4Skgzdlo3NUZCYk0zWWpOVTN2M0lBbWtyV1BGSVJDMGc1eHVlTUlqc3JqNUorVTg2Qkk1Z2ptT2h6SFVlWWxiZ0IwNW8rc3BmV3Z3di90clp1djE5ZkRWT2FtTFd4MzNMVmRYSXhrWVJPRENyWlNHYnkwYU96UFdSbCtkWkltK20ybVh1encwa1k5b0o4cWdMbG9mRit6MGdQUEF5UkFJMW16WVl1emh2Z3hnRnMxbEV6dGlrd1ZzZGNsRE9lcE9rOGFHQ2dnZ0crcERNTHZMNmVMdFNPMnBkL25aTGlVT3RGb0tLUGIzMk9CQjdqTExDUjg0TXdhcTB1eTh3TlVjYVZxS2V4WUlQanpDZjdUOThCT3h4bEs1TTJ0SmVWZld3SisyUit2NjhmWFNJTHV6VWU0Tm1KTVlvQTVGbTZ4a2MvSVZFaDB4ZjN0MytBL3c5TVF2d2lsRUdJQjZFakFvSHVJWER3YnBxaWJ3bU9hbTFDSUNUdVJka2JucTd1dU5idmhraFFNZzNoSEpIOXZKMGVkZTVlbTlYeXlpMk8zUjNyamM2YmtRcEQ5RStQeWhoTU9zWW1TckdLOXoiLCJtYWMiOiJkZjNkZGY4NmI5YTgwZDk2MDc4ZDllOGQzNjI3ZTI0NjI1NDM1ZjBmNmFjYTZhNWRlNGE5YTlkM2FiNTVmZTEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848512919\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1559747668 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559747668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:50:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkzU0o2NlR3K28zdXJNMjhMZk1rSHc9PSIsInZhbHVlIjoiWXhNQmhLaXVPVFpzdURrNHJieGFxQWtrYVlVSks5My9sSURTcEx5MlVIQWFPOHRMRFcwT1lpeDBiMFJVT2NJaFVFdmdVbmpMRjVqOUdpekR4Q29hcWEwZlhvN0FscjVOaGdxMXc1Z2pTSnk2SlovaUU3bk8vMnpxWTJLeFY2RzBmVHA5T0pNWmRrNUo1OVVHMEhLZGJkRlYyaEh5eHhqWThTbDlqMTFvTWZBTGRkaDJIZFpRUkZUNDBqdlEvRVd3aUNDL3VUaU5JV0htR0dwMFd3Y0VNdU00c255cWYvRUNXcEJCakUwcWZaN1ozVExlalEzaDlMcG03bmZMd0FhK2dPUmJnVm1IMWRoRHM1MFQwWWpGeEFIUHhPK1NYdjhCQUNvbWxCN0MrVWxYRHVianI1Y3VnSnRmd2djVHd1eUwvZC9yWGJCTlI4cmlqd2dwRzFqL1RZeFNyb255TzhwSFJmWExjL3ROaVhWbjFIUFl0eXlHUTVicmdUd1E0cUpjMFNnVzZTbTNIcUN2dElkczZrRXFCSkM5MHlqNFFGRTIzdlpXdk9QYmNCS29zN28zc3RMcVBZRHUvcVhHOCt4OGVrN1hVMW9vU0pieVBFS2ZwUkgyWWRycy9oQlQ4TFhEZ1QzV0JlaHVGNkE5OEU5M0h1c015dG5PeGdTSVMwS3YiLCJtYWMiOiJjM2U2MDk3MDk3MDExNTg2Y2RhNzQyZWI0ZWI2Yzk5MzA3YmVkOGFmN2Q2Y2I1MDJkZTE2MWUyYTE1ODNhNjZkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:50:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1qY2lJMTIrWm9lQ2J4emlkSGIxZUE9PSIsInZhbHVlIjoiNW1nNU0rbzBQTVlLOHhuQ1lUU1dISWdmak5JdFltTlh3NFhrSmNLSjE2UFNRLzdFcjFMQy9hcnFlOVdnNFNJNVpBMHBaRjdaYVBKQ2RPbGxvamxneGRnQVM4SFlWYTRPSFdLTEt4N0RxM0dFc0tLdGN0aC9rZU1QejNZcllzTkg1RDh4ckRFZ2RNVlRHMmR5QS9kSGl3SEsxbUZBSFZKa0Qza293Ri9KNU9yMXBQS29PV2lmd21zTGwxNWtia1NMS1FBaGFNSUlZbmFQbkx6cGpvT3pScjFOYkRtR3RuanUzeFRlcmdHakVqYkZlZmQzeG1IZlFjM1hubkd1ODR2ekYvRW9IVTNKOVNJcEdOdng4bndxVUcrNjg2QzdRZ1RWWnpBR3hXQ3BmazJtRTlzbzlSUEdQVnYwb2plUUkvRTNZUmdZZ3pPL3dUQk40Z3UzUUllRTBuT1NURU1BR20yZ2dHTk5PK2Q3Y3VkMHc3cVpIWVpZQktCUVlSK3ZSU1lGbjBhSHdvVzhZYVM0TFNqVFZ0bW10RUpndVE5WnU3ME9HMVdONlFGSmltYWE2c2VTRlFFNEVSendPR0tOOGw1RFppWmRjc1NnRkh0VHYxVE5tNDlwWm9DYlRqUmF2SmFXUzh6L050REdLdGU2QnBVcWN5czlRemZ1NjFyR3o2QSsiLCJtYWMiOiJkMDYyOTBhMDRiMTRkNTZlMmQxM2NjZTRjZDM2YTYzNDVkZDI0MjE4YTcwZTc1ZDAwNzU4ZDcyMWQ3NzA3ZjM4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:50:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkzU0o2NlR3K28zdXJNMjhMZk1rSHc9PSIsInZhbHVlIjoiWXhNQmhLaXVPVFpzdURrNHJieGFxQWtrYVlVSks5My9sSURTcEx5MlVIQWFPOHRMRFcwT1lpeDBiMFJVT2NJaFVFdmdVbmpMRjVqOUdpekR4Q29hcWEwZlhvN0FscjVOaGdxMXc1Z2pTSnk2SlovaUU3bk8vMnpxWTJLeFY2RzBmVHA5T0pNWmRrNUo1OVVHMEhLZGJkRlYyaEh5eHhqWThTbDlqMTFvTWZBTGRkaDJIZFpRUkZUNDBqdlEvRVd3aUNDL3VUaU5JV0htR0dwMFd3Y0VNdU00c255cWYvRUNXcEJCakUwcWZaN1ozVExlalEzaDlMcG03bmZMd0FhK2dPUmJnVm1IMWRoRHM1MFQwWWpGeEFIUHhPK1NYdjhCQUNvbWxCN0MrVWxYRHVianI1Y3VnSnRmd2djVHd1eUwvZC9yWGJCTlI4cmlqd2dwRzFqL1RZeFNyb255TzhwSFJmWExjL3ROaVhWbjFIUFl0eXlHUTVicmdUd1E0cUpjMFNnVzZTbTNIcUN2dElkczZrRXFCSkM5MHlqNFFGRTIzdlpXdk9QYmNCS29zN28zc3RMcVBZRHUvcVhHOCt4OGVrN1hVMW9vU0pieVBFS2ZwUkgyWWRycy9oQlQ4TFhEZ1QzV0JlaHVGNkE5OEU5M0h1c015dG5PeGdTSVMwS3YiLCJtYWMiOiJjM2U2MDk3MDk3MDExNTg2Y2RhNzQyZWI0ZWI2Yzk5MzA3YmVkOGFmN2Q2Y2I1MDJkZTE2MWUyYTE1ODNhNjZkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:50:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1qY2lJMTIrWm9lQ2J4emlkSGIxZUE9PSIsInZhbHVlIjoiNW1nNU0rbzBQTVlLOHhuQ1lUU1dISWdmak5JdFltTlh3NFhrSmNLSjE2UFNRLzdFcjFMQy9hcnFlOVdnNFNJNVpBMHBaRjdaYVBKQ2RPbGxvamxneGRnQVM4SFlWYTRPSFdLTEt4N0RxM0dFc0tLdGN0aC9rZU1QejNZcllzTkg1RDh4ckRFZ2RNVlRHMmR5QS9kSGl3SEsxbUZBSFZKa0Qza293Ri9KNU9yMXBQS29PV2lmd21zTGwxNWtia1NMS1FBaGFNSUlZbmFQbkx6cGpvT3pScjFOYkRtR3RuanUzeFRlcmdHakVqYkZlZmQzeG1IZlFjM1hubkd1ODR2ekYvRW9IVTNKOVNJcEdOdng4bndxVUcrNjg2QzdRZ1RWWnpBR3hXQ3BmazJtRTlzbzlSUEdQVnYwb2plUUkvRTNZUmdZZ3pPL3dUQk40Z3UzUUllRTBuT1NURU1BR20yZ2dHTk5PK2Q3Y3VkMHc3cVpIWVpZQktCUVlSK3ZSU1lGbjBhSHdvVzhZYVM0TFNqVFZ0bW10RUpndVE5WnU3ME9HMVdONlFGSmltYWE2c2VTRlFFNEVSendPR0tOOGw1RFppWmRjc1NnRkh0VHYxVE5tNDlwWm9DYlRqUmF2SmFXUzh6L050REdLdGU2QnBVcWN5czlRemZ1NjFyR3o2QSsiLCJtYWMiOiJkMDYyOTBhMDRiMTRkNTZlMmQxM2NjZTRjZDM2YTYzNDVkZDI0MjE4YTcwZTc1ZDAwNzU4ZDcyMWQ3NzA3ZjM4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:50:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1719534518 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1839</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1610;&#1583;&#1575;&#1604;&#1610;&#1575;&#1578; &#1588;&#1603;&#1604; &#1605;&#1581;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1839</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719534518\", {\"maxDepth\":0})</script>\n"}}