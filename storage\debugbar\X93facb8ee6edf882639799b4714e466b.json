{"__meta": {"id": "X93facb8ee6edf882639799b4714e466b", "datetime": "2025-06-30 18:10:42", "utime": **********.29372, "method": "GET", "uri": "/search-products?search=&cat_id=25&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:10:42] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.2725, "xdebug_link": null, "collector": "log"}, {"message": "[18:10:42] LOG.info: ✅ انتهاء searchProducts - الوقت: 16.13 مللي ثانية - المنتجات: 2", "message_html": null, "is_string": false, "label": "info", "time": **********.288275, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751307041.736663, "end": **********.293742, "duration": 0.5570788383483887, "duration_str": "557ms", "measures": [{"label": "Booting", "start": 1751307041.736663, "relative_start": 0, "end": **********.179289, "relative_end": **********.179289, "duration": 0.4426259994506836, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.179298, "relative_start": 0.44263482093811035, "end": **********.293744, "relative_end": 2.1457672119140625e-06, "duration": 0.11444616317749023, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47777656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01219, "accumulated_duration_str": "12.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.224237, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 17.145}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.238332, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 17.145, "width_percent": 5.989}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2642312, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 23.134, "width_percent": 5.086}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.267319, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 28.22, "width_percent": 3.199}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`category_id` = '25' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.273538, "duration": 0.007549999999999999, "duration_str": "7.55ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 31.419, "width_percent": 61.936}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.282987, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.355, "width_percent": 3.774}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.285963, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 97.129, "width_percent": 2.871}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1254370692 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254370692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271607, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-852563973 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-852563973\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-75664811 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75664811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-610107966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610107966\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-968505065 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhaNDd3dkY1Z3pMLzZsMnZXYU9wU3c9PSIsInZhbHVlIjoiS0xkQVpnLzE2amNPd2ZnMWhvVDA4QkJMRG5uTFVrRDNHTTB4MEVXQmpMbFJDYitCWW5FVzZiYXRVWCszcDhuQ2h6QmxobEpYdGZBYlFyVFNSK2wxcHlUeDlBeWdid0Exb0MzYkd1b0tjUEszeU9wM0sxSmIrRERLNFpFKzQ5bVVxNnFzbEszWXBQaGpLN0RyMXBhSUE4YU1rVHF2b0dBcGFqRW9pM3VuYmZETjJBNEhmL3kxeUVaaDJoc2dqOXdpbSs5amVrOWY0aWZpUmNmNGxYcHNJNXRVaXhGSi9zVUV1WGVtMElZSGxrMGVOOWRxb3lpZERsclo0V0d0UWZlbnI1enMvL0ovOGt0YmxDaUdqRlJmWndZc0VxLzU0aURtc2hLZzdJRlJlNW1YVlVuUkppU1RPNU1DaC84eWU1aEJkSHFUbllEWlY1RHZzdzdtR01QcHErSnlnSHFIanVVQjZocVF6am1VTUdJVWJXZzRxTElWa3l4Q0xLbVBmRHNVSHg1bjNqWXF3QjRSMFc5QlJMVnQzc2ZQWWFtWi9LTENOamJuYU5nV0w0T3QwZHBjalhtaGkzWG41Ui9FZkpNMjNTWk1wSUFzZ0NjeDRGZ3FZOVlIWmtvU3BQL0xaVDcvK0Q2UGdZNjgwb1dQQjBJU0o3Q2F5UDFiRmFxNHNzMVYiLCJtYWMiOiJhYjMyY2Y4NmNhOTIyZDM4ZTY0YmVhZGM5OWU1OWJlYzRmZDExOTdkNDA4Njc4NmFiMTE1ZmRiZWY1NDZkMjI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNQM001VytOc1REclozMnNzNWpiVGc9PSIsInZhbHVlIjoibXNxRTZKNkI0OGZzYUZEQ3czTGlQdExodGZJYW9lcU9KZXkxSkJHamk5Z0QwYUhwOUdScGd3WktScVZPR3JmbWltTktCWHArclJVeVZuSldhQ3k2YytqK1pOWE5aZElLMkVrR3hValBrLzNuL0o5UTRqb1l5WXdQVkN1ZXZPL09MZjhUUlBhQVN0LzhEZjd5MjA3b3M5WU1WeHRJNXh2aXdRcFh4WmFwR0txMTVPcHh2dit5UUhqYVptQ041MWZIZ25hRnhyY2JRdmIxcEhGcW9QcVpTdTRPZXpCeSs0S1lFbG5WMWxWZGhVKy9ZK0RSR0tBZG10c0MzaFJXK0pqRndEK3h4WEdCdVN2WGV5cWltbDkyRmgyalBYczlEWjJHQm5KTEY0QzU4YStwek9ZRTlWN3dwOWJqSGRWNy93V0J3MEFIZC9YV20vdzNNUlhrTjFEQlQ1M3VjUHA4YWh4alR4dlp4Q3EzQnlzVVZKY0FFSTJLVW5hMEYwbFRJaDVtOUlaSkhCMkp3ellkUmJOeG4wWFRJd2lJUG5KNmRucjNhZ2JCWUtwMit1WG9BQk9HNjdCU1hDSHJJS1RjYmd6LzRzdm1VL2ZhRWNMejdSTTNDQWhyN2FHVXEvcm53cmpsYkRiYkJEV0FaeGoxcGRHY2M0RlladEVSWWlHNjhRYnEiLCJtYWMiOiI5NjNmNmFiMWI3MmYxN2YzYzk1ZjAyMDJiYWI0NTViNmNjMmE0Yjk5ZmU3MzA1ZTIzZTI1YjU0MzcxNGZkZTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968505065\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2136325088 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136325088\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1209293589 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhXQjJQWnZkLzRHQ2hyTW53eGZyK0E9PSIsInZhbHVlIjoibzZFWVpxRjRWQ2RxdjU4YUVZVVlNMVM5c0JjdG9tRGJTNlA0Y0orVFlSY0VJYVlxSVNERnlYUWNXK3RQWkdYTDRPM25RZWUwaDVxMHRFWVo0TlVRd1l6Z1d6VWY2MXp3aG5rdjZuWTVJZ1BDSGtLRU1PU1lUWk03WTZwaWk2azc1dWE3d1ZlZXBEVmhyNXJjYmpvcjNYV1BsUnJNNmNGZUJpSmFFalhmRnJVeFBDMHpEM0UrV2pZU2NKK1JTTnhoNGtSUmJ1c3orbkpobHRQbjAveThWVEU4c0I2S3JERHByemt6aG9nWjBLQWdvMkkzN3JBeVVjdXhRQzhaQjlUa1IybmV3YVJXY3gyU3BLQVhyWkNTYTZlbEdQQUdXdHdSRjg4U0U4a2g5RGc2RDNtWHBXTUprbmZNeVFzUUJ6RW5OWHZLQzlUTFZRZ0lJamMzZHRlMDFoY21wN1hHMnl1Wk9uWmVkd3Y4ZjdsN1ptSWVmYjRZOUFnUDIxanZzMmhyQzRvaGZKcm5uVXNRZHhBb1hLUkJvRVFGSVUyZG56UVZ1RnBFbmVBRlh0TG5zeUtua1FsakE5TDBOdjc0QnIwZzluWEQ0ZWZpU1JKdE9VaDFleks2aW8zMEpELzZpTHhpNnE4Qkl0ZWV6SGdDeFhZVDVFb2E3YTVTdEdKT3hNcUoiLCJtYWMiOiJjM2QzMzQ1NDM0Nzg0ODcwZTVhNTFlZjFiNTI2ZWQzMTg3OTk0NTIwMjc1YTI1MDliMTA3MWRlZjQyYTRjN2Q5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZFV0xmdXhoOHNkYmtwTHVuSzhYcXc9PSIsInZhbHVlIjoiSWVkd09nMUVQWE1PQVd2U3g0d29SUUZjckZJTUM3Z2lYb2ZGaVIyUUZjYlhXRm9DVGgyanA2Q1R6bGEwTldUNFFyOUZzdDZMbVJ2QnMydlFsT01yY0V6WnA5UzYxVDc1dU4wVjhPKzRLL3Nld2dDbllqbXJNT1NneTFRcjVnREVnelBXWVBTWVdIc2t2NXZLMW9zM2t2Mkk0U2dEVzU0R0J5Wkc5S3pXeEdmZnlqVG13UTZ6Wjl3d1BJYitNekg1UEVlaDZsQXhxL2pFR1NBbXcyblRLZG1HRk5HWFVZUFlLM0pCNitnU1ZBQ3dVYVM4SU0xUHFyWkpSTnhINVg3S0VQTGF1M21oNXU0dzB4RjBqL3FKcFZyNmVqREZLUndDbytuTFM0Sm5sL1RGcldkcjhNNE1QSkxrekhaSnB0K0hVd2FYelkwSXRENVF6MjVGZ09GdFViZlY1SUVRbi9pTkVlTUtpU1FrQW95Y1hxS1RTOUVkcGdvMTJhbDlXT0Q4ODBPdUthSHJ2dHdLRTBZODhhQU5qMTZ0SDExdHFvYzE5czllUEc1QXhBb05NaUp1WDIyR1FhUCt2SWpVYnJYNnRpMldHU3d6cmc4eGU0Zk5kRjQ5YWRpTllBUDF2cERJMWNmYmJxaWpzYVBPcVdiUGJqYzZQbS9SRklxemFRdjYiLCJtYWMiOiJjMTkxYjAwMTVlYzM4YzlmMDUwZWRmMDhiOTE2NjI5OTlkNjZiMjMwMWQ3ZTdlNjViNGE4ZGYwMjlmOTA3NWQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhXQjJQWnZkLzRHQ2hyTW53eGZyK0E9PSIsInZhbHVlIjoibzZFWVpxRjRWQ2RxdjU4YUVZVVlNMVM5c0JjdG9tRGJTNlA0Y0orVFlSY0VJYVlxSVNERnlYUWNXK3RQWkdYTDRPM25RZWUwaDVxMHRFWVo0TlVRd1l6Z1d6VWY2MXp3aG5rdjZuWTVJZ1BDSGtLRU1PU1lUWk03WTZwaWk2azc1dWE3d1ZlZXBEVmhyNXJjYmpvcjNYV1BsUnJNNmNGZUJpSmFFalhmRnJVeFBDMHpEM0UrV2pZU2NKK1JTTnhoNGtSUmJ1c3orbkpobHRQbjAveThWVEU4c0I2S3JERHByemt6aG9nWjBLQWdvMkkzN3JBeVVjdXhRQzhaQjlUa1IybmV3YVJXY3gyU3BLQVhyWkNTYTZlbEdQQUdXdHdSRjg4U0U4a2g5RGc2RDNtWHBXTUprbmZNeVFzUUJ6RW5OWHZLQzlUTFZRZ0lJamMzZHRlMDFoY21wN1hHMnl1Wk9uWmVkd3Y4ZjdsN1ptSWVmYjRZOUFnUDIxanZzMmhyQzRvaGZKcm5uVXNRZHhBb1hLUkJvRVFGSVUyZG56UVZ1RnBFbmVBRlh0TG5zeUtua1FsakE5TDBOdjc0QnIwZzluWEQ0ZWZpU1JKdE9VaDFleks2aW8zMEpELzZpTHhpNnE4Qkl0ZWV6SGdDeFhZVDVFb2E3YTVTdEdKT3hNcUoiLCJtYWMiOiJjM2QzMzQ1NDM0Nzg0ODcwZTVhNTFlZjFiNTI2ZWQzMTg3OTk0NTIwMjc1YTI1MDliMTA3MWRlZjQyYTRjN2Q5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZFV0xmdXhoOHNkYmtwTHVuSzhYcXc9PSIsInZhbHVlIjoiSWVkd09nMUVQWE1PQVd2U3g0d29SUUZjckZJTUM3Z2lYb2ZGaVIyUUZjYlhXRm9DVGgyanA2Q1R6bGEwTldUNFFyOUZzdDZMbVJ2QnMydlFsT01yY0V6WnA5UzYxVDc1dU4wVjhPKzRLL3Nld2dDbllqbXJNT1NneTFRcjVnREVnelBXWVBTWVdIc2t2NXZLMW9zM2t2Mkk0U2dEVzU0R0J5Wkc5S3pXeEdmZnlqVG13UTZ6Wjl3d1BJYitNekg1UEVlaDZsQXhxL2pFR1NBbXcyblRLZG1HRk5HWFVZUFlLM0pCNitnU1ZBQ3dVYVM4SU0xUHFyWkpSTnhINVg3S0VQTGF1M21oNXU0dzB4RjBqL3FKcFZyNmVqREZLUndDbytuTFM0Sm5sL1RGcldkcjhNNE1QSkxrekhaSnB0K0hVd2FYelkwSXRENVF6MjVGZ09GdFViZlY1SUVRbi9pTkVlTUtpU1FrQW95Y1hxS1RTOUVkcGdvMTJhbDlXT0Q4ODBPdUthSHJ2dHdLRTBZODhhQU5qMTZ0SDExdHFvYzE5czllUEc1QXhBb05NaUp1WDIyR1FhUCt2SWpVYnJYNnRpMldHU3d6cmc4eGU0Zk5kRjQ5YWRpTllBUDF2cERJMWNmYmJxaWpzYVBPcVdiUGJqYzZQbS9SRklxemFRdjYiLCJtYWMiOiJjMTkxYjAwMTVlYzM4YzlmMDUwZWRmMDhiOTE2NjI5OTlkNjZiMjMwMWQ3ZTdlNjViNGE4ZGYwMjlmOTA3NWQ4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209293589\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1161282672 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161282672\", {\"maxDepth\":0})</script>\n"}}