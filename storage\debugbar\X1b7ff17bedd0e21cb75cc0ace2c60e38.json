{"__meta": {"id": "X1b7ff17bedd0e21cb75cc0ace2c60e38", "datetime": "2025-06-30 18:58:19", "utime": **********.36714, "method": "GET", "uri": "/search-products?search=&cat_id=25&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[18:58:19] LOG.info: 🚀 بدء searchProducts للمستودع: 8", "message_html": null, "is_string": false, "label": "info", "time": **********.34681, "xdebug_link": null, "collector": "log"}, {"message": "[18:58:19] LOG.info: ✅ انتهاء searchProducts - الوقت: 15 مللي ثانية - المنتجات: 3", "message_html": null, "is_string": false, "label": "info", "time": **********.361582, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751309898.926179, "end": **********.36716, "duration": 0.4409811496734619, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1751309898.926179, "relative_start": 0, "end": **********.284238, "relative_end": **********.284238, "duration": 0.3580591678619385, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.284246, "relative_start": 0.35806703567504883, "end": **********.367162, "relative_end": 1.9073486328125e-06, "duration": 0.0829160213470459, "duration_str": "82.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47771512, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1342</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.009130000000000001, "accumulated_duration_str": "9.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.316933, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.182}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3267329, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.182, "width_percent": 4.053}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3406131, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 22.234, "width_percent": 5.586}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.342655, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 27.82, "width_percent": 4.381}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname`, `u`.`name` as `unit_name`, `wp`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` as `wp` on `product_services`.`id` = `wp`.`product_id` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` left join `product_service_units` as `u` on `u`.`id` = `product_services`.`unit_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `wp`.`warehouse_id` = '8' and `product_services`.`category_id` = '25' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "8", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.347634, "duration": 0.00525, "duration_str": "5.25ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1265", "source": "app/Http/Controllers/ProductServiceController.php:1265", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1265", "ajax": false, "filename": "ProductServiceController.php", "line": "1265"}, "connection": "kdmkjkqknb", "start_percent": 32.202, "width_percent": 57.503}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.354755, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 89.704, "width_percent": 3.943}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3572721, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.647, "width_percent": 3.067}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.359484, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 96.714, "width_percent": 3.286}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-24636019 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24636019\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.346221, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1128514419 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1128514419\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-177406054 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177406054\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-311887526 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-311887526\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1228698382 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6IkZDZnBURXJ2N3ZNb2hPcDdLL2RWS0E9PSIsInZhbHVlIjoiUnFWaGgvUkhOcmVKZC9rRGFlVE1ZNE9YdTFiUUNYUGF1dmlaWEVyWjNzK0JtcHJvQ1FJNUJPN2VTN2JrcVU5aDd3ZHd3WTliRGc4S01jWER0RFc5bGRKMXlWNGZya0N3T2pZMmFqU0ZoeVlyV0VJZEtVZ21Lc21YSEczN1VqSnZHYmhsN3RERHZwcjdaVXMzZUplT3QyY3orQmVsUzJ4dTdLK2NmSGxDUUJLRVJXSTJvWWM2VzFFejBpSllFUXdBZ1pPc0gyVmw1L21Pd3NyRng3a0NsVVJvQW56bTVIUlI0T005MnM5eG5IdnJ6WHpaOGdLbUlaeUUxK1VYVnFtUHFKcjFvS20rbDFialhXK0FxYTJDQi93akM2SVRqVWxNbkYvTHVkOW96cmVoOTVFNnlVaE0vWkNsaHhYWG1QY1plWE9yVDI5WlZVSk5kcTJPT09Da3lnNWFWSU5tcjZ3QzlKemFhRm5qV21jTzFuQzVpUU50R0loQ09HVUQ3TE5WU3RCMVYxUE1RaEJFV2ZtRHZKaXdEWjhWZmZrY21NaW1YTnh6U1hpZHNNWE9PMTJKOGFTYnhlV2tXMVVJa0s4MnRCb2MzVHc2TEh0UUcrZStxM1NhYWtxN2RSbmFUWXMwWmFzdUdreld6N25TT1MydmxzYlVubzZXNlZyK1lOMGkiLCJtYWMiOiI0MjlhMDZiN2VjMTg2NDg5MjQzNDQwYjA4OTAzOTMwYzhjYzFlNGMyNGZkNGFiNTAwMjE2Y2FlNmJkZjk0YTNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNJN1ZMaURSSXU1K1h4OFczelpENGc9PSIsInZhbHVlIjoiRDV1Z0RvNnBpa3ZjUzQwdmJHN0dQemZhMUM3L2tHNkhwdGJRdVFHem5JMUtiZHcyQlJJS0h4V3hHRHIvVFJ1N042TFV2c3RxeWhhNmJsendITE1EdUpaRjZQZnpqOGlGNWdPRTF2dUl0c1R5OW10SjFKTDdGbkpMN1lRU0ZlVCtZdU95QUdmOHp2eXpMWnV2WkhqTE5ZQUNxLzVWZjkzWm5TVGdiNWJYc2tuS0tmM3hSWHhzSVpIcG9ONXBSbWZZRGhhYUZjeFQySUd5Q0h0azc4MTROMlJBTkk4d1VJanNiWmlSWGJhMXNUNzZCOTVnYWVkVVpEbVRMeExISnVlTC9wZk5QN0RWREw3L2xtSkhiYWNGOWhmaTBtYVhGUnRVa1lSNE1FRzNFcnFCNlVLeUpjVm5GQjJNQnRxWCtJbzk1SG15N2RUanNqOTNIQ2REUEp3cExmSzQvbllTdHlmVDJPK29xYjhXR0E1NVpzUko0UFNQQUFHOGgzOWJyQTNNRGZhOVU1ajN6aUhDV0lmZGNBTDZQSytRT1BYNjZLS2dIcDFreFJJR2drWmkySDJKZTdFZWNoM29naFRRSUxGVWlUNFFnM0llTHV0eEtGWGY1Z2N3QmEwVHhGaEkxZXppRG9CUVBpSEZ4VHJGeWpjMG51TkpIMmhFNmNaWDhxL3MiLCJtYWMiOiIzZjgxYWJjNTRkYzI4Y2EwMTc1MDIzZDIyMDBkNWFiOTI3NWQzODhiMzdjZmQwOTkyNmMzYmMxODdjZGRjYzc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228698382\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1813206858 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813206858\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-343449038 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:58:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1yaHp3OUdjL0gxR3J1MXNPSDZyZEE9PSIsInZhbHVlIjoiVEdRNlI1cWhGcStKaUcxV2kwM0ZpOW81Z2xVdDdySlg4S25SNFoxVnNmUFBpZmV0QmNwWFNmRDBpM01Jc0tMc2ZqRnluK3lyYkdFZTdTaUlyNDNXMnNHbmxSZDZTVXFwdk5zVDBPOFVGN3lid0w1T2kyUmM3SnJqVnBGRWFmbHFJSTVoMGxnYjYvUmlWVUNuWGthSXZPQ0ZNSkl6M1gvcSs3ZWE3UnZ0ekErV2tJY3hXZWNZUjdyZWV1RVo1STNzWDlNcDlWY29ncVllZFYya1JTNkIzMjJKRkRJYS8rMittaEk1STUxNVl6U1hNRURrSkZ2ckVaMlFxcWVFTHZNZ2JETlg5L0toOC9rVVdWa2NsWndvNXZPMzZMQWdYdlk1dDJ4MDJtU3JEMXV2dHZVMWJQYS93dVZPbXgzb0NCcE12Zk5SSEZDNTFpWVNSaDZYMitHcitFYTRhdWNwc0RxZkZBVWNQbTdNVU9NNWhwaXozMTk1Vlc4T1pzTGN1aHRrVkh6YVRMaUVTaTNFdEYrTVBYdURNbXQrMXpXcGgwSXRpMVl3NmlKYjRUdUw1K25lQXM4VDcrbHpVMERzMEJHbGJjZnNwYkhQQjZuREdwQ0UzNHg5WmhOV1dKWWg1NzdPWnRodFFLbHlVTHBkR0RkU0VKQndHOGdaTk5SUHdjRzUiLCJtYWMiOiIwZWJkZjM4MDBiYjUxMTFhZGE4MzIyODNlMmM2MzkwMmJhMTIzNDVmYzlhZjk4NGIzZGU1YjYwYjNkZTYwMDIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:58:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldBVlVQS1U4ZjJXbVBNallWMHZ3T0E9PSIsInZhbHVlIjoicjhEejBDMVFuMkRqczZVZ2lSVmJYMFFBNm1sUzFSa0s0eG1xcFkzVzhyV24xdEZjVEI2YisvU1dUMTZXZXd2WUdOUXc5RkVrQUpqaExRbjE4bDA2bGpCYnQ4S0ZXMEt6T09KWU9UMDYzQlNpSzAwdHp6aXlHV2d0Wng4VlV6MmFmR2NkQ2s2bElLNWUrMEdrY1NyckpuUU1mMDRkS05xcy9uWlNQQUVQQS9JV2JuTWJlVDdhUGRMUlU4SnRuZnhSY1Ivc3cwVTFtZGd1Wm5oTndNTUt6OFNwSnBQd0E0YmpEOWVOamx3ZThsYklzSldxSzZUTmZHbjFhdnZSQ1ZzZ0Y5cHlaOWdWYXhNbHFBQjZIZDRJQk1vbE1Nb0ZZSnRKQmxNK2lrSmJJR0tKcDVYU0tvWGNvMGh6b0wrbURQbzJuSCtOS0g0Mm5BWXFycG1WVlYrUU1ZcUN6d3BmZ2gzaHF6WEh6LytWWGxzVllXMGgvK1NQQU9jRFdrZkJnR25rdFZFY2hId0RqalRQeGduemk1OWw0blpiS1g0cXZhSDVJcEVaTEh4ekRjYW5yN1lZZzdud2hrUWZjcjdZTmxsTFovSmxMTnJvakhQcXArTTluUHV1VjJlV1B1V1pya3o3ZzBsWHVzL21NdDZNSnIvNjZlNUQ1V1Eway9TTC8xRi8iLCJtYWMiOiIxMTQ3ZmU4NWY3YjNlZTMwMzYyYjQ5NmU5M2ZkMjc1NDAyNzc2ODJhNWZjMGM1Yjk4ODdmMjgzMjRjODU5OWRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:58:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1yaHp3OUdjL0gxR3J1MXNPSDZyZEE9PSIsInZhbHVlIjoiVEdRNlI1cWhGcStKaUcxV2kwM0ZpOW81Z2xVdDdySlg4S25SNFoxVnNmUFBpZmV0QmNwWFNmRDBpM01Jc0tMc2ZqRnluK3lyYkdFZTdTaUlyNDNXMnNHbmxSZDZTVXFwdk5zVDBPOFVGN3lid0w1T2kyUmM3SnJqVnBGRWFmbHFJSTVoMGxnYjYvUmlWVUNuWGthSXZPQ0ZNSkl6M1gvcSs3ZWE3UnZ0ekErV2tJY3hXZWNZUjdyZWV1RVo1STNzWDlNcDlWY29ncVllZFYya1JTNkIzMjJKRkRJYS8rMittaEk1STUxNVl6U1hNRURrSkZ2ckVaMlFxcWVFTHZNZ2JETlg5L0toOC9rVVdWa2NsWndvNXZPMzZMQWdYdlk1dDJ4MDJtU3JEMXV2dHZVMWJQYS93dVZPbXgzb0NCcE12Zk5SSEZDNTFpWVNSaDZYMitHcitFYTRhdWNwc0RxZkZBVWNQbTdNVU9NNWhwaXozMTk1Vlc4T1pzTGN1aHRrVkh6YVRMaUVTaTNFdEYrTVBYdURNbXQrMXpXcGgwSXRpMVl3NmlKYjRUdUw1K25lQXM4VDcrbHpVMERzMEJHbGJjZnNwYkhQQjZuREdwQ0UzNHg5WmhOV1dKWWg1NzdPWnRodFFLbHlVTHBkR0RkU0VKQndHOGdaTk5SUHdjRzUiLCJtYWMiOiIwZWJkZjM4MDBiYjUxMTFhZGE4MzIyODNlMmM2MzkwMmJhMTIzNDVmYzlhZjk4NGIzZGU1YjYwYjNkZTYwMDIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:58:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldBVlVQS1U4ZjJXbVBNallWMHZ3T0E9PSIsInZhbHVlIjoicjhEejBDMVFuMkRqczZVZ2lSVmJYMFFBNm1sUzFSa0s0eG1xcFkzVzhyV24xdEZjVEI2YisvU1dUMTZXZXd2WUdOUXc5RkVrQUpqaExRbjE4bDA2bGpCYnQ4S0ZXMEt6T09KWU9UMDYzQlNpSzAwdHp6aXlHV2d0Wng4VlV6MmFmR2NkQ2s2bElLNWUrMEdrY1NyckpuUU1mMDRkS05xcy9uWlNQQUVQQS9JV2JuTWJlVDdhUGRMUlU4SnRuZnhSY1Ivc3cwVTFtZGd1Wm5oTndNTUt6OFNwSnBQd0E0YmpEOWVOamx3ZThsYklzSldxSzZUTmZHbjFhdnZSQ1ZzZ0Y5cHlaOWdWYXhNbHFBQjZIZDRJQk1vbE1Nb0ZZSnRKQmxNK2lrSmJJR0tKcDVYU0tvWGNvMGh6b0wrbURQbzJuSCtOS0g0Mm5BWXFycG1WVlYrUU1ZcUN6d3BmZ2gzaHF6WEh6LytWWGxzVllXMGgvK1NQQU9jRFdrZkJnR25rdFZFY2hId0RqalRQeGduemk1OWw0blpiS1g0cXZhSDVJcEVaTEh4ekRjYW5yN1lZZzdud2hrUWZjcjdZTmxsTFovSmxMTnJvakhQcXArTTluUHV1VjJlV1B1V1pya3o3ZzBsWHVzL21NdDZNSnIvNjZlNUQ1V1Eway9TTC8xRi8iLCJtYWMiOiIxMTQ3ZmU4NWY3YjNlZTMwMzYyYjQ5NmU5M2ZkMjc1NDAyNzc2ODJhNWZjMGM1Yjk4ODdmMjgzMjRjODU5OWRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:58:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343449038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}