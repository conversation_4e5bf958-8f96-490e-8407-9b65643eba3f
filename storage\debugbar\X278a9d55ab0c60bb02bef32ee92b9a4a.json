{"__meta": {"id": "X278a9d55ab0c60bb02bef32ee92b9a4a", "datetime": "2025-06-30 16:17:46", "utime": **********.954498, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.435008, "end": **********.954523, "duration": 0.5195150375366211, "duration_str": "520ms", "measures": [{"label": "Booting", "start": **********.435008, "relative_start": 0, "end": **********.867103, "relative_end": **********.867103, "duration": 0.4320950508117676, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867113, "relative_start": 0.43210506439208984, "end": **********.954526, "relative_end": 2.86102294921875e-06, "duration": 0.08741283416748047, "duration_str": "87.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709328, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0199, "accumulated_duration_str": "19.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.903433, "duration": 0.0182, "duration_str": "18.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.457}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.931094, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.457, "width_percent": 3.719}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.937877, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.176, "width_percent": 4.824}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-994017979 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-994017979\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-278076768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-278076768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-292529770 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292529770\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-643626246 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; _clsk=1w4ne3l%7C1751300255121%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjQ2MktZY3pWSmdncnJ1RUwzNEpwUVE9PSIsInZhbHVlIjoiMElTcm5kWTZIbG1JengyQ1RxZjlkVi9QZWRiL2dzUXJKVkpTTkg2RzFmQUhPWnk3aW1xY1NScVFaMThzbGMvR21ZOUplT2hoZUJBRUpzTnYyYmYzUzRPT1VXaitra2wzcTFvVnQ0R0R1dktpMk9lb3REd05jM2ZyUEdXb1JrRllUWWhWZmhpbDJ6amNJc3QvZlZZampDTGdRTDNrYUpvNmpydDRxaXcvZDdQNHpDc3BvQzRlY1oxb0VSb0x3aXdUQVJHMGd5Vk1vdXpoMjlCTFNDRWtaRStJcjZMcVpJWUNabXVGZjVYQ05LcTV0aUdlbnJFMXk3bXpOSnhCUW5DOVEvc3lCVFkrQ3I3ZHhEeUhQeHNCa2M1NHBPSGFEK1BIQXQ0UVlvbGR6VG9hMVhsb1dGZ0QrR0QzZUhleWhFUFRrZWhUb2NEbWNiREwrMHdjV1RoS1FzcWNxRzFEb3NHc1pFRVR6d1lCYUVBV2RIMUxxZXI3T09QSmxXb2RaSDhCYUpQWWE2WUxFRVBMOUlybmllR3B1NGRudWJlMEpKNURNdU9obnQxUXI4WHpTK0pWMm1yNlJGNDdyMUQ1c29hek5EQnpOSWh4YXN1YXdBeUZzZER6aFJ5RlJYaFc3ay83NGFZd0FQUzJIR0tSODR0TDkzdFgwekM3ak9OZGZsRkciLCJtYWMiOiJkZjA4NTZjOTI0YWRiODhjM2Q4M2UwYjQ4MDVkZGE1M2JlYTFiMjc5MmI5ZTNiNzI3NDY2MGNiMDFiY2QyYWE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5EL2ViWS9hMHFuYTZwa1ZjbUZ4NUE9PSIsInZhbHVlIjoiUmRQVFYwSjQ1ZTJ3S3JkQlFIV2tHYVVtcGIxSy9sOWQrQ3pMVHY5R21jd2tlam9hRGN0Qjk4YUFZb1ZtZjVHSVJFN2NCYUExYVl2NGtpeFVDbHNZWXB4bURObmRld0gwYVJ0STZFUVNyZWlMbnorRWNlOHd3YmJ5eU51STY0RUJSbVBZSU4wRnZJQVZYL01URGp2Z1dGTTd1QklZUnFqUFZsVGs0T0FpU2FhaDFPY3NsbUVkN0hJcUZYc3l3NVM3cDJCdkhSbFA1QWgzVjFzOXIybVAwVld1Z3U1NUpjYkZGQUszT3pCdFhybTNJQTRaN3ByOFVlTVZuUitvOFJIZWY5a2hSSnhxUGJqa3dtNGQwSW5RU1JXOERrUDRzeTlHc2xFK1JYQ3dxdExMMnNUdXZQN0NSalU1NXhKcktJQ0ErcWFiTDJOWFlxb0NrVW0zblJCalRnMENjYjlzZWwxYXRlT0NCMVdwd2tiZWZwREZGNGdmNkliM3krSDNBL3BEZk9WSTN3bWZ0R0wwbzgvSmgyNWhOUnRUellObHhKSzRzTkdsWmFzOXh5NUNZT2xmbVNIVUZTd1UwbVEyZGlNaXlkMmNvVkdVM1hMYVFOYWtoek1pM0JOV25OYVUvTlZIMlEvRlZ6Tm90Ly9LR1kyamxUQVFHWjAycVRnZU0wRk0iLCJtYWMiOiJjMzMwYjEyZDFkNzc5NDI3Njg0NDBjYzk5ZDI3NDc1NDRlNjM2ZWUzMzNjY2NiODM2ZGFjNDViMWMxNDYzODIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643626246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-269122300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269122300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1450381269 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imt6aWNhN2tyV2RjRmNrcFpXZ2xaSlE9PSIsInZhbHVlIjoia3dyb0h2aHNvSnpUZER4QWVDV0hyWTY2a0h2TGhaR1FZQWNFdENXcFVWMHpENHdJYWJ3RkpZWWFtOEFVMEU3UTFhRWdaS2xhcXpYakdKN29pMVNQVmVPZDdWaTlIZ0hqcHppd1RmQ25ySjgveUFUL3pFQ2pPalpxbjlLL0ZlZHEvbGRVenJpUFgrWmh0NDJSaWNMUjQ0bTh4YjJnTFN1ZlUybEowdEI1U2YrWjBMQ3cxdksxQ1MyMFM3MHdPL2RJamJrNStUVWxkOEhGQlFJQkNncWhyTUY5MjdCcUJpVHVuMlVsUEpXM3RDTVp2OHZRZHFhV0VwdjZFNXhnandSb2dkWENueTZhMEF1T3FRcHp4cGZkNWJDWkdJSS9NcGIrYVJqWFI4QUozUkFOUVoyVXU4aXUxSW0wOXY1YmtDb3FscndBaXFtVC9yczlDSktkRER1TDNPUlBCVkMrWG5vZHdYOXBxNWJKNVlENS9JRkJZN0tVd0hzTmlaSXBETGFIR2FlZGtYZWt5dGR5WE4wNnIwdzBjOEFiZG92b2JSbnZvWnJDOXRObHppQ1N1elUybGdRUEx5QnlEQ2kxVDI3WnVITSt2eWMvQ1d6dlllay9aWW1MZ3M3Y2lvSkdUVkpmSysvd2MyaGFSQmdaaHpnM0p3WVgxOGJtY0ppdlVEZTQiLCJtYWMiOiI5OTc0MzBlMzNmOGMzZTE4M2ZjNjI2YmRiYjBiODJjNDU3ZjIzZjUwMDZhMTIwYWRiMjFlOWRhZDZiNzc5MTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFNeHdqRGVHY2F6a25objVhb0o2dnc9PSIsInZhbHVlIjoiVHlVM3BBN01uTCt3aCt2bXNNZlNIMmthREFYYldNQUwyRzhTZStOUWhOTURkaWJrd25MOTgyUThlbWdsQ2lZR1FzM1g2UGsyYUdrTGk1Vlc3UmlmMTFBMjFPWHJxUXJ1UDhVRXpBTnMwajNSU3J3YUFTVWtMSEpWMHZubko4WFFCK1JnSkkzY1RJVU1CUDdkMkVBMWgydkVSNElVK24rejV3SytNYkI0Nm5HblAzZFVrbi91ZjFyUVFYbStTcDlNSUNXbVlWSXN0MVNTVkFUQS9FbWx1RXpQeEd1STV2dU8rL2xucjBNR0Y1WkkzNVJ2K1l2OE5iYmtMdFpXdmlmQ1BCRzQyeGVWaTFESldKUXhzRU9iMGFaVTNod1lMVzJqQ1VIZms1cmNvemU0V1JoVEZxdUF0SWgvbTdFZUovVW9MUjVRdFA3Q2cyemo2U2ttMXRCNWxzUElMbmt2RDF2bmhseDhLRmxjRkJCdFU3dXpYdnZVRGhoYnRwTzBUeWl1UEM0aVpvTEloSVBiT21CU0Y5eFYxbDMxV0VoSlEzM1N5OU0wUndvQWI4RmMrMzRtdDNrQ2FqVmxrY1FuRDhWMU8yUXAvcDJLeUgzaGVYZmdBbDZtM2NoVUk3bG9yejJpSFF3WW96Rjc3a3IxUXU4Y0RoKzFtVzEyd3FIaHk3ZE0iLCJtYWMiOiIxNTRhN2NlZDk3MTUxMGQxN2VlNWQwNjllZmRjOWFhOTg0ZTk3NTkwMDBjNjliNGJmNjc4ZTNmNTY4NGQ5YjdkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imt6aWNhN2tyV2RjRmNrcFpXZ2xaSlE9PSIsInZhbHVlIjoia3dyb0h2aHNvSnpUZER4QWVDV0hyWTY2a0h2TGhaR1FZQWNFdENXcFVWMHpENHdJYWJ3RkpZWWFtOEFVMEU3UTFhRWdaS2xhcXpYakdKN29pMVNQVmVPZDdWaTlIZ0hqcHppd1RmQ25ySjgveUFUL3pFQ2pPalpxbjlLL0ZlZHEvbGRVenJpUFgrWmh0NDJSaWNMUjQ0bTh4YjJnTFN1ZlUybEowdEI1U2YrWjBMQ3cxdksxQ1MyMFM3MHdPL2RJamJrNStUVWxkOEhGQlFJQkNncWhyTUY5MjdCcUJpVHVuMlVsUEpXM3RDTVp2OHZRZHFhV0VwdjZFNXhnandSb2dkWENueTZhMEF1T3FRcHp4cGZkNWJDWkdJSS9NcGIrYVJqWFI4QUozUkFOUVoyVXU4aXUxSW0wOXY1YmtDb3FscndBaXFtVC9yczlDSktkRER1TDNPUlBCVkMrWG5vZHdYOXBxNWJKNVlENS9JRkJZN0tVd0hzTmlaSXBETGFIR2FlZGtYZWt5dGR5WE4wNnIwdzBjOEFiZG92b2JSbnZvWnJDOXRObHppQ1N1elUybGdRUEx5QnlEQ2kxVDI3WnVITSt2eWMvQ1d6dlllay9aWW1MZ3M3Y2lvSkdUVkpmSysvd2MyaGFSQmdaaHpnM0p3WVgxOGJtY0ppdlVEZTQiLCJtYWMiOiI5OTc0MzBlMzNmOGMzZTE4M2ZjNjI2YmRiYjBiODJjNDU3ZjIzZjUwMDZhMTIwYWRiMjFlOWRhZDZiNzc5MTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFNeHdqRGVHY2F6a25objVhb0o2dnc9PSIsInZhbHVlIjoiVHlVM3BBN01uTCt3aCt2bXNNZlNIMmthREFYYldNQUwyRzhTZStOUWhOTURkaWJrd25MOTgyUThlbWdsQ2lZR1FzM1g2UGsyYUdrTGk1Vlc3UmlmMTFBMjFPWHJxUXJ1UDhVRXpBTnMwajNSU3J3YUFTVWtMSEpWMHZubko4WFFCK1JnSkkzY1RJVU1CUDdkMkVBMWgydkVSNElVK24rejV3SytNYkI0Nm5HblAzZFVrbi91ZjFyUVFYbStTcDlNSUNXbVlWSXN0MVNTVkFUQS9FbWx1RXpQeEd1STV2dU8rL2xucjBNR0Y1WkkzNVJ2K1l2OE5iYmtMdFpXdmlmQ1BCRzQyeGVWaTFESldKUXhzRU9iMGFaVTNod1lMVzJqQ1VIZms1cmNvemU0V1JoVEZxdUF0SWgvbTdFZUovVW9MUjVRdFA3Q2cyemo2U2ttMXRCNWxzUElMbmt2RDF2bmhseDhLRmxjRkJCdFU3dXpYdnZVRGhoYnRwTzBUeWl1UEM0aVpvTEloSVBiT21CU0Y5eFYxbDMxV0VoSlEzM1N5OU0wUndvQWI4RmMrMzRtdDNrQ2FqVmxrY1FuRDhWMU8yUXAvcDJLeUgzaGVYZmdBbDZtM2NoVUk3bG9yejJpSFF3WW96Rjc3a3IxUXU4Y0RoKzFtVzEyd3FIaHk3ZE0iLCJtYWMiOiIxNTRhN2NlZDk3MTUxMGQxN2VlNWQwNjllZmRjOWFhOTg0ZTk3NTkwMDBjNjliNGJmNjc4ZTNmNTY4NGQ5YjdkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450381269\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1427893525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427893525\", {\"maxDepth\":0})</script>\n"}}