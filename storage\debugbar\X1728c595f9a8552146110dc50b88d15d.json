{"__meta": {"id": "X1728c595f9a8552146110dc50b88d15d", "datetime": "2025-06-30 16:17:35", "utime": **********.792867, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.135442, "end": **********.792893, "duration": 0.6574509143829346, "duration_str": "657ms", "measures": [{"label": "Booting", "start": **********.135442, "relative_start": 0, "end": **********.49418, "relative_end": **********.49418, "duration": 0.3587379455566406, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.494189, "relative_start": 0.3587470054626465, "end": **********.792896, "relative_end": 3.0994415283203125e-06, "duration": 0.2987070083618164, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50280928, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01641, "accumulated_duration_str": "16.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5302548, "duration": 0.01641, "duration_str": "16.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1868886095 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868886095\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1239590723 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1239590723\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-86197303 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">XSRF-TOKEN=eyJpdiI6IlkyZThvTkVmcWtiWkJvdjhOVlg0S1E9PSIsInZhbHVlIjoiWVNCcXYxZnZ1SjBCeHNnMUE2cDRGMkxpdzlRUnRuR2l1NGNaazBJVzMrcDltd0Q0TVN3UEo2TzFkd2lEQ3FRa3JYQmRaZHRlWWdzMnZFWFIreFl3ZzQ1QkkxMHM1NVRYbGtFQmgyWjVBRTlVQVAwRGg5cFYwV3MzOWdseXE0MHhqTElnUCsrWVZxTWp4RUF6QTdjS3M2c253aUxDU2xieUp0NjF4TC81YmV5OXNlaTB2SFdtSnlJM2Rwc0tpa2pDUVZZaHZ6VlYvMjlIMEFQWHdsbUlkWUJwdUV6aW9jY1dwMk42b1o5bURMSnJOUzVSdlgwQ2ExcEpPR0dCeDJNeWxDMk5iTyt5QXl5VVdRbUhNSTNIeXhXbURIa3FpZmFWTHltc2xnL0E3TkNpTGpuOE5VbS8yQTNXUzV1K3UrZzV4QlQvb3dnT1FKZm1HMHFlRy8xY0xXZkR3eTloU3BldXZWTjZWZGFWVHlLQmo3ZHhqSjd3ekcxbU5KTzdjWFJNUkZldUZFU1QyRlhncHFOSjNsZENvak5ieVc0SVEyNVNpeDRqbWcwTS9NcldrbitrTWxLOU1TSGduRlNnaVJScklFN0xRVTRlMEwzVjRiZkh3a1lYQ1h1ZFNubVNPbkdINWVPNHNjSUt3WjExQkFRZHR6WnM4VW1nVDd4VTNQMDQiLCJtYWMiOiIzNmY4YzZjM2I3NTA5ZDRlYzhmNjc1MmI1ZjA0ZmU5OTU3MGRkNGJjNWY1YTE3YjIzM2VjMTkzZDVmNmEyNTkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImM3ei9ad2FjL0JFL3FwbmVTYW5sYVE9PSIsInZhbHVlIjoiL3E4M0kwRGwydHJYOFpHNkJ0U0E1WkliTGs0cjFCTEtoRXNqazhwam9GWTJIdXp1RzUrQWxVUFpxV3BFZnJGeldnaUVxR0lMWHFmMWJ1TGZRUFRUOC9tSitLNTZhdFdXMnNRKzlOQXpoQnBwcGNKd0J3VzlsMkhFeXNwR3J4bk9nZzBodnZMNy9XVmpSNW85Q0RSalFGdkI2K1FxQ0c3dGZNRSttbWhZeXQySlljMk53S3ZwVHdNVGhwMmpkc2Jna3dmeXUyV3hLTTZPOG5TbHlzeEVFYURmQVB4Z0V2RXQ5bE5pL1ZIVzc2eEU5LzdjeU1YUm5KQm05c1Yrc2JhOFpNSjdaUGREcHV5ZzA0aFVld0Q4OVFXOVFENVpucW9lMmQ5V0tJcURGdGVCMGNiVjFOaVdlUnREK29RVXBXZ1dJeHdiL1JCenJqeExzMllIVFdLcW1mMmRpZkFnVi90NWQzaGFWMEN1aDM1NkpkTVBoam1rUjhoeU81UkE3b09nWWlZclBGTzhiWXRTUm5abnZHaU5MZ0pKYjNucUw0ZUtHcVZ0TWpVM1JZdGlOcFRjNWhZUzBmMHBRYVNlOUFGREFQeEw5eUgwRStEYzVQN2wzNmlIQko3RkZIeEVQclRwekhmMW5XT2ZGenlraENiVWZ6anlXbnhNQUpOZ2lPTHciLCJtYWMiOiI1OGU0MDk0OTA1MjFlNDc4ZGM1MzI5NTNiZTNhMTQ2NTFjZWE1OGEwOGFiOTQ5NGQwYmU3ZjBmYWFhMzBjNjQwIiwidGFnIjoiIn0%3D; _clck=leclya%7C2%7Cfx7%7C0%7C2007; _clsk=1w4ne3l%7C**********121%7C1%7C1%7Co.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86197303\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2010645167 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">24632tD8ZfzoNWlbTseZcTmC2MmrHNwvZ4fTsg6B</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010645167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1029314708 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRGcGZSTFMzMXRoR1RiNGZlU1hlWWc9PSIsInZhbHVlIjoielVHT05PdWdhVUJtZWFxYkNUYUNucWw4aHAwVlRmbXBIRFlIRTVYNjRxdEt0SVJ1WHZkOHd0VWQ3Vi9KV3BuaHIvL09hUzJxRllKMUU4VkVwVGR4Nmlhd0pDU2lrY3JLZnFQUlF6RGMvZEdTODdSS0tXSjJUMjBSNTdHQXFUL3RqVUk1amROKzQyVVZ6R0x5Zm8wVU56UkJ3Tk05OGl6QTlHZUFodDFrTmVHbnVibCtyKzhaRjdRQ0xKUC83M0JpQ2s1cS95dVBPaGFhVEdGeHYxaUthSWhLZUR5UWRKVkEyVG5BQ1ROQ3N3clNXWENiM0JwOXdQT0svRi9Ndk5WV2pBUmtNTDlXRkFKcDcyS1VjMTc2RkhnUDFlVW1aaTUrbDlqbDhmVm1jSmRJMllPOXZjbVBDSEF4R1h4WmZpWjM5VWhSemxGZUx4NlBKMk4xaldLMzN3dWdtTUpVZ1VkK1Bka1owL3B2T1phSFQyb3EwYzk3eW1VQXA3TVV0cTNzWlQ3bDJHdE10SUVaRExyNzNDT20rTGY0aWg3YlIra0N2elJobDJzNEh2TFY3dEo4aCtuOWN4RFdQVUJ1VjhieFMxdHZpK3N2Mk9KditvYTJQMXVpNmxuNTQ2MHR1ODFUK1Y3K0RqeXp3RDloelFUbFNtQnlFTGhSemVPaW9kTVIiLCJtYWMiOiI5NGY0NTdiZTYzZDhiMTViNzM4ZjY2ZGQ4YTU1YzgwYzk5YTBhZmYyZmQ5YjYzMzhmNGU5MzY3MDE1MWVmM2U4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjUybjR4N0xLUlhmdXhVSU03UEZ6Wmc9PSIsInZhbHVlIjoiejhUK04xQitITWhwcGRRMkZIZElRaXhGaDcvL3AvZ1NPV1dEdUdqcUVreDVHTENVUzl6dXRlaTNmbXVTcUQ1S3VVVHlDaEJoU1ZHYlVMMzBwWjZPYWJWdWtWTGx5WHJnb2ROSldZb01UbUdIaUYrN3VjOU40MzZlbUFaTEVZY0RBYTVtTkQxVUFraGo1UVVncTRMTnhmZngvRDZYdjl3RHEyb3pLM1dPYTQwTmphVEtxcWZpcHQwNTk3UVdYZk1YVGd5WTB0N0l2a2FNRUkzaGFBZ3NzMFlGeWplZ3ZpL2U5Q25jazRUOXNaVnNNRmRGVGRZRVdNNnZ6STlRMW1kZVRlekZBNDdwaWE0WitROUs2dzkxOTc0QlFENXV6NzNXNzVSYTliUWlSeHJHL1FvNmRXdWdLNUJhZWNvL0ExazNqSk5ScmprS1lXSGRpZUJBSWtEd2YxUUd3TnBSMU9mc3kwZ1NCOFZGQWRNdHp5TVYyTHJRNi90SmV3KzVjaHFTTFFlK1Y1WnNqcW9DaE1YbGMyMTM3ZWFocHMyYTZHUkVVSGhkVThoSmcrS0tBWEJIcHBOcFdHbEVBUnd5R3ZmQzNKOEo2RmdHRVRKV1dkbVdObm4wbVltQWFOMWl0aVpMRlBaMVhhekJIYmExYUc4ZkFtUjBNdWNnVElMaTA4RkMiLCJtYWMiOiIyZWZkMzc0YjQ3OWMwMGE3YTllODFlYzNiZDJjYTk4NGViNDlhYTEyZjUzMThhMTVjMGNjMWY5Yzk2ZmM5MzVlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRGcGZSTFMzMXRoR1RiNGZlU1hlWWc9PSIsInZhbHVlIjoielVHT05PdWdhVUJtZWFxYkNUYUNucWw4aHAwVlRmbXBIRFlIRTVYNjRxdEt0SVJ1WHZkOHd0VWQ3Vi9KV3BuaHIvL09hUzJxRllKMUU4VkVwVGR4Nmlhd0pDU2lrY3JLZnFQUlF6RGMvZEdTODdSS0tXSjJUMjBSNTdHQXFUL3RqVUk1amROKzQyVVZ6R0x5Zm8wVU56UkJ3Tk05OGl6QTlHZUFodDFrTmVHbnVibCtyKzhaRjdRQ0xKUC83M0JpQ2s1cS95dVBPaGFhVEdGeHYxaUthSWhLZUR5UWRKVkEyVG5BQ1ROQ3N3clNXWENiM0JwOXdQT0svRi9Ndk5WV2pBUmtNTDlXRkFKcDcyS1VjMTc2RkhnUDFlVW1aaTUrbDlqbDhmVm1jSmRJMllPOXZjbVBDSEF4R1h4WmZpWjM5VWhSemxGZUx4NlBKMk4xaldLMzN3dWdtTUpVZ1VkK1Bka1owL3B2T1phSFQyb3EwYzk3eW1VQXA3TVV0cTNzWlQ3bDJHdE10SUVaRExyNzNDT20rTGY0aWg3YlIra0N2elJobDJzNEh2TFY3dEo4aCtuOWN4RFdQVUJ1VjhieFMxdHZpK3N2Mk9KditvYTJQMXVpNmxuNTQ2MHR1ODFUK1Y3K0RqeXp3RDloelFUbFNtQnlFTGhSemVPaW9kTVIiLCJtYWMiOiI5NGY0NTdiZTYzZDhiMTViNzM4ZjY2ZGQ4YTU1YzgwYzk5YTBhZmYyZmQ5YjYzMzhmNGU5MzY3MDE1MWVmM2U4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjUybjR4N0xLUlhmdXhVSU03UEZ6Wmc9PSIsInZhbHVlIjoiejhUK04xQitITWhwcGRRMkZIZElRaXhGaDcvL3AvZ1NPV1dEdUdqcUVreDVHTENVUzl6dXRlaTNmbXVTcUQ1S3VVVHlDaEJoU1ZHYlVMMzBwWjZPYWJWdWtWTGx5WHJnb2ROSldZb01UbUdIaUYrN3VjOU40MzZlbUFaTEVZY0RBYTVtTkQxVUFraGo1UVVncTRMTnhmZngvRDZYdjl3RHEyb3pLM1dPYTQwTmphVEtxcWZpcHQwNTk3UVdYZk1YVGd5WTB0N0l2a2FNRUkzaGFBZ3NzMFlGeWplZ3ZpL2U5Q25jazRUOXNaVnNNRmRGVGRZRVdNNnZ6STlRMW1kZVRlekZBNDdwaWE0WitROUs2dzkxOTc0QlFENXV6NzNXNzVSYTliUWlSeHJHL1FvNmRXdWdLNUJhZWNvL0ExazNqSk5ScmprS1lXSGRpZUJBSWtEd2YxUUd3TnBSMU9mc3kwZ1NCOFZGQWRNdHp5TVYyTHJRNi90SmV3KzVjaHFTTFFlK1Y1WnNqcW9DaE1YbGMyMTM3ZWFocHMyYTZHUkVVSGhkVThoSmcrS0tBWEJIcHBOcFdHbEVBUnd5R3ZmQzNKOEo2RmdHRVRKV1dkbVdObm4wbVltQWFOMWl0aVpMRlBaMVhhekJIYmExYUc4ZkFtUjBNdWNnVElMaTA4RkMiLCJtYWMiOiIyZWZkMzc0YjQ3OWMwMGE3YTllODFlYzNiZDJjYTk4NGViNDlhYTEyZjUzMThhMTVjMGNjMWY5Yzk2ZmM5MzVlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029314708\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2141105854 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cON8HbsZIa4J74M0cJKy8WlHDtXlL7fxu2dHefOM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141105854\", {\"maxDepth\":0})</script>\n"}}