{"__meta": {"id": "Xb97f0f02d935abffe720614508b8ff83", "datetime": "2025-06-30 16:06:03", "utime": **********.354553, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299562.807345, "end": **********.354574, "duration": 0.5472290515899658, "duration_str": "547ms", "measures": [{"label": "Booting", "start": 1751299562.807345, "relative_start": 0, "end": **********.220561, "relative_end": **********.220561, "duration": 0.41321611404418945, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220571, "relative_start": 0.4132261276245117, "end": **********.354576, "relative_end": 2.1457672119140625e-06, "duration": 0.13400506973266602, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43762480, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02628, "accumulated_duration_str": "26.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2496998, "duration": 0.025670000000000002, "duration_str": "25.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.679}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.278709, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 97.679, "width_percent": 2.321}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1915925765 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1915925765\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1034168285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1034168285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1828344334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828344334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299561091%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhKSE82YXZGNjFRZVZGbmVySTI1Wnc9PSIsInZhbHVlIjoieEJmQjN2MmF3c1J0ZGs5d0VQRHk5UzU1eGIydUpSbHhaN0JvVTVybzg2S1RNQ3o1V3RaWnBTc2N6V3NTMjM5RERqenZUWHRycEdnTFJNOFZjS1dxUHBXdjNub2ltWndZdTVrVlhNOVVDQ3VuNVVmNW9oYkFZQTNlZ3V6VDA4cEdmNHJPVVlidEE2VWlNNDRMcUJZLzBLUjdtRDVOMWJUYms0NVVtWkovdEtrSnkrNWhBY3hBS1lKeHdqdU5TcGNDZlJrTU5mVE5KOG55aVNqbWdKcVBsS3FTQUNrOXg2VUFkZ01ERWI5c2pwbUN2cE5IanM5ZlpiVjhhTlpFZFFuMjBqbEFhSDNJU2t1c3ZUS1FGemNDNjEvNlhrWXpRZW1mTDdFb0lteVNFRmpDbWkwcEpoN25WNUJoUHErY0lGRXBJUS9tN2hZYUFYTnF3aXVRVTNiUnBsMTNZNk8zeHV1eGtZM2dCN3puZWQxWXdRZ2FBeE9mR0dkZzZ6N0c5NkwrKzAzRWhmNHk3YWhRejVUNDI4M3lrTE80VUpGL0dnUzdMSFdVdHdtZ3RFQ2NHM2lncGtqMW5mSkdxZmtVLzNYRE9ZVHI1d3JHVXR6TWZnVkkrNjI5TzF0UkhXRDJ4NUE2QjlZQnhnQlczNFM0cnNrSVk3QUYxZDRZYjJQNVZ5ekQiLCJtYWMiOiI0ZmIwMDA3MDc3ODVhOGE3Yzc2MTFkNDU1YzlmZGY2NjhkZGMxMzU0N2Y5NGRjMzcwNWRlNmNiZDg3YmEwNTY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklSbzBlb2IrZU02SFJpQXhqOTU3YWc9PSIsInZhbHVlIjoic3pDejYzVGJJbndRRXZvTlhlYmZyTDRjSG1tbld5QS81VE9wZGlHTEZGWElZd0lMRWk0YU1jNFAwQTJsWVI1V0tmcXJKbzEra0x0SVo0TG1WRjgwZ2orK1B2ZlRUT2JoK3hzNVF0N3VIU1Uxd3Y5TEV1cW1Sd0k5b1czcmhMdWhFN3VkQzdvQk1SSy9wU0RZVlZUTS95SDFKclZsNDJCdEkxeXVIQ3FvR1Z0enhsTUp6aExOTnpqVG1IUi9VZFlCeHJrVGFsRThOTG5MUmZ5UDdvZFVkOEdDTW0yU0pvSnl5UHRrNHdZUkJmbHFQSlY5REN4cDFTekgrWnd4OWtGcUZvc0NQcjAyS0F5ZjNjOXk3Y012cEZYQ1RWZ29UVThOL0kyMjdjOEQ2NlFxTHpQVUMzeTJNR2VQRmFKWnMrUzEzYXRkdFNWcmFTdlJLZE1yZmNIMlZQMXdCUWxKRCttL3kzcm45UW41Z056TnlLdlVSUXRyNmhQTUZRVnNSWm9aUkYxdk1TL1k3ZUYvbEREL21pMWxoUG1VL29iK2ZlS3pRTkpCR28zakwraG5EL3VvaWVTR0FtWWt0cis4SWQ2QkwrdlJUVHZwNWd3YXQ4V0MyRDRwYnl6UE5NS3VoRElEYldwci9vTmYxQVgzV2NqUC8xSmJTTXdidDBTODJPQ0wiLCJtYWMiOiIxMDU3ZDk4MTU1ZDg0Yzg0YmQ2NWZiMjg2YzJiMTQ1M2MxYWNmMTAxYzkzMzk1ZjVjNzAzNDYxNDY0NzA2ODE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2009014223 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZrQzliY2VPMmlINzh3SHVCWWs4N3c9PSIsInZhbHVlIjoiOXpXdy9GcWljYUNGcVpVN29XRDBFd3FRdkNyZmxDcGFtZld6TERmczlxakhTc2J0UFdYUmtnUFFDcWxTZlc0UmNyTnZGdThvV3BHUjF4aFVTK0xvTlJjbW1MS29FNGNGYU9GNHovVW52VjQxVndXZG1KU0R4c2JuZThMNWJ1emlnSmgvWDNFTHlzY2lhbm5XTS9HOVhVeWdxM05wc3VZOUYvSHQzckRaYnN2eFN4QWVhUXJSTEFIVTBSYkp1ZDBBck01WDF3bnRFZHNPOEdmMURtQS9SRDk5OWpBdU5yTDZRMS8wRVlhaUpOdEpOKzNIenZINXh4RWF6RmFncTUyalhXeWM0TzloOEVpemUxR2pVSWlSNzhBWUZ3NFdQU0tGV0N1YjdBbzN5WW1uc3pRR0JacHZnUVhGRGdjc21KcnNRT013QVM4L1VLcTN3eDZ0YWFFbmZSV0pTaUdpZTc0U0w4VWcvdktORmM4MFZGR29iVWFHc2VZSGZiUGpDWUpHVEJIbEFUUFlGQXZLYXpWSEY0eTd5NHBxR1oxK2VDeE80dkF3UE5DSk0rMW1kYlJocGZTbE8va1lsdlhhZVBmL0paaWRjRUg0ZURFSWl6Tkg4KzNOWUl0by9HcStUY0pFU2F3NUh6K3ZYNW1MMkJlREtXUXhycFA1NE54SE1DYmMiLCJtYWMiOiJjMzdmMmRiZjU2ZTY4ZDhkM2YxMzY1OTY5OTgzNWViODJiNWViOTExOGVkZGMyYjllY2MyOTRiYTg2MTg3ZTVkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5adGFpUXV1Mmo2Q1p4RmhMcW9nMXc9PSIsInZhbHVlIjoibkJObW5BQWcyNm5yWXVVS3pNcFNna1Y0QUZyOXNOdmYyUVFEaWZra0xqTlA0VXlmRVh4R1pWVzIzdGkxVVBLaHRER1E1RXpGNlg5d2E2eHg2Z0xERzd6b1pZeG55TUx5WGl1UVZCOXNUYXhhbnp1MUtqQzd4RVgvajFOTFR3QUxaYzMrV2g3QzhkRmlKaWJzWDdJS2d5dW5VNG1CTWc3ODMzWlRyb0Z0MUdSOCs4OEd3N1hwczZmSEdNSzlNYXZFcEExZ2Y5emswOHdIZUYxek9nL2pTWTRWY25WL1NTc2ZaM2E4S0d1S2dhZDhhVUZXUGdEUTJMSGFValJ3RDRPSlR2V3FWUTRJaFJOWStqRGdiakJIS2o2ZWpWdmgvVzhBZTNIMDdoSjRXUU5zY2Z5dVQ5MVQ1azZnY282aytGQ3FIcHkyYzlHL1RBZndVYTJmWUhrU0NOc0dzWXVKMC9hZHZQNExBa2YzNUI5ZHJGQWtGaTVVbCtuYjZWSHh0c2tISzgvL053Yi9MRTQ2eVRYUjVVUUprSEdwdUJ1RXphc09wQzBZb1ZsWlREekMwaWdKaFIrUzdlODYvY3ZvVnpWbGRqbTFBL1R0SXJTcjEwN1BhOTBsckZyeExMT3VUUGRmT2hPdk02Z2o2QXUzSGxwMVJGWVl2akpTbGpDSVlRUEEiLCJtYWMiOiI0NjA5NmIxZWM1ZDE0YzVmYmVkMjk1NmVjNzJiYzUzZWIxZGQ2ZjAwNjNjZDI3MWE2OGVlNTJkMzA4YmMyZjM3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZrQzliY2VPMmlINzh3SHVCWWs4N3c9PSIsInZhbHVlIjoiOXpXdy9GcWljYUNGcVpVN29XRDBFd3FRdkNyZmxDcGFtZld6TERmczlxakhTc2J0UFdYUmtnUFFDcWxTZlc0UmNyTnZGdThvV3BHUjF4aFVTK0xvTlJjbW1MS29FNGNGYU9GNHovVW52VjQxVndXZG1KU0R4c2JuZThMNWJ1emlnSmgvWDNFTHlzY2lhbm5XTS9HOVhVeWdxM05wc3VZOUYvSHQzckRaYnN2eFN4QWVhUXJSTEFIVTBSYkp1ZDBBck01WDF3bnRFZHNPOEdmMURtQS9SRDk5OWpBdU5yTDZRMS8wRVlhaUpOdEpOKzNIenZINXh4RWF6RmFncTUyalhXeWM0TzloOEVpemUxR2pVSWlSNzhBWUZ3NFdQU0tGV0N1YjdBbzN5WW1uc3pRR0JacHZnUVhGRGdjc21KcnNRT013QVM4L1VLcTN3eDZ0YWFFbmZSV0pTaUdpZTc0U0w4VWcvdktORmM4MFZGR29iVWFHc2VZSGZiUGpDWUpHVEJIbEFUUFlGQXZLYXpWSEY0eTd5NHBxR1oxK2VDeE80dkF3UE5DSk0rMW1kYlJocGZTbE8va1lsdlhhZVBmL0paaWRjRUg0ZURFSWl6Tkg4KzNOWUl0by9HcStUY0pFU2F3NUh6K3ZYNW1MMkJlREtXUXhycFA1NE54SE1DYmMiLCJtYWMiOiJjMzdmMmRiZjU2ZTY4ZDhkM2YxMzY1OTY5OTgzNWViODJiNWViOTExOGVkZGMyYjllY2MyOTRiYTg2MTg3ZTVkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5adGFpUXV1Mmo2Q1p4RmhMcW9nMXc9PSIsInZhbHVlIjoibkJObW5BQWcyNm5yWXVVS3pNcFNna1Y0QUZyOXNOdmYyUVFEaWZra0xqTlA0VXlmRVh4R1pWVzIzdGkxVVBLaHRER1E1RXpGNlg5d2E2eHg2Z0xERzd6b1pZeG55TUx5WGl1UVZCOXNUYXhhbnp1MUtqQzd4RVgvajFOTFR3QUxaYzMrV2g3QzhkRmlKaWJzWDdJS2d5dW5VNG1CTWc3ODMzWlRyb0Z0MUdSOCs4OEd3N1hwczZmSEdNSzlNYXZFcEExZ2Y5emswOHdIZUYxek9nL2pTWTRWY25WL1NTc2ZaM2E4S0d1S2dhZDhhVUZXUGdEUTJMSGFValJ3RDRPSlR2V3FWUTRJaFJOWStqRGdiakJIS2o2ZWpWdmgvVzhBZTNIMDdoSjRXUU5zY2Z5dVQ5MVQ1azZnY282aytGQ3FIcHkyYzlHL1RBZndVYTJmWUhrU0NOc0dzWXVKMC9hZHZQNExBa2YzNUI5ZHJGQWtGaTVVbCtuYjZWSHh0c2tISzgvL053Yi9MRTQ2eVRYUjVVUUprSEdwdUJ1RXphc09wQzBZb1ZsWlREekMwaWdKaFIrUzdlODYvY3ZvVnpWbGRqbTFBL1R0SXJTcjEwN1BhOTBsckZyeExMT3VUUGRmT2hPdk02Z2o2QXUzSGxwMVJGWVl2akpTbGpDSVlRUEEiLCJtYWMiOiI0NjA5NmIxZWM1ZDE0YzVmYmVkMjk1NmVjNzJiYzUzZWIxZGQ2ZjAwNjNjZDI3MWE2OGVlNTJkMzA4YmMyZjM3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009014223\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}