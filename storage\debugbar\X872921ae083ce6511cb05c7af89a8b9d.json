{"__meta": {"id": "X872921ae083ce6511cb05c7af89a8b9d", "datetime": "2025-06-30 16:17:51", "utime": **********.816293, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.403548, "end": **********.816306, "duration": 0.41275811195373535, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.403548, "relative_start": 0, "end": **********.740414, "relative_end": **********.740414, "duration": 0.3368659019470215, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.740422, "relative_start": 0.33687400817871094, "end": **********.816307, "relative_end": 9.5367431640625e-07, "duration": 0.07588505744934082, "duration_str": "75.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00788, "accumulated_duration_str": "7.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.772238, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 24.239}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.782576, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 24.239, "width_percent": 6.599}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.795899, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 30.838, "width_percent": 11.294}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7982728, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 42.132, "width_percent": 5.33}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.802642, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 47.462, "width_percent": 35.152}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.807657, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.614, "width_percent": 17.386}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-734837142 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734837142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.801695, "xdebug_link": null}]}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-139302624 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-139302624\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1525411742 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525411742\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2145334066 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2145334066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-442005697 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751300266634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1ieTQ3angxNkVMYmVYaGhuazVnYnc9PSIsInZhbHVlIjoiQUhMaFltbFNmOWlxV0tDRkVPMUZLTU1HN2JnZjdUaFFBK3hjWGpqUy9CT2liU2lTS0FHRzJHbFdrVmNVMVlPYnlvK28zeHJ3QVlkdFlYSXZRY052N1FnRHd3U0RXR0c4VFdCcERDQ01vNEk0dFFOSlRkOTVrb3dVRURjYkFXOE1NUU1kNFBWQk1RMk55R1hpOUMwWlVRMytnL2NTUm5JYkJjSUdZT0N0RmpYU0lXRG8wZTFHSUdOWUFFTHdvR1hlZEZzWWwwOE1hSkF0L1N2OGVWazM1TXFjRzY2ZTA3dkNObjNuY0NDZ2ZNSHduWGIrZzhabnZzcWl6ZWRoRW1yTCtRVklUMlhVVGlTK2VEMEhDZkx3Nk9EemRZV2Z0eXNxRlowQndJeTNqVElWdEY3VENnN0pGb2ZWQTZDOUczaytZZi9iQjIybS9YN1VWNHlXbUlzMlNiSk5DUEFsZVRkOGsrM2NrUUkzQlBUbVFuZkJtb0JGUk1BUElSWnoweUs4clVZSWd1OG5rSnQxTkovT0FTYmJpUXVoZlRvU1ZTc0tYNnRyaEtlRkI5c2hJU0w5dXhQV2hCM0tHSGd1VTc1dmNPSW5yU2N0eWtVVWNiVXg4TTNKczB2Z2VkeUlVVkxENndYTUpjOFFjMlZnOWZFRzhUalF2L2gxZUg4UkVKVkoiLCJtYWMiOiIxMmVlMWI5ZDZiZjAyYTZjNWY3YzM4ODg2Mjk1NjYyMmIxZmU1MjE1NDUxNTA3YjkyZDY1NmY0NThlOGI4YzcxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklNa2VhWFpYUGZsZ0tqRFVuQ0pwcWc9PSIsInZhbHVlIjoia2REZHZ2TjVWekNlTUN1bXNldDd6Y2lMMS9yU2t3ZVlWNTJ4VU9Dc0tpenJhQ0FmTVA1Zk5TT0lrYnNHZWVVRmhRVm11Tk54bWNVYk5rcUJNeUxpTEtRK3RONXNIeFRvL29MUlRkbktiTG4raFdMSTJORC9QRS9GUXkwVXZpTzhPVWwyUXFXZCt0aHlzMzBPa01MTktENys5M2ovTElWbDJ5SWpGeGxLeVduNEQ3U1VUNlRMWVkwQ1BsRllkdFY4ZDhmaUkwTmRvL244S3pxR0RZY2h5VlFIczJxazFHbWdvZnprNkc5aEt4WkZHSGgwaktUV3lJQ0F0ZDRlS0xEQVlNYnQrQmh0ZUZwZEhqUExJVmhBR3NlZ25iNnFNTFBCM1JXOUF0S1dVWmw4S0tocW9LMFVsMDNLZUNKbzJuaGs1RnAvay83NExaaTByZzM5QkRnNnloaFp1dEVmM0tZd0MvY0lQaDVLT0hwOTJrV0VSR3IzbW9PZjZCQnJlRlNMcE0rSDhMdCtBTWs0WmdzUk1mNjJrR1BSODBjaEg2VmNWN3ZONlZaK3lYcS80TnJEa0UrY1pzWmpVNFBvOUMwekJjd00zUFRVOVNrTEhjek4wWm1PZk5YSnVqTTVXaS9qajFBMTlmd3BrLzNaQkJMNUZPeXM2OXBJWWdESHBKSzAiLCJtYWMiOiI2OWUyZmM3OWZlMjlkYmVhZjI5NGI3M2JjNGNhYjBlMzk3MTNjY2YxZDI0OTkyZDFkZDg3ZjMzOGRjMWQ1NjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442005697\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-130849739 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130849739\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-508551296 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:17:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im54bnpqMmcrcUhpNUtlaW1iRFAvcWc9PSIsInZhbHVlIjoibldGWW9sbjBqS1JBMUZaV3hnczRLamJZS09ITXJOcnd3eDk3aTdhK3M5M1lKUlZ4UGl0VGFuNmF4Tmh2NjcxTTBtaEFUbk5qY253MGxmQ1V4OWFTVTY3enRnSk9KZ3J3U2pQYWlSdURmTXk2YWtuVHcydHdHTkNMcTBnb0lmeDBSWi9PeEdTdTlBZFdEc1RQZHh5Y1VwVmtPM1Zjd0FibE81RVlRZGNTYjFGRjJEVjh6bkRIV2ZuNGpiWklhQUJ6ZnorVkx0RWRrQ1RSS1FCU3FxOVMwUldQWmVod2t2dzRoM08zbWFHbktFd2lydEorSndqL2RTaFNTRUhUZGdXaWVmMjBFSXlSM2prdVZBMmQrekx0M1Vodk4xTjNiWFNuMGdENmZjK2daa013SDNXdXlEUUEvRFVWaHlOcUg3eDZBM3hhUkdVNU42NVdSZ3NoWDcxZU1KOWg5SmpqcGduQVUyYUxUd2FCY3N6MTMzVWN3ZHNZVU5aU1RyR3BIQmZXZjM0cEdSNEJOc1VaZ2JwbCtHbTVlRE5FTEQ0Qi9jc1NEQnBZbW43Tm5oYy9nNXhvY010TXJ0STZYblFjR29lMDNaRmJuU0lSMkJkYUw2MjdoNzBkSnBjb3IxTG5BbFBlUG5YU1VzNCtjdHRYeDlaYkpOWFBsUkZwbFVrQThKVkwiLCJtYWMiOiJhYjEzZGRiODllNjQwNmM0MDk0NmExMjgyMjEzNDY2NmI5YjRlOGI1ZmVhMTEyZjJhYjQ4ZGVhZTZmNzNhOGI0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlN3YTBkNGFsZ21jWWVZZzBqMkZITlE9PSIsInZhbHVlIjoiUUdCWkIrS0QrODZ4K21RcldWRVk5SVN0M20yeXdMRkpxTzQwOGlzcXRaK1llSDM1RVlWSFplRDFRdXU4aU1EUmRGekJ3STRJUlU4TXk5d0xGeHFKYUFqVDFsaGhJakxBa1QrL0prNGlESUJHTUl3NW1Va29QUldGT2xjc1ozZCtDTmRCckRaTUFvSmNZaW9zTGtOMUI5RW1sZWJsVmRkblhwYXd1NWxDL2RCM0pJNGlHVUgrNGxrOUU2UlFOL1lXcVl0eFUzTE9rbVVDQmRUc1dKZnFNd3FaZ3gzMXNka3M5d25vU1RxRWd2OFBzWDB6cXh6Q1h4RjVtVE1qM0ZaMUVqUmhvMUtvR2pIU2VLNFlQOE42a0Z5eWJoQjVWc1h1SzJKa1MybHQ1dzBiOTA2ZTYzcDZlZGVUaFNhbVlqYzRIem9DOFVDYWVha2dQTjF2Uk1adjkwWmJPN1dPWE1KN2YrcGUyeVNQYWxQQ1FHT3k0TEh6VVZCcTBCMkE5VFZYamgxNDE5MjlYTGw0OE1RN3MyUG5XUGFrNHdVeEZWTGhkc0wzZ21ZTnpnbHlmU1hXaHZ6NEFHUnpJaTlweTVMUFVZeGZVY3MvT2R4RnpIT1ZIZVE1UkkxblVEbE1zWVZ0UjE1OExZcDlsN3lLdDQ2UVF3eGFJRWVPc1M0WUlQaEYiLCJtYWMiOiI3ODU3OWU3NDUyZTM5Mzc5ODhkMTk1ZTQ3NTg4YzFlNTZlOGJlZjRjOGY4YjA4Nzk2ODA3NGI3ODU0NjJkMGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:17:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im54bnpqMmcrcUhpNUtlaW1iRFAvcWc9PSIsInZhbHVlIjoibldGWW9sbjBqS1JBMUZaV3hnczRLamJZS09ITXJOcnd3eDk3aTdhK3M5M1lKUlZ4UGl0VGFuNmF4Tmh2NjcxTTBtaEFUbk5qY253MGxmQ1V4OWFTVTY3enRnSk9KZ3J3U2pQYWlSdURmTXk2YWtuVHcydHdHTkNMcTBnb0lmeDBSWi9PeEdTdTlBZFdEc1RQZHh5Y1VwVmtPM1Zjd0FibE81RVlRZGNTYjFGRjJEVjh6bkRIV2ZuNGpiWklhQUJ6ZnorVkx0RWRrQ1RSS1FCU3FxOVMwUldQWmVod2t2dzRoM08zbWFHbktFd2lydEorSndqL2RTaFNTRUhUZGdXaWVmMjBFSXlSM2prdVZBMmQrekx0M1Vodk4xTjNiWFNuMGdENmZjK2daa013SDNXdXlEUUEvRFVWaHlOcUg3eDZBM3hhUkdVNU42NVdSZ3NoWDcxZU1KOWg5SmpqcGduQVUyYUxUd2FCY3N6MTMzVWN3ZHNZVU5aU1RyR3BIQmZXZjM0cEdSNEJOc1VaZ2JwbCtHbTVlRE5FTEQ0Qi9jc1NEQnBZbW43Tm5oYy9nNXhvY010TXJ0STZYblFjR29lMDNaRmJuU0lSMkJkYUw2MjdoNzBkSnBjb3IxTG5BbFBlUG5YU1VzNCtjdHRYeDlaYkpOWFBsUkZwbFVrQThKVkwiLCJtYWMiOiJhYjEzZGRiODllNjQwNmM0MDk0NmExMjgyMjEzNDY2NmI5YjRlOGI1ZmVhMTEyZjJhYjQ4ZGVhZTZmNzNhOGI0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlN3YTBkNGFsZ21jWWVZZzBqMkZITlE9PSIsInZhbHVlIjoiUUdCWkIrS0QrODZ4K21RcldWRVk5SVN0M20yeXdMRkpxTzQwOGlzcXRaK1llSDM1RVlWSFplRDFRdXU4aU1EUmRGekJ3STRJUlU4TXk5d0xGeHFKYUFqVDFsaGhJakxBa1QrL0prNGlESUJHTUl3NW1Va29QUldGT2xjc1ozZCtDTmRCckRaTUFvSmNZaW9zTGtOMUI5RW1sZWJsVmRkblhwYXd1NWxDL2RCM0pJNGlHVUgrNGxrOUU2UlFOL1lXcVl0eFUzTE9rbVVDQmRUc1dKZnFNd3FaZ3gzMXNka3M5d25vU1RxRWd2OFBzWDB6cXh6Q1h4RjVtVE1qM0ZaMUVqUmhvMUtvR2pIU2VLNFlQOE42a0Z5eWJoQjVWc1h1SzJKa1MybHQ1dzBiOTA2ZTYzcDZlZGVUaFNhbVlqYzRIem9DOFVDYWVha2dQTjF2Uk1adjkwWmJPN1dPWE1KN2YrcGUyeVNQYWxQQ1FHT3k0TEh6VVZCcTBCMkE5VFZYamgxNDE5MjlYTGw0OE1RN3MyUG5XUGFrNHdVeEZWTGhkc0wzZ21ZTnpnbHlmU1hXaHZ6NEFHUnpJaTlweTVMUFVZeGZVY3MvT2R4RnpIT1ZIZVE1UkkxblVEbE1zWVZ0UjE1OExZcDlsN3lLdDQ2UVF3eGFJRWVPc1M0WUlQaEYiLCJtYWMiOiI3ODU3OWU3NDUyZTM5Mzc5ODhkMTk1ZTQ3NTg4YzFlNTZlOGJlZjRjOGY4YjA4Nzk2ODA3NGI3ODU0NjJkMGU2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:17:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508551296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-350761084 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350761084\", {\"maxDepth\":0})</script>\n"}}