{"__meta": {"id": "Xc10703a8419e6c374a77c4fac9872310", "datetime": "2025-06-30 18:55:38", "utime": **********.652334, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.155426, "end": **********.652347, "duration": 0.4969210624694824, "duration_str": "497ms", "measures": [{"label": "Booting", "start": **********.155426, "relative_start": 0, "end": **********.568949, "relative_end": **********.568949, "duration": 0.41352295875549316, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.568961, "relative_start": 0.41353487968444824, "end": **********.652349, "relative_end": 1.9073486328125e-06, "duration": 0.08338809013366699, "duration_str": "83.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48169640, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01085, "accumulated_duration_str": "10.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.603029, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 15.945}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6129558, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 15.945, "width_percent": 8.848}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.627324, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 24.793, "width_percent": 7.558}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.630029, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 32.35, "width_percent": 11.429}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.635798, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 43.779, "width_percent": 43.963}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6431901, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 87.742, "width_percent": 12.258}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-483133057 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483133057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63472, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-805967121 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-805967121\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-967074609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-967074609\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1287754470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287754470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-742075241 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6InBiMmFtTHlmMTBBMTJ6SUNqTUZqZVE9PSIsInZhbHVlIjoiRXRvbGY2L3VqSElZY01sVGE4VVNrZkFDWGNId0JCbGFCNHJSSVJDMTFSYVZyUU85Uzh4dnNsSWRBbzZhNmR3VlhsN3R5czFvMXgrekRPbFVpRTdyNVhqLzF4d1hNOGZuV3ErYUpyNGtpYWQ5VE90YmsvYnlibnZsYTJ0WkFWUnczT2p1NUUzVm5TaDhWRFNrY1RacmtPdWQwalJaUURvbzFPNzk4TFNjRitNVmo4djhxaW1wK3RFYXA5YzMrRU40R2xNdytNcE95Qm55cFBLS1E2S3VmQXhvUVBjYmRtSVRlN2l1SnNqN1VKc0czRnZZZStYUUdTQjVwSGtHcU5hVmpsTHF1TmNqaDR1ZXEwd0VZRlpwNDdCdDdBWlRWV2F3RGxvOHp6c042ckxQZnBqKzFPK0paMHpTWE5jUEtHcG12WnZQdThicUg3V1RwUWMrVXFkOGVkL2MvV3d2MUY2TVdtUUdJZE1oaytXZUUxUDVLNkQ0Y3o5OCtsMjJYSXJCMmQxZ1JiakhadVZvUTRwdjlHbWlnZnJYQUcvWkwySlNXc0RLNjhRQnRKekpYdlJtZGIrQkhoaXQ1eW9KR1FtZ1pFdEpWZnRxNjhuWGk2R053ZVpiT1pNZEFZLzJ5S2V6NjlWSG5ZcjFzemVtMmtxZ2pJOUNJZTFXVms0ODI2YjAiLCJtYWMiOiIyNDU4OGZlMTA2NjFjM2M5ZGRlOGIwM2Y1NmI2ODczNDA5MDA2NWEyZDc0ZmY2NWExZDBiZGMyNGRjNDljZDFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkR5Ty9sY3dhb0tRSHBkWnhFWTV3Wnc9PSIsInZhbHVlIjoibjZYNXRmMkdrbHE3bEhZTmpHRXMzdmpVd25URzEwUjlBazM2a0g3dlBTUmUvbnhWV2F3WmtzRUpGZVpyVUVJWHNyMWI1YXhQOGpobXkwMFREWk1rVUJnUW1rQUxIRlZ1RWpBNTNxQTUrV1Y4Snh2elVkNVAvZ1FQbUwvVUQvcDJXZjVlckJqZmowb0R5VGxUVVNwTTRxSk1jNDJjOHJIeWkxTTY1eTF5RFA2TWI1bUxQTHZSUU5ieEE1MnFhbmlaUjFNWmQwZGgxc1M4b1prRXFBZnlGMzM3NUpCZzUzbkdhU205Z1ZkNGVaUVpPbDRpRVRsdllYZlJVMzA5Qk5kVFFCN0xldktzZHZJM05hOVFGRExvUHBkL3JWSUxaQU54U3hEbTMwQXF3ZEJ1Y1RGRDVtWlFVLzFBUWZQeU5WWXJGYjZhenFiamh0RGFwNDRBbUFCeUNJZzRnbGdSTng1WXFGMmJ0SngwcmtzZHNOODJvbTBCcnEwMmdmc2MxTy95RytXSndPcnBxOEQ5L1FXUjliM2d2bGhTU2xOUVpYTURVNHVlYkxRT1R4dU1lTXh4ZU5UMk5FNGxxREt0cmhVOWZMM0ZxSTNIc2ltcFBSWUp5clh2TnNXQWxJSUk2R0VhNG9XeEhHMi9WUmdmSjIxbmlWb1JkOUVWUk5HUVhCdW0iLCJtYWMiOiIxZWExMzNhMjAyZmJjNDg4M2FmMmQ1YjdiNDFjNzk3Y2QwOTdjYjM1ZjlkY2QzM2Y0ZTJkMDg0ZGI2MWMzZWUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742075241\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1086658781 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086658781\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-164484675 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:55:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitQa0RmNEt0QnhJOG1GbHpsL1d2cmc9PSIsInZhbHVlIjoiUkZMWTBXV3pDVzlrVnI3RmlBS2h0ZDQzSHhOZk5UMFBYZHE5OERid2MvRzJSQ1V3YW1pdU1FMEd3dHFLNVlwckc5YUQ1am9vUTFZOUxPTkxweU02MDVFMDJGUHl1MjhQbXpERWM1OFNGMEtpN3ptcmJMd3dOVUxqQ0d3dlMzSWRpUEFHV0UyZFdwMmpsdFlFZzIrYmh4M1hvUDBMd1ZlRUhFdFdNbWtqM1J5NkI4eDNQQjdYbkZGNDkzUEh4OWlXTWUxVW9ibDR1azBLTy9yQnJtdXpnbE9HZGZsV1dxRVljVkdVTWFoSlBOYzdnRUV2S3pLLzVXbktZOVlENlNwdk82QStPQ1F6OVJ5YUhETldHZnA5QWNialpTYXgrK0JaMHd1ZGVSeGxDYldPMEdQODlKQTVaZ1NETEdveDVyNUlhSmVLTzFQRW1URDZsU3J5dHFoeVIzWWdNY1BZU0lUUExYVlhSUTcvMy92QjZiM2ppWUd3bHZkakdvYWNaNTdqbDlTMnpBaWdEck9DREhwS1RWeC81cWNtZVNRRC9zbTdxeFFnTlgyQ3hwVUorbXhtY0NDdTJtTEk0dHVQQ3FtcnlaRkNiSXRLVzFZWEI0ZkFZa1N1ZDNWT1E4QmpoYlU3cmRsbjNQM3lPMEJFN1h6WWRMUXVxTGFEZXk4RHJFWjciLCJtYWMiOiJmYzI2YzY1MzZkOWI1NGU1MDNmMjQ1N2M2MzUwMWEyNmZlNWFjYjgxY2FjODJmOTEyNmZhM2RjMWQ3ZmMxYjliIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:55:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJLd3JWSW14WEU5UWNqcEhJMVViVlE9PSIsInZhbHVlIjoiczJCRFJOa0J1NWRJZXRuM1BITlpMRmRGd0hLMWk4TEVidEV6RVcxa3JsUkRsczdncUh4dDNkSFRGRjhnNldERVNvT2RNTzFteTJuS0FrWnZzdy8xdjNaazJKZGJpbVhPb1VvN0NsSVp6V2dRSm5NNCtFOUlZbHZzbkdXZFNKQkR0dGphL0hsYWxNSlVpS3dHNTcyR2dHdW9vd28yVkM5akVMU0NRSGtzZVJpSU1jcnVNRytnN0V5eklRdHVZTVhNeEdvdFlXc1gyQldFalhUUUpEbEt0WHZXdmlNU2RHeDNGMng4TXBxMVpSS0JJZnNIRjExYjBGdjltTzUyYmVBUnRNQzdtVDlHelo5OHcxUXc4cnFZVDE2bHBTVkJMNHM3cHBqbjgwL0c4ZW1yZ3NuK2hOQXBlaWlIV0U0SlBvRlJJS0M0TXY5VHlMaEhQbDcrOHMxMEdBZnZYM1RVcmJubjNpTSsxd1FESk5EQ1ljKzgrOXRpM1p4b2N5ckpBZWJSMldnVXpERG15cjJPbmFGNCtSQjIrNjJpbU9IRUY3SVZJa0JOcXdCM1RYR0hnL2k3VXBWWGQxR29hM1JENlNMckQ3Z2dnRVRjN1loTHVhNFVqRFN5TU91MEJsRTd2dGk4Z1hCdms0c3ZybjBRUnYyUk1aekxwL1h0QWdIZDZOMVoiLCJtYWMiOiI3NDRjNTM2NDU1MTBkNmE2MDZiN2RlZWE1MmY2NDJlMGNmYjFiNGMyYzZjNmVlYWQwMmU4YzAzOWE2NjVlMjlmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:55:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitQa0RmNEt0QnhJOG1GbHpsL1d2cmc9PSIsInZhbHVlIjoiUkZMWTBXV3pDVzlrVnI3RmlBS2h0ZDQzSHhOZk5UMFBYZHE5OERid2MvRzJSQ1V3YW1pdU1FMEd3dHFLNVlwckc5YUQ1am9vUTFZOUxPTkxweU02MDVFMDJGUHl1MjhQbXpERWM1OFNGMEtpN3ptcmJMd3dOVUxqQ0d3dlMzSWRpUEFHV0UyZFdwMmpsdFlFZzIrYmh4M1hvUDBMd1ZlRUhFdFdNbWtqM1J5NkI4eDNQQjdYbkZGNDkzUEh4OWlXTWUxVW9ibDR1azBLTy9yQnJtdXpnbE9HZGZsV1dxRVljVkdVTWFoSlBOYzdnRUV2S3pLLzVXbktZOVlENlNwdk82QStPQ1F6OVJ5YUhETldHZnA5QWNialpTYXgrK0JaMHd1ZGVSeGxDYldPMEdQODlKQTVaZ1NETEdveDVyNUlhSmVLTzFQRW1URDZsU3J5dHFoeVIzWWdNY1BZU0lUUExYVlhSUTcvMy92QjZiM2ppWUd3bHZkakdvYWNaNTdqbDlTMnpBaWdEck9DREhwS1RWeC81cWNtZVNRRC9zbTdxeFFnTlgyQ3hwVUorbXhtY0NDdTJtTEk0dHVQQ3FtcnlaRkNiSXRLVzFZWEI0ZkFZa1N1ZDNWT1E4QmpoYlU3cmRsbjNQM3lPMEJFN1h6WWRMUXVxTGFEZXk4RHJFWjciLCJtYWMiOiJmYzI2YzY1MzZkOWI1NGU1MDNmMjQ1N2M2MzUwMWEyNmZlNWFjYjgxY2FjODJmOTEyNmZhM2RjMWQ3ZmMxYjliIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:55:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJLd3JWSW14WEU5UWNqcEhJMVViVlE9PSIsInZhbHVlIjoiczJCRFJOa0J1NWRJZXRuM1BITlpMRmRGd0hLMWk4TEVidEV6RVcxa3JsUkRsczdncUh4dDNkSFRGRjhnNldERVNvT2RNTzFteTJuS0FrWnZzdy8xdjNaazJKZGJpbVhPb1VvN0NsSVp6V2dRSm5NNCtFOUlZbHZzbkdXZFNKQkR0dGphL0hsYWxNSlVpS3dHNTcyR2dHdW9vd28yVkM5akVMU0NRSGtzZVJpSU1jcnVNRytnN0V5eklRdHVZTVhNeEdvdFlXc1gyQldFalhUUUpEbEt0WHZXdmlNU2RHeDNGMng4TXBxMVpSS0JJZnNIRjExYjBGdjltTzUyYmVBUnRNQzdtVDlHelo5OHcxUXc4cnFZVDE2bHBTVkJMNHM3cHBqbjgwL0c4ZW1yZ3NuK2hOQXBlaWlIV0U0SlBvRlJJS0M0TXY5VHlMaEhQbDcrOHMxMEdBZnZYM1RVcmJubjNpTSsxd1FESk5EQ1ljKzgrOXRpM1p4b2N5ckpBZWJSMldnVXpERG15cjJPbmFGNCtSQjIrNjJpbU9IRUY3SVZJa0JOcXdCM1RYR0hnL2k3VXBWWGQxR29hM1JENlNMckQ3Z2dnRVRjN1loTHVhNFVqRFN5TU91MEJsRTd2dGk4Z1hCdms0c3ZybjBRUnYyUk1aekxwL1h0QWdIZDZOMVoiLCJtYWMiOiI3NDRjNTM2NDU1MTBkNmE2MDZiN2RlZWE1MmY2NDJlMGNmYjFiNGMyYzZjNmVlYWQwMmU4YzAzOWE2NjVlMjlmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:55:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164484675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-802431828 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802431828\", {\"maxDepth\":0})</script>\n"}}