{"__meta": {"id": "Xe32b10c90c28d20eb5bc04ad755ab4d8", "datetime": "2025-06-30 18:38:32", "utime": **********.736319, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.25323, "end": **********.736335, "duration": 0.483104944229126, "duration_str": "483ms", "measures": [{"label": "Booting", "start": **********.25323, "relative_start": 0, "end": **********.662967, "relative_end": **********.662967, "duration": 0.40973687171936035, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.662976, "relative_start": 0.4097459316253662, "end": **********.736337, "relative_end": 1.9073486328125e-06, "duration": 0.07336091995239258, "duration_str": "73.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348360, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0207, "accumulated_duration_str": "20.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.695836, "duration": 0.019870000000000002, "duration_str": "19.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.99}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.726882, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.99, "width_percent": 1.836}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.729412, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.826, "width_percent": 2.174}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308318634%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNPL2NpekVIcGdBaGo1YkhHMXpXM3c9PSIsInZhbHVlIjoiRkV6Y0pVdTF5VlhSRVFJbm1kMGx2RTRmSjc0OTAzNHh0b25JTnpwNTRkQXhpdTNtQVk4L0hyRnFCRndYdngyTEhNNDR2SW1oZFNCVmdvaWV2WTJ0YlcvTmdMWGx1bWJNOTlKbkhvaWlPNURwVTVVVDJHRVNOaG9pc0FoUWsyTGduVXIwRDYyYmp2Y285YVJMZmVmcHBzdE03cklvLzlBbFlZSml1T1R0R3prdUhGSmorMitBdUFPZW5udGFoS2krbDBCWFdkcFBtaFpZWDFFa3owWm10UVhkUUhhVkhQVG9ya1BWSXY4NjZ0VEdlenNURGFsVkl3WnJZcWxBcHd0aEEyRWdEZEJlR2wvMG45dllvd2xqSHhiTDQxN2I0clZFUmN4T3U0bnZidzFCb1NWZktTVjRiVEJpVTc4RlVvSUEvQWt5cjVOSkxhM2tUZ0ZkbzI1ci95TlNDb3owb3lqNnd2ZjJCVE4yZWJOWDJ1SmlMR2xKbDd4eXpDdGxOZUVDQ1NOTkhXb052Si9XUjUybElkSE52WGlTWWpxeGZLR2E0NUxySXhubHZaWkpiKzVVdnFYSjlJdmp4SXAxcDd1d2tWeWF1RkdFbTR3Z1VreFgrY0xNcjhKSWRnUE11VytjVzFRRWw1VmJoR2ZXY0pSMHdPMDZrYWwyRGVLejRGM2MiLCJtYWMiOiJiODhkOWExZTZkMTQxMzI2MzFkZWE5MDM3MGMzMWM2ZGFlNzg3ZmQwMmY2MWI4OGJjMWQzNTg2ZGE4NmI1MGUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii82WW9Od3NlYUQxeG5aWTlhM09pc1E9PSIsInZhbHVlIjoibXU0WGZwc0JQRFVmRFQ0bnZlUlZWSjd6UHBOUmNHU3p3N0Z5aWh5aEhCZXJhemFVcjR3eUJtOGc0STRqVmtVSGxRcHBzeHg1M3dHRnlFZHYzSVExcVlUOFNrWDFGTjVaS25wZjJnMGtuRFJDN05pbllZbW1yZFJoZTRHSjBicnhaVXFKUlRKaWZJYk52dmE4dEhKOVFCRVY0eFNXdEJmVkJqYmdmdm9pYjhuU29lR01Od09QZm4xWENVTkhQOUxtTCs1Yk1nRTZaTUFpdEg4TlpJVWNnU29oUi9UNlRyZlp1OFJjM1lVVjAyWjkrbkxmNFZzOHB4eUptSlpGUlVWMWVsMXdJMW41YktXYzQ4Ujg4ZExhRlZDY3N4b3V6TXhlaTRWNUhIbWxNWktkNm9lcFFwNUlOOUpxNlN2SHpFMkRja1RRekFNSlpUYVY1VnFTWFhHOVR1ZlVmQ2RKb2RpZk1lVzBQRlFkL2M1L080MjI3OFQzTHpRLzl5NG52T2ZJSHM0Y2pMSUw2L0NnZXg4Y1gwRk1TYlBjMHpmSlJCb1AxUVBGdGRUNTRRMUIxWVBDSVlvYk0wQWx2a0REdEs2c1FwTFY2eWhDSjR4dGNneVR2Rm9kcWVmVmdUMXFGV0YxNzFFNWJ5TFNrT0JGampqcStXcGhiTThCWHIrM2JqL2siLCJtYWMiOiIxZjdlNWE5MDRkYWZhYTc5MjcwZmQ4ODZlZjgyZTI3NDUxZWU5YjNlYzExN2ZiZmU2YWFiNDNhMTJmMTlkMmRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1545741435 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:38:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxxM3MwaGtYSFRzOW1jb2U5SUZGSnc9PSIsInZhbHVlIjoiTnZLTWFaZ3N6dUdKR2MwS0RWN1NpUlJPSk1IN0t5UTVkSUtSY0xRUW5OWDhWYWFYODJvS1FXcnN5WHhVUFEvM2hSR0NONFJLeUk4VCszZEorNFA5QWplUjg0SUloRWxmeEFwTmJmQU1PWGZhaHZ6VnpRQXI2YTFTUW56eGxXdEVzQjZJaDF4ZU94MW54dDY3QmFwNTlSeWxJanNJWGFlcnBVbHdFYkxJLzZ2c1RHOW5CTnUvOFBwNjU0NVAvSXA5WUdhRkZHdzZLSE9XUHdTcFFGT2w4WW1PeWZ3ZWhWdXdxTTE3UGxwZk5iTVdUb28wNXVsM3ljZmEwUUppN3N6VzhydURlNTI0UmNnaXFtb1Z6bEdQN1F2MithMzVWK0tpVlRlNU1EK1NCaExXcHZmZEdyMG5nRmZxNEIvSU1TejBJd1dKZnhENnZ1WXdLY2hWVExxbktTQkpNblc2aGJuK01iUGVwdGhzZXJnazhoWGZvMU1LYXVGYnEydG9EZEs3bXhtYXVFWDZRdGxzaHd1a2d1MWo4cVl2SWtha0lNWHNRUmNyZXhBNVV2ejcrTlZPcE95Y0VhUk5nNGpwWm5CTmY4QVllRDE2aG1LY3ZYMGFQeXFodjJyMnRLMStXTGlIN2FkNk5ralhPSG1xK1NBOFc5ZklxeXFEYlFMM3d0Z1ciLCJtYWMiOiJiNGI1YmE2NzBlMmIwYjI5YTdiYjZlYTY5NzEzMmEyODNhZWRhNWZiYWJhMGYzZWMwZDkwMzUyMWZkMWMwNDkwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im11eWdWK0c3YlVVVGxXVWJRajJlY0E9PSIsInZhbHVlIjoiOTNVei9zZlpjek01T3ZTMEd0bFJKWG5iZkpZOUhVMjMvK1dKaEJNcmEwNDMxVDdadkVYVlB1eFIyNmRBVng0clArZnlhNGkvbVFpdWhyWURhOElzREliNzNnZFlHRkc2SzU2T3UvQmtBMUNFUXNKZnBMZ2swUjZUVFFWTTFXSzgzZld1YnZBQXVhS0RabTBVbXBJaWkzRjk5ZVVnY0MzTXQ3WHJ0YXp4UWswSi9rZmZUNGlvR0xCeGtlTVlLcWVPWElxbFhsZExQdzd1akZmUldHRURITTg4WEhqeEZJS2Nic1h5R3RsQ0JNMk5hUGd0OVVTM3Y4MFJaNFNLallZTDFocUdQeFJEVUJDTXpIR3JOemlRQWs5cTdCdmZVNjhFd0pJR0lTQUJ2V2xTZ1ZmRnRBbzJTV2ZNSzFtV1hpNk1oWmRxVWYvb2RwK0l1ajd0emExNTJxZjdvWmwxbE9jZnNRdElrU0Z4R0Y5YlNvOEQ5dkxKQThQMDFZbGhKbFBFR2lGbU9DemN4RGQzQjI4TGN5TWlpOWVNMUFvTVFRaTJadHVRR01XU1ZMelhUTGpYeTNnWWp3WFY5YjhoeGxHbysrdnFaWXE2NHpnOFRSQXZhYWZNSGJKamNqbzArUVZJTnJnZVJCWWtHUU01NWcybTd0TG0wa0d6UllnbGpQbEgiLCJtYWMiOiJhZWMyNGQxZTgyNjI3NDhiOGVjMjgwZTIxMjYxYjY1NmU4NTI2MTYwMTBhNWZhZGVmMWYxMjI2OTY4MGQxNmNhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:38:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxxM3MwaGtYSFRzOW1jb2U5SUZGSnc9PSIsInZhbHVlIjoiTnZLTWFaZ3N6dUdKR2MwS0RWN1NpUlJPSk1IN0t5UTVkSUtSY0xRUW5OWDhWYWFYODJvS1FXcnN5WHhVUFEvM2hSR0NONFJLeUk4VCszZEorNFA5QWplUjg0SUloRWxmeEFwTmJmQU1PWGZhaHZ6VnpRQXI2YTFTUW56eGxXdEVzQjZJaDF4ZU94MW54dDY3QmFwNTlSeWxJanNJWGFlcnBVbHdFYkxJLzZ2c1RHOW5CTnUvOFBwNjU0NVAvSXA5WUdhRkZHdzZLSE9XUHdTcFFGT2w4WW1PeWZ3ZWhWdXdxTTE3UGxwZk5iTVdUb28wNXVsM3ljZmEwUUppN3N6VzhydURlNTI0UmNnaXFtb1Z6bEdQN1F2MithMzVWK0tpVlRlNU1EK1NCaExXcHZmZEdyMG5nRmZxNEIvSU1TejBJd1dKZnhENnZ1WXdLY2hWVExxbktTQkpNblc2aGJuK01iUGVwdGhzZXJnazhoWGZvMU1LYXVGYnEydG9EZEs3bXhtYXVFWDZRdGxzaHd1a2d1MWo4cVl2SWtha0lNWHNRUmNyZXhBNVV2ejcrTlZPcE95Y0VhUk5nNGpwWm5CTmY4QVllRDE2aG1LY3ZYMGFQeXFodjJyMnRLMStXTGlIN2FkNk5ralhPSG1xK1NBOFc5ZklxeXFEYlFMM3d0Z1ciLCJtYWMiOiJiNGI1YmE2NzBlMmIwYjI5YTdiYjZlYTY5NzEzMmEyODNhZWRhNWZiYWJhMGYzZWMwZDkwMzUyMWZkMWMwNDkwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im11eWdWK0c3YlVVVGxXVWJRajJlY0E9PSIsInZhbHVlIjoiOTNVei9zZlpjek01T3ZTMEd0bFJKWG5iZkpZOUhVMjMvK1dKaEJNcmEwNDMxVDdadkVYVlB1eFIyNmRBVng0clArZnlhNGkvbVFpdWhyWURhOElzREliNzNnZFlHRkc2SzU2T3UvQmtBMUNFUXNKZnBMZ2swUjZUVFFWTTFXSzgzZld1YnZBQXVhS0RabTBVbXBJaWkzRjk5ZVVnY0MzTXQ3WHJ0YXp4UWswSi9rZmZUNGlvR0xCeGtlTVlLcWVPWElxbFhsZExQdzd1akZmUldHRURITTg4WEhqeEZJS2Nic1h5R3RsQ0JNMk5hUGd0OVVTM3Y4MFJaNFNLallZTDFocUdQeFJEVUJDTXpIR3JOemlRQWs5cTdCdmZVNjhFd0pJR0lTQUJ2V2xTZ1ZmRnRBbzJTV2ZNSzFtV1hpNk1oWmRxVWYvb2RwK0l1ajd0emExNTJxZjdvWmwxbE9jZnNRdElrU0Z4R0Y5YlNvOEQ5dkxKQThQMDFZbGhKbFBFR2lGbU9DemN4RGQzQjI4TGN5TWlpOWVNMUFvTVFRaTJadHVRR01XU1ZMelhUTGpYeTNnWWp3WFY5YjhoeGxHbysrdnFaWXE2NHpnOFRSQXZhYWZNSGJKamNqbzArUVZJTnJnZVJCWWtHUU01NWcybTd0TG0wa0d6UllnbGpQbEgiLCJtYWMiOiJhZWMyNGQxZTgyNjI3NDhiOGVjMjgwZTIxMjYxYjY1NmU4NTI2MTYwMTBhNWZhZGVmMWYxMjI2OTY4MGQxNmNhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:38:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545741435\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}