{"__meta": {"id": "X551a2c3caf85ca50a18bcc2eb5b1efb7", "datetime": "2025-06-30 16:37:09", "utime": **********.074792, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751301428.65031, "end": **********.074805, "duration": 0.42449498176574707, "duration_str": "424ms", "measures": [{"label": "Booting", "start": 1751301428.65031, "relative_start": 0, "end": 1751301428.99672, "relative_end": 1751301428.99672, "duration": 0.34641003608703613, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751301428.99673, "relative_start": 0.3464200496673584, "end": **********.074807, "relative_end": 1.9073486328125e-06, "duration": 0.07807683944702148, "duration_str": "78.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184248, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.007679999999999999, "accumulated_duration_str": "7.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0300279, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.172}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.040127, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.172, "width_percent": 5.729}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.054222, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.901, "width_percent": 5.729}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.056021, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.63, "width_percent": 4.948}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.060975, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 42.578, "width_percent": 38.021}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.066141, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 80.599, "width_percent": 19.401}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-216428499 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216428499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.059771, "xdebug_link": null}]}, "session": {"_token": "fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1571353935 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1571353935\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-211366826 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211366826\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-237205986 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-237205986\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-51023391 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=leclya%7C2%7Cfx7%7C0%7C2007; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1w4ne3l%7C1751301269373%7C9%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRIcVRWdzJ4YmYwZUZDeTdCc0lURWc9PSIsInZhbHVlIjoiR0c4cEFlMjlEY0FLSkV1QkVDSnBiWkoraHJPTDg2NXRIc3Vib3FZVVNYdTE2VUdEVGJ3dDZ6engvSXBqem5KbGZvR2Zjb0pmWG9URGNFRis0TnNlZkd6RjZZcGNNYmdFRHNFaHVPTE9QUDlvYWZUV3dFWkpVTFBIMDFnaE5vSzVNUkMvWm1NTVFmZUN3SjgwVjg2Z1dnaFhBN3FWbzVvMUM1cWJOZVhBbGl2V2hLVG5XQlV0d1FXb0x5d3BsV2lMRDdjZUh6SnB3cGJ1d0kzbmxPUjVuMzJwTkt4MHdZQitacjlpdEMzRFhIVlh6SlhvT2tTTU94T3JWT2ZVK3Y0V29yc0J3b2RDOENkUGRYVkZKZk5XQWo2V3BIMFUvckVKaysrVld5bFQvTEl6Zm53eG4vVlpCbGhGZlJ1RTVNUzBUVzdDRzBUZU5QdjMxY3JraHRwY2ZraW83QzRpbkYrclRLWmc5cS9WUUJmYzZib1lmMlZWN1BKYkxqZVhna2lKaFVVbGYrbEZlRXlOeEdVL0pSK1FHc3NIUGpJbjdqNmo4OVQ4WU90QTk5SU5GVWFxYzB0ejQ4cXdrZ24xMVpWOE9ydytXY1VpbEFSUkNZcnRiVWdnU2IycVYvMkRjTEt6dXdidjBXaVBpajBqakhxWHBJWmYxNS9QZkI3ak1WL0UiLCJtYWMiOiIwNDYwYzUxZWU1MzY5Y2E1NTk4MTM1OTllYzM0NzcxMzdjNDY2Njc5YzhhMmU1YTk0YmVkNDE0N2U5YmUyOGU4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdDenRISVNIZ0wxVTZTS3gvOEM1R0E9PSIsInZhbHVlIjoiSm9FRU0waXNFcFcxaURIQkJDOWI2S3BuaS8wSTFBUlJ5QUYwQ1dhNkhwS0ZvN0RtTnhOQWFkOTQ4QmV5dmxqMGY5VTZHcFAraVVQc1lZZXF2Y3lEcEpYTVdDR3dyZCthL1dLV2ptbTB2K2lvL0IxU1MxY01tdTQ0MHZUM1Y5OEhuYUFTQ2xMQ2VSUEZFM1V0Wkh6VkJUZ2lFYjZlVUwvQXFVdXBkRmhDVDZZVGw1TWpQQ0xNWDI1eExJQVNXczdUUVAzVFlmSGxGN0MwMk9pNlpLeDJNSHRlUHRWWXRrUjAvaTlxQ0pOUldkNGVIT21Wb00xN083dkdWdVgrV2dIWWVaQTdFQk04UHRmeVFkdXZNcGJCTkphQ0tjSnZJN3dTTTVLVldjVG45ZEp5NnljUkNMYjBEMkRSc3ppdk9pcGlDYTNTaFkraEM4ZXJhUHBFdWxHT3hrZjBGN0Zzdkh0OVdGd0RtMjFIaTc0QXhFZ3FxdjNUbGNrU09ZazBWbjJoRmxoUEhOSzNMcjFkcTkrcmRwa2Z3b09adWExelZTaWF2enNBV0VodzA5eGFRMGxuSTEvZDFXd0Ivc3NtOHo0RWluRWsrVmRJeksveStTNGVnMjRlcDErWTE3ZzBQNkNsL3Q5eUtscWdBK0Z5YzVHdk9SNk1xcnNhZkYweWV5SksiLCJtYWMiOiIzYzIyZTUzNTExMmFmOWIxMGVjYzk0NDM4ODJiYTUzZmE1MjI4YjlhZGM0NzMyYmUwMDQ4ZmNkYzA1MzJjZDM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51023391\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1586428044 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUSzbKqkZZSZCpy6HNrci2qoQqtZ4sqxT2xbzMyc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1586428044\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2106200584 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:37:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjloNThTM09PbS9Vbm5KODlWbDNLc1E9PSIsInZhbHVlIjoiNjc3YU9zRlg3SGF2UkVGdXZYOUlqTy9Na0ZKY281Rm1LWjg1RitHNWJGZ1RGVlozazJnY0RZS0dRSUI0K3czOUErbjFUaVBacEl0S0x4dlhVV0NWdEtKWnVQRTY5eHhxdjJucWUxZFNWNG1DMFBWbHVxN1VZS3lYa21PNHgyay80enJTWUw3M1ZITE5yUjJpZjB2eDRrUy9Sa0ZLNkplTzd3dW8vOHkzR0lYQWtEbldmeGhUZVNQVXp3ajB2UFFMYUE2T1UydUZ4UXdwQjNyUzYvcUVMUVdoS0pkbTVWVmUydGNqbnZvWGpUOTdOMkdESVFCaHlEdURHMGN5NVZPMlNWeFNLQ1NaT281dTlBWllqTlZUYS9ZbU5FUXZoMG0vUTVoeXFwb0lQYm9DTktKZG5lM1FyOGxhQ1Q3eTlOejJpdjI4TVdiNXExM3RUNUJEc1BYTDl0VWh0Y1NTZnVyN0QvNVNPdGV1cGxvMjEzTUc2cGNvdk16bTUyOStHVEVaeEh4aXBldmZ2ckxiYUFiZnBqSXFEaGtKdUUzajNRSmp0QWJsRlhmNFZwb3VpeDRUcWxTM1JaeEtRbzFMU3laekpRWEppelpyZ2V2R0lXa3pPeVlVRGVxZW9IRlN3djhCdUVwZ0JSOGYyakUxNXRzODRKZmdyQ0FSRnJmWXdUdS8iLCJtYWMiOiJhNTJmMmQ2MTY2MDEwMTQzY2RmMzg2ZDkzNjIwMzA4NDM4ZmQ5NTg2ZDQ4ZWUwMDJjMTZjNjBkODkwNjA1NGIzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:37:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii83SWl1dktlNU5yL3NyNHVHb2ZscWc9PSIsInZhbHVlIjoianFhT3pCWFh2SWpOT3NuaWdFcTVwQmsrVU5DWXBGWEsvdGZydUV3Q092M09KRUlhWVA0Y1J6bWZhN2pFM3ZjbVJIWktPb29aSjJ2RS9MaTZoNEsxRU1zQ2N6djQ3bGVibHZiNkhlWFVFTTZrVHZwZEhhdGo5QUQxcjBia3l1Q2NBOFFXWS9TNU5NUVVkeUZNeFR0MU8ydkFpSzJ2Q3pJUFJ4S05MVFdsNkVwUlNMdEdGSm5weUpEVTNwY1g3TWszU1pJc3duUU0rbC93ZDJQd3RuT0FMYVVSR3FSZ3pJOXQvYnhaUVVBa3pBM2dWdzJkNGNRT0lPQzdQYnlocTA0MUE1MU93bFBVdU9wVThZOFIxeWtUTVI5WkpCbkczK3R1ZG9uSFdZU2IrdjFxRThtZzhTVHpOMHliWXVNeWIyQW9uRUV2NWlKbTBjcEl2Wk9mQTRVY1dnUUV3V2dpelVTa2dtV1pZQVh6UWE3L0RpZjMxWDVrVnM0WWhtVVM1NXZZdW5BcXA4dmtXTzdnN1FGWHhRMkM5ZXIrU1BSbUdVQm5HM0NHblEvK24vbWNETW9lUWs2cDA0ZUhDVFdzWURIRllGMXBmK05MLzRKRzFMVktwVzV5Zml0bmpQZ1djenhIMDFCOXlmblZVcWM0aGV6WmthSFkwSnh2dDJtL295KzUiLCJtYWMiOiJjMTMxYjAzYWM5NDI3Y2UyZDUxY2IxNmIzNjhiY2M2NDcyNmQyZTM5YThlYzdmNDUzMzY1M2Y5OWUyODc4MzBjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:37:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjloNThTM09PbS9Vbm5KODlWbDNLc1E9PSIsInZhbHVlIjoiNjc3YU9zRlg3SGF2UkVGdXZYOUlqTy9Na0ZKY281Rm1LWjg1RitHNWJGZ1RGVlozazJnY0RZS0dRSUI0K3czOUErbjFUaVBacEl0S0x4dlhVV0NWdEtKWnVQRTY5eHhxdjJucWUxZFNWNG1DMFBWbHVxN1VZS3lYa21PNHgyay80enJTWUw3M1ZITE5yUjJpZjB2eDRrUy9Sa0ZLNkplTzd3dW8vOHkzR0lYQWtEbldmeGhUZVNQVXp3ajB2UFFMYUE2T1UydUZ4UXdwQjNyUzYvcUVMUVdoS0pkbTVWVmUydGNqbnZvWGpUOTdOMkdESVFCaHlEdURHMGN5NVZPMlNWeFNLQ1NaT281dTlBWllqTlZUYS9ZbU5FUXZoMG0vUTVoeXFwb0lQYm9DTktKZG5lM1FyOGxhQ1Q3eTlOejJpdjI4TVdiNXExM3RUNUJEc1BYTDl0VWh0Y1NTZnVyN0QvNVNPdGV1cGxvMjEzTUc2cGNvdk16bTUyOStHVEVaeEh4aXBldmZ2ckxiYUFiZnBqSXFEaGtKdUUzajNRSmp0QWJsRlhmNFZwb3VpeDRUcWxTM1JaeEtRbzFMU3laekpRWEppelpyZ2V2R0lXa3pPeVlVRGVxZW9IRlN3djhCdUVwZ0JSOGYyakUxNXRzODRKZmdyQ0FSRnJmWXdUdS8iLCJtYWMiOiJhNTJmMmQ2MTY2MDEwMTQzY2RmMzg2ZDkzNjIwMzA4NDM4ZmQ5NTg2ZDQ4ZWUwMDJjMTZjNjBkODkwNjA1NGIzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:37:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii83SWl1dktlNU5yL3NyNHVHb2ZscWc9PSIsInZhbHVlIjoianFhT3pCWFh2SWpOT3NuaWdFcTVwQmsrVU5DWXBGWEsvdGZydUV3Q092M09KRUlhWVA0Y1J6bWZhN2pFM3ZjbVJIWktPb29aSjJ2RS9MaTZoNEsxRU1zQ2N6djQ3bGVibHZiNkhlWFVFTTZrVHZwZEhhdGo5QUQxcjBia3l1Q2NBOFFXWS9TNU5NUVVkeUZNeFR0MU8ydkFpSzJ2Q3pJUFJ4S05MVFdsNkVwUlNMdEdGSm5weUpEVTNwY1g3TWszU1pJc3duUU0rbC93ZDJQd3RuT0FMYVVSR3FSZ3pJOXQvYnhaUVVBa3pBM2dWdzJkNGNRT0lPQzdQYnlocTA0MUE1MU93bFBVdU9wVThZOFIxeWtUTVI5WkpCbkczK3R1ZG9uSFdZU2IrdjFxRThtZzhTVHpOMHliWXVNeWIyQW9uRUV2NWlKbTBjcEl2Wk9mQTRVY1dnUUV3V2dpelVTa2dtV1pZQVh6UWE3L0RpZjMxWDVrVnM0WWhtVVM1NXZZdW5BcXA4dmtXTzdnN1FGWHhRMkM5ZXIrU1BSbUdVQm5HM0NHblEvK24vbWNETW9lUWs2cDA0ZUhDVFdzWURIRllGMXBmK05MLzRKRzFMVktwVzV5Zml0bmpQZ1djenhIMDFCOXlmblZVcWM0aGV6WmthSFkwSnh2dDJtL295KzUiLCJtYWMiOiJjMTMxYjAzYWM5NDI3Y2UyZDUxY2IxNmIzNjhiY2M2NDcyNmQyZTM5YThlYzdmNDUzMzY1M2Y5OWUyODc4MzBjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:37:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106200584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1285442044 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fZOV1vXWKVLZhW7zCcaJgctKnKry2eou4AYI7Ct9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285442044\", {\"maxDepth\":0})</script>\n"}}