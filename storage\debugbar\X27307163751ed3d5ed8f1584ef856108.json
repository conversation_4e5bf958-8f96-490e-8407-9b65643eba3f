{"__meta": {"id": "X27307163751ed3d5ed8f1584ef856108", "datetime": "2025-06-30 18:49:52", "utime": **********.925003, "method": "GET", "uri": "/change-language/en", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.539427, "end": **********.925019, "duration": 0.385591983795166, "duration_str": "386ms", "measures": [{"label": "Booting", "start": **********.539427, "relative_start": 0, "end": **********.865158, "relative_end": **********.865158, "duration": 0.3257310390472412, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.865169, "relative_start": 0.3257420063018799, "end": **********.925021, "relative_end": 1.9073486328125e-06, "duration": 0.059851884841918945, "duration_str": "59.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45259224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET change-language/{lang}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\LanguageController@changeLanquage", "namespace": null, "prefix": "", "where": [], "as": "change.language", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=17\" onclick=\"\">app/Http/Controllers/LanguageController.php:17-51</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00886, "accumulated_duration_str": "8.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.891304, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 17.833}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9012198, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 17.833, "width_percent": 4.628}, {"sql": "update `users` set `lang` = 'en', `users`.`updated_at` = '2025-06-30 18:49:52' where `id` = 22", "type": "query", "params": [], "bindings": ["en", "2025-06-30 18:49:52", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\LanguageController.php", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.903677, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "LanguageController.php:21", "source": "app/Http/Controllers/LanguageController.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=21", "ajax": false, "filename": "LanguageController.php", "line": "21"}, "connection": "kdmkjkqknb", "start_percent": 22.46, "width_percent": 33.296}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'SITE_RTL', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "SITE_RTL", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\LanguageController.php", "line": 41}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.908149, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "LanguageController.php:41", "source": "app/Http/Controllers/LanguageController.php:41", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=41", "ajax": false, "filename": "LanguageController.php", "line": "41"}, "connection": "kdmkjkqknb", "start_percent": 55.756, "width_percent": 44.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/change-language/en\"\n]", "pos": "array:2 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]", "success": "تغيير اللغة بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/change-language/en", "status_code": "<pre class=sf-dump id=sf-dump-1702265192 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1702265192\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1892558378 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309391540%7C7%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im16dFNkM2hmRnRqeFkxY1o0bWxwQlE9PSIsInZhbHVlIjoibkpFcUxCZVp6MFQ4eVJHRmlyOUFVMzhkc1FYUEFReiswL044dHJDeWZLdEh1RHJHejAzMCtvNHZzNjl2REF3U2NCazJJdWFJZDk0NU5BTjM5R2JENVUrQWExeFgranJaMHlNbWh2ekZMRlFMdzI1TXhjdWF4Uyt4dFEwNGN3TmUvNVNweUpkMUwyQ2htbyt2SkkyczZBR2kwOFhTQ3JGKzVmeEFGWUNDK3lXdWw2Nm5TQ2RmNUZWb1l1bkFyTHNEcG9reDBBaUU1UFN2NzlFWlBqQkhBMXNmam1FdHpFSlVVYkFNRGJobnBidk5iTDArQjlYaFdwdHhRRTc0WGpkR3dUaWp2eG5qS1pWbHRIWVNkK2p2U1ZDWkU3aDRrWGhoSFlNMlBUam0wVHZiL2hLKzViTVZONm44UThKakhHNzNuMERNK0N2RXR0SDlxRDF5NWNGZnJ4Q0xjZHd3aCtidmtXN2xsb084M1VhNFV5WVF4VnJLV2t6Y1FYclFsQkFLeCt2NXBUWkhmcjJxZnFjQVNQbXY3OEtwK3JFNVJHbW1SczdQWWE3YUxySGRLSXZkZ0ZvVko1TW54NHdrNlVZUGQyZWZPTFNrd2N3OGwrUTlnYVNFRW5wQVZTNDEyTmlTeGZvTWYrNGx2UXJYM0VlNitramllOG9xSnVrRlh0SWoiLCJtYWMiOiJmYjQwNTVkZWY2MjE4MzcwMDE4ZGUxMzAxYWY0YzM2ODllMmU1MGI3MmVjZWNhZDYwYzc5NzU2NTViNWJmZjYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQxYmU2YzgwcDAwc0owU0FDckdRMkE9PSIsInZhbHVlIjoiV3ZkRllmWFRZNzRXVlpNWkVVaVh6cmlUQ1ArNXprQWNNWUNZdHVSMlR6ZnBic1luTEUrT3VuUUF3eWNnbzNuZU1uNFVmWUhobzRzREVKMVVBa0xlY1Mrc0lGcUp0eFlyWTBBcVZlTGdCdm13WVdPYWdDamJXUDV6M2FQQ3dnV1FmQzljQkRwN0Izc2tLSjRWaXBpdlpBNHl0alYzK3ArMXVFSC9oOExLV1NxS0NGbW5zcHB6UkQyNFhXYjBjZTlMZStram9kWTZnR2RmNjF3ZEIyK3M5bHVobDZZdWpXZnlNS0lwT2NwUFZpdFRzMCtuTDhsVzQxTkNZQ3dkZXpxSW9LV1JpekYxa2tQY3E5T2NXWlo4eHdPM1ltREVySEQ3SDVXVWpDQ1VKb3ZrMFkveEd3dWUvWGRiUi90UGhqdGFBK0s1OVpNMWZvOG55THNsR0JKbktVWnNhaFdNQkJpTHFHQ1M1YWJIbVEwbFNKNGRVNy81YkFqWWR5U05tL0FBSDNoWEE2ZWJzYjZhZHVnSDFwVlAraVFIeWZkUHEzQTljRzFSY0d3NExpK3NJYTJUVVlYWFlEaEtRZ3FUMUROT3BvMEM5dXFGVHRYdmd1TE5rZTU4TllOeVJqYWZRU3dDR095TmNHUWl6SWNEOTZKZGg5dDFXQ3R3eHpKWVYvc2UiLCJtYWMiOiI5YmM4ZmQ2YjJiMWJkZGM4MTFlOGY0MjhiNzI2YzFhYzNkNzlhN2JmNjg2ZTdjMjNmNDVlYzZmZGI0Nzc4YzhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892558378\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2094258124 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094258124\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1100219178 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:49:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRHRjRIR0R4b1ZwRjRGMUxEUllpQUE9PSIsInZhbHVlIjoiT2JRQkNxNDJLZjNUVXJNUGNaTmk1TTdkMjBRbk5IdDNicEk2SS9yZzRCUTJFaUwzZnBXaDRjUXhoRXY4U29XeHd4Z0l6R1I0VnR1d1ZHUHZqeER2V01ld1BGU0p4Qy9WMlYvS0JpY1JQMk5STFpVNzMwRHZ2S2syeHExeFNLOWZLWmFjUE03a20vcXY4YXBubjVubW56VDR0eUhmZHN4WDkreU5JUmkwdFN1bnBCdmJkQnFjR3hXM0F4ZlBLcHlmUUc2UzFueUt3T3hadldRV3lvSDIyTmJwQmpXOUZLMjJTRnF5UFhRQXZ4L2RWUElETlJQOHlJQ0ZFMGNhZThXVlZLdVF0cFRXQ2lMVVlKc3VteUtYb0dMRXNkUjRwTnhqc2ZzN2dselpIeXVtOCtaNTQ0MC92a2h5dmJVU0lBZUZKdWN5Z0c4SEdua2hiaGFYcHpoUmNNbTBiRHBSN29XNUprRTcrcnlwM1l1eHpjR0ZLb3YrNDZvd0xsb3U1dWp3MXlFdWYvdjA1RVVUc2FzTitaVjN2bHYwRjh5OEI0b3dQNGhlYW5tUDBKTnhidjIxMHVvbkM4TEZlM3IwbWtQc29OUUg2ZVM4ZGlPWUUxQktoNTF2dDU5TUI3ZC8wdjlxUENibTBqZDU1Rm9zSi9SYndIQUw1aVVpcEJiSXpFREgiLCJtYWMiOiI1YjUyMWUzZTExMWMxNjViYmE3NDY2NDc2ZjkxOTU3MTc2YzQzNjRkM2JlYTQ2NWJlZWZkYzA0M2ZiNzliOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhQbmVDenNrekU1YmdYcjNWcXlibkE9PSIsInZhbHVlIjoiblNYNlZLbWFFUnhnYzJ1S21mN01EZ3FWaVh0OXZBWG9uRjJOTmUwNzIrS2hXMDc0UkhKa0NDSmJzdHptSm1jYmhGU3dhYVM3OWVxNFdtblBmQWlxUGRjRldQTXhpcmRqRm9sOVEvSEpJTGdDUkNOSUFONkovd0d6QUhCZXZOeWtCQ0RxbVphTUhReDdQSHZQekpSMFZudGZLKzc0STZJa09ZejJiUi8vdjE5MkJHSGxQL1RreU9kSXM4YjhxdFJJNXIrQXRhTmxoZFpjMm9Scjlpa3BpcjI2VXQyaEQxZUoyQTk4cWlrZzFHT0xydFBxM1hucHhoOEg1RjF4WmdORUdQRWJXaEovcGhSU2hEWmxZd09hNk1RTW96SmtqeHdMUk81T2tKd2ZQTk40bGE1UXRPRDd6S1pmeG5HaSt6VE9Yc0p4ZWY5QWphdXliMms3bTdRMTVOVTg3NnVySGh0TWVvd25EdENPY1krWDVlMWNIOHdrSWFWK2ZMSkJVeDllWGdRQzNwRXZ4bWszemJwNnliRjA0QjlyWGRxSWJlcXFoS1NacWZheTdsbHJMMWlOZ1BmRVZKYVdyeG14dmorcWthUlZ2M0ozMk40K21KVDd2SnJmV0xYdFdTWHRyNDN2dDdIRlMvai8yaVNjb1pJenBQc1ZXWGM5L1AxaUdKRjQiLCJtYWMiOiJlNjM2MGI5MmRlZTYyNTU3NzFlYjc4M2Y2NDMyYzA0OGZmZjFmZjQ1NTlkNWY2YWZmNDEwMDQ3MTQ5OGU0OTI1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:49:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRHRjRIR0R4b1ZwRjRGMUxEUllpQUE9PSIsInZhbHVlIjoiT2JRQkNxNDJLZjNUVXJNUGNaTmk1TTdkMjBRbk5IdDNicEk2SS9yZzRCUTJFaUwzZnBXaDRjUXhoRXY4U29XeHd4Z0l6R1I0VnR1d1ZHUHZqeER2V01ld1BGU0p4Qy9WMlYvS0JpY1JQMk5STFpVNzMwRHZ2S2syeHExeFNLOWZLWmFjUE03a20vcXY4YXBubjVubW56VDR0eUhmZHN4WDkreU5JUmkwdFN1bnBCdmJkQnFjR3hXM0F4ZlBLcHlmUUc2UzFueUt3T3hadldRV3lvSDIyTmJwQmpXOUZLMjJTRnF5UFhRQXZ4L2RWUElETlJQOHlJQ0ZFMGNhZThXVlZLdVF0cFRXQ2lMVVlKc3VteUtYb0dMRXNkUjRwTnhqc2ZzN2dselpIeXVtOCtaNTQ0MC92a2h5dmJVU0lBZUZKdWN5Z0c4SEdua2hiaGFYcHpoUmNNbTBiRHBSN29XNUprRTcrcnlwM1l1eHpjR0ZLb3YrNDZvd0xsb3U1dWp3MXlFdWYvdjA1RVVUc2FzTitaVjN2bHYwRjh5OEI0b3dQNGhlYW5tUDBKTnhidjIxMHVvbkM4TEZlM3IwbWtQc29OUUg2ZVM4ZGlPWUUxQktoNTF2dDU5TUI3ZC8wdjlxUENibTBqZDU1Rm9zSi9SYndIQUw1aVVpcEJiSXpFREgiLCJtYWMiOiI1YjUyMWUzZTExMWMxNjViYmE3NDY2NDc2ZjkxOTU3MTc2YzQzNjRkM2JlYTQ2NWJlZWZkYzA0M2ZiNzliOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhQbmVDenNrekU1YmdYcjNWcXlibkE9PSIsInZhbHVlIjoiblNYNlZLbWFFUnhnYzJ1S21mN01EZ3FWaVh0OXZBWG9uRjJOTmUwNzIrS2hXMDc0UkhKa0NDSmJzdHptSm1jYmhGU3dhYVM3OWVxNFdtblBmQWlxUGRjRldQTXhpcmRqRm9sOVEvSEpJTGdDUkNOSUFONkovd0d6QUhCZXZOeWtCQ0RxbVphTUhReDdQSHZQekpSMFZudGZLKzc0STZJa09ZejJiUi8vdjE5MkJHSGxQL1RreU9kSXM4YjhxdFJJNXIrQXRhTmxoZFpjMm9Scjlpa3BpcjI2VXQyaEQxZUoyQTk4cWlrZzFHT0xydFBxM1hucHhoOEg1RjF4WmdORUdQRWJXaEovcGhSU2hEWmxZd09hNk1RTW96SmtqeHdMUk81T2tKd2ZQTk40bGE1UXRPRDd6S1pmeG5HaSt6VE9Yc0p4ZWY5QWphdXliMms3bTdRMTVOVTg3NnVySGh0TWVvd25EdENPY1krWDVlMWNIOHdrSWFWK2ZMSkJVeDllWGdRQzNwRXZ4bWszemJwNnliRjA0QjlyWGRxSWJlcXFoS1NacWZheTdsbHJMMWlOZ1BmRVZKYVdyeG14dmorcWthUlZ2M0ozMk40K21KVDd2SnJmV0xYdFdTWHRyNDN2dDdIRlMvai8yaVNjb1pJenBQc1ZXWGM5L1AxaUdKRjQiLCJtYWMiOiJlNjM2MGI5MmRlZTYyNTU3NzFlYjc4M2Y2NDMyYzA0OGZmZjFmZjQ1NTlkNWY2YWZmNDEwMDQ3MTQ5OGU0OTI1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:49:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100219178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379955356 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/change-language/en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1578;&#1594;&#1610;&#1610;&#1585; &#1575;&#1604;&#1604;&#1594;&#1577; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379955356\", {\"maxDepth\":0})</script>\n"}}