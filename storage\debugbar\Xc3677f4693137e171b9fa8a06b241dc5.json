{"__meta": {"id": "Xc3677f4693137e171b9fa8a06b241dc5", "datetime": "2025-06-30 16:07:30", "utime": **********.101425, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299649.605931, "end": **********.10144, "duration": 0.49550890922546387, "duration_str": "496ms", "measures": [{"label": "Booting", "start": 1751299649.605931, "relative_start": 0, "end": **********.031528, "relative_end": **********.031528, "duration": 0.4255969524383545, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.031536, "relative_start": 0.42560505867004395, "end": **********.101441, "relative_end": 9.5367431640625e-07, "duration": 0.06990480422973633, "duration_str": "69.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020460000000000002, "accumulated_duration_str": "20.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.058285, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.135}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.087183, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.135, "width_percent": 3.177}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.093983, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.312, "width_percent": 2.688}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management/60\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-692451206 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-692451206\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-554665485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-554665485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-330717280 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330717280\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1880309178 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299627314%7C12%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxPK3liMWFjZ2tLNWJlZlFLb0QyeHc9PSIsInZhbHVlIjoicFBlRVBFVUFneGhsRFdPeWptK2VoVFhaMXpDUFErSWJESlE2YVREdStOMHcyZUtXZnRZaFN4eUR4a0JOTTBFdE81L2tLTWk2djdITDVXeHpwYXZURUJ1d245Tzlqa05QTW5CUTRZY3JGVlMwZVZUOUJCTjF5TlU5VGlIZmppREl0YUF0MlZmVklWVks0YzlLUUZuNkZTR21JQmw5bmpiNFdCUGx6TjFNWURaMlhwSWdYa2M1UW5JYUZwM05ab3M5NGZaSGxLRW41dDZiUVFCV1NkK21tMHRucUNHMlZpanZHNGtMNzNsV0R3bUJTWWJPa2pESDRNK09McmZtSEJJcTd1cjZsSWJaazdYZEtkTW96eUh1aVBBOWlzRUFWcTdBZThKR2xEZXlYOUtiYkthOXBISDYzYWJKK2p2NEpNaVR5cU1hSWUvNDhqVi9kV01pWHJ5TjZnVGhEeW9JTDkxWm9TeXRlNWJ4VW5Ua1k4TjIvZVd0dWdvUytxZHc0TVFqbE5wdDVZQ29sWGVPV3ozNWVRQ0Nnb3lZQWNqSXVodDh0eDRUNDVScXJVNmRKWUVXT2pTZkRBdDlhMVRKTVAzd2xvc1o4TzVEdGorR2dhWEx3NDlzNXdIZ0s4Wi9QRDVPY3QvWVg2ZG4wcXJnVUh0YkRXakdaWDgzOFhhUHpYZFMiLCJtYWMiOiI4NjRmZDI0N2Q1ZDlmMzkyZGJhZjYwM2NiZDAzOGMzZWZlMzExNmJhY2YxNWNmY2U2MDE1MTNiYzBjOWEyMDA4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9KcHB5OTRrMFJwNVZJQWpCbG1LbFE9PSIsInZhbHVlIjoiK2s0cS9JeHVScFNrWmU3a3JERnJLYVl6MHN3NkhPNHExS3FVdytuc0poYzc1OWhMNzZLNVkvL3crRDA4L0NDUFdmZkFvTlFWdjJ5UnFSeE1PTEpOYjdoYjVDM1JZY2lsN0drZnJ1bDNPQmlBWFNOVzlrL0NoeUIvZno3MlY5eVRsM1BmRjlTZFdBV1RlcndtVGFpYm56K3NyTld5NDZ0WEVNTElpcGFiV0lhMlF4aG1xQWRhRnZGNVE3YTg5SHpTNmNXb3VMZ0d6UllnNnNMYWdqS0NSMEtiYzdzVy9QQTJDMjJOUzVzejVxc2xJN2tnbitSdTFXa3Q4dDh0WSsxQ0tlQytHWGRMakV3RktlUG11Z21uQ2orRnRzdk5iOFJjdmJUc2pCQ213OWQveGl1RkNxdnJDemRhbWlTUjEyR3pJMnE4UzQxWkZIbWhtcGpJSnhPUDBkVGt2cHZMZkxpdnYzcnN0WjFnalptRXhjWXBXQkxOZElJa0hpSnJvRUJEN0p2bnRwcDdIcGRaUFBTcFdOUzRWYzI2M3pHNzNsdWxmUDVjSDE0OXJaTnNLTzJEeWM4OExCS1BZRTJDN01rbXVkaEQvWmVrdWo1ZVcwbWV5RWVTemh1dVNjQTI2NWVaVUJWc0IzZUY3WENuMUJEQXgvNUw3VWI2YjFHRThjNksiLCJtYWMiOiJlOGExZDRjYzMyODFkZmI0NDM1ZmJmNWI4MGI0YjQzMzRiYzkxNWY5NTBkN2M2YmQwNWQyZjc2YWQzYTk1NzhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880309178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-221797094 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221797094\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1137236263 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:07:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFYNjNUVkwrZmF4MTBsQ3hEVmtabXc9PSIsInZhbHVlIjoiR256c2VyeVl5ektoQmNQVTAvaHNTZStaZWhUMGsvSFcycDhjTTh4L3FHTENNbWJRM2ZEYnFSeEhvOUM0Z3E4WUZLY2hJZ212eGRSOFg3SjQ2OUNiakdyb3J2YmR4c0s2WkFubDQzMEJwb053MnZtN2kyZTIweWhybVR6b2JQRVpiS05LUWFkTWRLNEF2eEpMTDQzMGEzUEFFbUZxMUd0SHh3M25hc0Qzd0F2dVk4OURiSmVTY3Q0ODE4QXJZR3F2UmlkQjBISlBjQ2RrcnBDVUQrMHBidE9oOC9FTUNaRnM2WVVGeFFkMnNSbVBheU1DNkwrbDZXMWRLem1aZVRPWEtRYm4vbDR6NW50WXFncWNUREVuVDM5SnVhYmMxNUcyTlNsdUlYa3U4Z2ZJRmtZZHcyVzd5TUxqRmc5V1pLeG9kVGg2V2ZBL2lmT2plYndSTGRkc2VYWVJVOUYzV1NZL1ZPYnBUeW51bXlnQzhsVUlPODhqSkxxVWthelJmdmNvRWJpYlFGRGhQY2Zxd0J5Nmhoa2ZqaGFVYUx0ODh5L1lrRElBa2g5T3NCSzUrTk5NZHpPMnRpeGNoNTNneGk0WmZwQWd2MFRDenlXNGJxM0lFbzZDMVp6U0h2NFRwbHpuSVBuMXpHUGQraHRmb2JOR0pOeFpUaUgreE9ETkF2cnciLCJtYWMiOiIwZTFiNDliYmVkMGI4YjkxNjYxMjEyMmJiNGQzNzJiY2IzNDBjNTE0ZTIyYTU0YzM0YmE1ZmFiNGE1NmM1NDA0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZURVMxTTZRQjduRWxGV1l0SlJMOUE9PSIsInZhbHVlIjoiZ3p3by9kbTREQzVwZGxUSVFqZG9RUzlyWlQwdldaSjBWMWF2Y0FyS2NLSzd3ZGN4TmJDckVGMHRjWnhVSi9CamQwRXFrYTNpQit0emZkTGRaQk9PbWVJVjJ3T3c3dzFQZ1o0OWhpZVVXdzh0TkRuRllNdDJ6V2dKOW1TUGFPNk10VWZnWCs2QnMveDhHRkx1aWk3L3I1L0dJWWZiYlNBRFFsMFFTWVVPYWRKSjg0TElJakRUbUI0Z2FCd2R2SnlJeU4xRXJ1Z2FqN0dyWkY2ZkRZeWV4dXZpZExIUG9mZEtWNUllbUlUMmRrdFMwd1UvUFhKSStZWklmYVJ6VE5DZXFWUlRYSWlvWjFHQ0VObjl3Ukw3TmsrekdNUDV4R0NrblBHWW11eWphMmx3YkRxbVpBdDRGQTRsMVBsRXJlanVuT1gzNFdtazhBUE1LckU5TUpqSWhEN3ZOalBzVXFka0dPZDZRZzBsdXRlMmtieXczVnUwR21xbDlzRXlXMXdqNmgxUG1vTTk2VmtvYjhEaExDRnB1QndORGp2S0VuUUliYTNJT1psS1crWENGUXg1QlNEMHJ3VHJhT3BXQzIraVp3VzdVZW5WSk91U1QwU0lwY01CTS81TmlTZnRYa0xnS0dtbnpGS0RGRnRoR1JqK3gzS3h4YXQ0WUZaOHZKczAiLCJtYWMiOiIyNGNkZGJlMGIwYjVhNmM0MzU5YzFkYmEyOGExOWQzMjNiMWYxODJiNWI1NTQwNzQ2ZmNjMjNkN2QxN2UxMTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:07:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFYNjNUVkwrZmF4MTBsQ3hEVmtabXc9PSIsInZhbHVlIjoiR256c2VyeVl5ektoQmNQVTAvaHNTZStaZWhUMGsvSFcycDhjTTh4L3FHTENNbWJRM2ZEYnFSeEhvOUM0Z3E4WUZLY2hJZ212eGRSOFg3SjQ2OUNiakdyb3J2YmR4c0s2WkFubDQzMEJwb053MnZtN2kyZTIweWhybVR6b2JQRVpiS05LUWFkTWRLNEF2eEpMTDQzMGEzUEFFbUZxMUd0SHh3M25hc0Qzd0F2dVk4OURiSmVTY3Q0ODE4QXJZR3F2UmlkQjBISlBjQ2RrcnBDVUQrMHBidE9oOC9FTUNaRnM2WVVGeFFkMnNSbVBheU1DNkwrbDZXMWRLem1aZVRPWEtRYm4vbDR6NW50WXFncWNUREVuVDM5SnVhYmMxNUcyTlNsdUlYa3U4Z2ZJRmtZZHcyVzd5TUxqRmc5V1pLeG9kVGg2V2ZBL2lmT2plYndSTGRkc2VYWVJVOUYzV1NZL1ZPYnBUeW51bXlnQzhsVUlPODhqSkxxVWthelJmdmNvRWJpYlFGRGhQY2Zxd0J5Nmhoa2ZqaGFVYUx0ODh5L1lrRElBa2g5T3NCSzUrTk5NZHpPMnRpeGNoNTNneGk0WmZwQWd2MFRDenlXNGJxM0lFbzZDMVp6U0h2NFRwbHpuSVBuMXpHUGQraHRmb2JOR0pOeFpUaUgreE9ETkF2cnciLCJtYWMiOiIwZTFiNDliYmVkMGI4YjkxNjYxMjEyMmJiNGQzNzJiY2IzNDBjNTE0ZTIyYTU0YzM0YmE1ZmFiNGE1NmM1NDA0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZURVMxTTZRQjduRWxGV1l0SlJMOUE9PSIsInZhbHVlIjoiZ3p3by9kbTREQzVwZGxUSVFqZG9RUzlyWlQwdldaSjBWMWF2Y0FyS2NLSzd3ZGN4TmJDckVGMHRjWnhVSi9CamQwRXFrYTNpQit0emZkTGRaQk9PbWVJVjJ3T3c3dzFQZ1o0OWhpZVVXdzh0TkRuRllNdDJ6V2dKOW1TUGFPNk10VWZnWCs2QnMveDhHRkx1aWk3L3I1L0dJWWZiYlNBRFFsMFFTWVVPYWRKSjg0TElJakRUbUI0Z2FCd2R2SnlJeU4xRXJ1Z2FqN0dyWkY2ZkRZeWV4dXZpZExIUG9mZEtWNUllbUlUMmRrdFMwd1UvUFhKSStZWklmYVJ6VE5DZXFWUlRYSWlvWjFHQ0VObjl3Ukw3TmsrekdNUDV4R0NrblBHWW11eWphMmx3YkRxbVpBdDRGQTRsMVBsRXJlanVuT1gzNFdtazhBUE1LckU5TUpqSWhEN3ZOalBzVXFka0dPZDZRZzBsdXRlMmtieXczVnUwR21xbDlzRXlXMXdqNmgxUG1vTTk2VmtvYjhEaExDRnB1QndORGp2S0VuUUliYTNJT1psS1crWENGUXg1QlNEMHJ3VHJhT3BXQzIraVp3VzdVZW5WSk91U1QwU0lwY01CTS81TmlTZnRYa0xnS0dtbnpGS0RGRnRoR1JqK3gzS3h4YXQ0WUZaOHZKczAiLCJtYWMiOiIyNGNkZGJlMGIwYjVhNmM0MzU5YzFkYmEyOGExOWQzMjNiMWYxODJiNWI1NTQwNzQ2ZmNjMjNkN2QxN2UxMTQ0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:07:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137236263\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-152637631 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/branch-cash-management/60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152637631\", {\"maxDepth\":0})</script>\n"}}