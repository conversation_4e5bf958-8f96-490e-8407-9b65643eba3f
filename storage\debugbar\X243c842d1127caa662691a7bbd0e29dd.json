{"__meta": {"id": "X243c842d1127caa662691a7bbd0e29dd", "datetime": "2025-06-30 18:50:16", "utime": **********.217579, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309415.853409, "end": **********.217591, "duration": 0.3641819953918457, "duration_str": "364ms", "measures": [{"label": "Booting", "start": 1751309415.853409, "relative_start": 0, "end": **********.174647, "relative_end": **********.174647, "duration": 0.32123804092407227, "duration_str": "321ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.174655, "relative_start": 0.3212459087371826, "end": **********.217592, "relative_end": 9.5367431640625e-07, "duration": 0.04293704032897949, "duration_str": "42.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00238, "accumulated_duration_str": "2.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.200076, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.445}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.209215, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.445, "width_percent": 14.286}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.211607, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 77.731, "width_percent": 22.269}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2300 => array:9 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"2300\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2303 => array:8 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"id\" => \"2303\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2304 => array:8 [\n    \"name\" => \"ليدي فينجرز بسكوتي كلاسيك فيسينزوفو 400 جرام\"\n    \"quantity\" => 1\n    \"price\" => \"11.00\"\n    \"tax\" => 0\n    \"subtotal\" => 11.0\n    \"id\" => \"2304\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1928262163 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1928262163\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-70427448 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751309393964%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZST0lnMXlVRVA1SWJRK3B4N1BPZGc9PSIsInZhbHVlIjoiaFMvbjVtQTBpNVNlNzlFTndlRXpiTXRFbVZwWVdXRXcwaENra1l6cHA5bjdDRDJ4UUJZSEcycnVUQ2I0Wlh4bTVERUhMbGJyQUFWL3ZpVDRmNUtqUDF3eW42dDJ3YUVOSUpGVkQ2a1hVUXNuM1JCM3VLS1UxUVpsenh0VS9OZnNxMVgxR1ozMGJBS2p2bXlId3owZGxBd2lwUDFVZnBpNXdrTUdjSUUzRW9ueU5Yb29pQmFUZUN3MmlxbEhabGZwSSs0NEV1TkJpWjcyU0crYU9MYVRUb1ZNanNGL2ljWllXQTNETGdNRXZhb0xMQzlyelpXeVRFL09iWmZ1T2VaTy96aGZiei9mY3krR1VsZUdta3gwYlFZNEF5S202NmRiVlhOOEFxVEV6TVNJWE84Nkg1b05Xd1l4U2JEa1VQYVZWNjhwNW1NeUwxK1AxbEI4dVdqN2h1TkZFbXZUTHFiQVZBV1RYRjZMTmpZc0tkd2FtL3Y3LzFZZnJ0cmd6bmFRN05IZGhRZnNlRVIrRDRnd2NYbGxLRFgzZCtFVzVGdk95aHRYL3NHdnhSeWloRndETmRNWUkvRk5SWTJGYjR0NGZpOE42Q3cwVW52emZhZytvWEdjTzIySmJtbk9qdlRTam9UUDZtcmxXU3BqYk42NDBxazJFSlRrSGJFUGJaZ2UiLCJtYWMiOiI5MTI5NzlhMzZiOGQ1MTRiMTViNzM5MTY0NDllZjAyYjNlYmYzODdhMGFhYjEwOTQ1NTNlZWQ0ZDVjMDNmOWJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iks2Z3hLL0hGWEtITnEyd1VvL3haMXc9PSIsInZhbHVlIjoialVHYXIxMWs3L2dRWFBXTnkzeTJPanh3ZmNjUDJuM3hDc0JwZzF6QnExeTNTR0NIUS8vdlhLMEJBUTN5eXpOTkNEckpEVVNlTDJYNUpUSDJhdk1NYU03NGRDaldOc0cwaUMwbjlOdlU5L0lhRUNwZHZ0NmNGZC9CTmpIbHFINkg1czIrU2pxMFEzOUFOdVFJN1pWOHhqRXVzVXlRRDJ3bTBQY1EzOWhNYWtnbG9tYWdGQ3RRM1BCRFFKaEhQYVBXQnlCeFlPZ2o3V0NURkNZTW1VTklQQm9uZkpoNnlwbG00NEJkODI1cWhTeWpySklRYVpaN1dCTlNBZDZGWDVZVkgrZk5tT2UvWjJySDFnMkhTVUJNL0pMemFUMENITzRnd3g0RjlteWFXY21FZlhUUHBTYUlDZ1RldGZ1NmZmUUQyc09qcW5CdnByUVhnTWViVXI2cno0YWduK2FrU2tpVTdiQU8wOU1pZFV3aWRrNE1BMDlueGtJZ1UvWnc3bkx4azJLVDlxd050TTBlNFh3OGpOQzVmR0dIQnpBdnErQnFlZUdWR29wREF4Q0xkbWtHbFUycExQQkxTYXQ2VkdrbDV6eVBVTjJCQ1ZXaFE2OW9mUHVCdngvbExSUEVHeVRnbG1BSUVlK29vYjR0SFRzN1p1ODJ4MHBhajBJQlJOWXgiLCJtYWMiOiIzYjY2ZTc5MWE3MWVhNTRlODFhOTc4YTc1NGNkOThjNmUwZWUxYWU3OWU1YTMxMDM0ZmNhOGU2ZWRkZDZlYzFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70427448\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1517169836 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517169836\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1552180685 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:50:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFmTDR1b3dJeWs0bFpNdFB1V2Y1bkE9PSIsInZhbHVlIjoibVFmbDZuaXNXMmlKK3B1eTFFWVJDMXFCWTlPeitLalVDWk1GNGFHYlBGOTNxU2RlNTVTQXpCQ1B6bDdjTkVxTHpjS2dud1lBb01GbDMrbE9EcE1ScFBybGx5MHVwY3A5NHVxUm9HaGJWaEh4Um9DWjZOeHE5UGt2Zk9WVzVvT016a1dOUTBRRFZ4VTNPa0dJSEVZQS9UQU04UDJ4ZWtzNFRxODcyWlBWakxzMm03MFZXM1J4ODZ5R0pQWVhqdVZzYmwvaExYQnU0eCsvMHdwNmd0eDVZR0lrdlpxTWNiWHJMWHVTMGdMaWlYTUIwQW9rVlN0NVRnenhtZXZIYThVWmY3NVNvVGg1VjdlcHcrLzVERWVNNElvWm9MVFhNcC8wdEZMK1U1UXZCQUxFMFdyOHBLTXNhTlVGQ0VXM1RHN1QvbDdNaHhsR0poTGNvTHFpYTh4ZjNHZGFDczRtcDVBeU4rTmtQcDZZcUhLamd0cEJSSFJoWlVIZ0VrWWxWS1R1alRDaFRkTUgwSlkyMExYeU9YSTMyVStRbld2TTE2RkVvd29YOHkydjVPRUN6eVlDMXhLSmZ2TUZKN2pDeGgxblp6RzE4MWhsNFhPVERKSzdBRldpUWl2Nk5SSGtSV0E3azlsb1NpaWI3TE1DTGJQcGVaT1VJNVVqQ3U0d1YzMm4iLCJtYWMiOiJjYWQ2YmM3NGVhOGFhMTQwNTE2OGNiYzE5ZjkwY2NlNDMwOTI4MTRjNmE4MGI4YzY0OGJjODc0OWRhMTU3NmRkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFPcTZwcktkTUNrVU80ZStYQ0J1Z2c9PSIsInZhbHVlIjoiL3diVHhTR3Y1TnUxR3J3bDA3UDc3ckF0czY1cXVMOWRqN0Y2SXFnclBIUVoveW9kRDhYMEt6MXVmaExZN2NZZng1eDVvSDloM1hHajJEVEFkOEhoZWdkWVg0djdnVnNkTVVQekszMGdtTEloVXZmWlZhTGpURXhqZ1c5cGdEb0VVSld6TWppNEdtaHJLSGNOUWcxU2FFY1NGMHZaMy9uQmw4aTgyMjRlWmVicEk3SmN4bjBnMnViWWNaU0tQOVBmTmFqN3Y4T0w2cVR3WkZWdWFyKzFHdEFvdnJYa2lSclJiNk9ka2hyeGtlZzRTV3czQTlheGdtTnpxWkhFSG51UTZJRCtXVmRmZDBrR29nYVBSMFg4N25DbzZqMkRKa3Y4SzVYVjhqNVFPRHNOSWxOWk01bmZnV3dGcmEyR1cxdkV1bnlSNVNad1J1dFFZeVJYaFVIb3pqY2RNUExMZmtNV2cyQXI3QzE2c0VqRmt4OFczN3RWNFdQWHd1R09MYzh1VytHUlZBZXlVWURsVjY0VDdwWlV4YVNlV3VDc1hqOE93ejBlVU5mM29WZE1OSjFLWTZ1dWtMYS9xTWwrRDdLUk1leFhwditkNzkrdHlnWS84UGMrN2hmN2p6SXRvR1lobzYvNlpya0JrdFc5OXJrNk8wTnpzdXNHQnZPN0g1SWYiLCJtYWMiOiJiM2UxZTc0MjU3NWIwYTMzNzRhYzE3M2IwOWJhYTUyZGVhMDE4MzM4ZTczZjkxNDdjMjg1NjhlZDEzNjMyYmJmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:50:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFmTDR1b3dJeWs0bFpNdFB1V2Y1bkE9PSIsInZhbHVlIjoibVFmbDZuaXNXMmlKK3B1eTFFWVJDMXFCWTlPeitLalVDWk1GNGFHYlBGOTNxU2RlNTVTQXpCQ1B6bDdjTkVxTHpjS2dud1lBb01GbDMrbE9EcE1ScFBybGx5MHVwY3A5NHVxUm9HaGJWaEh4Um9DWjZOeHE5UGt2Zk9WVzVvT016a1dOUTBRRFZ4VTNPa0dJSEVZQS9UQU04UDJ4ZWtzNFRxODcyWlBWakxzMm03MFZXM1J4ODZ5R0pQWVhqdVZzYmwvaExYQnU0eCsvMHdwNmd0eDVZR0lrdlpxTWNiWHJMWHVTMGdMaWlYTUIwQW9rVlN0NVRnenhtZXZIYThVWmY3NVNvVGg1VjdlcHcrLzVERWVNNElvWm9MVFhNcC8wdEZMK1U1UXZCQUxFMFdyOHBLTXNhTlVGQ0VXM1RHN1QvbDdNaHhsR0poTGNvTHFpYTh4ZjNHZGFDczRtcDVBeU4rTmtQcDZZcUhLamd0cEJSSFJoWlVIZ0VrWWxWS1R1alRDaFRkTUgwSlkyMExYeU9YSTMyVStRbld2TTE2RkVvd29YOHkydjVPRUN6eVlDMXhLSmZ2TUZKN2pDeGgxblp6RzE4MWhsNFhPVERKSzdBRldpUWl2Nk5SSGtSV0E3azlsb1NpaWI3TE1DTGJQcGVaT1VJNVVqQ3U0d1YzMm4iLCJtYWMiOiJjYWQ2YmM3NGVhOGFhMTQwNTE2OGNiYzE5ZjkwY2NlNDMwOTI4MTRjNmE4MGI4YzY0OGJjODc0OWRhMTU3NmRkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFPcTZwcktkTUNrVU80ZStYQ0J1Z2c9PSIsInZhbHVlIjoiL3diVHhTR3Y1TnUxR3J3bDA3UDc3ckF0czY1cXVMOWRqN0Y2SXFnclBIUVoveW9kRDhYMEt6MXVmaExZN2NZZng1eDVvSDloM1hHajJEVEFkOEhoZWdkWVg0djdnVnNkTVVQekszMGdtTEloVXZmWlZhTGpURXhqZ1c5cGdEb0VVSld6TWppNEdtaHJLSGNOUWcxU2FFY1NGMHZaMy9uQmw4aTgyMjRlWmVicEk3SmN4bjBnMnViWWNaU0tQOVBmTmFqN3Y4T0w2cVR3WkZWdWFyKzFHdEFvdnJYa2lSclJiNk9ka2hyeGtlZzRTV3czQTlheGdtTnpxWkhFSG51UTZJRCtXVmRmZDBrR29nYVBSMFg4N25DbzZqMkRKa3Y4SzVYVjhqNVFPRHNOSWxOWk01bmZnV3dGcmEyR1cxdkV1bnlSNVNad1J1dFFZeVJYaFVIb3pqY2RNUExMZmtNV2cyQXI3QzE2c0VqRmt4OFczN3RWNFdQWHd1R09MYzh1VytHUlZBZXlVWURsVjY0VDdwWlV4YVNlV3VDc1hqOE93ejBlVU5mM29WZE1OSjFLWTZ1dWtMYS9xTWwrRDdLUk1leFhwditkNzkrdHlnWS84UGMrN2hmN2p6SXRvR1lobzYvNlpya0JrdFc5OXJrNk8wTnpzdXNHQnZPN0g1SWYiLCJtYWMiOiJiM2UxZTc0MjU3NWIwYTMzNzRhYzE3M2IwOWJhYTUyZGVhMDE4MzM4ZTczZjkxNDdjMjg1NjhlZDEzNjMyYmJmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:50:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552180685\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1605046242 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2304</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1604;&#1610;&#1583;&#1610; &#1601;&#1610;&#1606;&#1580;&#1585;&#1586; &#1576;&#1587;&#1603;&#1608;&#1578;&#1610; &#1603;&#1604;&#1575;&#1587;&#1610;&#1603; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1608;&#1601;&#1608; 400 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>11.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2304</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605046242\", {\"maxDepth\":0})</script>\n"}}