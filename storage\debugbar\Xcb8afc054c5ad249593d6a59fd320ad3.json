{"__meta": {"id": "Xcb8afc054c5ad249593d6a59fd320ad3", "datetime": "2025-06-30 16:05:54", "utime": **********.008863, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.599551, "end": **********.008881, "duration": 0.****************, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.599551, "relative_start": 0, "end": **********.951517, "relative_end": **********.951517, "duration": 0.*****************, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.951528, "relative_start": 0.****************, "end": **********.008883, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "57.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00302, "accumulated_duration_str": "3.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9802701, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.589}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.990408, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.589, "width_percent": 16.556}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.997919, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.146, "width_percent": 21.854}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNJVW1xVmR3YTdaQ1JObzMxRVUrd1E9PSIsInZhbHVlIjoiY2FobDA2enlpUCt0K0lmTWViNUM2Q1RnREtZRjJsZCt5OHZ1VjBKTEcrWW45V1ZhM0RxOHZNR2JXb3R3NVZKY21hL093T2FqWGx2MlhqdDF3ZXE4d1Q3SHMxWkVhNHgvVFdxWnZDL1gxdnI4NzF5SE56STNLK1lRMnVjb3lGb3FPZzdFeXZSL21UdGhzQ2lNTlNmcWlmUElIaGhFd0xDV0ZxT0VaVDFrM1lWWnhORkIyL1ZyUjFTeDFmSVE3Y2RmMTM4L0tVKzdvRnRXV1NZS20wMHpEak9xdjRFSmJnM1V3VUlkYU9oSzA2V0RHOVJVU0J4UWs0UzRyY0NpRHVmckhIaUVBUHlxY0o1ZWl4bUU1VFVQRjhwYzU3UDFvM1pQcll3OFNZTGUyU21XQ3B5NFdhOFIwZjl3eVg2QTJnYlpQUkEwYmtVUDF3WU4za0QrNWVHOFpJT1FldGRMN01sNUxNNUNGRmdOZXk4M1lVby9sdXB6KzlRVVEvM0UrR1NqQTBNRDRSYkJwa0Myay9QZitwd1Y3NVhtaDdYU3JmYjdvV2VFK3JXNG1UVWpFSE03Y1dHTTY2eWxCVXdTYUtZcVc4TDdzNk5wd2g3SG5SMVNHeUJqWFg0bnZ4Z09YR1cyUEdZTzdnVG9XaSt4UkhVVldjNVZnajk4SHg2SUd5TDgiLCJtYWMiOiIwMGVmNTdjYmEwNDgwOWRjYjBlZTEwYTA1NmJmNWZiNTg2MzZkMjQ0OWNiODlkODE4YTlhOWNkOTk0ZTJiZTVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFIVVB1VkxMVElmN2pyMW91MmRxb1E9PSIsInZhbHVlIjoidlRsOFdBVTV6ZEl2TlJrWUMzbGlvWG54RGNoWFBsL1dUaWN2YmVuSU5qbDFSVzZNKzNHNlNERysvNjBMWVgyWmVIS0xJQlN5WXpvU3hiVXZ6R2lSTXJ0NW9aMEd3cEVlZWExQWxsa1VyT3NOY3ZNY1Z0SUJvVGtlL0dhc2c2M1k3MC9GbnJjWXVEeGU2YXY2aTgzYTJTMGYxdTZVT2dBOVJyU3lrb29WMGFtYjNZWG9yclpMa2lCQTVtSW1UTUdiYTFKczBjTTJsL21pNGIxc3NRRzVRTzF3aWFnRGxLMWtTZXplRytac2RLSnZjUlhSbTdNaTFzQTUwK291RVZLUXRReHFTc01oKzdMOVp1WDZsazVQSUtMNlE0bm43YkYwUnRDcnBsN3hhanQvbERCd2JOV2V1Y2FPei9mS1dOOHZwYWQ0V3NuRXJEbnJHeHppZmt3d2VqckJCTHdoSzNwWmVzVHNscTFvT0MybjhVQWdJRGNxVXl0eFdkVEJMNEJrYjJWSE12T1A0WFJKRXl2ZyswNkhzRno1MmtmOXZReFJFU2VnOVEzQ0FaYm1ENFpaeURCcUJKbFlHM2lkSHNFdk9tS0tkNDlxZ3h2OHgwMnBqNWw1RmdVOHh2Yld4dUV6eFdUTUdvVlY3bVF0aW5kSXoreXNLY0IzdmxBUUppM1kiLCJtYWMiOiIxMjcxYzk0MjI0NjM0NWE2YmZjYjQyMzZhZWE1ZGZjZTQwM2RlNGQzNTA3OTBlMzI1YzM0M2NlYzZjNjFkM2UwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-284381764 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284381764\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-90360702 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5TY0w2RnF6RFVIbEQ3TElvSFN6R2c9PSIsInZhbHVlIjoiVCtIa3BxUklSNHNoNmoxOXlqWkRsZ2ZoRG1kaDlDNDNrY2NyM2w2MUV2TTlMZTJVWTM1cTVMSzRWYUZRWm1DRGhUWUdhVC9IcUlFNS9wLytrSVhFdDEyWTVqUXFRbmtYazloWUs3c01HTGRIdzhiV2hVaThCWDlmUnpBNGg4anNoSHVNT1FPMUJldFVPaXRCK0dYYjAvakd5WTk4Vkx3UTh1TnFqcjFYaUF3MHU2REpMN3EvZzkycDNQcFB1eElxd25JNE81L0xreERXVXFyUkxud2VYUkd6M05Sc21jQVgySDdvRHZtdUNnUkxYY2VQeGdQZWRWLzBCVjJFVEE5ZVVUTDNhSC9ic0RwcTVBaW1lbTUrQURYbVErVTUzaWVURUM2UER3aVdCSmdzMldmM1VBRnZPVUNyMWVBRUpGcFVFaWc2dGhOZ09EU2wyMTk2Rk9VeC9VazFZNStTRWpjMzdxVy9wUE9lTFdGdks3d2pWZW8rS3FBNFpGRFprbWIxamxZaVVCdldxQmZQMEkzaEtpd2FYY1JLSmUvam1GdjUzVlNUNy9sRUlHaUUvc09lSm5OQ3daYlUrUlpMWER5aEdZb1h5dGJlTGYvcnlESlRIUFdpL2REMDAxdkROQXVnYXlqQkg0VGFTMkFWREVqSzk4UEZNUU1qNGhRZjkxTnQiLCJtYWMiOiI2MGE5MDczM2FlOTJhMmVkNTM4N2ZiYTFhMzY1YzM4MzA1OTA2ODc5NjQ0NmQwZDI5YTNiZDQ2NWRmMDliNjE0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5OdGk1bGF1bnRDV0VYL2pLb3NnMkE9PSIsInZhbHVlIjoiUmE2MU5JeTRPTExJaTJHcmFUV0RaUFhzb2paamMvaU82SVg4KzF2OFRoNHlxTXB6YURlS0Vjd0lLK1BNMXF3cUJGMDNiK0s3VHlyZjRLejVQSlZWYm9jd2dHK0NvWUNOR1Z0dlZnbyt3RDg3dHd5blpkUk1ScWVDSGd3M3BSUldGc01FaVVzN1RzQmJjemZqY2txZ0pMdDFNMlhPZWtKVko2T0JVMnFjT1U3bjJ5dnpEWkNxdE5nSUZoYVlYajNIZmx2eWdHUlJvSG8yU0d4VmFVM2pnczl5aklLT1h2ZDhOSjBwMXJLcVBSVzZNc3pSVnIzcEEvL3NWVmk4VXhTZmY0S1RwWDlUWjdzSm5DczlncXBkZmxVd25IWEMweWVudHpCOFdpc1pKWmNSU1JWczQzUDgxWEg5QnMzUGd4WHBENjI3VlFyd3I0SmxxYXlLYU50cklJT3BDeWV5RGdscmVGVlhQdTlXZkQrdWhtYTNOQUVBNHkxZzlveVY1ZW8yWmY4UGFsOWJLZGV6UmYra1Y3Z2lqRjlBZmp5REtzdmgyMy9iejZEcko3YmFSM0hUM2Rjb0NZMU5zN1Vwb0hTdHVOTmFqanhLd3J0M2VyZ3Q1Z25JTU8zaVkrN1FsMFdsTlNGNWR6OS9KZThQbkNFNGNIWXdJNGR0RzZmemJhREciLCJtYWMiOiIwZWJlMmY4NDg4N2E4ODM4NDFkNDZiZDYwMzQ0YTY5NmQ0MDY0OWY3ZGVjMzNhN2JkMjU3YTkzMmIyZjI4OTA3IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5TY0w2RnF6RFVIbEQ3TElvSFN6R2c9PSIsInZhbHVlIjoiVCtIa3BxUklSNHNoNmoxOXlqWkRsZ2ZoRG1kaDlDNDNrY2NyM2w2MUV2TTlMZTJVWTM1cTVMSzRWYUZRWm1DRGhUWUdhVC9IcUlFNS9wLytrSVhFdDEyWTVqUXFRbmtYazloWUs3c01HTGRIdzhiV2hVaThCWDlmUnpBNGg4anNoSHVNT1FPMUJldFVPaXRCK0dYYjAvakd5WTk4Vkx3UTh1TnFqcjFYaUF3MHU2REpMN3EvZzkycDNQcFB1eElxd25JNE81L0xreERXVXFyUkxud2VYUkd6M05Sc21jQVgySDdvRHZtdUNnUkxYY2VQeGdQZWRWLzBCVjJFVEE5ZVVUTDNhSC9ic0RwcTVBaW1lbTUrQURYbVErVTUzaWVURUM2UER3aVdCSmdzMldmM1VBRnZPVUNyMWVBRUpGcFVFaWc2dGhOZ09EU2wyMTk2Rk9VeC9VazFZNStTRWpjMzdxVy9wUE9lTFdGdks3d2pWZW8rS3FBNFpGRFprbWIxamxZaVVCdldxQmZQMEkzaEtpd2FYY1JLSmUvam1GdjUzVlNUNy9sRUlHaUUvc09lSm5OQ3daYlUrUlpMWER5aEdZb1h5dGJlTGYvcnlESlRIUFdpL2REMDAxdkROQXVnYXlqQkg0VGFTMkFWREVqSzk4UEZNUU1qNGhRZjkxTnQiLCJtYWMiOiI2MGE5MDczM2FlOTJhMmVkNTM4N2ZiYTFhMzY1YzM4MzA1OTA2ODc5NjQ0NmQwZDI5YTNiZDQ2NWRmMDliNjE0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5OdGk1bGF1bnRDV0VYL2pLb3NnMkE9PSIsInZhbHVlIjoiUmE2MU5JeTRPTExJaTJHcmFUV0RaUFhzb2paamMvaU82SVg4KzF2OFRoNHlxTXB6YURlS0Vjd0lLK1BNMXF3cUJGMDNiK0s3VHlyZjRLejVQSlZWYm9jd2dHK0NvWUNOR1Z0dlZnbyt3RDg3dHd5blpkUk1ScWVDSGd3M3BSUldGc01FaVVzN1RzQmJjemZqY2txZ0pMdDFNMlhPZWtKVko2T0JVMnFjT1U3bjJ5dnpEWkNxdE5nSUZoYVlYajNIZmx2eWdHUlJvSG8yU0d4VmFVM2pnczl5aklLT1h2ZDhOSjBwMXJLcVBSVzZNc3pSVnIzcEEvL3NWVmk4VXhTZmY0S1RwWDlUWjdzSm5DczlncXBkZmxVd25IWEMweWVudHpCOFdpc1pKWmNSU1JWczQzUDgxWEg5QnMzUGd4WHBENjI3VlFyd3I0SmxxYXlLYU50cklJT3BDeWV5RGdscmVGVlhQdTlXZkQrdWhtYTNOQUVBNHkxZzlveVY1ZW8yWmY4UGFsOWJLZGV6UmYra1Y3Z2lqRjlBZmp5REtzdmgyMy9iejZEcko3YmFSM0hUM2Rjb0NZMU5zN1Vwb0hTdHVOTmFqanhLd3J0M2VyZ3Q1Z25JTU8zaVkrN1FsMFdsTlNGNWR6OS9KZThQbkNFNGNIWXdJNGR0RzZmemJhREciLCJtYWMiOiIwZWJlMmY4NDg4N2E4ODM4NDFkNDZiZDYwMzQ0YTY5NmQ0MDY0OWY3ZGVjMzNhN2JkMjU3YTkzMmIyZjI4OTA3IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90360702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}