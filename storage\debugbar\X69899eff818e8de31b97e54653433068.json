{"__meta": {"id": "X69899eff818e8de31b97e54653433068", "datetime": "2025-06-30 18:11:30", "utime": **********.643448, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.20956, "end": **********.643462, "duration": 0.4339020252227783, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.20956, "relative_start": 0, "end": **********.587251, "relative_end": **********.587251, "duration": 0.37769103050231934, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.587259, "relative_start": 0.3776991367340088, "end": **********.643463, "relative_end": 9.5367431640625e-07, "duration": 0.05620384216308594, "duration_str": "56.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45124312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031899999999999997, "accumulated_duration_str": "3.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.61722, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.144}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6307118, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.144, "width_percent": 18.809}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.634622, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 84.953, "width_percent": 15.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 17\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 977.5\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-482544318 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikg1ZHErSGhlNDRlaUI4aGxPK2NEb3c9PSIsInZhbHVlIjoicFVOTEdKTDFoQk1zSW1RazBkZjUzc1d0WEhtcWEwMzJza2dmamJMVkRKTFNrNDg5TlQ1dkg5aDZPN3NuZHltOWd5Zk9tWFRQc01aSEhLVzNhVjdPNU03ajNibCtkMFVxS0h2bVhhcHc0NW9FZkNkTUhFMUhDYUxyUDFVQ09OdVNkREc4UFdBNDJva01mSHNwSlpEazNMcXQwcE5ZV1cxaG83MEZ4bmxGYlBGNUZBSmVXdzNCSnhMZWJCb3dOQjk1b1lpcnVrVkMveS8wTmhwM29sWE9mc2Y5eDBWSWpUeVU1Z1BMSk1DZEtieWVJb1JNMVpTdENXQVJhTm5NUjFRV08ySnp0OStIUGVCNXQzOU9MUDh6aTNDS3F6VjcvWGlzdnU0amo0TXI0VjYzNkN2M29abzFyWXBGL2hKUDgybVJkaHI2L2krY3hxNGhCbEVKVXF2Tkgya2x1WnloZmNiOUUzTThvNUZkRDVXK1NwMENvdGs0QncxazAwZDBrdDVmaFlhWTZiZGFKc1VnMFpZaGh6STFUbHNSc3d1NHA2ZVJaSG42K0Q3UmRteDBsRjdFUVB1OXBBVmN5ZVhBbUtHb0hGM3JjZnl2allGQUMraFBYSmpQOTBpMU9IbjRYRXkyUGtnR0Y3VnpSZ3NKRTVBTWlHMEJ3ZEgzWEliTWRuTHAiLCJtYWMiOiJiNWI2Y2FlNWI3NjMxZTc5NzUxZDlmNGFkNWRjMWQ3YTNiNDJmZGY1NDgwZjEyZjI0MzBhNjg4OGRkMmVhNWQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9IYmZCQTZFZURRRWFMbkFoR2t6YUE9PSIsInZhbHVlIjoiTndrWnlZNjVSemRzRHo3RUhFZjdwSGVsR1BzaHdYSGhMemY0MlhNeVJUMUVlZDJsVk9jb2hjZjNUazluSVRpTTlHcWpYZDJkVk5DRkRPOThYYjI1dm4xcDJYeTZIeEQ4N1ppNVMzako0OEtaQk1EVkxYYVFMdXRVWnNwWHVzMldHbHhxOXI1N1RwWGsxa2VoSzJUSUVhdmZTdXdYM3dDZTNGSDRWQ0c0eFN3aUxFYzdxZUd1Ukk0ckNGWllLS2N3dC84dGMyM0ZZTy82b1VSN3NVbUQ3KzZKWTkrMVJFdVZqMUh4dnZsNi9aQXg5bk8yTy83bjBtUHJSUHdpOUc5bTZxVExvNmlVaDROVUhPZ0wveHo1ZmxMMlpWa1dyN0x1cGFWenUwaW5wNGRxWU5rSnR6TnhwVGpJdHBvYS83RXdnMjlQV1NwMnJGUzQwM3lvbGIzbzlRVzk1eVFKbmJPUVh5YnNKWHRSc2tpVDA2MmFPK092WEx1a3k0Y09JaHZOMmRaUFl1WThuZERvcEhYT3VBZVQvQStBQnRlVHhaRWlkcDlHb29ZVmYreWVtbnJ0QXZCbTZrbjh2Qjk4UnBtWGpxZlR2MS9uNkNTMjB3cW95UFBxTXhUY2lzd3BhbzBpUjcvV3F1b3cvcVc0eUpEYUtjSGZsdjFFUmUwWG5KVkkiLCJtYWMiOiIyYmE4MTU0YmE5YmE2ODY3OTMzY2YxY2RiNGI2MTc5MGU2M2IzMzk5YWM1M2MzNDdmZjU1YWMzNGZlMzgyMGZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482544318\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1970904457 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970904457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1536063375 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlQV1RWSDBNczJZVmV5d0F0YmtHeWc9PSIsInZhbHVlIjoiRWlQMjRXa3JCTmRQTWxvanZMbUd0aUN1RldnN1dSc05jWG90aXZiZTZ5Vkk2dGFLbW9YbEIvelZGMlhKd29nNlZDcWtRL2RGSXUzelhOd2RvdkV3UldCakxwTjZUSnYxejZRS0FyM0RTeHg2RW1Kbi9tUWZBZjFhL0VuV04zYjlXUEd3NmdYREJVWnh2U1hEOWJvQzJpWUc0Mjh6SitFQjM5dlF5eG5hdzFnbW1ERWREQS8zek85dEZLZGxBSnVxVFpBemxwNzVjYW9jQnBwUEpGZFFDYit1SDN2bXV3aGp0SDJDdVFFakR2MHIxR1BYTDd1aHY3TmlYcEpkWERXV3ZFekFHYUkrbzVaM1paV1pKdjFBVlFFeEVMdWcyU0tqWHRpeWlZVGFwbHFLRDVNNVA0QjNJcHJqcCtsYnlVWGs3Yy9UZG1sdnV3M21jMkF3amFQb3VhN3RYd3M5ODNPZ1RVb05nUHFhYUprdXJMTDhhZTB2YVo4azcrSzFZTjNJbldROUVkcFZIY1lJWlZIUDhpcHlwSERENTVURldTN3JhL1ZIUWRmMXFiRHAycE1kZzRjbW5WUFhrMHQ2a0JadDB6YnI2ZEZ3Y0dFTlpyTnkrZDJ2OGpuaXZzWm9qazNQWTFZWVhHOTRQbzNhbU1UUEhpcjAzbjJiK09hSDNVQUgiLCJtYWMiOiI3NTE3M2U2ZWQ0ZjNjNGMwNTc5MTY0NTQ5YzQyNjlmYWRkYzY2NzJhNGQ4YzgxZGU0ODMyYWFiOTUyN2U3ZmE5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdkZC9HZ3QxSjJMQ3M3eFJncEpINGc9PSIsInZhbHVlIjoicFkraFpqeVVhTjZMN3ZBNDVIVzQrajRrSTBXa2lkVElSMEl2MDZ1SG1DVFlyN3EzWjU5MjBsZUNjVnA4OWtYei9vQVdsbUZJeENTb0xlc3ZkNmpLZkt4RThyeWJKelFSVWR5a1hsQzNBazZNSWNva05TWnR6LzBCM2M3OHdRM3Y4cUcySzN1MWQ4eWdLMVQ5THZMRkJkU1VBcGRPaHhKM0hZRm1YdjZ3cUNVSk5tQVp3RFlJeUU5dmhlamRUN3k0R25mQmRrM1Y2dmJPNzhHSllRV2V4OHlZOEF2Z2NGVUNnUEFUdTc0dmYvZnVJM3hSblVWNk9DdTBpaG5uVFZPWFBFU2hmM1ZJUnhmR1hyU29nc2xDY3d5em5uRGNpMnZHUlZJTmRldlVzMXU0MUE2Q3FNcWNSMC94b1c4cGhJSEVwRkd3YURaSTkwR3NGMVlYZ2tTTlNZVWYvVThra3doU2dIYk1aaHJDb3hjdUV4SFpzdG96a1c5YmcwOHZnWFFDTTZRajhXd0cvSmpsdW14NXJLeXFEbDRLOHgwU2JQejJhN1hINVJmb0h6cldBT1R2anZVeU9XcjZWaGxtcWZNZGJremt4eUxLd2dMWjFwNnBnTHFDUENld0RuakFZQUFSdXFHZlV4TTA5SjM5eTVTQ0RiZHpRa1ZpZ1Y3RWV0U0YiLCJtYWMiOiI0YmFmNTlkMTM1MDJhZTY0NDMyODI4MTUzYzJlZWFiODkzNzE4NTYzZGU5YTU5NmY4MzQxOTQ4OWJmYTcwOTk1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlQV1RWSDBNczJZVmV5d0F0YmtHeWc9PSIsInZhbHVlIjoiRWlQMjRXa3JCTmRQTWxvanZMbUd0aUN1RldnN1dSc05jWG90aXZiZTZ5Vkk2dGFLbW9YbEIvelZGMlhKd29nNlZDcWtRL2RGSXUzelhOd2RvdkV3UldCakxwTjZUSnYxejZRS0FyM0RTeHg2RW1Kbi9tUWZBZjFhL0VuV04zYjlXUEd3NmdYREJVWnh2U1hEOWJvQzJpWUc0Mjh6SitFQjM5dlF5eG5hdzFnbW1ERWREQS8zek85dEZLZGxBSnVxVFpBemxwNzVjYW9jQnBwUEpGZFFDYit1SDN2bXV3aGp0SDJDdVFFakR2MHIxR1BYTDd1aHY3TmlYcEpkWERXV3ZFekFHYUkrbzVaM1paV1pKdjFBVlFFeEVMdWcyU0tqWHRpeWlZVGFwbHFLRDVNNVA0QjNJcHJqcCtsYnlVWGs3Yy9UZG1sdnV3M21jMkF3amFQb3VhN3RYd3M5ODNPZ1RVb05nUHFhYUprdXJMTDhhZTB2YVo4azcrSzFZTjNJbldROUVkcFZIY1lJWlZIUDhpcHlwSERENTVURldTN3JhL1ZIUWRmMXFiRHAycE1kZzRjbW5WUFhrMHQ2a0JadDB6YnI2ZEZ3Y0dFTlpyTnkrZDJ2OGpuaXZzWm9qazNQWTFZWVhHOTRQbzNhbU1UUEhpcjAzbjJiK09hSDNVQUgiLCJtYWMiOiI3NTE3M2U2ZWQ0ZjNjNGMwNTc5MTY0NTQ5YzQyNjlmYWRkYzY2NzJhNGQ4YzgxZGU0ODMyYWFiOTUyN2U3ZmE5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdkZC9HZ3QxSjJMQ3M3eFJncEpINGc9PSIsInZhbHVlIjoicFkraFpqeVVhTjZMN3ZBNDVIVzQrajRrSTBXa2lkVElSMEl2MDZ1SG1DVFlyN3EzWjU5MjBsZUNjVnA4OWtYei9vQVdsbUZJeENTb0xlc3ZkNmpLZkt4RThyeWJKelFSVWR5a1hsQzNBazZNSWNva05TWnR6LzBCM2M3OHdRM3Y4cUcySzN1MWQ4eWdLMVQ5THZMRkJkU1VBcGRPaHhKM0hZRm1YdjZ3cUNVSk5tQVp3RFlJeUU5dmhlamRUN3k0R25mQmRrM1Y2dmJPNzhHSllRV2V4OHlZOEF2Z2NGVUNnUEFUdTc0dmYvZnVJM3hSblVWNk9DdTBpaG5uVFZPWFBFU2hmM1ZJUnhmR1hyU29nc2xDY3d5em5uRGNpMnZHUlZJTmRldlVzMXU0MUE2Q3FNcWNSMC94b1c4cGhJSEVwRkd3YURaSTkwR3NGMVlYZ2tTTlNZVWYvVThra3doU2dIYk1aaHJDb3hjdUV4SFpzdG96a1c5YmcwOHZnWFFDTTZRajhXd0cvSmpsdW14NXJLeXFEbDRLOHgwU2JQejJhN1hINVJmb0h6cldBT1R2anZVeU9XcjZWaGxtcWZNZGJremt4eUxLd2dMWjFwNnBnTHFDUENld0RuakFZQUFSdXFHZlV4TTA5SjM5eTVTQ0RiZHpRa1ZpZ1Y3RWV0U0YiLCJtYWMiOiI0YmFmNTlkMTM1MDJhZTY0NDMyODI4MTUzYzJlZWFiODkzNzE4NTYzZGU5YTU5NmY4MzQxOTQ4OWJmYTcwOTk1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536063375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>17</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>977.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}