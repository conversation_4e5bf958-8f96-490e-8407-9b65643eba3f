{"__meta": {"id": "X2c164c094af972b9e303ab225baba8ac", "datetime": "2025-06-30 16:05:56", "utime": **********.288027, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.862218, "end": **********.288042, "duration": 0.****************, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.862218, "relative_start": 0, "end": **********.230956, "relative_end": **********.230956, "duration": 0.*****************, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.230966, "relative_start": 0.*****************, "end": **********.288044, "relative_end": 1.9073486328125e-06, "duration": 0.057077884674072266, "duration_str": "57.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00354, "accumulated_duration_str": "3.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.258984, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.672}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.269628, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.672, "width_percent": 14.124}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2790098, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 67.797, "width_percent": 32.203}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299403284%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik41N0k3ZTQxVGw1Y003NXNwYVhVdGc9PSIsInZhbHVlIjoibE1uSGxoQXlLbUk2SXhxeFFlNEhJcE56T1NOZVN5WnVWbzBZY0I1dnFrc0hqbUM0M0tNdjhic3BIRldLZjJwWGI4QUVEdUlsRGM0T1VvcDdhdUJIcldwN3NjRi80aEFiTTA5aVIzVWN5MnZ2WXo2cnUzMlljMTU5WEx1ODcweW9yMmFKVWlnR2tQWkw2OHY2d1RlSkxiYXJFaTM2cjIxZVJkRWd2bE5PWGpOVmZzWnF0cVNHMHNSQWxBdmlYRVF3em05UFN6T0lkQUlSZW53WnZaWmNZaFZEbmpJcWZyNTg4bnBMTEg1NHlHYTZlSGUyeE5LZ0ZTbnVVb1hERy9WS1hOU1IwMEUyeDNpUnlTeVpTUWhSRGtha3V3enhnZGs1QW0xeklteHR6NGg3T1BRWEl3bzRHOFM5OUxPMnFkK3lHU2ozck91WUlJZXpnNEc3UWE5bHA2YUVPYThYQWxpVTZLTnhhZEUwRlV5OUZabDVyZjdYaVhlTEkwRTFJb1RWOWxUdVBFZnRwNmxXMGM1bmlNR3NhMmRYa0YxNEFZOStkcUtTcmJydnVZWlgwcVUxazIyUDZZZStaalRhWnA4cFJGVWVoOGxuOXdRZGV6OW5IUWV4V2tIQUE5WnBZa2tFUW9mU0ZGSE40NXcxZWczRnl0RHByTnJtMXoyeERKR28iLCJtYWMiOiJmNzVlMmJiZDcxNzJhY2IxODExZWZhMjE2MTY3MjdkNTM3Yzk2YzcyNDRiMTFiMjNjODAyYzU1YTQ0ZTcxOGRkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkN4U0R2dXNkWURwWDZqUHdwZm1aZEE9PSIsInZhbHVlIjoiWUN2ZjdYZnRJYjZtZHVXWGtHaU11Q3NEOVBkM2txYTFRaDFTTG9INVZQV3gxNnZtRHpYaFUzbC9qRmxpRndEVjlYZ2lVTVdKNnRCQU9OVlVBc3ZPWlRTYXBBbVQzdkU2cVNLV2ZSRTlDc1lzbE5hbGZxM1MwNldDOFJ4SmdiM2YzR3J6TmZBbzBxOHN4dSs0ZDVmbDdPSm9BRXFHbFB6clV4TG9FNFBtZ1M1UmhOVmdRZDN3SG1ORDdRNjlmcXR6d2pEOGJjSTRaaHE2WnhWMVdzdUFkdG1hbklTamRSU2t1aUVXcFBUamVjTXdsVThySHNGQXpXaVVDdXExMDRsaisxTVhqZHh6a0I1S29CaENtcDRuWkVjMWd4NjQrTTRBL2tnLzI0NGJpYVUwWnNZeG1LYVl2bzVtVGsvV1BxeTRiY0RkUlpXL1pHMVEwRXhRYkF3Qkl6ZlorbXluQWl5ZFE3MlVmUVVKREtCQ1JZZ2xJYzFyMXNKeG5KWGRlSFQvQUNxekRmNDJ5YkJJZjgrMjM2UWlFQVhrekd6dDBjRmFwM2hoTmtiTEpZemNtcEJyMVE4VzNPNEZzT3FHTWcwaHdqNHdKQ2NZK1Jla0ZzaUFKLzN1U0pJdTkwRW5la241R0pHejJzbEFZb21hVlBMQ2MxVG1qL21TbkY2R0dndjEiLCJtYWMiOiIwYjNiZTJhNWVlOGRjYmRmMmNkYzBiOTliZTBmYjVmNDMzNDM4YWZiMTc2ZTI1N2ZhM2ExYzEzYzYwZmEyMDBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1730049419 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dnW2GtOkH1McwpPgUpjB1FSogAcH5jv7y4beloPJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730049419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRnblJIRGtqQkVtc0hVNmhIYXBXNnc9PSIsInZhbHVlIjoiWXFKSUo3bDlDVzRMcmZBY0pqaUQ1MDVFK0ZSYnNRUDZpVW91WlN3QXV5cGYyL0lZaCsyN1ZJLzJCRklmQ2JtRzFjQ3FlRUpEbnFKOVRWeW01OFRxRXBmWTBDL0h6TUpvdXNPbXlsZnJoMnNSUWgyOEJPSThwM2pqZkRRdjlXOHZldEZwaFF6Q1Q5NDd1M0ZYL3FZTktvd0tNQ0ZER1k3aVFQVnVSc3dSTzJXUmxqaG1GOHNYZ2dYVjJRTWYyZHVjdklEKzFtZXByUDAzT0wweGlzNlZWQVI0RER3dUlNdEtUbnl4VkVKaUoxdThydkNlVXh0Si9jL0VWYStFQllJU210a3hOYWVNbVlhUFRuQml3UUFsdnJRK3NEQXQzZ1BFc2NNQWtNRi9SaTVXUTBvUnJaTk53aFQ1RmNLWGdlUkJWc3ZCdUhCdkRUMVkyMG85RnZWd2N2S29WUVA3a3BWaUpxdlU2U0o3SkVmenJjZHNrNjBkVlE1cXc0SmMwTHNBN2h0R21vZzQ2ajZXa0xHZWx0U0U0Q3JnT09DemtUMm5UcHN2VkhMUXh4WHd0OTdGTGVzR012THpOYzRHYVJ5SmRPTG1WWlBwY2tzL3ZqRDFMd1l3ZlBISHBmVFBJV1RieUVieVZWWUwvUEpTdzVNNGtyUGthTkRJUCtJQzcvKzYiLCJtYWMiOiJhNzUzMmZlZTg0NGM3YTdjMDdmZmYyM2YyMzg5ZmQwYWI1YTZhYjhhMGM5NTY4MDgwNWNlZjU2ZjhlZTZkZmFjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZqdkp0bnNVQ3BqRjh0bWRwVFhnRmc9PSIsInZhbHVlIjoiNUg3bEFOVnZReVNFQ09LdDlSWGZLaTdmdnRuNEgvN3Bud3dieUt1Mm9SZzVwUFNaSlhzcmZLTU9HU1E5c3duRWlrZk1RMlhvaVdNdGgyTVFLdnM0cVB4UHZzVGkvanVFQVJWTnlvK00zKzZnWWNtYVNzN2ExVGd3SEZWOUVqakUwU2wyZ0JjUXJYRXpwVnBrMzE1N0tjeTJZUDlRSy9URTRVektzU25XbjNVbHdvd0l5N2lqcCsyT2FVdm83ZVJBTzFPeTByVjVsY3o3VGx2cFBvTXlBUDN1YmxuOHVuVVZrNm1TNTAycUErMjBJVWFRTkdGVlZTZG95YmRsaHdxMEF0MllxSW9UNFF3Q3lGTjZFWFMrTGE3Z0lKNHN2akJJUXB3L25WODFDMXlSc29vSjJ0NEpzRnR6aGNOWXIzcFBCS0ZxSWV6ZDNyNHlHOVVxRlptclBmUnJYM3pIR1JPdXJ2VzZJK1JjbnphNnNHMy9rdDBNYXZOWS9Qc0s0VWJxbC9RZVgvNUZLckY3SlJxRk5UcmIxZEJ0R09QWEJiaDd3Y2l4TDlKbmwvWHViUzFtSkFXNTBQMndkZjhONGF5WlFKWEhjVkkyMWo3bWhha2VpTFNLTzMydGRxNURubGZ2Skh1SVBEWTdRaGw5VmNOelQ1S0V0cGhvTEpVK05hbEkiLCJtYWMiOiI4N2RiOGI5NDZmM2E2M2U1MmY0YzQ1MzIxNjVmZWVlZGE3ZGJkMWNiYmE5ZjI4YzFlMDgzZTVkNTAxOTY5ZmU4IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRnblJIRGtqQkVtc0hVNmhIYXBXNnc9PSIsInZhbHVlIjoiWXFKSUo3bDlDVzRMcmZBY0pqaUQ1MDVFK0ZSYnNRUDZpVW91WlN3QXV5cGYyL0lZaCsyN1ZJLzJCRklmQ2JtRzFjQ3FlRUpEbnFKOVRWeW01OFRxRXBmWTBDL0h6TUpvdXNPbXlsZnJoMnNSUWgyOEJPSThwM2pqZkRRdjlXOHZldEZwaFF6Q1Q5NDd1M0ZYL3FZTktvd0tNQ0ZER1k3aVFQVnVSc3dSTzJXUmxqaG1GOHNYZ2dYVjJRTWYyZHVjdklEKzFtZXByUDAzT0wweGlzNlZWQVI0RER3dUlNdEtUbnl4VkVKaUoxdThydkNlVXh0Si9jL0VWYStFQllJU210a3hOYWVNbVlhUFRuQml3UUFsdnJRK3NEQXQzZ1BFc2NNQWtNRi9SaTVXUTBvUnJaTk53aFQ1RmNLWGdlUkJWc3ZCdUhCdkRUMVkyMG85RnZWd2N2S29WUVA3a3BWaUpxdlU2U0o3SkVmenJjZHNrNjBkVlE1cXc0SmMwTHNBN2h0R21vZzQ2ajZXa0xHZWx0U0U0Q3JnT09DemtUMm5UcHN2VkhMUXh4WHd0OTdGTGVzR012THpOYzRHYVJ5SmRPTG1WWlBwY2tzL3ZqRDFMd1l3ZlBISHBmVFBJV1RieUVieVZWWUwvUEpTdzVNNGtyUGthTkRJUCtJQzcvKzYiLCJtYWMiOiJhNzUzMmZlZTg0NGM3YTdjMDdmZmYyM2YyMzg5ZmQwYWI1YTZhYjhhMGM5NTY4MDgwNWNlZjU2ZjhlZTZkZmFjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZqdkp0bnNVQ3BqRjh0bWRwVFhnRmc9PSIsInZhbHVlIjoiNUg3bEFOVnZReVNFQ09LdDlSWGZLaTdmdnRuNEgvN3Bud3dieUt1Mm9SZzVwUFNaSlhzcmZLTU9HU1E5c3duRWlrZk1RMlhvaVdNdGgyTVFLdnM0cVB4UHZzVGkvanVFQVJWTnlvK00zKzZnWWNtYVNzN2ExVGd3SEZWOUVqakUwU2wyZ0JjUXJYRXpwVnBrMzE1N0tjeTJZUDlRSy9URTRVektzU25XbjNVbHdvd0l5N2lqcCsyT2FVdm83ZVJBTzFPeTByVjVsY3o3VGx2cFBvTXlBUDN1YmxuOHVuVVZrNm1TNTAycUErMjBJVWFRTkdGVlZTZG95YmRsaHdxMEF0MllxSW9UNFF3Q3lGTjZFWFMrTGE3Z0lKNHN2akJJUXB3L25WODFDMXlSc29vSjJ0NEpzRnR6aGNOWXIzcFBCS0ZxSWV6ZDNyNHlHOVVxRlptclBmUnJYM3pIR1JPdXJ2VzZJK1JjbnphNnNHMy9rdDBNYXZOWS9Qc0s0VWJxbC9RZVgvNUZLckY3SlJxRk5UcmIxZEJ0R09QWEJiaDd3Y2l4TDlKbmwvWHViUzFtSkFXNTBQMndkZjhONGF5WlFKWEhjVkkyMWo3bWhha2VpTFNLTzMydGRxNURubGZ2Skh1SVBEWTdRaGw5VmNOelQ1S0V0cGhvTEpVK05hbEkiLCJtYWMiOiI4N2RiOGI5NDZmM2E2M2U1MmY0YzQ1MzIxNjVmZWVlZGE3ZGJkMWNiYmE5ZjI4YzFlMDgzZTVkNTAxOTY5ZmU4IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1522592923 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522592923\", {\"maxDepth\":0})</script>\n"}}