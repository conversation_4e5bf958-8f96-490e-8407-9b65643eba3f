{"__meta": {"id": "Xefb266e73536e273f2cd5e6addcd9346", "datetime": "2025-06-30 16:09:56", "utime": **********.301496, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[16:09:56] LOG.info: Opening Balance Request Started {\n    \"user_id\": 22,\n    \"warehouse_id\": 9,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.286051, "xdebug_link": null, "collector": "log"}, {"message": "[16:09:56] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.287151, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751299795.706008, "end": **********.301517, "duration": 0.5955090522766113, "duration_str": "596ms", "measures": [{"label": "Booting", "start": 1751299795.706008, "relative_start": 0, "end": **********.075862, "relative_end": **********.075862, "duration": 0.3698539733886719, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.075875, "relative_start": 0.36986708641052246, "end": **********.30152, "relative_end": 3.0994415283203125e-06, "duration": 0.2256450653076172, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52614208, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.295919, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00346, "accumulated_duration_str": "3.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.251435, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.264673, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50, "width_percent": 17.341}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.279532, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.341, "width_percent": 19.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.28168, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.416, "width_percent": 13.584}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1941052692 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941052692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.285388, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-400452666 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400452666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287046, "xdebug_link": null}]}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-196322353 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-196322353\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-382769107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-382769107\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-911709896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-911709896\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299794746%7C5%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkMybkZJYmxxNUk5RDRkKzNRQWNJcHc9PSIsInZhbHVlIjoiNGxxeWloVmdHMVhwTE1UR2JZVjV6UUN6UXpBY2d3WEVnZmpid1JhRHc1aXpJcFJkRlZOYnpiOXRIemhrZEZFbjU3M1RnZU1QcnNZVlJxbzBmazRJZU1UWHBUKzVuSkg3TUJoNDI1ZGZ5WmFnQ0E5TUtOaFFRUU9Rd3JQMTNWaFU4Y2h1T2hMRzYyeGlEOU5KN2ZIK2VDWEpwNjBrbDZjK0NqOWlpRlUzaE9lTnRhbEtCVXlPVEFNckd2b293dk5HR1hKeURKenJJcCtoeTVINUVJRVZRUUdsSlRKSVY1bjNTdHZHZU9IMVJmSHBvOWxEcFd0RXRsS0VZZkNLeVV6T2gzazI1d1ZPZGQxQWFNVjJqcVRJRE9DdVYrRlRMZUNKbGdKVW03cGpsMnlkZVhncnFpRmZBS0YzbDJqRnhOM0FUcHBSdWFRR2QxTkxZVTBkVXgveDhJbDVkRHFkUlR6aUdYbWlVK2RIK2pyN3VHZTVlTE1KbjdBMHFQa3NlNlZYekhXN0RpRGxKVytSeXRHR0QxR1NrVmErdXZGaEZuMHZQNE4yMUVKSzhFNU8yRDh3VXlCYStnNUdaUU1MZG5xTVBEYlg0cUdlbXJPVnJPck1zdDQzenBIM2JEOHJZNG1nd1lnSUc4NkkyZDdQb2dEVWNVSWRlZmtoL29rQklOdi8iLCJtYWMiOiJlMTE2NjgwNWU2ZGEwZGMyNmU5N2U0MjkzMGE3NzJlMjhiOGQ2ZmI5MWJlODgxNDQ4ZjQyNGM5ZmJjYjBkZjgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFLWkV2V0hSbC81czFpK0hqTUwrM0E9PSIsInZhbHVlIjoiVUlmWXI1MTliM1ZLeGNJRjZTMVBDY1NMQXZWby80MkxqdlFSRENxOEhKR1M3U0hOVW9oa0lLM3c4RzQ1OFZuaFF2cUhhL2xabldSeEVuem5DT2NFSGExQjROL2NlQnhpRjhKWC8vUXZyWUZwOVhJaUdGZDBnZXBPMFRydFhQMGVsTFhEZkRSQVZHNTZ0aVd6Z2ZiWVpWT0MxRGlwcTdkZGJCcWZzS2lLRjByaTBxeWZOck5wQk9RcnZnREJBdFFQS2JlSTdMRHJzdDZ6Yy9CQVJ6ejBhZHR3QU82aS9JeWRIL3pPWDBqT3NVazNrY1dWVmJzdXI4cW9pYm5naGVxQTRGQmhYcDZOTzdLNmtSbUtGS29UMmVHN2RSbE9IV0RlMG5GT1ZDSXQwVGVmc3VwdWRlQ3JKVDVSYno1bmNUSnpiaWRpY1dad3VNMTEvTzlJOGlPOUVSWmtud2R6MVVGZEk4L1JSc3ZBYkNhcVRRQ2pYRUNPOGxrTm1GZlRwMGdNNnQwWjRYR3VNVExDN0kyODUwSEc1by9CV3V5Y0xPUEs2UTlWREo0UjVLTHZuK0JLV0pLYVVJRnUzemdCSWVMSkZFdjRPWnJTZ2dUcEkzL0dqWUJ0YVNYaUNkUmFac25PVi85Yk0vSXNKMERYZTFqUHlVbzNRQU15eDNpdzA5aTYiLCJtYWMiOiI1OWFhY2M2YmQwYTYzODRjMDczZGFkNGE1YWQ4NGEyNzU3MTljZWE3MThhMzVhNTk0M2QwZWY2Y2IzNTYyMWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-536375351 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536375351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-365508001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:09:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9XNXdQbmVqNGx1TnVMOERCUnlnNkE9PSIsInZhbHVlIjoib0pNMnI2M3EvWkN2UDlKZkVDYnBTQVRjSnZWbTIzeEhsUHhlUkx6RGl5UlFNUmpFd0M3bGVIOXMrWWkzcXp6czJHVmxSaGVNejJmMmNNcFcycWR5QlU5RU4yUk1IQUhmbjlxdWIvM2p6b2drKzJFa0s1SXNMYzBuNS9xclhBUkdISHJTKy9RbnRUM2o2SHVMNVp2SWVqZ0RjclY5Qm1PTDBQVll3TksvZHF0Q1plSWFkeTQrcDJXQ2lFaHVib3YxR2NiOUNrcG9hQVFGaWNkY2xLZVVNMFhjYWFJbjJwZ2xsY3lmK3RDQk10WDYrc29oU2lsYUFHbzFpNFd2amxQck9jWXA3Q29IMi9LOTVzQSsyclFrdU9WNWZPdlVHNzVNUzBWajB1MkxDL21XMGh1SUhMZCtjNnJ6dmFmem5FMXY2RXl2NDdSdTRLdi9LUVdrN01iUTNLb2tsMUpWTElpYW1ZczdQa1RoaTViMW45WitScWJJeUp4Q0VqeUpVUEFDL200ZWdLRjhCejBrL3d0cXFZeFZxd3BwNVROcWk3aEhvTUNwTXNQWkV5bitHSTVDVFVNbXpkNmRDNFZPb2JnamI2cTFQRXZGb3J6L0locFdmdEkwcEsrMjRBc1dCRHRMeWZ1dmM0UXlsY0pKMWl5SFF2Zjc0MmE1eTVnemZibHYiLCJtYWMiOiI5M2ExN2MwM2Q0OThiNjMzNjdlYTgxNGNiMzkxZTRlODFjZmU3YTY0MDUzZDVjZWNlOTVhMTNhODA4OGNiMGVlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9STmVBbmdKNklUYXVBN0VHeTE0MFE9PSIsInZhbHVlIjoiV2RIaTJHcXFBMlhqYmhWc0hoTGM4WGNhektzMFpZbFNxdGR4MVlMaHhaUnVhWnBUeW94OW1QNjh5S3lWQXF5L2JaRjNtQjV2T2p1Ykx4bEhKU2dNOEE5MkFKRWF5TTIrd210TWtFOHFVVkZvZmQxTW56ckgvSDlUTUZKWGF3U3VUd1BWNTBsaGdIYzFSaDEvbVBsblFjT0JYZGZVWW9kUGJsMmhDZHA4cGhYcmRkYzY2WWlMTm5WejRhSlpmWm9ERGI1QUVMZk93UHd1MG9xWllMZHhsVlM5cVY2Q1VXTE5wa2lPdG5EcmprNGRtV2VyQW85VG4yQUdyY2o0R3JpeWd2UDZUczBkeWU5Y1NFa0JMR1haWVZvUm9FM1dkSUs4aWhlcjQ0TlRoVTJ2bnRoL2xWS1JvSUNEK3ZBSnhSL2tiY3ZxcUNSa21BYjZHMEdZekZId3JaMS9FcHQ2VnVka3p3SzNnNDdidjRlNkVMdDE2bGJCeFVtYUN1RWJPekdpUEZHeHp0aTZrWmRLdFI4WVFabEtuYkVDdFp1c2ZIOHByQ0VBc2tpemhTbWJLRDBjVWFDdkJiRVgxejZGME5LQW45WVlCQTBCVmZIek8zejVYTHJzb0tIN2JOdVNGNkFxTzk2V20zOE9GMW9wL240QlNiUUNJam03UTFHamlDWWEiLCJtYWMiOiI3MmY1OGE5ZTgzY2NjNGZmNzU4NjIxMjZiOTdlNmE0YjViNzQ5YTEyMWM0MDExZWZjZTgzNzJhNmExMjA3ZmNjIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:09:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9XNXdQbmVqNGx1TnVMOERCUnlnNkE9PSIsInZhbHVlIjoib0pNMnI2M3EvWkN2UDlKZkVDYnBTQVRjSnZWbTIzeEhsUHhlUkx6RGl5UlFNUmpFd0M3bGVIOXMrWWkzcXp6czJHVmxSaGVNejJmMmNNcFcycWR5QlU5RU4yUk1IQUhmbjlxdWIvM2p6b2drKzJFa0s1SXNMYzBuNS9xclhBUkdISHJTKy9RbnRUM2o2SHVMNVp2SWVqZ0RjclY5Qm1PTDBQVll3TksvZHF0Q1plSWFkeTQrcDJXQ2lFaHVib3YxR2NiOUNrcG9hQVFGaWNkY2xLZVVNMFhjYWFJbjJwZ2xsY3lmK3RDQk10WDYrc29oU2lsYUFHbzFpNFd2amxQck9jWXA3Q29IMi9LOTVzQSsyclFrdU9WNWZPdlVHNzVNUzBWajB1MkxDL21XMGh1SUhMZCtjNnJ6dmFmem5FMXY2RXl2NDdSdTRLdi9LUVdrN01iUTNLb2tsMUpWTElpYW1ZczdQa1RoaTViMW45WitScWJJeUp4Q0VqeUpVUEFDL200ZWdLRjhCejBrL3d0cXFZeFZxd3BwNVROcWk3aEhvTUNwTXNQWkV5bitHSTVDVFVNbXpkNmRDNFZPb2JnamI2cTFQRXZGb3J6L0locFdmdEkwcEsrMjRBc1dCRHRMeWZ1dmM0UXlsY0pKMWl5SFF2Zjc0MmE1eTVnemZibHYiLCJtYWMiOiI5M2ExN2MwM2Q0OThiNjMzNjdlYTgxNGNiMzkxZTRlODFjZmU3YTY0MDUzZDVjZWNlOTVhMTNhODA4OGNiMGVlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9STmVBbmdKNklUYXVBN0VHeTE0MFE9PSIsInZhbHVlIjoiV2RIaTJHcXFBMlhqYmhWc0hoTGM4WGNhektzMFpZbFNxdGR4MVlMaHhaUnVhWnBUeW94OW1QNjh5S3lWQXF5L2JaRjNtQjV2T2p1Ykx4bEhKU2dNOEE5MkFKRWF5TTIrd210TWtFOHFVVkZvZmQxTW56ckgvSDlUTUZKWGF3U3VUd1BWNTBsaGdIYzFSaDEvbVBsblFjT0JYZGZVWW9kUGJsMmhDZHA4cGhYcmRkYzY2WWlMTm5WejRhSlpmWm9ERGI1QUVMZk93UHd1MG9xWllMZHhsVlM5cVY2Q1VXTE5wa2lPdG5EcmprNGRtV2VyQW85VG4yQUdyY2o0R3JpeWd2UDZUczBkeWU5Y1NFa0JMR1haWVZvUm9FM1dkSUs4aWhlcjQ0TlRoVTJ2bnRoL2xWS1JvSUNEK3ZBSnhSL2tiY3ZxcUNSa21BYjZHMEdZekZId3JaMS9FcHQ2VnVka3p3SzNnNDdidjRlNkVMdDE2bGJCeFVtYUN1RWJPekdpUEZHeHp0aTZrWmRLdFI4WVFabEtuYkVDdFp1c2ZIOHByQ0VBc2tpemhTbWJLRDBjVWFDdkJiRVgxejZGME5LQW45WVlCQTBCVmZIek8zejVYTHJzb0tIN2JOdVNGNkFxTzk2V20zOE9GMW9wL240QlNiUUNJam03UTFHamlDWWEiLCJtYWMiOiI3MmY1OGE5ZTgzY2NjNGZmNzU4NjIxMjZiOTdlNmE0YjViNzQ5YTEyMWM0MDExZWZjZTgzNzJhNmExMjA3ZmNjIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:09:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365508001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-368613634 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368613634\", {\"maxDepth\":0})</script>\n"}}