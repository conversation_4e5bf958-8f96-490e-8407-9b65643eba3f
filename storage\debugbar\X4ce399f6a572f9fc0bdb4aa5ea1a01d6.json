{"__meta": {"id": "X4ce399f6a572f9fc0bdb4aa5ea1a01d6", "datetime": "2025-06-30 16:06:49", "utime": **********.938791, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.477779, "end": **********.938818, "duration": 0.46103906631469727, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.477779, "relative_start": 0, "end": **********.886602, "relative_end": **********.886602, "duration": 0.40882301330566406, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.886612, "relative_start": 0.40883302688598633, "end": **********.93882, "relative_end": 1.9073486328125e-06, "duration": 0.05220794677734375, "duration_str": "52.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00292, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9156191, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.384}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.925678, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.384, "width_percent": 15.068}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.931701, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.452, "width_percent": 20.548}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/branch-cash-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1176963291 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1176963291\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-992829288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-992829288\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-335399216 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335399216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx7%7C0%7C1998; _clsk=bsl672%7C1751299604028%7C8%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllveVBTNEdVNkFBUnFZL1g4akJqQ3c9PSIsInZhbHVlIjoiV3UwUk5QSVFVUEoyT25rYXhmNGpXNHJUSUgzaW1xRDhZT09KcW9TUkQ5SHhLOFpKRkE1Mm5CWXFlWkRjSFZsMUxjTHpsZSt5YjZMalBVTXhyMjhwTVZSdXRxc1ppZnpaN29WS2NxNTBNYVQrRjlqUEYrQXR1bXg5WjdvMmhvazlaeFV2V1FRTEhyU1N4ZUJ4K2NwTmxoZWRLYUN5L0ZtbGJGSEpLcmRYWXI4cFkxZFBQeE4vVTVRaDhORU1xWnAxOWwrQXlVVFdiUDByTWJhNENXT0d3Y3FmNit1TFFVSmxVb085SUVVYStLZldETDR1RmRPbTVjUTRoNlZFVURpS3BNVFNRSlc2ZUJFY2hVVlFpYzh6dGtESno1TGhuYXRwU1psRndSa2hYZDhjb0NhaHFWN0FKMnY3YlVkVHhCY1dGaHp5S1VCTDlSNEs0aS9FazZ3RXRERkE3d3FUS3BuTkFMMDZEUHI1bFUzVjZwMkpiSXc1czcxN2lPNU1YSjIxRk5EV0pSRGkwUVlaYWtCcER2LytvWWVtVk9jYkd5Mis4R2pHdlpkQmNFeUtUM1hRY29peTIyS2xLb05tODZUQXd0cmgrOVNjSzFCRFZtWjNVa1lrRVYxcFR1SjBtdXh5U29Gam1iQ2NYMWQ2NndxcDVxQytLY0k3d0cvK2MrWmoiLCJtYWMiOiI5YWUzODczYTUyZTc3MTU5YzBkMzA2MzQwYzJlOWNiYmJmYjUzZTE3YTBmN2RiYTA2Y2Q4YjM1MTAxNzFjNDIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkN3ckVIY0xnUDFHMHY3R2N0VzlOOGc9PSIsInZhbHVlIjoia3BoRUduSEhjMkFvYVdOdE1SdTlhMUxZdjhxSk54UlRmNXhoQ0VsM1RHK1loY3NQMkhPbzkyeDNKclphT3ZxM0trWEgzQnFiUk5INjFacEZITjlqay9zUmg3MlBUdjJMT0gwVDZzTXlOWld2UEhKR1JWRmxBSUZBc3pKbEp0elQ4ZFpHRVYxYXZwUkx5akdvRTMvQzdyTzBYOG9ONVFVVjJWcksydGl1Z3RxYWtKREJadFpmSDM0RGRLYVBLQTlrMEVnQmNpQ2ZqaWlWekZhTDRQdHZiNTJrbElhdmcwOE1ZOXRab3d1MzZkK1ZtYXNhZ2QwUEdtVWRheXNMa0hBM21DQTNWVXY4eDBNOFZCVDMzWUN0NU1JYlRHcGJmbkExMVlMdFN1aS9xTTZTZEt3TE1sWjVkRDd2SHQ3YzBZQjFqQjh1M2JSV1RHUEI2UDlWTmdNVElTZ0dURmpLaFBkVXJuRkJhRFByZzZJWm00S0Z3aTc4bHloc0xvMlUwTDBLam1TWkdWbWFrZTlYaUl5YkhkM3I2ajF1OW9oa1JwYXFlVXRjcjZzcElsSFcrYTQvSGxrclQzcWhvT3czbUFTU1ZyQUVtSm9pdVFwcnZIQm1OWXJXTWtnTkY3V3p3aXExNmdUYUNmUUhaUE9sU1NwQ24rTWtXTlNUbmY0T2JGbWkiLCJtYWMiOiJlNzdiNDIwNTkwOGQzZWZlN2U4NWEyOWUzMGM4YTNlYjI4MDE2ZDNlMWZjMzE4MzY3MzQ0ODVlNjg4NjcxYjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1129686954 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lEj5lvT26psATMn590IwbkpytmDaS60kwLEQpsP2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129686954\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-475663188 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:06:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9DZStmQlBOTFhjQWNKeUxHYVYxR1E9PSIsInZhbHVlIjoic29sWUpnQ0ZNWmpIVlpsWGt3MWMyTEN3QW5XSU1XTFlWVlQwV2lQNHBWS3JqbE04OVV2YzlnalVLWUdqTjVUT2E1YVN5bDRDTytGM0R5UEdOZXdBZ01TWGp1SURzVUpkdW5hR1ozN2pGcjh2eEVrclN1eWtONTZUamZhUVNRNlI1Q0VwSTNyZ0VnNGhoTVBTd0hjWmVpQ3NWaTM0NVA2UlFjMXBGcnhpRkEzR1hFZU5RdlFXR01OenBkekVkbDVpYkxweHQ5enFkT1cwOTUzVkg1UHp1RTcyVEFIOUJFa2pla3hBVnJIWnJRZytrWGlZVjN3Mm54TWZQOTMvcnM0eWovVHdTSFV1YkVzZmZENVpUQVhUbU5OamtWTmw3UEl1MlhPaWllbHhiZmdXMWxlbGZIK3V3ZTIweklYZmRRVTBmT1crTXMxQU41Sms3cUo3WFJWSWo1OUJ0ekhwYWhhUyszaFBqaDcvcG9OQ2tUK3BsOHZEL2gzYUVIOHRPSndwUmdlcEZNY3ZwZ0F3dmYvdk9SU3ZoakE4aXN0SUVYWkFFbngvWG9OMEcxWnJ5d0psNGZLNVZOKy9xd1dBZ2dsWGZVSEE5bkFUdEkrUDBWZDFjcENaWEF1VVdISlRnVFdnQndYaU1GY2I3V0dxUzFoNTNRTDhrVnhKU0M1U2ZVRFgiLCJtYWMiOiJhMTk0ZmY4MDgwNTFlYmYxN2JmNTBjYWViNjhiNWU5OGZkNGZjMGNhMjNkNGQwZDc2ZjFmOTk2ZmE2Mzc0MGVhIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRsTUdYeFJrVmd0dUlGVDJYR3kwTWc9PSIsInZhbHVlIjoiM1V1LzRVVGcvQUV5N3lhYThiV1B0d24zQkNrZ01BVmZiZmdrTUEzdC9iTDRLRTFVYktsUnQzSk9SNTR1ZGtDZUxBVVZIandCZkkwd3I1RE0yLzhTcTNaTnoweTZzR3kzZ2hxUlJZMkpYWkh1WjdGSmc4ZWdZOVBDOURYYjJMWUtIQzQwckVBRFpiTk1mMGhIRXNmSFIzL3drMityemNZdVZqOXZ2WTVoYm1PVUw0QmplclZJZEQzN3R4NFZoclpOenFpc2YvL081ZnBoNnVibkovd0w2NWltdGI3UFdlYWQ1c0QwaWlGanlJSXhlUU9mOXdUM1lkbTdnOGM0N0dIYW5iZ1J0SUY5b2VCdldmQ1ZOa0ZxMG9OMHphQVQvYzZYMlBiN0RaZ0pMMXVtR1FqVGE4U3BVRnMwby9TY05kSVRLK1JSNGZCMjlTOVRKK2h0RkNYV1B5N1BuZEdlbW1iODg2c0F2RWJOZkEwZjgrc0IvRVQ0QWpyMUM0T0VEMTlvRGlYVzNBd200MkxBekpEYmZzeHBsOElEaE8wcU4xZmwxUzJGRDJmdmpqWGg5U1pBa1BGd0xJSk00THZtUnl4ZUdQcW1qMURIZTIyZkVESktTdDR4SFdGTW9GbjRQOEw3TGVRbnpyYWduOURMUDBweUFTR2J5NlIwTXp2ZDcwM1giLCJtYWMiOiI5MjkzYjI5M2M2NDE4YjI5Y2IwZDZlNDRjNjk5MThmMGFjYmUyNTNiY2EwNDRiODA4OWQ1NzYwYWE4NjQwYWJlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:06:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9DZStmQlBOTFhjQWNKeUxHYVYxR1E9PSIsInZhbHVlIjoic29sWUpnQ0ZNWmpIVlpsWGt3MWMyTEN3QW5XSU1XTFlWVlQwV2lQNHBWS3JqbE04OVV2YzlnalVLWUdqTjVUT2E1YVN5bDRDTytGM0R5UEdOZXdBZ01TWGp1SURzVUpkdW5hR1ozN2pGcjh2eEVrclN1eWtONTZUamZhUVNRNlI1Q0VwSTNyZ0VnNGhoTVBTd0hjWmVpQ3NWaTM0NVA2UlFjMXBGcnhpRkEzR1hFZU5RdlFXR01OenBkekVkbDVpYkxweHQ5enFkT1cwOTUzVkg1UHp1RTcyVEFIOUJFa2pla3hBVnJIWnJRZytrWGlZVjN3Mm54TWZQOTMvcnM0eWovVHdTSFV1YkVzZmZENVpUQVhUbU5OamtWTmw3UEl1MlhPaWllbHhiZmdXMWxlbGZIK3V3ZTIweklYZmRRVTBmT1crTXMxQU41Sms3cUo3WFJWSWo1OUJ0ekhwYWhhUyszaFBqaDcvcG9OQ2tUK3BsOHZEL2gzYUVIOHRPSndwUmdlcEZNY3ZwZ0F3dmYvdk9SU3ZoakE4aXN0SUVYWkFFbngvWG9OMEcxWnJ5d0psNGZLNVZOKy9xd1dBZ2dsWGZVSEE5bkFUdEkrUDBWZDFjcENaWEF1VVdISlRnVFdnQndYaU1GY2I3V0dxUzFoNTNRTDhrVnhKU0M1U2ZVRFgiLCJtYWMiOiJhMTk0ZmY4MDgwNTFlYmYxN2JmNTBjYWViNjhiNWU5OGZkNGZjMGNhMjNkNGQwZDc2ZjFmOTk2ZmE2Mzc0MGVhIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRsTUdYeFJrVmd0dUlGVDJYR3kwTWc9PSIsInZhbHVlIjoiM1V1LzRVVGcvQUV5N3lhYThiV1B0d24zQkNrZ01BVmZiZmdrTUEzdC9iTDRLRTFVYktsUnQzSk9SNTR1ZGtDZUxBVVZIandCZkkwd3I1RE0yLzhTcTNaTnoweTZzR3kzZ2hxUlJZMkpYWkh1WjdGSmc4ZWdZOVBDOURYYjJMWUtIQzQwckVBRFpiTk1mMGhIRXNmSFIzL3drMityemNZdVZqOXZ2WTVoYm1PVUw0QmplclZJZEQzN3R4NFZoclpOenFpc2YvL081ZnBoNnVibkovd0w2NWltdGI3UFdlYWQ1c0QwaWlGanlJSXhlUU9mOXdUM1lkbTdnOGM0N0dIYW5iZ1J0SUY5b2VCdldmQ1ZOa0ZxMG9OMHphQVQvYzZYMlBiN0RaZ0pMMXVtR1FqVGE4U3BVRnMwby9TY05kSVRLK1JSNGZCMjlTOVRKK2h0RkNYV1B5N1BuZEdlbW1iODg2c0F2RWJOZkEwZjgrc0IvRVQ0QWpyMUM0T0VEMTlvRGlYVzNBd200MkxBekpEYmZzeHBsOElEaE8wcU4xZmwxUzJGRDJmdmpqWGg5U1pBa1BGd0xJSk00THZtUnl4ZUdQcW1qMURIZTIyZkVESktTdDR4SFdGTW9GbjRQOEw3TGVRbnpyYWduOURMUDBweUFTR2J5NlIwTXp2ZDcwM1giLCJtYWMiOiI5MjkzYjI5M2M2NDE4YjI5Y2IwZDZlNDRjNjk5MThmMGFjYmUyNTNiY2EwNDRiODA4OWQ1NzYwYWE4NjQwYWJlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:06:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475663188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-602670526 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">slmYAnAt8VLJRDXcnhQRNnihkqHym8GH8dlU7PGd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost/branch-cash-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602670526\", {\"maxDepth\":0})</script>\n"}}