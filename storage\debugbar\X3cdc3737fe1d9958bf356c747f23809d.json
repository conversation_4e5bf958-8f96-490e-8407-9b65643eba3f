{"__meta": {"id": "X3cdc3737fe1d9958bf356c747f23809d", "datetime": "2025-06-30 16:10:01", "utime": **********.215374, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299800.726444, "end": **********.215392, "duration": 0.48894810676574707, "duration_str": "489ms", "measures": [{"label": "Booting", "start": 1751299800.726444, "relative_start": 0, "end": **********.13406, "relative_end": **********.13406, "duration": 0.40761590003967285, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.13407, "relative_start": 0.4076259136199951, "end": **********.215394, "relative_end": 1.9073486328125e-06, "duration": 0.08132410049438477, "duration_str": "81.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45348336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02537, "accumulated_duration_str": "25.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1697261, "duration": 0.024, "duration_str": "24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.6}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.203521, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.6, "width_percent": 2.365}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.206836, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.965, "width_percent": 3.035}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1397725645 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1397725645\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-85715114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-85715114\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1783555014 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783555014\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419805846 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299796773%7C6%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik44THFhY3pVZElvZFZ2MlVCVEs5WEE9PSIsInZhbHVlIjoiWVkzQkhRMmo5MDZpcTZjT2JwTW1FejlicTBMOTFXRnpScHdLdStVYWlialk2Wkp1YklyaUNGZWhad3hhTFBRR3o5YlpQblIrTkFGdXVML0VLbG1zR0tsMDlHMmgwSldTRzFsdWwvSXhvL1dubC9XdlZrL0VYcXlGcWdCVHZ2aWdaK3lMS25HczRFN0g0dUczUWZLa21FQnRqU0U4UTdDTFFqTjVnaDZKdUc3bjlzeUpncm9JeVNsd2lNc256Rkl2Y2JHdUVCK2tnQnc5VXovWDJHK2VaNStscmY4MGxVVGpzNFJ5TDFlZlZucXB0Vkl4MFhDeXFJR1dyR29EcXJSV3RoK3o0TTd6bERoSUNnWHpYVmVERkhIQVE1OWkzOEQzeU5lTmlxWjFpMWpaTUFaKzdhUTkrUGlaSVhvREZNZ0VreDVubmJIYnZrRkhMeDdEVENSMVhWWjFINXkyRTAyN0J0a2hweExEbGtQM1hKc3V5L2N3TGlkVWZIMUpzU3h5VkZIaWMzU29aMEk1VittSVltWnlEKzZTdEdpMWhlbXAvUXluaDd4Nnp0OXBZN0ZMemJFSXpzM2FCWFh0OEdQTCs4RFpGb2NjRlpFS296NXhrOGtYOFlpNlVkR1hnWmNoKytqNU5aQzdIZldiSjBqNHFHZUNiYmE2UmYxaXR6a2ciLCJtYWMiOiI3MDBiYzczZGE4MzU2ZWZlOGE1ODYzYjA5YTQ3MmZhZmVjNzk0ZTlmNzFkMGFhYmU3MDQyMDk2NDU5ZDAwOTcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjM2OHdXYjJTM3pXYW1VTEhQVFVlUWc9PSIsInZhbHVlIjoiazNSajdia3JnOVNvQWFkbHY5SVhWZzNVdi9EdEdRWGRjNG1CSHRnQ3BUREYxZ0wxTExoaDVFQWdJWWkrL2JrazRuM1ZQYklIMmVyRzRJeVJPbDdUM2xhazNMK1lCZ0xkdUszeThhd3M4TGtWZmhmZE8xZGM0UU1CNG0zWllUN0w2UVhmUEJCZThYYWNlb045U0FiaEtPQzVMT2UvV29sVjBYWHkxcFFXelo3a21TclNmb3k0R2Y5L2cranFJekl0VFh6aWU2QzNUNi9pSC9NQklGcWpIWVRPSDVLZzdlZ2RVcGZleDNYb2NoZlc3ZjVJck9BaWlOSVdMMUhnZHYvbGYxTUh3YW1IakpSSkNlaCt5MFJIQXpmTDRoNnBydkNHeElXT1FGbSt3UGVOaVR5OEt2bkZjU1ZTNW9OaW5ncEhadzExRVpQb0VnT2tIUC9DWEZ3YnNUUC9hRWdNYnQrS0MvQm5LaGdyRlZNQ0dGV0RDNHBBUTJqV1R5ZnF5QVJkc0ZkNkhYbXFzbGpNcWlzbk0ybnR0UmpKNWNteVJHSU5SMUZsenE2Vy8xSkdFamJvZ2h3eFBzUUNkNnVPN2xTOTExRVlNbHZmc1lTMW4ydG9FTEEydDZETUVDdUMwTktseU5uVGd2dHJxekxjRTJYMXJTWjVMeXYrS1ltZXpWeXEiLCJtYWMiOiJjMDQxMzg1NDQyMGQ1YTRhMDYyYzc0ZmY4MzZlNDE4ODZkNGU3ZDhlMGE4ZDhkNzkzMjgwZmEzMzYwZjBhOTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419805846\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1998587999 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998587999\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1969928254 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:10:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJwOG1KNjd0eVlKcWlUQUd3VnYyL1E9PSIsInZhbHVlIjoiQ2pxdzIvN0IwQjdlLzhNUElnNlRQVjhFNG9GMnJwb3pKL0NGNXdWczVXZmhBMXU3bEhHZGp2MHBSN2NCT2x5aXFTWE9uc3dnZHQyU0ZSYkZVdC9PZE5pQkdZOVI0aExLd0crc2JxU2pyVWVzbGorTVNIL0RYQ21tTXZGdDNBOHVpdmFleGdGVFNiTVd2bTN5dGlYNjB5RkY4MENIc2ZQeC8rSFA2amlwT2VIR3dWNlBWbmRDcXJyL3cyVUl4bjB3eDFhVFZkWkVoTGdwM09wZmFiUVN5VHVtMFdWQXo0QVJSZTRFdmVnMWlPSE1lVXRrOCtzeTYwQ2pQN3FJVGxZU0VOSzFrdW1NenJRcm1BN3FFeWtCeHNISXJ6dmRKSGNpMXRkQUx0dDhmNzBTZTFkdUIyWFlCYm41T1hSR01uZFd1bHhENldPb2JRckRwWmZCWXBIRjlZWHpLWEZwd2h0WjNoTTJsalpFSzhpbFhmMWJvYlgxUE9sS1IvNlJ3UkJPV0p0UkZob0xnb2VwOHFGZURCRGhqbVVqMGM2MU1pRzdReDc2QXJ1c1JoTlpBZTloM3hUM05BUkttVGkwd09xOFdISTRDbGh3WXV3aDFwZEFWLzdEbE1meDlvZGRIRlFuV2Vsa3BnNXlaUWxHRFFRblhjdjFTS01MTWNFaUJPaEciLCJtYWMiOiI5Y2U0ZmU0MzI5YmE5NGVlOGNmOThjMTk3ZTlkMmVlNzA3NTNhN2JiOWU4ZGFhMTE5ZjUyYjgwNjU2YjgyMzEzIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFiTjQvc0dXU3o0MmJ1QVhTOVlkUkE9PSIsInZhbHVlIjoiQk5MbnRydlQrc0RqcGE0MnROT0ZST2lvck5pR2M1WFdHUHVXaUp4V05XU2pTOVlRTlNJN21JK0ZxRS90MDZqYndybmw2SzRQTEtnTXp1L05rRk1TNEZxSFZGYVNscDIzS2xrRmM0aURMYkRST3BjVmZLM1R1UWIwVXdNVFZCSjU0N3RiYlB5S29RSlVXNVMrMkhRQTdqbkVPYldtUnNlSWZ2dGVTajVRWGVCUGhJNENrOFdwenRzUGVNa2E4M3BaZHl4bDQyaDQ0VzV0Y3JkQlJtSVJaVXNJS2pDZ042MmpXdGtFbDJidklOSDdXZ3g5WU5rL1dNMzBMenRkZTdtRmxXYmtkenZhS0ZwaG5pL2FNLzkzRXpCQzZxMHN6VkUrVlNZT2NBWWxrOHc2YnV6bWdiaWNxU0FPYUFuRUVqbldJTGNGQlpYb2k3YWpNeW1DL21KVU10bnNyZ0hjZCtmbVV4RTliZFlYalh0VmFpZmwyS1RZMUtiV1NtTGUzc3YwYVVvVXVHaUdpV1F4elkybEZ4dmJIa29YeGRwUS9WY3VxMTBzVkx6SEFRalpoZUwxRUNlR3hGZ3Rab0hzaEd5Y2p0M2JocjZqdGIrZUhncFprb2dVbi9Qc2RacVYwYXc1RXQ2K2lKV2dDREFCYTBCS1VSYzhpWW1sK0taVWdUNmsiLCJtYWMiOiI2YTlmMzRjM2U5NTNmYTNlZTcwNjA1MjY1MzZmYmE3ZDNiNjM4MTc1N2IwZDg5MTk1MTJkMjdlY2VmY2FjZWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:10:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJwOG1KNjd0eVlKcWlUQUd3VnYyL1E9PSIsInZhbHVlIjoiQ2pxdzIvN0IwQjdlLzhNUElnNlRQVjhFNG9GMnJwb3pKL0NGNXdWczVXZmhBMXU3bEhHZGp2MHBSN2NCT2x5aXFTWE9uc3dnZHQyU0ZSYkZVdC9PZE5pQkdZOVI0aExLd0crc2JxU2pyVWVzbGorTVNIL0RYQ21tTXZGdDNBOHVpdmFleGdGVFNiTVd2bTN5dGlYNjB5RkY4MENIc2ZQeC8rSFA2amlwT2VIR3dWNlBWbmRDcXJyL3cyVUl4bjB3eDFhVFZkWkVoTGdwM09wZmFiUVN5VHVtMFdWQXo0QVJSZTRFdmVnMWlPSE1lVXRrOCtzeTYwQ2pQN3FJVGxZU0VOSzFrdW1NenJRcm1BN3FFeWtCeHNISXJ6dmRKSGNpMXRkQUx0dDhmNzBTZTFkdUIyWFlCYm41T1hSR01uZFd1bHhENldPb2JRckRwWmZCWXBIRjlZWHpLWEZwd2h0WjNoTTJsalpFSzhpbFhmMWJvYlgxUE9sS1IvNlJ3UkJPV0p0UkZob0xnb2VwOHFGZURCRGhqbVVqMGM2MU1pRzdReDc2QXJ1c1JoTlpBZTloM3hUM05BUkttVGkwd09xOFdISTRDbGh3WXV3aDFwZEFWLzdEbE1meDlvZGRIRlFuV2Vsa3BnNXlaUWxHRFFRblhjdjFTS01MTWNFaUJPaEciLCJtYWMiOiI5Y2U0ZmU0MzI5YmE5NGVlOGNmOThjMTk3ZTlkMmVlNzA3NTNhN2JiOWU4ZGFhMTE5ZjUyYjgwNjU2YjgyMzEzIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFiTjQvc0dXU3o0MmJ1QVhTOVlkUkE9PSIsInZhbHVlIjoiQk5MbnRydlQrc0RqcGE0MnROT0ZST2lvck5pR2M1WFdHUHVXaUp4V05XU2pTOVlRTlNJN21JK0ZxRS90MDZqYndybmw2SzRQTEtnTXp1L05rRk1TNEZxSFZGYVNscDIzS2xrRmM0aURMYkRST3BjVmZLM1R1UWIwVXdNVFZCSjU0N3RiYlB5S29RSlVXNVMrMkhRQTdqbkVPYldtUnNlSWZ2dGVTajVRWGVCUGhJNENrOFdwenRzUGVNa2E4M3BaZHl4bDQyaDQ0VzV0Y3JkQlJtSVJaVXNJS2pDZ042MmpXdGtFbDJidklOSDdXZ3g5WU5rL1dNMzBMenRkZTdtRmxXYmtkenZhS0ZwaG5pL2FNLzkzRXpCQzZxMHN6VkUrVlNZT2NBWWxrOHc2YnV6bWdiaWNxU0FPYUFuRUVqbldJTGNGQlpYb2k3YWpNeW1DL21KVU10bnNyZ0hjZCtmbVV4RTliZFlYalh0VmFpZmwyS1RZMUtiV1NtTGUzc3YwYVVvVXVHaUdpV1F4elkybEZ4dmJIa29YeGRwUS9WY3VxMTBzVkx6SEFRalpoZUwxRUNlR3hGZ3Rab0hzaEd5Y2p0M2JocjZqdGIrZUhncFprb2dVbi9Qc2RacVYwYXc1RXQ2K2lKV2dDREFCYTBCS1VSYzhpWW1sK0taVWdUNmsiLCJtYWMiOiI2YTlmMzRjM2U5NTNmYTNlZTcwNjA1MjY1MzZmYmE3ZDNiNjM4MTc1N2IwZDg5MTk1MTJkMjdlY2VmY2FjZWZkIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:10:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969928254\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1615313750 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615313750\", {\"maxDepth\":0})</script>\n"}}