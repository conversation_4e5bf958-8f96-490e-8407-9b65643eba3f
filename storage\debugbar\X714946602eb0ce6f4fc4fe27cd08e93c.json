{"__meta": {"id": "X714946602eb0ce6f4fc4fe27cd08e93c", "datetime": "2025-06-30 18:11:30", "utime": **********.38333, "method": "GET", "uri": "/add-to-cart/2141/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751307089.919479, "end": **********.383346, "duration": 0.4638671875, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1751307089.919479, "relative_start": 0, "end": **********.297574, "relative_end": **********.297574, "duration": 0.3780951499938965, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297591, "relative_start": 0.3781120777130127, "end": **********.383348, "relative_end": 1.9073486328125e-06, "duration": 0.08575701713562012, "duration_str": "85.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48667616, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1344\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1344-1568</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0059299999999999995, "accumulated_duration_str": "5.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.33578, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.981}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3454862, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.981, "width_percent": 7.757}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3589978, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 34.739, "width_percent": 9.781}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.361428, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.519, "width_percent": 5.565}, {"sql": "select * from `product_services` where `product_services`.`id` = '2141' limit 1", "type": "query", "params": [], "bindings": ["2141"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1348}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.366287, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1348", "source": "app/Http/Controllers/ProductServiceController.php:1348", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1348", "ajax": false, "filename": "ProductServiceController.php", "line": "1348"}, "connection": "kdmkjkqknb", "start_percent": 50.084, "width_percent": 5.396}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2141 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2141", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1352}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.36986, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.481, "width_percent": 38.28}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1421}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.373803, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 93.761, "width_percent": 6.239}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1608870784 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608870784\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.365334, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2141 => array:9 [\n    \"name\" => \"STC calling card 50\"\n    \"quantity\" => 17\n    \"price\" => \"57.50\"\n    \"id\" => \"2141\"\n    \"tax\" => 0\n    \"subtotal\" => 977.5\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2141/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-97182041 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97182041\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2122017220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2122017220\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1290071014 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNUdjBFTE54TFllZVZVOEJJVEhNSGc9PSIsInZhbHVlIjoiL2JDTmxDUjQxcTh0TDhiVEVCNy8vYTlGZlJIQ0RBeFNTNlpRdW9RaXcwaHBhT2lUYU5XV01id1N1YVRuZGxuRk9obkJlQmdiRUF6RnFacHdIZ3BqNGQzQWNsNjlLZXBJVlQ4bnFhZFhSeVUyZmpyYVJYWHZhdE5pc0IrOTltWGJwbjF3Q0JISkZwNGJDbnBiVVRFT1JQam8yOCtaeVQvcmRWYUZ2bEVyKzRxU2dIQjBHcXhCenF0OC8wWTg2Nng1YkV5MkxDNlBDU3h5Rkx3cFFrL1lmN1paWGVmVHdGSTFsalhlLzZ3eFp4bXE5dk1FeHRtMTJ6bDFQeGh1TGo3ekxnT1I5b3Zlb082bVFwd0tPZkJBRjBITW40R2hGYVJocnpmQTZReVVFRVF3SHdrcFhEbmttUFU3OGdLTjhZczB0VVc4UVRXNkthVHlFc1VVS3I4N3h4Vmw1NTRVUW8xLzNlbVVoYnllTUR3ZjRlYUkyVktqU29jNHhSUmtzeERpTkpMRzQwcUhRZnc1aktudFl5ZkdWdFE1NWdPeWNuclkzbUFHMkp3dW1UelE1eTJXQWFWWW03NWJoc2RsN0RzREFoc2tqazRtRzI5VlFrNDJUNjh3bkFZc29hT2Z4T1ZJL25oNzF0U3o5RGpoUDlnUHRhZU5XblJyTXZzNnFsT0QiLCJtYWMiOiI0YTQ5MTU2ZmYwMjc4NGNjNGQ5NzBmYjY1Y2Y4NDZjNTFiOTAzNDY4NWQyZjQ1OTg2ZmQ1NjFhNDBhODA2NDQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii83aEw3NUNoMTJHOEcxYlVaditFQ1E9PSIsInZhbHVlIjoiWTdYUkdlZkg1QVVpNzRQWHZHeDh4UWNvcGVqMEF5Tm90ZnhFZ1U4a0xreVcyM0t1dkFZc255SE5mNm0yVVhSZGxxd0F4RGRiZ3dHVUR5QVRvaUlCN1gyNURwSlQvSURlSC9CdDZzcC8vbFdBdHZnbTFwaTB0RjFZSmRHRmJZOHdjanR2b2hCSEc4VDlkLzNZd1VwSUdISHFnejhvZzZDb0JlWGM2RXYyZzhrTUxFL2hLeEg4UnpTQVFuaGxUZnFVSUoxcDVNeHE2N3F2UjBnNFpNWFpYUXhRdVBjRy9Qanowci93YjUvbktqaTN3c2o5STRvOXhHWlBnblVPVE9IejlRM0xxNEpNYVJaY3VrYVNqWDkxMWdiZEZaS3pPV011RktZVHdDTmh4ZmZKNkVzTU44cCt0Tno2dEw5M2NmYjhBRXFoMEZRRHFqdE8xcnh0QlJzTW5sL0dtN3lzRllWdG1kOVJReFhoUjNjL3ZSRytmdXQrQ28xN1hWR2psL0ZFNm1teXo1VHZRTjNWUVNqdWtvNHROK2lMQjRVM2VXY1FKZHcrbEhpY0l2b0tBV2U2ejNWMkJxN3J3UjFreXY3cHliV1pCN3A0NnB3M0JHcnZzbnNVcjlkajdzRGI1SDZYUFN4LzdIVTRiZUFzQTZNSVAvS29zMDl5aDNsdmVkT2ciLCJtYWMiOiIyYzNmNjgyNmEwN2YyMTJkMjRmNzA0OTFmODg2NmI1MWVlOTMzN2FhZTVlMzkxYjBiMDBhNWNlZWM1ODU0ZDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290071014\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-500950394 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500950394\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1245880892 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:11:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZFYmwxajVUZVRsSDVGODhHSXB0NXc9PSIsInZhbHVlIjoiRmdtSTdVOC85N1BCRmZsRis0Q2d4K2VTQ1VqZXZNdndlUGIwMy9QVk14aTh2T2h4RWUzeklNM0k3d29iL1ZrY0Z2RVNoamxJemxWNnRjK1Rxc0U0dTRoYks3bU40YzVaRWZuZHk1Vm5tbEdOdWo1eTgxVHhBd3JwdUo1MUhQMDBaMDR0UC80VG1VbG1rdmxpZ1VXejliMmlkN0E5bE5XOHFWUWYwck5qVDFNUHhYREloMWwvcXh2RWZNUXlJVlVZbVdkTzNURVM4NXZQU3pHUWhYYU1UVFNlMnpBdjhMTVJZWjlHS1FjR1dQbmxWN0pHTW1CZzY3UC9CNEgrdnNJUFc0WWpKTUFzUldOWkJlQVpTSkpubFczSjAvcnZqNHJHcGxORFRtV0M2UlF4dTJ5UzlTSW9VTFZXM0VXQ2JXUzNvWWtna2tiY0orTkZXMU1wOFVSM0hwVFdneXFEa1M3S2I3TFg1c1B2dWRDTWdyQzQ0MkJoU3VvdWxKVHZSZ3ZsRmg4MGtLUFpjNksvRjY0VFI5QUp0NUNJcVRuRzROaUJ6MS9OSS9hYmtvYXZVRW1Qc2Fyd3VCZ1ZYQkowZkpZaVFmMnJwSUdWZVFlZjJOdm9USEFnV1Q3NDRXMzMwRDM2bWdQbkdVUWxrYnI2WjZUaC93ZHVUdkxJRzZIR04vZlQiLCJtYWMiOiI4Mzc0ZjFiYTVhOTkwYWM2NWM1OTM5MjE1YjZhMGFmNWUzODc4OTgxNDEzMWFkZjE0NWY5MjlhYjJhYWFiMzJlIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBmWmhJb245d0ZRald5VU1FQmNhL1E9PSIsInZhbHVlIjoiRWRsVWkvTlJTREdQWklyZG1HVWtlWDUwc2o0M0VYNG9wUCtHdlQ5Q1ZkV1pVdnVSa2hVYWw5cFdXWFIydUlyNDRmVGphc1NYT21LWUZCdEtWTmloSlZCZmprRnV2bnc5aWdBdk1JM2lLUUVxUjg2ZFhra0xFbXpnN1d0TzRmZHN2cTR1aHRQQ1FxRlRXQjZrajJWTS9TTEpuK3RnNGNKaFRJY3BRZTlEdWtWdDAyaHl3dW9YbmpwUU1WVWcwQXZibWQ5SUszeTZpVlEwZDdSZHp0UUR4cHdTUGhQQTV5eElJQTNubVdNcE50cGE4Qno5SkpXdzdPVWRVdVpNYzJSTzdsd0g4UVZlcjFpTG03eURNdzJaak96S2dIM1pYdmVBOVF0ZUJXWXhNVGFMdyt2Y1NVTDYxMFR5bis4K29wbGRjSXRpQUVuNm1vMXRRMWM2YUlBYnlKQnVxKzRTZ21xS212MEJnVXRPVFlFTlFZbUw4eVNlc3lPOHlRalZBVkQ5Ym1RTmJrSEtqQjhnMVVPQzBmRHQxdTFselF3WDJxQU9GbklSc1NjY1k1T09MTENSS2hZMDl2akxkNGZ4czNwWFNhN1RLWHVJSGtPQlZyYkNTVGxIbkU5ZWdwbjhMQ3BwV3dXRnIvenA5TU1VUXl4b3hrK2Z3UjhUYU13ZEY2TmQiLCJtYWMiOiIwYmE0NjQ2MmUwOWU3OGE3OGMzMGYxNDk2YzkxZGFkMTgxZGU1ZTFjZDBmMDI4ZDNlMTkzMDUzOTNmZTU1MGFmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:11:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZFYmwxajVUZVRsSDVGODhHSXB0NXc9PSIsInZhbHVlIjoiRmdtSTdVOC85N1BCRmZsRis0Q2d4K2VTQ1VqZXZNdndlUGIwMy9QVk14aTh2T2h4RWUzeklNM0k3d29iL1ZrY0Z2RVNoamxJemxWNnRjK1Rxc0U0dTRoYks3bU40YzVaRWZuZHk1Vm5tbEdOdWo1eTgxVHhBd3JwdUo1MUhQMDBaMDR0UC80VG1VbG1rdmxpZ1VXejliMmlkN0E5bE5XOHFWUWYwck5qVDFNUHhYREloMWwvcXh2RWZNUXlJVlVZbVdkTzNURVM4NXZQU3pHUWhYYU1UVFNlMnpBdjhMTVJZWjlHS1FjR1dQbmxWN0pHTW1CZzY3UC9CNEgrdnNJUFc0WWpKTUFzUldOWkJlQVpTSkpubFczSjAvcnZqNHJHcGxORFRtV0M2UlF4dTJ5UzlTSW9VTFZXM0VXQ2JXUzNvWWtna2tiY0orTkZXMU1wOFVSM0hwVFdneXFEa1M3S2I3TFg1c1B2dWRDTWdyQzQ0MkJoU3VvdWxKVHZSZ3ZsRmg4MGtLUFpjNksvRjY0VFI5QUp0NUNJcVRuRzROaUJ6MS9OSS9hYmtvYXZVRW1Qc2Fyd3VCZ1ZYQkowZkpZaVFmMnJwSUdWZVFlZjJOdm9USEFnV1Q3NDRXMzMwRDM2bWdQbkdVUWxrYnI2WjZUaC93ZHVUdkxJRzZIR04vZlQiLCJtYWMiOiI4Mzc0ZjFiYTVhOTkwYWM2NWM1OTM5MjE1YjZhMGFmNWUzODc4OTgxNDEzMWFkZjE0NWY5MjlhYjJhYWFiMzJlIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBmWmhJb245d0ZRald5VU1FQmNhL1E9PSIsInZhbHVlIjoiRWRsVWkvTlJTREdQWklyZG1HVWtlWDUwc2o0M0VYNG9wUCtHdlQ5Q1ZkV1pVdnVSa2hVYWw5cFdXWFIydUlyNDRmVGphc1NYT21LWUZCdEtWTmloSlZCZmprRnV2bnc5aWdBdk1JM2lLUUVxUjg2ZFhra0xFbXpnN1d0TzRmZHN2cTR1aHRQQ1FxRlRXQjZrajJWTS9TTEpuK3RnNGNKaFRJY3BRZTlEdWtWdDAyaHl3dW9YbmpwUU1WVWcwQXZibWQ5SUszeTZpVlEwZDdSZHp0UUR4cHdTUGhQQTV5eElJQTNubVdNcE50cGE4Qno5SkpXdzdPVWRVdVpNYzJSTzdsd0g4UVZlcjFpTG03eURNdzJaak96S2dIM1pYdmVBOVF0ZUJXWXhNVGFMdyt2Y1NVTDYxMFR5bis4K29wbGRjSXRpQUVuNm1vMXRRMWM2YUlBYnlKQnVxKzRTZ21xS212MEJnVXRPVFlFTlFZbUw4eVNlc3lPOHlRalZBVkQ5Ym1RTmJrSEtqQjhnMVVPQzBmRHQxdTFselF3WDJxQU9GbklSc1NjY1k1T09MTENSS2hZMDl2akxkNGZ4czNwWFNhN1RLWHVJSGtPQlZyYkNTVGxIbkU5ZWdwbjhMQ3BwV3dXRnIvenA5TU1VUXl4b3hrK2Z3UjhUYU13ZEY2TmQiLCJtYWMiOiIwYmE0NjQ2MmUwOWU3OGE3OGMzMGYxNDk2YzkxZGFkMTgxZGU1ZTFjZDBmMDI4ZDNlMTkzMDUzOTNmZTU1MGFmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:11:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245880892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1762108928 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2141</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">STC calling card 50</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>17</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57.50</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2141</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>977.5</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762108928\", {\"maxDepth\":0})</script>\n"}}