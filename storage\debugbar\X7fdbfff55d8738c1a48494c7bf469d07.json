{"__meta": {"id": "X7fdbfff55d8738c1a48494c7bf469d07", "datetime": "2025-06-30 18:48:57", "utime": **********.356361, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751309336.905705, "end": **********.356378, "duration": 0.45067310333251953, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1751309336.905705, "relative_start": 0, "end": **********.275031, "relative_end": **********.275031, "duration": 0.369326114654541, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.275039, "relative_start": 0.36933398246765137, "end": **********.35638, "relative_end": 1.9073486328125e-06, "duration": 0.08134102821350098, "duration_str": "81.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48184544, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0068200000000000005, "accumulated_duration_str": "6.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.310816, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 27.713}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3224769, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 27.713, "width_percent": 6.158}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.336532, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.871, "width_percent": 7.185}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3383908, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 41.056, "width_percent": 6.012}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3431282, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 47.067, "width_percent": 35.044}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.347751, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.111, "width_percent": 17.889}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-533491830 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533491830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342063, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-377074740 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-377074740\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1526729827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526729827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-806599214 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-806599214\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-560304471 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=xwimqe%7C1751308716671%7C4%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx3YWhDREx4VmlXT2tVaTdMVENNMnc9PSIsInZhbHVlIjoiUVpyb1pId3ZpWTJXSzduY3lpRk5aTGU1TDV4cEpkWW9rbmQwQ0E1TG5Eb2ZKWUtwWGtVU3M0UGNVWktpNGhPRFBaZFhZY0xsZTEzU2dVYUZUOXI2YXh0RmJJOHB2TXQ5MkV6aU1ZMnhEWTFmMjMxemtocEEvZ0xwRDdMNVdaR0FSOXMvYWU4bjZZQ1J0VU11WCtWRFNCV2kvMzl5cjl0S0k2QnVwV0grcENNclNtMFJRSlNXSzBYdmQ5Yml5Q0dGNFp0WElLTnVuNHJDL0tIc1M4VldUMVNQcEtRaDFxbjFhY0ZWVE1hU0VhUFFFeEZlZEhIU3JCTk81UmFxaTdLTXBqK1NsOG5ndGc2a1Y1Sllsd0N3SnBTQXN6WDFGODArZENpak9qMVlUdnl6NkZDLzJEWWlPYk1FQ0dGNEhaWUovdG9uWldjWFE2R1VxYWJUT0hkRjZhYU9YbEVEZjdCM3hPblpkK01wanpKOW12VWRVK3QwbXB1S0JPczZhTUdTVWFaaVVpOFpqNzU5Z1g0eEYvZFZyRTlveDVER1JGVkNKK1g4MDZMaUxpdG55NEpTVVpwOHg5VjRmcm5ua3EzUTVCeUVXSXo0Sy9DM0tFMU1NVHFicHorcUo2c2IzTTkrZGtMN2lYOEZNWkJsVC9jWVNYeFBRWXdzZkVFSXlUZFEiLCJtYWMiOiI2YmU2YjRiYjMzMDU5MWE3OGUyZDYyY2RhNTdmMzdiNzQzMjBiNmZiNTg4MmQyMzQzYWRjYzNhMmE4MWY5MGEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImR1eXNjaThSSVp2ZXBvUjZaZ1J2S3c9PSIsInZhbHVlIjoiOEJodjd0VkV2VWJrOWVJdTVPOXA0Tnl2NU41M0JuN1JPZ0pKb0JBSUk2eWtPZ0lGdXJIdy9ZQVNLeEZMZFpRTVdoZHNIbzdwcDhwd1FrdmdUNEE5dzYwM0QrNzJqNDF4UFpaZGN4ZDIxTlNTaEJ6SmhLRVNwRmpsbE1vT29TNkhMNjc3MDBVMkxmS05QWWNjRXhwTlJYSEk3WUJBd2lpbm5IKzJPaEdyUlZjYWZnT2J0QVVMR0ZaZTlkWldBY0o0UzdVSW9raUVPNVphZlNmK2o3SGVsOWpSbHM0bkozVC9nQ1pnZjZDbTE4a3dneXhSRXZCNEQxU3R6RGpvS1NqWmZtVlIwWHIvQ0kwWGR0VGlJeDdzakF3QmN4NEZ6NXR0OWFGRVNTYUdlRElWT1I1djNTaU1SblJrdjUwMGhJRW81YTdOa3dRRnVqVnB3SFZrVmo3UjQrZ0FzRDR6ZVFmdGJHZk4raEZOdWlqQUQ1aE9tc1l6dCtjUU15UHZ6Ti85cDBDMWt1NUVxSmFEU2lIR1VOdG9vMGQwV3Z0a3FYSUVud1pwb0dtYnFVVjV0U1Z2TVhpenh4RytHMTcwTmdoOElxZjUzaXBaSXRWcy92Z0xLUElQOG9sZHNMdnVaL1JMOHN6eVpUZ3lzc3A4ZTRuRHBtbHB2Q2NKaTVoTUtMS1UiLCJtYWMiOiIzMTJjMzhkOTc1OTI3ODI1NGZjMTNlNmU0YjFjMGZiNjU4NzBjZGMzNTNkZTk2NWI2M2JhYWQxZDhjZmYxZTRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560304471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-924919565 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924919565\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2024760659 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:48:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBjbzA0Y3RtUVVqeXk4bHEva3hUS2c9PSIsInZhbHVlIjoiOUZsMGdBNUdDMS9TUDQxQVN4QWxWcDlzdUVkNkRsd1lRRTJSeHZmWDhwd3Q0N3lPdE9TMVNEaXNjT2Mxam94SGd5WHprdkZxbk41Zi9TeWVCaUNHZ3ZDQzFGblUwUHNqVnIydFFVblAxYys5ejQ0cE5GRTRFOGJ4TW5pN0xDa0grekovbm9Kd215a3EzQ1grTXRMY2JwalhOTndDWG9zK2ZZMXlkTlp3Tk5OUUtYQUpEWitzeVdKU2hGbXEybGRSemRubnNONjZ6ZjBLUW1rOWF3NVlwVHd4SHJ2WDliYTFKZ1E0cERRK1MrUUpjcjUyMVlETTN3T3d1b0p2YWdNdXV0QlQ1blBwM08zeWtSY3RTU3VvMmFldmp1QkFxN0FLZlVFTDREQ21Za2ROWEZzY2h6QXA0Sm1CeTZwYUE5Y0hpdmNmRVJKKzBFbmlxN0ZuaTZoWlROcmtsQzFZWFR4SmRGcEhTMmo3d0lLdnhTc0tMMDhyc3hZOEhMT2ZnRnpFVmZJaTgveERjWUNRNFZ4aVhJeWpydmYyTlhtd1M5YXpCMmpPYVNNZUh5aFY4am1KZDYrVHhWbEJ2RlMxcFd2VStkbDIySXdVekppQXBSS1I0aE05Z2xLYWptQktRaHlYWmZhSUsyOUx0WUczZWt4WElqcXJKbmtuaFN0dWVUbXciLCJtYWMiOiJlYjJiM2UwZTg5M2ZkNTY0MGY2NmVkMzcyYWVhMDllZTI1YjhhZmY1OTQ2OWJhYTA1MWEwNjE1OTI0NjNiMDk5IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:48:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkYxelh2NVNYNWpyMU4rR1pINXFDWHc9PSIsInZhbHVlIjoicE5hT0dtVjBSbFJBbEErdHRBTktGNVJ6SjZvRUEveWo0Z1Q0YVYrNUpCSHhJV1ZEMndlT01rdjUwNENQcE5IbFh4N1ZSSHZGdkpNNHdjN0hSK3paQmo2NzE0S3oyNHpWZWNNSTBhZERJTDgzR2F1cUFDb2cweld0c1pyN2x2S1VRbE1GaFdxMksrTWhyNU5nYTU5anIvZkRlUitKSmpYeWxXK2hUekpGTHovTjk0dEZ1by9jN1ZRUkVKSm81ZXdpaXlXajFrNVUvTDZ4czhrczdaQzF6VkFqYlVZYmhsdjRkRDd3TFdtY3kveUFiTGlDTWlOZkNvelViZ3JyZHZpRytlVGIyWURYQVd2QVJvS09hWjlPN1JjTnIvcll5UjhMcVlyelhhZW84bFc1REZxdFN3NEZSa0xvMVFON1dJSVREWVpabEFKZ0xpVEpsUThSN2hZL2thS3Y2ZllLUzd2SXNJRG5GVTRUS0lkQ1EzU1RMbmZiSS8rOWdNNFNTanhpeWpyMS8vNkRqSUFCQXRZMTRodzBHakNTeC9vTkVjaUU1U09ac2ZDaUw0U0dwVitybjVPL0g2RE1uVGRsWG1qRzNVaktyZXlUM1J3dnpod3A1UUtSQnRJQnR0a0pLVnp4Z09TcjdYYjU5dU1SRW96UGJtajRHaG11bkpONGdCNDkiLCJtYWMiOiI3NTYxMDE3NWRiMjVkZDgxNTlhNjg4YjA5YTQxMTYyOGNkZDU2ZWU4YmU5OTRlZDRiNjgzMmVlZGUzMmJiZDk1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:48:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBjbzA0Y3RtUVVqeXk4bHEva3hUS2c9PSIsInZhbHVlIjoiOUZsMGdBNUdDMS9TUDQxQVN4QWxWcDlzdUVkNkRsd1lRRTJSeHZmWDhwd3Q0N3lPdE9TMVNEaXNjT2Mxam94SGd5WHprdkZxbk41Zi9TeWVCaUNHZ3ZDQzFGblUwUHNqVnIydFFVblAxYys5ejQ0cE5GRTRFOGJ4TW5pN0xDa0grekovbm9Kd215a3EzQ1grTXRMY2JwalhOTndDWG9zK2ZZMXlkTlp3Tk5OUUtYQUpEWitzeVdKU2hGbXEybGRSemRubnNONjZ6ZjBLUW1rOWF3NVlwVHd4SHJ2WDliYTFKZ1E0cERRK1MrUUpjcjUyMVlETTN3T3d1b0p2YWdNdXV0QlQ1blBwM08zeWtSY3RTU3VvMmFldmp1QkFxN0FLZlVFTDREQ21Za2ROWEZzY2h6QXA0Sm1CeTZwYUE5Y0hpdmNmRVJKKzBFbmlxN0ZuaTZoWlROcmtsQzFZWFR4SmRGcEhTMmo3d0lLdnhTc0tMMDhyc3hZOEhMT2ZnRnpFVmZJaTgveERjWUNRNFZ4aVhJeWpydmYyTlhtd1M5YXpCMmpPYVNNZUh5aFY4am1KZDYrVHhWbEJ2RlMxcFd2VStkbDIySXdVekppQXBSS1I0aE05Z2xLYWptQktRaHlYWmZhSUsyOUx0WUczZWt4WElqcXJKbmtuaFN0dWVUbXciLCJtYWMiOiJlYjJiM2UwZTg5M2ZkNTY0MGY2NmVkMzcyYWVhMDllZTI1YjhhZmY1OTQ2OWJhYTA1MWEwNjE1OTI0NjNiMDk5IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:48:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkYxelh2NVNYNWpyMU4rR1pINXFDWHc9PSIsInZhbHVlIjoicE5hT0dtVjBSbFJBbEErdHRBTktGNVJ6SjZvRUEveWo0Z1Q0YVYrNUpCSHhJV1ZEMndlT01rdjUwNENQcE5IbFh4N1ZSSHZGdkpNNHdjN0hSK3paQmo2NzE0S3oyNHpWZWNNSTBhZERJTDgzR2F1cUFDb2cweld0c1pyN2x2S1VRbE1GaFdxMksrTWhyNU5nYTU5anIvZkRlUitKSmpYeWxXK2hUekpGTHovTjk0dEZ1by9jN1ZRUkVKSm81ZXdpaXlXajFrNVUvTDZ4czhrczdaQzF6VkFqYlVZYmhsdjRkRDd3TFdtY3kveUFiTGlDTWlOZkNvelViZ3JyZHZpRytlVGIyWURYQVd2QVJvS09hWjlPN1JjTnIvcll5UjhMcVlyelhhZW84bFc1REZxdFN3NEZSa0xvMVFON1dJSVREWVpabEFKZ0xpVEpsUThSN2hZL2thS3Y2ZllLUzd2SXNJRG5GVTRUS0lkQ1EzU1RMbmZiSS8rOWdNNFNTanhpeWpyMS8vNkRqSUFCQXRZMTRodzBHakNTeC9vTkVjaUU1U09ac2ZDaUw0U0dwVitybjVPL0g2RE1uVGRsWG1qRzNVaktyZXlUM1J3dnpod3A1UUtSQnRJQnR0a0pLVnp4Z09TcjdYYjU5dU1SRW96UGJtajRHaG11bkpONGdCNDkiLCJtYWMiOiI3NTYxMDE3NWRiMjVkZDgxNTlhNjg4YjA5YTQxMTYyOGNkZDU2ZWU4YmU5OTRlZDRiNjgzMmVlZGUzMmJiZDk1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:48:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024760659\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-174178549 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174178549\", {\"maxDepth\":0})</script>\n"}}