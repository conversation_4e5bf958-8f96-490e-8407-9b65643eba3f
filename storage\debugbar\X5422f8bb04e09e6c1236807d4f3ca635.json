{"__meta": {"id": "X5422f8bb04e09e6c1236807d4f3ca635", "datetime": "2025-06-30 16:05:05", "utime": **********.301338, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751299504.853864, "end": **********.301353, "duration": 0.44748902320861816, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1751299504.853864, "relative_start": 0, "end": **********.22825, "relative_end": **********.22825, "duration": 0.3743860721588135, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.228258, "relative_start": 0.37439393997192383, "end": **********.301354, "relative_end": 9.5367431640625e-07, "duration": 0.07309603691101074, "duration_str": "73.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137864, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00282, "accumulated_duration_str": "2.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.277003, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.957}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.289695, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.957, "width_percent": 14.894}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.292967, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 80.851, "width_percent": 19.149}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  1877 => array:9 [\n    \"name\" => \"cleaner Mop\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"1877\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1380411195 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380411195\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1xim04k%7C2%7Cfx7%7C0%7C2007; _clsk=16h7wx3%7C1751299421782%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRYRGdtNjhSYnlTZXYxT3p4QitsaGc9PSIsInZhbHVlIjoibThRdjh6NjB3VWVwMjBjWVdKMElBOTRxbkhqdTd5ZVlvNURXc0F3emZleEZ0LzBxb1huZWtDd3BRdE0rUVJweHlXZHFicTR5aXNWeitvTSs3Y0ljSkVoaHk2NzJ0dzBqbVF1Q2hEbG0vZzBpQUdMRFhMZE1IeEFXcmhuZUVvK2xKWXZnYXVkWVhwL3FTYnZLOGJpWm1QVERMdjI2SVhaakpESnZGT0xQc3BlNUtCZ3U5Wi9aT3BZdy9iOEdHQ2l1Y0NFdTc4cUFCa2pUdEk0cVhXcUI3VEIyWXE3MzhSclRFQ0dYaHBoOHFuVkE2Ky84TExaVythdlhMTHkrbWZYdXpZc1RSQVhucitYaENHaFF3a3NWWkE4MjdidEkwWUVNaGk2cVM4RXN6S0pyWTlQSFl5OWZLclEvb3UraEdvOWViNXBjcEI5MWRWSGVuc2FzYUxYVmZvaTM0U1l4cnFteVZrc1FROE1CZG5SWDVhQnRPV055bkg5ZEdiVHNkd01KSGZhV2Q5WjZyV3pJeFE0R05YeitDcVdEbjNRc0Q1SVhrSXlwa1pwNm9vS004bVZNSlQrN2tHOU5zUWpCWTA4Tnl4TVcwd29yMmM5ay9lZGd5RzlsQkgvYXhwalFMSm5yY1lQQ3lucFlWeDg4cTk4YlloRWZ2ZE83UTByaWQ4UWgiLCJtYWMiOiIzNzY0NTczNzY0ZDJiMTdjZmVkMGFjNzU1NDkxOTQwNjFhNzRlMzRjM2U0NGZmYTA0ZWM4OGNjNGM4NzVhMzIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlYxZGFRcWlRK05qUlY1SlpRSlJaQUE9PSIsInZhbHVlIjoibGhTY3RLZFNZeUJCYlg4UVVmWXYxUlA3UndtY0F5dHJzNi9JenEwMnd1NEJPR2RHeTVEbjJwVkpkVlA0cVUrM2hzdDI2eG5GUEZvUDZvanYydGxZVTZLK0RxR3Fid1N2dDhpd3FvRW8xZ0dvVkZDVFBWdW93UnRYRjJMR3ZxQi96c3czZjFMcXlSS3pqNVI3U1hVRXptWVZzWWVvd1NVVTZGU2VkRlp0eUdtNGNTa1RtVjN2WWUvT1NCUE8yMzhKaUdVTHF3a2piZ0lRSGVENzVlYm5zcUEySHVPMW9tcEJjN0VHZlVaMzNuZWdQWmFrN1BFNTY5TElLMkhtVHpEMlBRUDFaWFNleG9ERUpuZnNrQ1NLTGNpQmFsc1ZBc3REaHdKVHhSa1ZzRU8rL2c1WWxjcnhHbm1kaDN5WWp3OEdFL1RVeWYxOTNwM3gxOUpJVVdJcjlCYW5ERkV0azlsaXpCeGNwYnQ3aWV6TzJFWTlmTFQ4czI5YTViWFZwMHRHZlpoRFV5K3lRTnI5TkJ5WkFlMjNrL0VQSFBiU0NvbVJGTy9RRExmaFo5djFsU3gweEprYStHaGdWUHIrcWcvcWViTkt2TTJaUW1CbUJOSFcrMGdRcUFNRElFdyt3NEJ4VzZ5dkNlcU9wM1pLSzJMSWpRUHRvSks1RkdvVTVULzMiLCJtYWMiOiI0MmE5NGIyMjg3Mzc3MzVjNmJhMTZmNWE4MTBhYzJjODc5ZDc1OTNmMzU0NmZlMzQ5ODAwNmJiY2Q0ZDI2N2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1965249811 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vDehmGwjPbFVFyq7uAxb5As1xZskdrmYUUzjkquw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965249811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-803207960 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 16:05:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRrU3U3bUZrdHVuU3ZYVzlHbTRsaFE9PSIsInZhbHVlIjoiVjBITTJRWmhPaDZ2aWdFT21xd3lrait1RmVyN2pxY29MMzdMbHFMWEF4d200UWdFVUhTOGs0a1BaSzRSUlJlVENheHV3dEZvUWhjNUxPMTErQTRmZU9NZHdybG1RQk1pU09qRTV0eFpyNHk2N2hvRWdlSTY4bk1ueHJJdERvWHEyUVlDOVJ4dFh2REduVitpcUpIRzdtRUpuMU9mTnRnT3lJdHJmZFlYWlplSVBocFBRVElZSFp3aE5tc0tQUFFWa0o5WEpRR2JOYjNNMWlGcW81dGFqM3kwVUFBQi9VcW9udUszb1JoeFZBQy9kUUc4QlRIdER4MnZ6VnRnK21kMWRwQnhjbEFETU5KZml4Vm9PNGdNWEMzdnRhM0dDb2VqMmUvcXZiM2pQK3NYY1FQc1F6M1djU3lIREI1WWxUQXpIME9tZ2c3cjl3ZnNLazRNcXdYYVI5TTRjS3o2S3cyakozWlZHaEVJZnFnTWpLclNvaWt5Ykd3ZS9aNVlhK0ZDZ0ZWR1ZxdEJsd1R5N1NjZE5JMVZWR2lnd1lJdGpaYVBibWVQSGxPN1BiVUxCSFdYTllWZ0M2T01KRHd6Q1B6R3BrT1FhSG9sR2xoaFM5RDY0MUdIaW4vakxFZ0pCUEVHT293RHY2eFJNbk1vclBWMjREc1pQd1NZcjVlTmlCdlgiLCJtYWMiOiI4M2JmYjM4YWUxM2MwZWI2ZjM5ZTBjMzkwNWVlYzhiNzkyMzJkMjVmZjllNmJiNzk3OTE2MTM4OGMxYzM2NzQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJGR2pxUEFHS3BubXBBbThpUG1SWWc9PSIsInZhbHVlIjoibEVtc2dkb2ZXQm1UamFicWpOWEowZis3R3lqbEtLQkhEckJ1bkQxZzB1TmI0VVBRSWltQ2dXMG4rYm9NWmdFYjNudGxkR0xFVnltWklESzZuR1M5STBFSVIyOFkzNDEzMHFEVCt0UWZBdkZpdnIxQkpSMmZycExWRWFDWFpuVlEwT3p1ZVBPZmRzVU8zWnczVTZURFFRYUhJejdCU1FRT2x0SHJJRms3N3l3bEtNQzBIR2V5UTVHd25aaUJkQ0x0aGtieFRidE9qelM1QjZzNlA0WElWY1UzWWRZZkFpVG01dmdmTHIrVGx5YTlUWUZiY3pnL3M2SWVMTUdoU0k0UjFTZVU5eGtmQWtidzVFTGE2elVLOWdnVEtHajllb0czY3JqNUczL0tuNnRLbDB2d1ZYdUxZY1RuZXljdWMxeFljamFxalMzRW1JVll3ZDB6cmN5SUxwU0dFQ3ZxWHNpZUVQWEoycE5lWmhDNFcwRDNNTTB2TENuU203b3BPSWZ2NjVWWFdkMFREUXFYclhTRXVYSDdsdm9DdHdtRmhSNzh1V09yZysyTFdPT2t4ekJ0TmhkV2tITVZxSGo2WXdwdUtkNnppMUdoMm01Q3U2UkRIcmVFVm0xak0xV1lpVWZEemFCaE51N3dBa3liWjRheXNEVjcyQjBLU1NjOU9tSk0iLCJtYWMiOiI1OWUwNTQ3N2VjNzRhMWM0YTIwN2ZiOTJiZmRmMWQzNjMxZmEzNjhkZWZiZTc1MzQxOTFhNjY4ZGJlZDFiNWIwIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 18:05:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRrU3U3bUZrdHVuU3ZYVzlHbTRsaFE9PSIsInZhbHVlIjoiVjBITTJRWmhPaDZ2aWdFT21xd3lrait1RmVyN2pxY29MMzdMbHFMWEF4d200UWdFVUhTOGs0a1BaSzRSUlJlVENheHV3dEZvUWhjNUxPMTErQTRmZU9NZHdybG1RQk1pU09qRTV0eFpyNHk2N2hvRWdlSTY4bk1ueHJJdERvWHEyUVlDOVJ4dFh2REduVitpcUpIRzdtRUpuMU9mTnRnT3lJdHJmZFlYWlplSVBocFBRVElZSFp3aE5tc0tQUFFWa0o5WEpRR2JOYjNNMWlGcW81dGFqM3kwVUFBQi9VcW9udUszb1JoeFZBQy9kUUc4QlRIdER4MnZ6VnRnK21kMWRwQnhjbEFETU5KZml4Vm9PNGdNWEMzdnRhM0dDb2VqMmUvcXZiM2pQK3NYY1FQc1F6M1djU3lIREI1WWxUQXpIME9tZ2c3cjl3ZnNLazRNcXdYYVI5TTRjS3o2S3cyakozWlZHaEVJZnFnTWpLclNvaWt5Ykd3ZS9aNVlhK0ZDZ0ZWR1ZxdEJsd1R5N1NjZE5JMVZWR2lnd1lJdGpaYVBibWVQSGxPN1BiVUxCSFdYTllWZ0M2T01KRHd6Q1B6R3BrT1FhSG9sR2xoaFM5RDY0MUdIaW4vakxFZ0pCUEVHT293RHY2eFJNbk1vclBWMjREc1pQd1NZcjVlTmlCdlgiLCJtYWMiOiI4M2JmYjM4YWUxM2MwZWI2ZjM5ZTBjMzkwNWVlYzhiNzkyMzJkMjVmZjllNmJiNzk3OTE2MTM4OGMxYzM2NzQ1IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJGR2pxUEFHS3BubXBBbThpUG1SWWc9PSIsInZhbHVlIjoibEVtc2dkb2ZXQm1UamFicWpOWEowZis3R3lqbEtLQkhEckJ1bkQxZzB1TmI0VVBRSWltQ2dXMG4rYm9NWmdFYjNudGxkR0xFVnltWklESzZuR1M5STBFSVIyOFkzNDEzMHFEVCt0UWZBdkZpdnIxQkpSMmZycExWRWFDWFpuVlEwT3p1ZVBPZmRzVU8zWnczVTZURFFRYUhJejdCU1FRT2x0SHJJRms3N3l3bEtNQzBIR2V5UTVHd25aaUJkQ0x0aGtieFRidE9qelM1QjZzNlA0WElWY1UzWWRZZkFpVG01dmdmTHIrVGx5YTlUWUZiY3pnL3M2SWVMTUdoU0k0UjFTZVU5eGtmQWtidzVFTGE2elVLOWdnVEtHajllb0czY3JqNUczL0tuNnRLbDB2d1ZYdUxZY1RuZXljdWMxeFljamFxalMzRW1JVll3ZDB6cmN5SUxwU0dFQ3ZxWHNpZUVQWEoycE5lWmhDNFcwRDNNTTB2TENuU203b3BPSWZ2NjVWWFdkMFREUXFYclhTRXVYSDdsdm9DdHdtRmhSNzh1V09yZysyTFdPT2t4ekJ0TmhkV2tITVZxSGo2WXdwdUtkNnppMUdoMm01Q3U2UkRIcmVFVm0xak0xV1lpVWZEemFCaE51N3dBa3liWjRheXNEVjcyQjBLU1NjOU9tSk0iLCJtYWMiOiI1OWUwNTQ3N2VjNzRhMWM0YTIwN2ZiOTJiZmRmMWQzNjMxZmEzNjhkZWZiZTc1MzQxOTFhNjY4ZGJlZDFiNWIwIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 18:05:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803207960\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">StALtWfU8NYnwkvXurgnX7WVZ1RJaeCN3tN5Fnje</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1877</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">cleaner Mop</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1877</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}