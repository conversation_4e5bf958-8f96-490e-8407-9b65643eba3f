{"__meta": {"id": "X0bced1b73ebe4b59d1a85f6d9302b9e0", "datetime": "2025-06-30 18:57:51", "utime": **********.477191, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.051738, "end": **********.477204, "duration": 0.42546606063842773, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.051738, "relative_start": 0, "end": **********.426103, "relative_end": **********.426103, "duration": 0.37436509132385254, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.426116, "relative_start": 0.374377965927124, "end": **********.477206, "relative_end": 1.9073486328125e-06, "duration": 0.05109000205993652, "duration_str": "51.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140600, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00315, "accumulated_duration_str": "3.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.456256, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.467277, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.54, "width_percent": 20.952}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.470655, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 83.492, "width_percent": 16.508}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2352 => array:9 [\n    \"name\" => \"مجسم شخصيات مختلفه\"\n    \"quantity\" => 1\n    \"price\" => \"5.75\"\n    \"id\" => \"2352\"\n    \"tax\" => 0\n    \"subtotal\" => 5.75\n    \"originalquantity\" => 39\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2353 => array:8 [\n    \"name\" => \"مفتاح علبه\"\n    \"quantity\" => 1\n    \"price\" => \"6.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2353\"\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n  ]\n  2354 => array:8 [\n    \"name\" => \"العاب شكل بطه\"\n    \"quantity\" => 1\n    \"price\" => \"6.50\"\n    \"tax\" => 0\n    \"subtotal\" => 6.5\n    \"id\" => \"2354\"\n    \"originalquantity\" => 10\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1794264841 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794264841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-944639545 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1777 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; XSRF-TOKEN=eyJpdiI6InZQTXdBeERweGhtRnJFN0dCYUFndlE9PSIsInZhbHVlIjoiMElDYnRhK1pUYlJBZWZSMG8xMTNmYk9rVUE2QlVaY0NYc3JIbG1FaWtXaVVrcEZ5MW90b0xiY0M3RGVCbi9GVmtQV0xlQmt2aXllQUNtTXMxakZTOFdDVGl4MUJOalFBRWl1RE5mWk1QaGtHVDhlY0hRK0J3R3hsWERoR2hJczZoQ1dVRUlRMGFuUklVTnlWaU00OEQ3K3o1NmVCdzI3NlRmaWUrOUpBQTRRYnk3Wmx3dlYrT04xL0xya0xlZFVyN2NKcTNDTWo1QjNMbnRvek12ZlhtRElSS1pxbU04Q21IRjIyd3A0bVNGdC9aelNaWW95bTJYSnJaTkdZVVJpaVhLc0ZYZ1lGOWFDNFdKcngzVUp3aUpsR09MVCtFSSsxREk1WllSK3JWUWJzS014c1dlVmlscUREMUdnbG1qbzlSY1VoZjVubDVBVUp4K2tmeVE1VDVwRFdUaW85NGV0NVlYMEthWHdMNlliczdsTVJrNlhGUGFMVEs4SW9RK0hHMjdpM2pHVGRVbUtHQUh5L2o1emNwWC9TN053eVdrbmFVUWxKRVptWm9QaHRmZWEvcXI0Mndzdnk2OTd6RlM4SEczUldpd0FsUE5CZDJlc2txRFJOUmlTdnFPSUdGKy9RdVNSNGZCVm16NDRQTkhvZmRJZTlJdVAzZjdaLzIrbVgiLCJtYWMiOiIyYjI1MGZiMzQyMzdkZGRlZjZiZjc4Yjk4YzYxMzM0ZTRjNjJmZTQ5MjM4ZDliOGRhY2UwNzIxNWVkM2UyYTNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZpS2NhNit6RGxCSy9ZVGJLdDB6RVE9PSIsInZhbHVlIjoiMm5MZ09DQzhIeEJyMjFzNm0xSGQxYmI0SlZTZTQxM01JdWp4NlJFR1M0SHBubEJSOUZMcEc4bDBJTytUSUVxRmpzNi9Pak5JOXNMdXIzTWxYUVU3R0FvZjhMVE40UGlqbkQ4eWtEekt0SDVhTU1wK2h5NXRDcWRmWVE5V3ZqWkZFbjVzM1UwODJhbEYvNUhBdkUrSHloem1ZOUdGWTMwSExuZ3VrQVRvcWNzMEFlc0dLNWxKcmNwcGJBYU9IbFJOTER4SGEvaVNOdW1VTnpLN0RLdTh1YUxpZ0RrYVArbGk2T05MSVVTOG1tUk9wNWxpRmFaWVlCL0hMYmhGbGZFdisxQUFIUmswbVFDY1VyQllHMThvVWdacENZcUU0Ulo1ekwwa0U5RXlSUk5UWnpKQkdkRFY2R0JJckkybU91Ukx6cTQzYnJ5N3QxN2lMT2RZUUlOOFlLcWZuWTI3Sm5GanBMbTQrSmpqVHl3SzJvbGNkOGpCeXc2cXJCVU5MSEdGeVY3ZlU2Qy90MEtKcjlhd2l4MlJEMk1DcG9abmNrSzVjNnA4RnovaThkQWtseGsxb1JrSWJNMVIwbTBJZ3ZQWDNvRkdnTFFOOFdYTmN4Ulk2dnhBVGtYUDVtam1kM0pJd2lxaUhEeUN2RjIweitoUExpYU5yWU5iSFd1RE11Y24iLCJtYWMiOiJmNjIwNGI1YmYwYWJiZWUyYTQxN2M5MWNlMGI0NmQ5ODQ3YTg0MGFkNjk0NzFhZDAyYTk3NWUxZGM4OTA4MmY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944639545\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1669012327 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669012327\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-817962929 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:57:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik11S04vSE4zMERlWG1BdEkwSmpUdlE9PSIsInZhbHVlIjoiU3pjV0xqVW9QRDFTYXM2ZDljMjNIZlN5MnNFOWhRZ3gxNVZSYjJKWkxLdWRxeWRFVlpLRmxuUWxnOW1LSFJva1VqOU5wRlVYeGRuM0x5VTlWY3RBM1JTcXVXdkd6bkFxTXQyTWpNbWI5bG1uOHB1bjVvOTU4d05QRnQvZFBvRE9tZDVjcEJybzhONFdsR3l5ckVnZlAxNW5rY2k1Y3lYc1NzV3orY1hWenlYWCtvZDF3aFBEamEvY3p1c2xwUWVDNkVzNTJ4SGl0YlJlSCtZY0cvUjRybjhsWnJmSWYwN1NlR2duNWhGV2Y1Z2diMFpPRkg4Sll6a0hkY1NTcHd0dGVSTDFpV3dOU01mdHJZWDdSNk9jWE1xcEdCMU9MckpTN2lNNHFIK1FnRS94VlVhNFpVNEtteUdCd015d3BFT0FBQVA5enF1Mk1nSzBGdFlnUWl0OW1sZXdQNXNtdFZsOUowMlJTU0U0ZFZYdU5zL0FTanBER21vODBTTUJvTXh4elRHZGUrMHRUZEx3d09ZaHVSakphS2k2V051ZlZxWVArSjNwSjNvR3Y5dFhuaHoyemFadEhoc2FEWmpEQ25VNndxWlVwZFNiZTltYU5uTWQ0dHpRY0NDVjdTYkxrMjI4WHhYSVdEb0FLQlVLQ0ZqNzN2Y0pzRzRubTJQM2pCYUkiLCJtYWMiOiI4YTlkNTZjNGFkY2I3YzMzNDY0NzYzNjM0YTA3YTc1NDY0MTc4YjczODE2MjEyYzZlYjhhNWVkZDljZjRlOTYxIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:57:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlgyODRiYXltaEF1VUZPZGk1cWxyUEE9PSIsInZhbHVlIjoidTZWTzY4R0pjaHlCVisvV2ZNMXQ3ZXU4NnZPZTBJVWdWRCswZGR4QXBwVnFVYlNsMWV4UFFRSE5iUFFuMUJRMkJ2T0dYL04yclBqWW5rYTNKeFJWQnhQNTZ4MmdWZG9pei9DUE5zRTY0OUhHME9ITjJ0MlN6SVNnZmJLMngvSmtHQW5ZY0RicENpNEJQR2xqYzlrVVRiZVhRdHNRNjUzalZVZ1NLR0NiK2lHUnFIODArUnc3dG5Tc2c2bC81NWhlcTVMbXlQS3F4TThsZmFGcjdIRUFzOUdoaG5nVVpEcWhaaEpnaTc4NTE4MCt6VlhINWFvYjRneENxaWcvVGdQQ3FCTUR4bFRQY2NMLzhxVVlOeGVLUHA3b3pJb1lJYmUrdCtjZUxhdTdpTUhMQ1lGcytIV2htUFNOY3F6czNiTWJDMFpkT3l5dzF1MlhCdERhcDcyeks3Wms1eHQwd3RXd0RTVG5DMDNqZWZCL1podm8vZmMwcGhVa2ljV0NiRTJhdzNKYTVmeCtVdDZucXpObWZpalh6Q3lUTndDanM2YjRMQitESmcrdEJTWktqMHBzYko5WUhQTWN5YXNYaFF1VElVQTBXQWI0enNsdDRLSjF5NjZaczM1L0ZLYU1qVE9vTFdhcmFMa011UTZJZzgzNzByM2lhUWhEQzEwMlJRRnYiLCJtYWMiOiI5NzQ3NGRiYTU1NWM4MThjMGNmZjcxMWZhMTJjNWYwNWYwYmQ2NGE2Zjg1MmQ4NjZkMzUyYWY3YWIxOGViZmU2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:57:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik11S04vSE4zMERlWG1BdEkwSmpUdlE9PSIsInZhbHVlIjoiU3pjV0xqVW9QRDFTYXM2ZDljMjNIZlN5MnNFOWhRZ3gxNVZSYjJKWkxLdWRxeWRFVlpLRmxuUWxnOW1LSFJva1VqOU5wRlVYeGRuM0x5VTlWY3RBM1JTcXVXdkd6bkFxTXQyTWpNbWI5bG1uOHB1bjVvOTU4d05QRnQvZFBvRE9tZDVjcEJybzhONFdsR3l5ckVnZlAxNW5rY2k1Y3lYc1NzV3orY1hWenlYWCtvZDF3aFBEamEvY3p1c2xwUWVDNkVzNTJ4SGl0YlJlSCtZY0cvUjRybjhsWnJmSWYwN1NlR2duNWhGV2Y1Z2diMFpPRkg4Sll6a0hkY1NTcHd0dGVSTDFpV3dOU01mdHJZWDdSNk9jWE1xcEdCMU9MckpTN2lNNHFIK1FnRS94VlVhNFpVNEtteUdCd015d3BFT0FBQVA5enF1Mk1nSzBGdFlnUWl0OW1sZXdQNXNtdFZsOUowMlJTU0U0ZFZYdU5zL0FTanBER21vODBTTUJvTXh4elRHZGUrMHRUZEx3d09ZaHVSakphS2k2V051ZlZxWVArSjNwSjNvR3Y5dFhuaHoyemFadEhoc2FEWmpEQ25VNndxWlVwZFNiZTltYU5uTWQ0dHpRY0NDVjdTYkxrMjI4WHhYSVdEb0FLQlVLQ0ZqNzN2Y0pzRzRubTJQM2pCYUkiLCJtYWMiOiI4YTlkNTZjNGFkY2I3YzMzNDY0NzYzNjM0YTA3YTc1NDY0MTc4YjczODE2MjEyYzZlYjhhNWVkZDljZjRlOTYxIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:57:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlgyODRiYXltaEF1VUZPZGk1cWxyUEE9PSIsInZhbHVlIjoidTZWTzY4R0pjaHlCVisvV2ZNMXQ3ZXU4NnZPZTBJVWdWRCswZGR4QXBwVnFVYlNsMWV4UFFRSE5iUFFuMUJRMkJ2T0dYL04yclBqWW5rYTNKeFJWQnhQNTZ4MmdWZG9pei9DUE5zRTY0OUhHME9ITjJ0MlN6SVNnZmJLMngvSmtHQW5ZY0RicENpNEJQR2xqYzlrVVRiZVhRdHNRNjUzalZVZ1NLR0NiK2lHUnFIODArUnc3dG5Tc2c2bC81NWhlcTVMbXlQS3F4TThsZmFGcjdIRUFzOUdoaG5nVVpEcWhaaEpnaTc4NTE4MCt6VlhINWFvYjRneENxaWcvVGdQQ3FCTUR4bFRQY2NMLzhxVVlOeGVLUHA3b3pJb1lJYmUrdCtjZUxhdTdpTUhMQ1lGcytIV2htUFNOY3F6czNiTWJDMFpkT3l5dzF1MlhCdERhcDcyeks3Wms1eHQwd3RXd0RTVG5DMDNqZWZCL1podm8vZmMwcGhVa2ljV0NiRTJhdzNKYTVmeCtVdDZucXpObWZpalh6Q3lUTndDanM2YjRMQitESmcrdEJTWktqMHBzYko5WUhQTWN5YXNYaFF1VElVQTBXQWI0enNsdDRLSjF5NjZaczM1L0ZLYU1qVE9vTFdhcmFMa011UTZJZzgzNzByM2lhUWhEQzEwMlJRRnYiLCJtYWMiOiI5NzQ3NGRiYTU1NWM4MThjMGNmZjcxMWZhMTJjNWYwNWYwYmQ2NGE2Zjg1MmQ4NjZkMzUyYWY3YWIxOGViZmU2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:57:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817962929\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1530720741 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2352</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1605;&#1580;&#1587;&#1605; &#1588;&#1582;&#1589;&#1610;&#1575;&#1578; &#1605;&#1582;&#1578;&#1604;&#1601;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.75</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2352</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.75</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>39</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2353</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1605;&#1601;&#1578;&#1575;&#1581; &#1593;&#1604;&#1576;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2353</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2354</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1575;&#1576; &#1588;&#1603;&#1604; &#1576;&#1591;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2354</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530720741\", {\"maxDepth\":0})</script>\n"}}