{"__meta": {"id": "Xd5509f86a3e9ce7079412ca71763ead5", "datetime": "2025-06-30 18:10:19", "utime": **********.657653, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.232067, "end": **********.657666, "duration": 0.4255988597869873, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.232067, "relative_start": 0, "end": **********.559874, "relative_end": **********.559874, "duration": 0.3278069496154785, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.559884, "relative_start": 0.3278169631958008, "end": **********.657668, "relative_end": 2.1457672119140625e-06, "duration": 0.09778404235839844, "duration_str": "97.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53423416, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.65403, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1416\" onclick=\"\">app/Http/Controllers/PosController.php:1416-1524</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.007859999999999999, "accumulated_duration_str": "7.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5931048, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.196}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.606026, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.196, "width_percent": 6.616}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.619613, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.812, "width_percent": 6.743}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6216588, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.555, "width_percent": 5.471}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1427}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.62589, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1427", "source": "app/Http/Controllers/PosController.php:1427", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1427", "ajax": false, "filename": "PosController.php", "line": "1427"}, "connection": "kdmkjkqknb", "start_percent": 80.025, "width_percent": 5.089}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1428}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.627737, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1428", "source": "app/Http/Controllers/PosController.php:1428", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1428", "ajax": false, "filename": "PosController.php", "line": "1428"}, "connection": "kdmkjkqknb", "start_percent": 85.115, "width_percent": 3.817}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 523}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1432}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6309302, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PosController.php:523", "source": "app/Http/Controllers/PosController.php:523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=523", "ajax": false, "filename": "PosController.php", "line": "523"}, "connection": "kdmkjkqknb", "start_percent": 88.931, "width_percent": 5.725}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1511}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6447968, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1511", "source": "app/Http/Controllers/PosController.php:1511", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1511", "ajax": false, "filename": "PosController.php", "line": "1511"}, "connection": "kdmkjkqknb", "start_percent": 94.656, "width_percent": 5.344}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1251687049 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251687049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624968, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-718212202 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718212202\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630184, "xdebug_link": null}]}, "session": {"_token": "YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"American coffee-قهوة امركية\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 105\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1892 => array:8 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"1.50\"\n    \"tax\" => 0\n    \"subtotal\" => 1.5\n    \"id\" => \"1892\"\n    \"originalquantity\" => 200\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1135020929 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135020929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1019111017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1019111017\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1840 characters\">_clck=dyjnip%7C2%7Cfx7%7C0%7C2007; _clsk=c5lft1%7C1751306314991%7C2%7C1%7Co.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlgzV3Jwa1I4U0k1L2M1N2lBbmZzaVE9PSIsInZhbHVlIjoiOElVZXlVejJYT3NhQjMwVnBNbHNNRDF2cUFYVkxaTVI4OG1YeGFQbllFTXRheitJT0RZSDAwK29wNDY1dVUrWFN2eWo2TVJna1kxU1YwVFFBWDFFQUZGSk10ZkRaaXR3T1kvU3RIalJYQ2dRWEQzbTBkNDRaQUQxOVVpRVZkQkQra2N5L2l1eld2UmdITlJDTGtiUWV3OHRhQyt0bTFWeTNQaXR1bDk2V0lJWU5CaHBmdG1EaFl1aVdick51S1dYSXJxRWZ4N0JYb3BEUXh3MG5XbzJxcFNvK1FiY1lFWjZrNnNMZkpzNXhOTlBIOUNUbWNYUnVndzE5WEZPRnN5cjJTb3A1ZGZNUFdLUDhkM01DUnlFVXBRais4UW5haFM1RUozYjdXRUlLL2VWVzVNV2ZOZVdsR3NTUFJmejBSSWsza1dLTzQ4THV4VUk5a2kwVXdzdWkxbWF2Yy9Dckl2VmxnczFuelljaWkxKzZtcTJneElWT0pxMmExbTd6Q09sNktkTkFZRmtUa1RoL2IwekRIS0dqTVY4dWpNZURZYXFzRjVubklxVHY4UExrSHJRRWVKczk4NHQyaU00NGhhTktTZkdiRUdaVk83U3k2RFZTbWs1dUFQandEYlJkZktaUHdrUE4weG45cUliRU9sbTFvRnFIOTBtS3dSSEpRNEciLCJtYWMiOiI3MTdhMTExZjQ1MDM4ZjBkMjhhZTE4ZjE1MjlmNGZmNTE3NmE0OWRkNWM5N2FiYjNmMDEzY2NlMmJlNTViYjFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRwbHpiVW5CR0VvdWRrSDd2L3NqSEE9PSIsInZhbHVlIjoiOWM1Y3pmc1FWZHpKOWp5SVhjTHpvN1dQQ2tHb0NvcXRwcmNBbklOa0NsajloRzBXa2R3RTR4OUNLS294ckdjQmVOZVRFZUJpYXdDb2tFek16TWVUbTJNQUovRXh0dm16ampGT1dUS1JMYUt5VHkxemUxT3dGSjZ6VFhZdWQ2dUhiR3RsT1NFOWt6RWpCL3ZVeHBPNG5rMGVMZDhXbnQ1VmJsdlpYY20reEZjcjc3ZTFNQ3hxYWE5dkJWZENYdlQ4UWE3TEFoTDBOSTBMU3RjNDZVQld4ZHBPUXBJdUQ3WUQrbk8xSW5iOUEwczdBRUxVMnpXaXh4VVR3aWNnRTBxUTB4VGZpR2ZrMStsRXpIWEVCTUVYTURFSXg4Y3JEenJrbXcrYUg5RlMxa1BGcUliRUlGS3NJajBXTHNITlJzZjZoS0kwcmpIUGxsc2Q4TlptVUdiWU5OUnpwUUJtR2hxanNKMGt3aVhjNk9DcW1yR0tyTHhuZHRIMStpTUpxM1lKM25mTU1nSXU0TmlDU1JPbXFWdisvVFVJYTc4UHIvaG5welY0d3JvN3Zid2l5aUxRaUV6WmFDUi9SZ24ralc3RlJ5SisxaGZ4eldodGJRTG92NW5HM2NnWmdIY3A5b0hIMCtIZ05YdGJCbWR5T0l4Uk5sU1hKMHM4ZnJaMGRHUXYiLCJtYWMiOiJkOGFmYzlhMDBmNDYyOWNjZWE1NjFhOTg4ZGZhOTA2YTZlZTBiMTQxYjJmOGJhMmZiZGMzOGExYmIxMWY3OTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IWzZVmbnvAV228IxeCe5kTm98XJB9iL2U1kZEL3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1229815045 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 18:10:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZiaEJtQ3NTNThYQW9uN2lXaTErSXc9PSIsInZhbHVlIjoiQVUvOWdSSmszT080Y1ZYd3owRDUzU0dId1V1ZXMzc2pmd0R2NnZLdWdLMmpxblFRUHMyYnUvSWdpeW1EUXI3VU5kb0Y3SFN6OGJGTXhyeGxmM25JcThXdWhLRE1YWWMzUStHVXVzSXZBM1N5Z05KWDFWeldKRmlQTjdpRW83MDRHTXNLV0Z4YlNscWl4cUVEQThaeXRNRzBqeWwzdlQ3U3RQUDVsNVpFWjVGdnIxWlpLYVUvZERuaENUVVkzRXNFQXRGclFFY0dyTUZla0dWMFZCVFlVcjM5YWYrUW04aTFweXBpcFdabDlHUElLdlg5TWpnZTdVbUFDK0NyOHd4QVI4a09qTWdKQUt1bDViY1ZZS2VCQWQzZTZHWXFHM1RIZHhBZnNsdWQ2cnFOeFdzSCtVWHBtcUVrOW81aDFPK20rRVR6UTViMmdoOHFieEdzek1IeWFvdzlmSEt5SmMvM0V6NWxQRnBHRVdVai9DbzVIRzJ0MjdwQVNnWGJlckQycWJUVm9QWWc2MHNqbElJQTRXRm5TRkFlOHRVMWlWWGp0dXhnTHRORXhkSWhrWWJzUFI4U0FQYmxTMmlDc01lTVNFdHNWM21lcVZoVFY1ZC96SnFneE1NS3RrVkhWLzM4SjJaNTVjMUtjVTNLNTRQeWdhbW9mK0I0Z1NoZVBuTlUiLCJtYWMiOiJjOWNiNWFiZGFlY2JmZjdkMDhiMTlhMzU0N2I1OGYwYjhlYzNkMzE4MGUxN2EzYzg1NGZhODIxNDExYTI2ZWRiIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZjMU1OQ1plcnY1QWVodGVTTjV4RXc9PSIsInZhbHVlIjoib3A2blhZNGpxd0FtbmFZTjJBV011NWRGUUZyN2dBdndWUVdJYjN6MkM2ajVTSExnN3J0amRJb3lRQ0QxSnpqYlZQOGt2Rkx3QVo0aEQxSWlwR0p0S2w2cGZWck5nRkxLeEFUc2xGVW5pZkVNbTZOaWJpVTE1K2hMb29pbTA0MkEvNDNRMXJZdFVZU2Y1RHNSeEFRNUhGenRvR01pdzllSFpyb0ExMjBsR2FWMTRjTE53bkFFUnF0ckl5VjJQUzZVVWZwOTNzVU9nV1IyWG53ZHFySWthaGdaUWRIUHJIRG1YZVlLQ21CdUJmUkUyR3psR2VxeURTZ0pMOFFaa0I4R3VKU1pad3pHVTg4NEJvdEpCWUhlMmV0VGZQcXROOENZRUhZV2RDZ1c0U0w2RldseUNRbnp5Y2crUVlvMWNnZUdPOVVmQzNxQXBhMkxsK2ZjOXlsdDZQbWlMdElma2w1Titkc2xTdFRPb2FRb0NZTWg5RXJycTNCWFlTWEU1K2Zjei81V0g1TmswaVU0SmhhNWFDVmc0QmlrSVVCN002eklveFFzVERFdy9TSFV3RGVCc2J5SFdYSlJ3eGZHUk5WY0tDWmVwWkVaNjF3d2hCam9QT1hKV0lrcWY3MkFhT0luaitQTWllTUJhNUdQOWVWNUFJdlNKVDA4ZG00MnYyQkIiLCJtYWMiOiI0ZjkyNTVlN2JjNzU5MzY3OWQ3YzNhYjJhMjM0OWUzMzBiMjA2MDljOGVlMTRlZjVlZjQ1YTM1ZGMxZGE2YzM2IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 20:10:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZiaEJtQ3NTNThYQW9uN2lXaTErSXc9PSIsInZhbHVlIjoiQVUvOWdSSmszT080Y1ZYd3owRDUzU0dId1V1ZXMzc2pmd0R2NnZLdWdLMmpxblFRUHMyYnUvSWdpeW1EUXI3VU5kb0Y3SFN6OGJGTXhyeGxmM25JcThXdWhLRE1YWWMzUStHVXVzSXZBM1N5Z05KWDFWeldKRmlQTjdpRW83MDRHTXNLV0Z4YlNscWl4cUVEQThaeXRNRzBqeWwzdlQ3U3RQUDVsNVpFWjVGdnIxWlpLYVUvZERuaENUVVkzRXNFQXRGclFFY0dyTUZla0dWMFZCVFlVcjM5YWYrUW04aTFweXBpcFdabDlHUElLdlg5TWpnZTdVbUFDK0NyOHd4QVI4a09qTWdKQUt1bDViY1ZZS2VCQWQzZTZHWXFHM1RIZHhBZnNsdWQ2cnFOeFdzSCtVWHBtcUVrOW81aDFPK20rRVR6UTViMmdoOHFieEdzek1IeWFvdzlmSEt5SmMvM0V6NWxQRnBHRVdVai9DbzVIRzJ0MjdwQVNnWGJlckQycWJUVm9QWWc2MHNqbElJQTRXRm5TRkFlOHRVMWlWWGp0dXhnTHRORXhkSWhrWWJzUFI4U0FQYmxTMmlDc01lTVNFdHNWM21lcVZoVFY1ZC96SnFneE1NS3RrVkhWLzM4SjJaNTVjMUtjVTNLNTRQeWdhbW9mK0I0Z1NoZVBuTlUiLCJtYWMiOiJjOWNiNWFiZGFlY2JmZjdkMDhiMTlhMzU0N2I1OGYwYjhlYzNkMzE4MGUxN2EzYzg1NGZhODIxNDExYTI2ZWRiIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZjMU1OQ1plcnY1QWVodGVTTjV4RXc9PSIsInZhbHVlIjoib3A2blhZNGpxd0FtbmFZTjJBV011NWRGUUZyN2dBdndWUVdJYjN6MkM2ajVTSExnN3J0amRJb3lRQ0QxSnpqYlZQOGt2Rkx3QVo0aEQxSWlwR0p0S2w2cGZWck5nRkxLeEFUc2xGVW5pZkVNbTZOaWJpVTE1K2hMb29pbTA0MkEvNDNRMXJZdFVZU2Y1RHNSeEFRNUhGenRvR01pdzllSFpyb0ExMjBsR2FWMTRjTE53bkFFUnF0ckl5VjJQUzZVVWZwOTNzVU9nV1IyWG53ZHFySWthaGdaUWRIUHJIRG1YZVlLQ21CdUJmUkUyR3psR2VxeURTZ0pMOFFaa0I4R3VKU1pad3pHVTg4NEJvdEpCWUhlMmV0VGZQcXROOENZRUhZV2RDZ1c0U0w2RldseUNRbnp5Y2crUVlvMWNnZUdPOVVmQzNxQXBhMkxsK2ZjOXlsdDZQbWlMdElma2w1Titkc2xTdFRPb2FRb0NZTWg5RXJycTNCWFlTWEU1K2Zjei81V0g1TmswaVU0SmhhNWFDVmc0QmlrSVVCN002eklveFFzVERFdy9TSFV3RGVCc2J5SFdYSlJ3eGZHUk5WY0tDWmVwWkVaNjF3d2hCam9QT1hKV0lrcWY3MkFhT0luaitQTWllTUJhNUdQOWVWNUFJdlNKVDA4ZG00MnYyQkIiLCJtYWMiOiI0ZjkyNTVlN2JjNzU5MzY3OWQ3YzNhYjJhMjM0OWUzMzBiMjA2MDljOGVlMTRlZjVlZjQ1YTM1ZGMxZGE2YzM2IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 20:10:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229815045\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1089935797 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YRPdkI4yERoYTa6LniEKHqARBPjEMQZS79C4hWds</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">American coffee-&#1602;&#1607;&#1608;&#1577; &#1575;&#1605;&#1585;&#1603;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>105</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1892</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.50</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>1.5</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1892</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>200</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089935797\", {\"maxDepth\":0})</script>\n"}}